import { createReducer, on } from "@ngrx/store";
import { DynamicComponentLoaderState, dynamicComponentLoaderInitialState } from "./dynamic-loader.state";
import { setPopupConfig, clearBreadCrumbs, creationResultReset, creationSuccessfull,UpdateData, creationUnsuccessfull, fetchedData, PushIntoBreadCrumb, setRecentLinkClickEvent, statusChangeSuccessfull, statusChangeUnsuccessfull, deleteSuccessfull, deleteUnsuccessfull, recheckCurModuleIndex, PopFromBreadCrumbUptoIndex, fetchedStates, clearData,  updatedAccount,  } from './dynamic-loader.actions';
import { widgetTypes } from '../dynamic-component-loader.types';

// Helper function to group users with multiple training institutes
function groupUsersByTrainingInstitutes(tableData: any[]): any[] {
  const userMap = new Map();

  tableData.forEach((user, index) => {
    // Create a more specific unique key - prioritize personId for users without email
    let userKey: string;

    if (user.emailId) {
      // Users with email: use email as primary key
      userKey = `email_${user.emailId}`;
    } else if (user.personId) {
      // Users without email: use personId as primary key
      userKey = `person_${user.personId}`;
    } else if (user.id) {
      // Fallback to id
      userKey = `id_${user.id}`;
    } else {
      // Last resort: use name and contact with index
      userKey = `name_${user.firstName}_${user.lastName}_${user.contactNumber || 'nocontact'}_${index}`;
    }



    if (userMap.has(userKey)) {
      // User already exists, add training institute to the array
      const existingUser = userMap.get(userKey);

      // Ensure trainingInstitutes array exists
      if (!existingUser.trainingInstitutes) {
        existingUser.trainingInstitutes = [];
      }

      // Add training institute if it's not already in the array
      const trainingInstitute = {
        tenantName: user.tenantName,
        tenantId: user.tenantId
      };

      const exists = existingUser.trainingInstitutes.some((ti: any) =>
        ti.tenantName === trainingInstitute.tenantName && ti.tenantId === trainingInstitute.tenantId
      );

      if (!exists && user.tenantName) {
        existingUser.trainingInstitutes.push(trainingInstitute);
      }
    } else {
      // First time seeing this user, create entry
      const newUser = {
        ...user, // Spread all user properties
        trainingInstitutes: user.tenantName ? [{
          tenantName: user.tenantName,
          tenantId: user.tenantId
        }] : []
      };
      userMap.set(userKey, newUser);
    }
  });

  // Return array with proper deep copy to avoid reference issues
  const result = Array.from(userMap.values()).map(user => ({
    ...user,
    trainingInstitutes: user.trainingInstitutes ? [...user.trainingInstitutes] : []
  }));



  return result;
}

const _dynamicComponentLoaderReducers = createReducer<DynamicComponentLoaderState>(
  dynamicComponentLoaderInitialState,
  on(fetchedData, (state, action) => {
    switch (action.widgetType) {
      case widgetTypes.DynamicTable:
        // Get the table data
        let tableData = action?.response?.result ? action.response.result : action?.response ? action.response : [];

        // Handle different table data processing based on structure
        if (Array.isArray(tableData) && tableData.length > 0) {
          // Check if this looks like the manage-all-access-superadmin data structure
          const isUserAccessTable = tableData.some((item: any) =>
            item.hasOwnProperty('userRoles') &&
            item.hasOwnProperty('emailId') &&
            item.hasOwnProperty('firstName')
          );

          // Check if this looks like the manage-all-users-TP data structure (training program users)
          const isTrainingProgramTable = tableData.some((item: any) =>
            item.hasOwnProperty('tenantName') &&
            item.hasOwnProperty('emailId') &&
            item.hasOwnProperty('firstName') &&
            !item.hasOwnProperty('userRoles')
          );

          if (isUserAccessTable) {
            // Only for user access management table - ensure all users have active: true
            tableData = tableData.map((user: any) => ({
              ...user,
              active: true  // Always set active to true for user access management
            }));
          } else if (isTrainingProgramTable) {
            // Group users with multiple training institutes
            tableData = groupUsersByTrainingInstitutes(tableData);
          }
        }

        return {
          ...state,
          currentModuleIndex: action.moduleIndex,
          tableData: tableData,
          tableDataTotalRecords: action?.response?.totalRecords ? action.response.totalRecords : null
        }
      case widgetTypes.DynamicSingleEntryTables: return {
        ...state,
        currentModuleIndex: action.moduleIndex,
        singleEntryTableData: action.response
      }
      case widgetTypes.DynamicProcessLogs: return {
        ...state,
        currentModuleIndex: action.moduleIndex,
        processLogData: action.response,
      }
      case widgetTypes.DynamicForms:
        if (action.response.length == 1) return {
          ...state,
          currentModuleIndex: action.moduleIndex,
          formJSON: JSON.parse(action.response[0].formJson),
        }
        else return {
        ...state,
        currentModuleIndex: action.moduleIndex,
        formJSON: JSON.parse(action.response[0].formJson),
          userResponse: JSON.parse(action.response[1][0].formResponse).formValue ? JSON.parse(action.response[1][0].formResponse).formValue : JSON.parse(action.response[1][0].formResponse),
      }
    }
  }),
  on(PushIntoBreadCrumb, (state, action) => {
    return {
      ...state,
      breadCrumbs: [...state.breadCrumbs, action.crumb]
    }
  }),
  on(PopFromBreadCrumbUptoIndex, (state, action) => {
    return {
      ...state,
      breadCrumbs: [...state.breadCrumbs.slice(0, action.indexToPopUpto)]
    }
  }),
  on(clearBreadCrumbs, (state, action) => {
    return {
      ...state,
      breadCrumbs: [...state.breadCrumbs.slice(0, 1)]
    }
  }),
  on(setRecentLinkClickEvent, (state, action) => {
    return {
      ...state,
      recentLinkClickEvent: action.event
    }
  }),
  on(statusChangeSuccessfull, (state, action) => {
    return {
      ...state,
      statusChangeResult: {
        success: true,
        cellClickEvent: action.cellClickEvent
      }
    }
  }),
  on(statusChangeUnsuccessfull, (state, action) => {
    return {
      ...state,
      statusChangeResult: {
        success: false,
        cellClickEvent: action.cellClickEvent
      }
    }
  }),
  on(deleteSuccessfull, (state, action) => {
    return {
      ...state,
      deleteResult: {
        success: action.success,
        performedAction: action.performedAction,
        widgetRowIndex:action.widgetRowIndex,
        widgetColumnIndex:action.widgetColumnIndex
      }
    }
  }),
  on(deleteUnsuccessfull, (state, action) => {
    return {
      ...state,
      deleteResult: {
        success: action.success,
        performedAction: action.performedAction,
        widgetRowIndex:action.widgetRowIndex,
        widgetColumnIndex:action.widgetColumnIndex
      }
    }
  }),
  on(creationSuccessfull, (state, action) => {
    return {
      ...state,
      creationSuccess: true,
    }
  }),
  on(updatedAccount, (state, action) => {
    return {
      ...state,
      data: action.details,
    }
  }),
  on(creationUnsuccessfull, (state, action) => {
    return {
      ...state,
      creationSuccess: false,
    }
  }),
  on(creationResultReset, (state, action) => {
    return {
      ...state,
      creationSuccess: null
    }
  }),
  on(setPopupConfig, (state, action) => {
    return {
      ...state,
      popupConfig: action.popupConfig
    }
  }),
  on(recheckCurModuleIndex, (state, action) => {
    return {
      ...state,
      currentModuleIndex: null
    }
  }),
  on(fetchedStates, (state, action) => {
    return {
      ...state,
      states: [
        { id: null, stateName: "Clear Selection"},
        ...action.states]
    }
  }),
  on(clearData, (state, action) => {
    return {
        ...state,
        statusChangeResult: null,
        deleteResult: null,
    }
  }),
  on(UpdateData, (state, action) => {
    
    return {
      ...state,
      OnupdateSucess: !state.OnupdateSucess,
    }
  }),
 


)

export function DynamicComponentLoaderReducers(state, action) {
  return _dynamicComponentLoaderReducers(state, action);
}