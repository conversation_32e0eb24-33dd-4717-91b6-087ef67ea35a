<div
  class="text-sm"
  gdColumn="1/ 2"
  gdColumn.lt-md="1 / 2"
  gdColumn.lt-sm="1 /-1"
>
  <b>Forms Order</b>
  <app-ng-dynamic-breadcrumb
    [lastLinkColor]="'var(--text-color2)'"
    [fontColor]="'#A7A8AC'"
    [fontSize]="'0.65rem'"
  >
  </app-ng-dynamic-breadcrumb>
</div>

<form [formGroup]="orderForm" >
  <div
    class=""
    gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
    gdColumns.lt-md="1fr"
    gdColumns.lt-sm="1fr"
    gdGap="12px"
  >
    <div
      class="card cardBorder shadow-none m-auto w-full mt-3"
      gdColumn="2 / 8"
      gdColumn.lt-md="1 / -1"
      gdColumn.lt-sm="1 /-1"
    >
      <div class="p-6 eligibility-list2">
        <div
          class="loginInput registerInput"
          fxLayout="row"
          fxLayout.lt-sm="column"
          fxLayoutGap="16px"
          fxLayoutGap.lt-sm="0"
        >
          <mat-form-field
            fxFlex="50%"
            class="exai-flex-form-field"
            appearance="outline"
            class="genderInput stateInput"
          >
            <mat-select formControlName="state" placeholder="State">
              <mat-option
                *ngFor="let state of states"
                [value]="state.stateId"
                (click)="examEvent(state)"
              >
                {{ state.stateName }}
              </mat-option>
            </mat-select>
            <!-- <mat-error *ngFor="let validation of validation_messages.state">
              <mat-error class="error-message"
                  *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                  {{validation.message}}</mat-error>
          </mat-error> -->
          </mat-form-field>
          <mat-form-field
            fxFlex="50%"
            class="exai-flex-form-field"
            appearance="outline"
            class="genderInput stateInput"
          >
            <mat-select formControlName="testCenter" placeholder="Test Center">
              <mat-option
                (click)="SelectedTestcenter(testsite)"
                *ngFor="let testsite of testCenterId"
                [value]="testsite.testCenterId"
              >
                {{ testsite.testcenterName }} {{ testsite.testCenterId }} -
                {{ testsite.tenantCode }}
              </mat-option>
            </mat-select>
            <!-- <mat-error *ngFor="let validation of validation_messages.state">
          <mat-error class="error-message"
              *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
              {{validation.message}}</mat-error>
      </mat-error> -->
          </mat-form-field>
        </div>

        <div
          class="loginInput registerInput flex"
          fxLayout="row"
          fxLayout.lt-sm="column"
          fxLayoutGap="16px"
          fxLayoutGap.lt-sm="0"
        >
          <mat-form-field appearance="outline">
            <mat-label>Event date</mat-label>
            <input
              clas="w-full"
              matInput
              [matDatepicker]="picker1"
              (dateChange)="changeCalendar($event)"
              formControlName="requiredDate"
              [min]="today"
              readonly
            />
            <mat-datepicker-toggle
              matSuffix
              [for]="picker1"
            ></mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>
        </div>

        <div
          class="loginInput registerInput"
          fxLayout="row"
          fxLayout.lt-sm="column"
          fxLayoutGap="16px"
          fxLayoutGap.lt-sm="0"
        >
          <mat-form-field
            fxFlex="100%"
            class="exai-flex-form-field"
            appearance="outline"
          >
            <mat-label>Order Notes</mat-label>
            <textarea
              type="text"
              class="form-control"
              matInput
              formControlName="orderNotes"
            >
            </textarea>

            <!-- <mat-error *ngFor="let validation of validation_messages.address">
                <mat-error class="error-message"
                    *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                    {{validation.message}}</mat-error>
            </mat-error> -->
          </mat-form-field>
        </div>
        Exams
        <div formArrayName="exams">
          <div *ngFor="let exam of exams.controls; let i = index">
            <div
              class="loginInput registerInput"
              fxLayout="row"
              fxLayout.lt-sm="column"
              fxLayoutGap="16px"
              fxLayoutGap.lt-sm="0"
              [formGroupName]="i"
            >
              <mat-form-field
                fxFlex="50%"
                class="exai-flex-form-field"
                appearance="outline"
                class="genderInput stateInput"
              >
                <mat-select
                  placeholder="Exam Name"
                  formControlName="examName"
                  (selectionChange)="onSelectionChange($event, i)"
                  [disabled]="disableExamSelection"
                >
                  <mat-option
                    *ngFor="let exam of ExamName"
                    [value]="exam.id"
                    (click)="examSelect(exam)"
                  >
                    {{ exam.examName }}
                  </mat-option>
                </mat-select>
                <!-- <mat-error *ngFor="let validation of validation_messages.state">
              <mat-error class="error-message"
                  *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                  {{validation.message}}</mat-error>
          </mat-error> -->
              </mat-form-field>
              <mat-form-field
                fxFlex="50%"
                class="exai-flex-form-field"
                appearance="outline"
                class="genderInput stateInput"
              >
                <mat-select placeholder="Form Id" formControlName="formId">
                  <mat-option
                    *ngFor="
                      let formId of formIdOptions[
                        exams.at(i).get('examName')?.value
                      ] || []
                    "
                    [value]="formId"
                    (click)="examSelect(formId)"
                  >
                    {{ formId }}
                  </mat-option>
                </mat-select>
                <!-- <mat-error *ngFor="let validation of validation_messages.state">
          <mat-error class="error-message"
              *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
              {{validation.message}}</mat-error>
      </mat-error> -->
              </mat-form-field>
              <mat-form-field
                fxFlex="100%"
                class="exai-flex-form-field"
                appearance="outline"
              >
                <mat-label>Quantity</mat-label>
                <input
                  type="number"
                  class="form-control"
                  matInput
                  formControlName="quantity"
                />

                <!-- <mat-error *ngFor="let validation of validation_messages.address">
                <mat-error class="error-message"
                    *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                    {{validation.message}}</mat-error>
            </mat-error> -->
              </mat-form-field>
              <button *ngIf="exams.length > 1" (click)="removeExam(i)">
                <mat-icon color="warn" fontSet="material-icons-outlined"
                  >delete</mat-icon
                >
              </button>
            </div>
          </div>
        </div>

        <button color="primary" class="flex" (click)="addExam()">
          <mat-icon color="primary">add</mat-icon>
          <p class="pc">Add New Exam</p>
        </button>
        <p class="mt mb-2">Shipping Information</p>
        <form [formGroup]="ShipInformation">
          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="100%"
              class="exai-flex-form-field"
              appearance="outline"
            >
              <mat-label>Contact Name</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                formControlName="contactName"
              />

              <!-- <mat-error *ngFor="let validation of validation_messages.address">
                <mat-error class="error-message"
                    *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                    {{validation.message}}</mat-error>
            </mat-error> -->
            </mat-form-field>
          </div>
          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>Contact Phone Number</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="contactPhoneNumber"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
            <mat-error class="error-message"
                *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                {{validation.message}}</mat-error>
        </mat-error> -->
            </mat-form-field>
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>Contact Email Address</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="contactEmail"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
        <mat-error class="error-message"
            *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
            {{validation.message}}</mat-error>
    </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="100%"
              class="exai-flex-form-field"
              appearance="outline"
            >
              <mat-label>Address Line 1</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="addressLine1"
              />

              <!-- <mat-error *ngFor="let validation of validation_messages.address">
                <mat-error class="error-message"
                    *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                    {{validation.message}}</mat-error>
            </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="100%"
              class="exai-flex-form-field"
              appearance="outline"
            >
              <mat-label>Address Line 2</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="addressLine2"
              />

              <!-- <mat-error *ngFor="let validation of validation_messages.address">
            <mat-error class="error-message"
                *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                {{validation.message}}</mat-error>
        </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>City</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="city"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
            <mat-error class="error-message"
                *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                {{validation.message}}</mat-error>
        </mat-error> -->
            </mat-form-field>
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>State</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="state"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
        <mat-error class="error-message"
            *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
            {{validation.message}}</mat-error>
    </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>Country</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="country"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
            <mat-error class="error-message"
                *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
                {{validation.message}}</mat-error>
        </mat-error> -->
            </mat-form-field>
            <mat-form-field
              fxFlex="50%"
              class="exai-flex-form-field"
              appearance="outline"
              class="genderInput stateInput"
            >
              <mat-label>Postal Code</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="postalCode"
              />
              <!-- <mat-error *ngFor="let validation of validation_messages.state">
        <mat-error cla
        ss="error-message"
            *ngIf="form.get('state').hasError(validation.type) && (form.get('state').dirty || form.get('state').touched)">
            {{validation.message}}</mat-error>
    </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="100%"
              class="exai-flex-form-field"
              appearance="outline"
            >
              <mat-label>Preferred Courier</mat-label>
              <input
                type="text"
                class="form-control"
                matInput
                matInput
                formControlName="preferredCarrier"
              />

              <!-- <mat-error *ngFor="let validation of validation_messages.address">
              <mat-error class="error-message"
                  *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                  {{validation.message}}</mat-error>
          </mat-error> -->
            </mat-form-field>
          </div>

          <div
            class="loginInput registerInput"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            fxLayoutGap.lt-sm="0"
          >
            <mat-form-field
              fxFlex="100%"
              class="exai-flex-form-field"
              appearance="outline"
            >
              <mat-label>Shipping Notes</mat-label>
              <textarea type="text" class="form-control" matInput  formControlName="shippingNotes"> </textarea>

              <!-- <mat-error *ngFor="let validation of validation_messages.address">
                <mat-error class="error-message"
                    *ngIf="form.get('address').hasError(validation.type) && (form.get('address').dirty || form.get('address').touched)">
                    {{validation.message}}</mat-error>
            </mat-error> -->
            </mat-form-field>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div
    class=""
    gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
    gdColumns.lt-md="1fr"
    gdColumns.lt-sm="1fr"
    gdGap="12px"
  >
    <div
      class="m-auto mt-6 mb-3 w-full"
      gdColumn="3 / 8"
      gdColumn.lt-md="1 / -1"
      gdColumn.lt-sm="1 /-1"
    >
      <div class="float-right actions action-grouped">
        <button mat-stroked-button color="warn" (click)="cancel()">Cancel</button>
        <button
          mat-flat-button
          color="primary" (click)="onSubmit()"
          [disabled]="!ShipInformation.valid && !orderForm.valid"
        >
          submit
        </button>
      </div>
    </div>
  </div>
</form>
