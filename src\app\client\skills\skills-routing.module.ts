import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SkillsComponent } from './skills.component';
import { SkillsDetailsComponent } from './skills/skills-details.component';



const routes: Routes = [
  {
    path: '', component: SkillsComponent,
//     data: {
//       title: 'Voucher',
//       breadcrumb: [
//         {
//           label: 'Home',
//           url: '/training'
//         },
//         {
//           label: 'Voucher',
//           url: ''
//         },
//       ]
//     },
  },
  { path: 'skills-details', component: SkillsDetailsComponent,
  data: {
    title: 'Forms Order',
    breadcrumb: [
      {
        label: 'Home',
        url: '/dashboard'
      },
      {
        label: 'Manage Booklet Inventory ',
        url: '/loader/booklet_inventory'
      },
      {
        label: 'Shipping Details',
        url: ''
      },
    ]
  },
},


//   { path: ':personId/:stateId/:id/:personTenantRoleId', component:VoucherComponent },
//   { path: ':personId/:stateId/id/:personTenantRoleId', component:BuyNewVoucherComponent },
  { path: '**', pathMatch: 'full', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SkillsRoutingModule { }