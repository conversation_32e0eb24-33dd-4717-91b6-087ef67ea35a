import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { FlexLayoutModule } from '@angular/flex-layout';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { TableModule } from '../../core/common-component/table/table.module';

import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { SingleEntryTableModule } from 'src/app/core/common-component/single-entry-table/single-entry-table.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SkillsComponent } from './skills.component';

import { MatExpansionModule } from '@angular/material/expansion';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { ScanModule } from '../scan/scan.module';
import { SkillsRoutingModule } from './skills-routing.module';
import { SkillsDetailsComponent } from './skills/skills-details.component';
import { BookletInventoryService } from './services/booklet-inventory.service';


@NgModule({
  declarations: [
    SkillsComponent,
    SkillsDetailsComponent

  ],
  imports: [
    MatIconModule,
    CommonModule,
    MatInputModule,
    NgDynamicBreadcrumbModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatCardModule,
    MatButtonModule,
    TableModule,
    SkillsRoutingModule,
    SingleEntryTableModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    FormsModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSelectModule,
    ScanModule
    // StoreModule.forFeature(VOUCHER_STATE_NAME, VoucherDetails),
    // EffectsModule.forFeature([VoucherEffects]),
  ],
  providers:[BookletInventoryService]
})
export class SkillsModule { }