import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Manage States",
            moduleID: "manage-states",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageStateIcon",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",

                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ["tab-col"],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter:true,
                                    showAddButton: [true],
                                    addButtonTexts:['Add State'],
                                    showMatTabs: false,
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,

                                      addPopupData: [
                                        {
                                          isCustom: true,
                                          customTemplateRef: "addApplicationForCandidate",
                                          title: ["Add State"],
                                          id: "addApplicationForCandidate",
                                          buttons: [
                                            
                                          ],
                                        },
                                      ],
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "link",
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "state-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "State Details",
                                            },
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: ["link-animation"],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateCode",
                                            name: "State Code",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "lastUpdatedDate",
                                        //     name: "Last Updated Date",
                                        //     cellType: "date",
                                        //     headerType: "sort",
                                        //     headerAlign: "center",
                                        //     cellAlign: "center",
                                        //     headerCssClasses: [],
                                        //     cellCssClasses: [],
                                        // },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "active",
                                            name: "Status",
                                            cellType: "toggle",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            showOnlyData:true,
                                            headerCssClasses: ["w-12"],
                                            cellCssClasses: [],
                                            statusUpdateDetails: {
                                                fetchUrl: ["client", "api", "state", "status"],
                                                requestBody: null,
                                                requestParams: null,
                                                method: "PUT",
                                            },
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Actions",
                                            
                                            cellType: "action",
                                            headerDisable:true,
                                           
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "edit",
                                                    showOnlyData:true,
                                                    type: tableTypes.actionTypes.Edit,
                                                    tooltip: "Edit",
                                                    color: '#7d7d7d',
                                                    
                                                    // requestDetails: [
                                                    //     {
                                                    //         fetchUrl: ["client", "api", `state`],
                                                    //         requestBody: null,
                                                    //         requestParams: [],
                                                    //         method: "PUT",
                                                    //     },
                                                    // ],
                                                },
                                                
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Independent,
                                                navigateTo: "application",
                                                eventDataUseful: false,
                                                navigateToLabel: "Application",
                                              },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12", ],
                                            cellCssClasses: ["cursor-pointer"],
                                        },
                                    ],
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: ["client", "api", "state"],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Required,
                                                paramName: null,
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId',
                                                position: 3,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ],
                                backwardsDataDependency: false,
                            },
                        },
                    ],
                },
            ],
        }
    ]
}