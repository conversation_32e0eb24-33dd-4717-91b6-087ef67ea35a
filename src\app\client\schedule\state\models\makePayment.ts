export interface MakePaymentResponse {
    transactionResponse: TransactionResponse
    messages: Messages
  }
  
  export interface TransactionResponse {
    responseCode: string
    authCode: string
    transId: string
    refTransID: string
    accountNumber: string
    accountType: string
    avsResultCode: string
    cvvResultCode: string
    cavvResultCode: string
    transHash: string
    transHashSha2: string
    messages: Message[]
  }
  
  export interface Message {
    code: string
    description: string
  }
  
  export interface Messages {
    resultCode: string
    message: Message2[]
  }
  
  export interface Message2 {
    code: string
    text: string
  }


  export interface MakePaymentBody {
    cartId: number
    currencyCode: string
    payment: Payment
  }
  
  export interface Payment {
    creditCard: CreditCard
  }
  
  export interface CreditCard {
    cardNumber: string
    expirationDate: string
    cardCode: string
  }

  export interface Reschedule {
    candidateId: number,
    examId: number,
    slotId:number,
    timeZone: string,
    offSet: string,
    scheduleId: number
    personTenantRoleId: number
    examModeId : number,
    examDateTime:string,
    testCenterId: any,
    testCenterName:string
    testCenterAddress:string ,
    testCenterCity:string,
    testCenterState:string ,
     testCenterPostalCode:string ,
     Slotime?:string,
     testSiteId?:string
     TestCenterDirections?:string
     testCenterCode?:string
     examClientEventId?:number
  }