<div class="w-full" fxLayout="column">
    <div class="border-b sticky left-0 table-head items-baseline" fxLayout="row" fxLayoutAlign="end">
        <div class="items-center mb-1 tableBackground-col">
            <button *ngIf="curModuleId == 'candidate-management' && selection.selected.length == 2"
                class="btn-1 t-xs mr-2  flex-row" color="primary" fxFlex="none" type="button" (click)="mergeAccount()">
                <mat-icon class="text-sm mt-1">
                    <mat-icon>call_merge</mat-icon>
                </mat-icon>
                Merge Accounts
            </button>
            <button
                *ngIf="(curModuleId == 'reciprocity-request' || curModuleId == 'certificate-renewal' || curModuleId == 'duplicate-certificates' || curModuleId =='regular-certificate') && selection.selected.length > 0"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" fxFlex="none" type="button"
                (click)="BulkCertificateDownload()">
                <mat-icon class="text-lg mt-1">
                    <mat-icon class="width">download</mat-icon>
                </mat-icon>
                Bulk Certificate Download
            </button>
            <button *ngIf="tableOptions.showAddButton[selectedIndex]"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
                type="button" (click)="add.emit(true)">
                <mat-icon class="text-lg mt-1" [icIcon]="icAdd"></mat-icon>
                {{tableOptions.addButtonTexts && tableOptions.addButtonTexts[selectedIndex] ?
                tableOptions.addButtonTexts[selectedIndex] : 'Add'}}
            </button>

            <button *ngIf="tableOptions.ShowDownload && dataSource"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
                type="button" (click)="download()">
                <mat-icon class="text-lg mt-1">download</mat-icon>
                Download Employee Data
            </button>

            <button *ngIf="tableOptions.showAddplaceorder && dataSource"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
                (click)="PlaceOrder()" type="button">
                <mat-icon class="text-lg mt-1">reorder</mat-icon>
                Place order
            </button>

            <button *ngIf="tableOptions.ShowDownloadPayment && dataSource"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
                type="button" (click)="downloadCandidatePayment()">
                <mat-icon class="text-lg mt-1">download</mat-icon>
                Download Payment Data
            </button>

            <!-- <button *ngIf="tableOptions.ShowBulkImport"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
                type="button" (click)="downloadBulkImportTemplate()">
                <mat-icon class="text-lg mt-1">download</mat-icon>
                Bulk Import
            </button> -->

            <button *ngIf="tableOptions.ShowFilenameFilter && curModuleId =='Pre-RegisterCandidate'"
                class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row " color="primary" fxFlex="none"
                type="button" (click)="downloadBulkImportTemplate()">
                <mat-icon class="text-lg mt-1">download</mat-icon>
                Bulk Import
            </button>
            <div *ngIf="tableOptions.ShowFilenameFilter && ([14].includes(this.globalUserService.userDetails.value.roleId) || (curModuleId =='reciprocity-request' || curModuleId =='certificate-renewal' ) && [17].includes(this.globalUserService.userDetails.value.roleId) && tabselected =='Approved' ) || curModuleId =='manage_nae' || curModuleId=='regular-certificate' || curModuleId=='booklet_inventory' || curModuleId=='candidate-search-slot' "
                class="bg-card rounded border mr-2 exam naetester nst" fxLayout="row" fxLayoutAlign="end center">
                <form [formGroup]="form">
                    <mat-form-field appearance="outline">
                        <mat-label *ngIf="curModuleId !='booklet_inventory' && curModuleId !='candidate-search-slot'"
                            class="text-xs fontColor2">Select your
                            certificate generated date range</mat-label>
                        <mat-label *ngIf="curModuleId =='booklet_inventory'" class="text-xs fontColor2">Select your
                            order date range</mat-label>
                        <mat-label *ngIf="curModuleId =='candidate-search-slot'" class="text-xs fontColor2">Select your
                            created date range</mat-label>
                        <mat-date-range-input [rangePicker]="picker">
                            <input class="text-xs" matStartDate formControlName="start" autocomplete="off"
                                (dateChange)="onDateChange($event)" required placeholder="Start date">
                            <input class="text-xs" matEndDate formControlName="end" (dateChange)="onDateChange($event)"
                                autocomplete="off" required placeholder="End date">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <button *ngIf="form.get('start')?.value || form.get('end')?.value" matSuffix mat-icon-button
                            type="button" (click)="clearDateRange()" matTooltip="Clear date range" class="ml-1">
                            <mat-icon class="text-sm">clear</mat-icon>
                        </button>
                        <mat-date-range-picker #picker></mat-date-range-picker>
                    </mat-form-field>
                </form>


            </div>

            <div *ngIf="tableOptions.ShowFilenameFilter && ([15].includes(this.globalUserService.userDetails.value.roleId) && (curModuleId =='Pre-RegisterCandidate' ))"
                class="bg-card rounded border mr-2 exam naetester nst" fxLayout="row" fxLayoutAlign="end center">
                <form [formGroup]="form">
                    <mat-form-field appearance="outline">
                        <mat-label class="text-xs fontColor2">Select your date range</mat-label>
                        <mat-date-range-input [rangePicker]="picker">
                            <input class="text-xs" matStartDate formControlName="start" autocomplete="off"
                                (dateChange)="onDateChange($event)" required placeholder="Start date">
                            <input class="text-xs" matEndDate formControlName="end" (dateChange)="onDateChange($event)"
                                autocomplete="off" required placeholder="End date">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-date-range-picker #picker></mat-date-range-picker>
                    </mat-form-field>
                </form>


            </div>

            <div *ngIf="tableOptions.ShowFilenameFilter && (curModuleId =='duplicate-certificates') && [17].includes(this.globalUserService.userDetails.value.roleId) && tabselected =='Approved' "
                class="bg-card rounded border mr-2 exam naetester nst" fxLayout="row" fxLayoutAlign="end center">
                <form [formGroup]="formReview">
                    <mat-form-field appearance="outline">
                        <mat-label class="text-xs fontColor2">Select application approved date range</mat-label>
                        <mat-date-range-input [rangePicker]="picker">
                            <input class="text-xs" matStartDate formControlName="reviewstart" autocomplete="off"
                                (dateChange)="onReviewDateChange($event)" required placeholder="Start date">
                            <input class="text-xs" matEndDate formControlName="reviewend"
                                (dateChange)="onReviewDateChange($event)" autocomplete="off" required
                                placeholder="End date">
                        </mat-date-range-input>
                        <mat-datepicker-toggle matSuffix [for]="picker">
                        </mat-datepicker-toggle>
                        <mat-date-range-picker #picker>


                        </mat-date-range-picker>

                    </mat-form-field>
                </form>


            </div>

            <div *ngIf="tableOptions.ShowFilenameFilter && [14].includes(this.globalUserService.userDetails.value.roleId)"
                class="bg-card rounded border mr-2 " fxFlex="230px" fxFlex.lt-md="120px" fxLayout="row"
                fxLayoutAlign="end center">
                <!-- <mat-select [formControl]="stateControl" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder" placeholder="Select State" (selectionChange)="handleStateSelection($event)">
    <mat-option *ngFor="let state of states" [value]="state.value">{{state.label}}
    </mat-option>
</mat-select> -->
                <mat-select [formControl]="stateControl" (selectionChange)="handleStateSelection($event)"
                    placeholder="Select File" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder"
                    fxFlex.lt-md="auto">
                    <input [(ngModel)]="searchText" class="px-3 py-4 outline-none w-full bg-transparent xs"
                        type="search" autofocus placeholder="Start Typing.." (input)="filterStatesFiles()" />
                    <mat-option *ngFor="let state of states "
                        [value]="this.tableOptions.isStateSearchServerSide !=undefined?state.fileName:state.id">
                        {{state.fileName}}
                    </mat-option>
                </mat-select>
            </div>


            <button *ngIf="tableOptions.showColumnFilter" [matMenuTriggerFor]="columnFilterMenu" class="ml-1"
                fxFlex="none" mat-icon-button matTooltip="Filter Columns" type="button">
                <mat-icon class="column-filter">view_column</mat-icon>
            </button>







            <!-- <button *ngIf="tableOptions.showColumnWiseFilter"  [matMenuTriggerFor]="columnWiseFilterMenu" class="ml-1" fxFlex="none" mat-icon-button matTooltip="Filter Records" type="button">
                <mat-icon class="column-filter"
                          [icIcon]="icFilterList"></mat-icon>
            </button> -->

            <!-- if the current user is present under a state then no need to show this dropdown  -->


        </div>

        <div *ngIf="tableOptions.showStateSelectionDropdown && [Roles.OperationStaff,Roles.SupportingStaff,Roles.SuperAdmin,Roles.QAC].includes(this.globalUserService.userDetails.value.roleId)"
            class="bg-card rounded border mr-2" fxFlex="130px" fxFlex.lt-md="120px" fxLayout="row"
            fxLayoutAlign="end center">
            <!-- <mat-select [formControl]="stateControl" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder" placeholder="Select State" (selectionChange)="handleStateSelection($event)">
            <mat-option *ngFor="let state of states" [value]="state.value">{{state.label}}
            </mat-option>
        </mat-select> -->
            <mat-select [attr.data-cre-type]='tableOptions.showSlectdropstatedataattributes'
                [formControl]="stateControl" (selectionChange)="handleStateSelection($event)" placeholder="Select State"
                class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                <input [(ngModel)]="searchText" class="px-3 py-4 outline-none w-full bg-transparent xs" type="search"
                    autofocus placeholder="Start Typing.." (input)="filterStates()" />
                <mat-option *ngFor="let state of states "
                    [value]="this.tableOptions.isStateSearchServerSide !=undefined?state.stateName:state.id">
                    {{state.stateName}}
                </mat-option>
            </mat-select>
        </div>

        <div *ngIf="tableOptions.showStateSelectionDropdown && [Roles.OperationStaff,Roles.SupportingStaff,Roles.SuperAdmin,Roles.QAC].includes(this.globalUserService.userDetails.value.roleId) && curModuleId=='manage_nae'"
            class="bg-card rounded border mr-2" fxFlex="200px" fxFlex.lt-md="120px" fxLayout="row"
            fxLayoutAlign="end center">
            <mat-select [formControl]="scoreControl" (selectionChange)="handleScoreSelection($event)"
                placeholder="Select Score" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder"
                fxFlex.lt-md="auto">
                <mat-option value="">None</mat-option>
                <mat-option *ngFor="let score of scores" [value]="score.value">
                    {{score.label}}
                </mat-option>
            </mat-select>
        </div>

        <div *ngIf="tableOptions.showOrderStatusDropdown" class="bg-card rounded border mr-2" fxFlex="140px"
            fxFlex.lt-md="120px" fxLayout="row" fxLayoutAlign="end center">
            <mat-select [formControl]="orderStatusControl" (selectionChange)="handleOrderStatusSelection($event)"
                [placeholder]="curModuleId == 'candidate-search-slot' ? 'Type of Issue' : 'Order Status'"
                fxFlex.lt-md="auto" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder">
                <mat-option value="all">All</mat-option>
                <mat-option *ngFor="let items of bookletOrderStatus$ | async"
                    [value]="items.orderStatusId">{{items.statusDescription}}</mat-option>
            </mat-select>
        </div>

        <div *ngIf="tableOptions.showExamModeDropdown" class="bg-card rounded border mr-2" fxFlex="130px"
            fxFlex.lt-md="120px" fxLayout="row" fxLayoutAlign="end center">
            <mat-select [formControl]="examModeControl" (selectionChange)="handleExamModeSelection($event)"
                placeholder="Exam Mode" fxFlex.lt-md="auto"
                class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder">
                <mat-option value="all">All</mat-option>
                <mat-option value="1">Online</mat-option>
                <mat-option value="2">Test Center</mat-option>
            </mat-select>
        </div>

        <div *ngIf="tableOptions.showTestCenterPreferenceDropdown" class="bg-card rounded border mr-2" fxFlex="200px"
            fxFlex.lt-md="120px" fxLayout="row" fxLayoutAlign="end center">
            <mat-select [formControl]="testCenterPreferenceControl"
                (selectionChange)="handleTestCenterPreferenceSelection($event)" placeholder="Test Center Preference"
                fxFlex.lt-md="auto" class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder">
                <mat-option value="all">All</mat-option>
                <mat-option value="INF">INF</mat-option>
                <mat-option value="RTS">RTS</mat-option>
            </mat-select>
        </div>

        <div *ngIf="tableOptions.ShowGreivanceStatus" class="bg-card rounded border mr-2" fxFlex="200px"
            fxFlex.lt-md="120px" fxLayout="row" fxLayoutAlign="end center">
            <mat-select [formControl]="orderStatusControl" (selectionChange)="handleOrderStatusSelection($event)"
                placeholder="Greivance Status" fxFlex.lt-md="auto"
                class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder">
                <mat-option [value]="">Clear Selection</mat-option>
                <mat-option *ngFor="let items of bookletOrderStatus$ | async"
                    [value]="items.id">{{items.name}}</mat-option>


            </mat-select>
        </div>









        <div *ngIf="tableOptions.showSearch" class="bg-card rounded border"
            [ngClass]="{'withSerachFlex':tableOptions.showColumnWiseFilter==true,withoutSerachFlex:tableOptions.showColumnWiseFilter==false}"
            fxLayout="row" fxLayoutAlign="end center">
            <span *ngIf="tableOptions.showColumnWiseFilter" class="w-full pl-3">
                {{this.SelectedColoumWiseFilter?this.SelectedColoumWiseFilter:"ALL"}} :
            </span>
            <mat-icon *ngIf="this.SelectedColoumWiseFilter" class="cursor-pointer close" (click)="clearData()"
                [matMenuTriggerFor]="columnWiseFilterMenu">close</mat-icon>
            <input *ngIf="this.SelectedColoumWiseFilter==null" [formControl]="searchControl"
                class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder" placeholder="Search"
                type="search" [matMenuTriggerFor]="columnWiseFilterMenu">
            <input *ngIf="this.SelectedColoumWiseFilter!=null" [formControl]="searchControl"
                class="px-4 py-2 border-0 outline-none w-full bg-transparent cardBorder" placeholder="Search"
                type="search">
            <ic-icon [icIcon]="icSearch" size="18px" class="pr-2 src-icon"></ic-icon>
        </div>

    </div>
    <!-- <div *ngIf="selection.hasValue()" class="mr-4 pr-4 border-r" fxFlex="none">
            <button (click)="deleteCustomers(selection.selected)" color="primary" mat-icon-button
                matTooltip="Delete selected" type="button">
                <mat-icon [icIcon]="icDelete"></mat-icon>
            </button>
        </div> -->


    <div class="title my-0 rtl:ml-4 text-sm font-medium justify-end tableBackground-col" fxFlex="none" fxHide.xs
        fxLayout="column">
        <ng-container *ngIf="tableOptions.tableName">
            <span class="font-semibold pt-4"
                *ngIf="selection.isEmpty() && tableOptions.tableName">{{tableOptions.tableName}}</span>
            <span *ngIf="selection.hasValue()">{{ selection.selected.length }}
                <span *ngIf="selection.selected.length > 1"></span> Selected</span>
        </ng-container>

        <div class="bg-transparent mat-tabGroup" *ngIf="tableOptions.showMatTabs" fxFlex.lt-md="auto" fxLayout="column">
            <mat-tab-group class="matTabGroup ml-1" (selectedTabChange)="tabClick($event)"
                (selectedIndexChange)="tabSelectionHandler($event)" [selectedIndex]="selectedIndex"
                (click)="selectedMatTab()">
                <ng-container *ngFor="let tab of tableOptions.matTabs;let tabIndex = tabIndex">
                    <mat-tab [label]="tab.name" *ngIf="getTabVisibility(tab.visibilityRule)">
                    </mat-tab>
                </ng-container>
            </mat-tab-group>
        </div>
    </div>





    <!-- Table -->
    <div class="exam-list cardBorder overflow-auto relative">
        <table mat-table *ngIf="dataSource " [dataSource]="dataSource" class="w-full relative" matSort>
            <ng-container *ngFor="let column of displayedCols; let columnIndex = index">
                <ng-container [matColumnDef]="getColumnId(column.id)">
                    <ng-container *ngIf="column.headerType == 'checkbox'">
                        <th *matHeaderCellDef mat-header-cell [ngClass]="column.headerCssClasses.join(' ')">
                            <mat-checkbox (change)="$event ? checkBoxClickedHandler() : null"
                                [checked]="selection.hasValue() && isAllSelected()"
                                [indeterminate]="selection.hasValue() && !isAllSelected()" primary>
                            </mat-checkbox>
                        </th>
                        <td *matCellDef="let element" mat-cell>
                            <mat-checkbox (change)="$event ? checkBoxClickedHandler(element) : null"
                                (click)="$event.stopPropagation()" [checked]="selection.isSelected(element)" primary>
                            </mat-checkbox>
                        </td>
                    </ng-container>

                    <ng-container *ngIf="column.headerType == 'sort'; else withoutSorting">
                        <th mat-header-cell *matHeaderCellDef mat-sort-header
                            [ngClass]="column.headerCssClasses.join(' ')">
                            {{column.name}}
                            <span *ngIf="column.headerType === 'dropdown'" [matMenuTriggerFor]="columnMenu">
                                <mat-icon class="cursor-pointer">keyboard_arrow_down</mat-icon>
                            </span>
                            <mat-menu #columnMenu="matMenu" yPosition="below" xPosition="before">
                                <button mat-menu-item *ngFor="let option of column.dropDownActions">{{option}}</button>
                            </mat-menu>
                        </th>
                    </ng-container>
                    <ng-template #withoutSorting>
                        <th (click)="headerClickHandler(column)" mat-header-cell *matHeaderCellDef
                            [ngClass]="column.headerCssClasses.join(' ')">
                            {{column.name}}
                            <span *ngIf="column.headerType === 'dropdown'" [matMenuTriggerFor]="columnMenu">
                                <mat-icon class="cursor-pointer">keyboard_arrow_down</mat-icon>
                            </span>
                            <mat-menu #columnMenu="matMenu" yPosition="below" xPosition="before">
                                <button mat-menu-item *ngFor="let option of column.dropDownActions"
                                    (click)="headerDropdownSelectionHandler(column,option)">{{option}}</button>
                            </mat-menu>
                        </th>
                    </ng-template>
                    <ng-container
                        *ngIf="['text','number','status','link','onlydate','date','array','dobtype','json','Date','regDate'].includes(column.cellType); else actionTemplate">
                        <td mat-cell *matCellDef="let element; let i = index;"
                            (click)="cellClickHandler(column,element,i)"
                            [ngClass]="column.cellCssClasses?.join(' ')+' '+element.cellCssClasses?.join(' ')">
                            <!-- <mat-icon class="text-base mt-1 statusIcon" *ngIf="column.cellType == 'status'" [style.color]="getObject(element[column.id],column.statuses)?.color">
                                {{getObject(element[column.id],column.statuses)?.icon}}</mat-icon> -->
                            <span *ngIf="column.cellType != 'status';else statusText">
                                <span>
                                    {{
                                    (column.cellType === 'onlydate')
                                    ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType) |
                                    date:(column.dateCellProps ? column.dateCellProps : 'MM-dd-yyyy'):'+0000')
                                    :
                                    (column.cellType === 'dobtype')
                                    ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType)
                                    |date:(column.dateCellProps
                                    ? column.dateCellProps : 'shortDate'):'+0000')
                                    :
                                    (column.cellType === 'date')
                                    ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType) |
                                    date:(column.dateCellProps ? column.dateCellProps : 'medium'):'+0000')
                                    :
                                    (column.cellType === 'Date')
                                    ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType) |
                                    date:(column.dateCellProps ? column.dateCellProps : 'medium'))
                                    :
                                    (column.cellType === 'regDate')
                                    ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType) |
                                    date:(column.dateCellProps ? column.dateCellProps : 'MM-dd-yyyy'))
                                    :

                                    columnText(element,column.id,column.cellArrayProps,column.cellType,column.cellDataMapping)
                                    }}
                                </span>
                            </span>

                            <ng-template #statusText>
                                <span class="status-text"
                                    style="padding:8px;border-radius:5px;display:block;width:70px;text-align:center;"
                                    [style.color]="getObject(element[column.id],column.statuses)?.color"
                                    [style.background]="getObject(element[column.id],column.statuses)?.background">
                                    {{ (column.cellType == 'date') ?
                                    (columnText(element,column.id,column.cellArrayProps,column.cellType) |
                                    date:'medium') :
                                    columnText(element,column.id,column.cellArrayProps,column.cellType)}}
                                </span>


                            </ng-template>

                            <span class="status-text" *ngIf="column.statusesboolean">
                                {{element.scoreReceived? 'Yes':'No'}}
                            </span>
                        </td>
                    </ng-container>
                    <ng-template #actionTemplate>
                        <td mat-cell *matCellDef="let element; let rowIndex = index;"
                            [ngClass]="column.cellCssClasses.join(' ')+' row'">
                            <!-- <button *ngIf="column.cellType == 'action'" (click)="$event.stopPropagation()" [matMenuTriggerData]="{ column:column,element:element,rowIndex: rowIndex}" [matMenuTriggerFor]="actionsMenu" mat-icon-button type="button">
                                <mat-icon [icIcon]="icMoreHoriz"></mat-icon>
                            </button> -->
                            <ng-container *ngIf="column.cellType == 'action'">
                                <ng-container *ngFor="let action of column.actions;let actionIndex = index">
                                    <button class="text-left" *ngIf="getActionVisibility(element,action.visibilityRule)"
                                        (click)="$event.stopPropagation()" mat-icon-button type="button">
                                        <mat-icon class="action-icons"
                                            [ngClass]="{'disabled' : action.showOnlyData && (globalUserService.userDetails.value.roleId == 13 )}"
                                            (click)="actionHandler(column,element,rowIndex,actionIndex)"
                                            [style.color]="action.color" [matTooltip]="action.tooltip">
                                            {{action.icon}}</mat-icon>
                                    </button>
                                </ng-container>
                            </ng-container>
                            <!-- <mat-slide-toggle [disabled]="MatToggleDisaable(column)"
                                (change)="toggleHandler($event,column,element,rowIndex)" [checked]="element[column.id]"
                                *ngIf="column.cellType == 'toggle'" color="primary"></mat-slide-toggle> -->

                            <mat-slide-toggle [disabled]="MatToggleDisaable(column)"
                                (change)="toggleHandler($event,column,element,rowIndex)" [checked]="element[column.id]"
                                *ngIf="column.cellType == 'toggle' && curModuleId !='manage-training-programs' && curModuleId !='Manage State Client' "
                                color="primary"></mat-slide-toggle>

                            <mat-slide-toggle [disabled]="MatToggleDisaable(column)"
                                (change)="toggleHandlers($event,column,element,rowIndex)" [checked]="element[column.id]"
                                *ngIf="column.cellType == 'toggle' && (curModuleId =='manage-training-programs' || curModuleId =='Manage State Client')"
                                color="primary"></mat-slide-toggle>

                            <ng-container
                                *ngIf="tabselected =='Registration Details' && column.id == 'action' && isDateallow ">
                                <mat-datepicker matTooltip="Extended Renewal" (opened)="toggleDatePopup(picker)"
                                    #picker></mat-datepicker>
                                <mat-datepicker-toggle [for]="picker"
                                    matTooltip="Extended Renewal"></mat-datepicker-toggle>
                                <input type="hidden" [matDatepicker]="picker" [min]="yesterday"
                                    matTooltip="Extended Renewal"
                                    (dateChange)="selectDate($event.target.value,element)">
                            </ng-container>
                            <ng-container
                                *ngIf="(tabselected =='Registration Details' && column.id == 'action' && element.StateCode =='PA' && element.RegistryStatusId == 4)">
                                <button matTooltip="Extended Renewal" class="cursor-pointer pag-button color"
                                    (click)="downloadCertificate(element)"><mat-icon
                                        matTooltip="Download Certificate">download</mat-icon></button>
                            </ng-container>
                        </td>
                    </ng-template>
                </ng-container>
            </ng-container>


            <tr class="text-sm" mat-header-row *matHeaderRowDef="displayedColsIndices"
                [ngClass]="tableOptions.headerRowCssClasses?.join(' ')"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColsIndices"
                [ngClass]="tableOptions.cellRowCssClasses?.join(' ')">
            </tr>

            <tr class="mat-row" *matNoDataRow>
                <td class="mat-cell text-center t-xs" [attr.colspan]="displayedCols.length">
                    <span>NO RECORD FOUND.</span>
                </td>
            </tr>
        </table>
    </div>
    <!-- Table -->

    <!-- Paginator -->
    <ng-container *ngIf="tableOptions.showPaginator">
        <div class="flex justify-between items-baseline page-border tableBackground-page alignCenter"
            [ngClass]="{'pb-2':(tableOptions.isPaginationServerSide && tableOptions.isPaginationServerSide[selectedIndex])}">
            <div class="paginators">
                <span class="pl-4 pr-2 t-xs">The Page You’re on</span>
                <mat-form-field class="form" appearance="outline">
                    <input matInput [formControl]="pageIndexControl" autocomplete="off" [readonly]="true">
                </mat-form-field>
            </div>
            <div class="flex"
                *ngIf="!tableOptions.isPaginationServerSide || (tableOptions.isPaginationServerSide && !tableOptions.isPaginationServerSide[selectedIndex])">
                <mat-paginator class="paginator" [pageSize]="tableOptions.pageSize"
                    [pageSizeOptions]="tableOptions.pageSizeOptions" (page)="getNewPageData({
                                        length:$event.length,
                                        pageSize:$event.pageSize,
                                        pageIndex:$event.pageIndex+1,
                                        previousPageIndex:$event.previousPageIndex})">
                </mat-paginator>
            </div>
            <div class="flex flex-row py-2"
                *ngIf="tableOptions.isPaginationServerSide && tableOptions.isPaginationServerSide[selectedIndex]">
                <div class="paginators">
                    <span class="pl-4 pr-2 t-xs">Items Per Page:&nbsp;</span>
                    <mat-form-field appearance="outline" class="form w-16 h-8">
                        <mat-select [formControl]="pageSizeControl" class="form" (selectionChange)="getNewPageData({
                                        length:null,
                                        pageSize:pageSizeControl.value,
                                        pageIndex:1,
                                        previousPageIndex:pageIndexControl.value})">
                            <mat-option *ngFor="let pageSizeOption of tableOptions.pageSizeOptions"
                                [value]="pageSizeOption">{{pageSizeOption}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span class="pl-4 pr-2 t-xs" *ngIf="totalRecords">{{
                        ((this.pageIndexControl.value-1)*this.pageSizeControl.value)+1}}
                        &nbsp;- &nbsp;
                        {{ minOfDataSizeAndPageLastRecordIndex}}
                        of {{totalRecords}}</span>
                </div>
                <div class="items-baseline pag-buttons pl-4 pr-4 text-lg">
                    <button class="cursor-pointer pag-button" [ngClass]="{'disabled':pageIndexControl.value <= 1 }">
                        <mat-icon (click)="
                        getNewPageData({
                            length:null,
                            pageSize:pageSizeControl.value,
                            pageIndex:pageIndexControl.value-1,
                            previousPageIndex:pageIndexControl.value})">navigate_before</mat-icon>
                    </button>
                    <button class="cursor-pointer pag-button"
                        [ngClass]="{'disabled': (dataSource.data.length == 0 
                        || (totalRecords ? (pageSizeControl.value * pageIndexControl.value) >= totalRecords : false))}">
                        <mat-icon (click)="
                        getNewPageData({
                            length:null,
                            pageSize:pageSizeControl.value,
                            pageIndex:pageIndexControl.value+1,
                            previousPageIndex:pageIndexControl.value})">navigate_next</mat-icon>
                    </button>
                </div>
            </div>
        </div>
    </ng-container>
    <!-- Paginator -->
</div>


<mat-menu #columnFilterMenu="matMenu" xPosition="before" yPosition="below" class="customize">
    <button (click)="toggleColumnVisibility(colIndex, $event)" *ngFor="let column of displayedCols;let colIndex=index"
        class="checkbox-item mat-menu-item ">
        <mat-checkbox class="pointer-events-none" (click)="$event.stopPropagation()"
            [(ngModel)]="displayedCols[colIndex].visible" color="primary">
            {{ column.name }}
        </mat-checkbox>
    </button>
</mat-menu>

<mat-menu #columnWiseFilterMenu="matMenu" xPosition="before" yPosition="below" class="column-wise-filter-menu">
    <button (click)="toggleColumnFilter(colIndex, $event)"
        *ngFor="let column of displayedFilterColmns;let colIndex=index" class="checkbox-item mat-menu-item">
        <mat-checkbox class="pointer-events-none" (click)="$event.stopPropagation()" [(ngModel)]="column.isFilter"
            color="primary">
            {{ column.name }}
        </mat-checkbox>
    </button>
</mat-menu>

<mat-menu #actionsMenu="matMenu" xPosition="before" yPosition="below">
    <ng-template let-column="column" let-element="element" let-rowIndex="rowIndex" matMenuContent>
        <ng-container *ngFor="let action of column.actions;let actionIndex = index">
            <!--  below is the different version that requires each row to have a array of strings -->
            <!-- <button *ngIf="element[column.id].includes(action.tooltip)" mat-menu-item (click)="actionHandler(column,element,rowIndex,actionIndex)">
                                        <mat-icon [style.color]="action.color">{{action.icon}}</mat-icon>
                                        {{action.tooltip}}
                                    </button> -->
            <button mat-menu-item *ngIf="getActionVisibility(element,action.visibilityRule)"
                (click)="actionHandler(column,element,rowIndex,actionIndex)">
                <mat-icon [style.color]="action.color">{{action.icon}}</mat-icon>
                {{action.tooltip}}
            </button>
        </ng-container>
    </ng-template>
</mat-menu>