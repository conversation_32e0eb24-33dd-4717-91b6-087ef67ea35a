import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { CdkDrag, CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';
import { PaymentRefundPopupComponent } from 'src/app/core/common-component/pop-up/payment-refund-popup/payment-refund-popup.component';
import { StreamService } from 'src/app/core/common-component/pop-up/video/services/stream.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';

@Component({
  selector: 'app-video-stream',
  templateUrl: './video-stream.component.html',
  styleUrls: ['./video-stream.component.scss']
})
export class VideoStreamComponent implements OnInit {
  @Input() BookletId;
  @Input() StartStopRecording: boolean;
  @Output("EventType") EventType: EventEmitter<any> = new EventEmitter<any>();
  videoStreams: any[] = [];
  constructor(public dialog: MatDialog,
    private _streamService: StreamService,
    private globalService:GlobalUserService,
    private services:SnackbarService
   ) { }

   openPopup(): void {
      const dialogRef = this.dialog.open(PaymentRefundPopupComponent, {
        data:{id:3,bookletId:this.BookletId.bookletId,personEventId:this.BookletId.personEventID,Confirm:this.BookletId},
        width:"400px",
        disableClose:true
   // Set the width as per your requirement
      });
  
      // You can subscribe to the afterClosed event to perform actions after the popup is closed
      dialogRef.afterClosed().subscribe(result => {
        this.EventType.emit({eventSku:this.BookletId.clientEventId})

      });

      this.globalService.personEventID = this.BookletId.personEventID;
    
    // You can subscribe to the afterClosed event to perform actions after the popup is closed
  


   
  }

  ngOnInit(): void {
    // this._streamService.createStreamElement();
  }
  drop(event: CdkDragDrop<any[]>) {
    moveItemInArray(this.videoStreams, event.previousIndex, event.currentIndex);
  }

}
