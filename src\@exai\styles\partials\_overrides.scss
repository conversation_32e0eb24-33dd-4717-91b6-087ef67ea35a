.mat-icon,
.mat-icon-button .mat-icon {
    font-size: var(--default-icon-size);
    height: unset;
    width: unset;
    @apply flex items-center justify-center;
}

.ic-inline > svg {
    display: inline-block;
}


.trainingUploadCandidate .mat-form-field-appearance-outline .mat-form-field-outline{
    top: 12px !important;
}
ic-icon:not(.ic-inline) > svg,
.iconify:not(.ic-inline) > svg {
    margin: 0 auto;
    vertical-align: middle;
}

.exai-scrollblock {
    position: fixed;
    width: 100%;
}

.mat-sidenav-fixed {
    position: absolute !important;
    background-color: var(--background-base5);
    z-index: 1000;
}

.paginators .mat-select-arrow-wrapper {
    vertical-align: bottom !important;
}

.mat-select-panel {
    background: white !important;
}

// Table
.mat-table thead {
    position: sticky;
    top: 0px;
    z-index: 100;
}

tr.mat-header-row {
    height: 40px !important;
    color: var(--text-toggle);
    // background-color: var(--background-base1)!important;
}

tr.mat-row,
tr.mat-footer-row {
    height: 36px;
}

.mat-cell,
.mat-footer-cell,
.mat-sort-header-content,
.mat-table th.mat-header-cell {
    font-size: 11px;
    font-weight: 400;
    color: var(--text-tr);
    word-break: break-word;
    text-align: left;
}

.mat-sort-header-content,
.mat-table th.mat-header-cell {
    font-size: 12px;
    color: var(--dropdown-color);
    font-weight: 500;
}

.mat-header-cell {
    font-size: 0.65rem;
    font-weight: 500;
    background-color: var(--background-base1) !important;
}

.exam .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.85em 0 0.65em 0;
    border-top: 0.2em solid transparent;
}


.naetester .mat-form-field-wrapper{
    padding-bottom: 0em !important;
  }
  .naetester .mat-form-field{
    margin-bottom: auto !important;
    max-width: 199px !important;
  }
  
  
  .naetester .mat-form-field-appearance-outline .mat-form-field-flex {
    max-width: 200px;
    height: 41px;
    background:#ffff;;
    border-radius: 0.65rem !important;
  }
  
  .naetester .mat-select-panel{
    max-width: 520px !important;
  }
  
  .naetester .mat-form-field-appearance-outline .mat-form-field-outline{
    top :0em !important;
  }

// Table
// Checkbox
.checkbox-item.mat-menu-item {
    line-height: 34px !important;
    height: 36px !important;
    color: var(--text-dropdown);
    border-bottom: 1px solid #7d7d7d67;
}

.mat-menu-content:not(:empty) {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    border: 1px solid #7d7d7d67 !important;
    border-radius: 4px;
}

.dropDown-view1.mat-menu-item {
    line-height: 30px !important;
    height: 36px !important;
    padding: 0 16px !important;
    color: var(--text-dropdown);
}

.dropDown-view2.mat-menu-item {
    line-height: 30px !important;
    height: 36px !important;
    padding: 0 16px !important;
    color: var(--sing-out);
}

.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background,
.mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--text-color2);
}

.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,
.mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element {
    background: var(--check-box-checked-color);
}

.mat-checkbox .mat-ripple-element {
    background: var(--check-box-checked-color);
}

.mat-checkbox-inner-container:hover .mat-checkbox-persistent-ripple {
    opacity: 0;
}

.mat-checkbox-checked:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element,
.mat-checkbox:active:not(.mat-checkbox-disabled).mat-accent .mat-ripple-element {
    background: none;
}

.mat-checkbox .mat-ripple-element {
    background: none;
}

.mat-checkbox-inner-container {
    height: 13px !important;
    width: 13px !important;
}

.mat-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-button.cdk-program-focused .mat-button-focus-overlay,
.mat-icon-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-icon-button.cdk-program-focused .mat-button-focus-overlay,
.mat-stroked-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-stroked-button.cdk-program-focused .mat-button-focus-overlay,
.mat-flat-button.cdk-keyboard-focused .mat-button-focus-overlay,
.mat-flat-button.cdk-program-focused .mat-button-focus-overlay {
    opacity: 0;
}

.mat-table mat-row,
.mat-table mat-header-row,
.mat-table mat-footer-row,
.mat-table th.mat-header-cell,
.mat-table td.mat-cell,
.mat-table td.mat-footer-cell {
    border-bottom-color: var(--border-color) !important;
}

.mat-table .mat-cell,
.mat-table .mat-header-cell {
    white-space: inherit !important;
}

// paginator
.paginator .mat-form-field-appearance-fill .mat-form-field-flex {
    background-color: transparent;
    padding: 0.55em 0.25em 0 0.35em;
}

.paginators .mat-form-field-appearance-fill .mat-form-field-infix {
    padding: 0.25em 0 0.25em 0;
}

.paginators .mat-form-field-appearance-fill .mat-select-arrow-wrapper {
    padding-top: 1em !important;
}

// .mat-paginator-container {
//   justify-content: space-between !important;
// }
.mat-paginator-page-size {
    display: flex;
    align-items: baseline;
    margin-right: 0px !important;
}

.paginators .mat-form-field-infix {
    padding: 0.7em 0;
    border-top: 0.2em solid transparent !important;
    color: var(--text-tr);
}

.paginators .mat-form-field-wrapper {
    padding-bottom: 0em !important;     
}

.paginators .mat-form-field-flex {
    width: 67px !important;
}
.paginators .mat-input-element {
    width: 120% !important;
    max-width: 150% !important;
}

.paginators .mat-form-field {
    margin-bottom: 0px;
}

.paginator .mat-form-field-wrapper {
    padding-bottom: 0.5em !important;
}

.paginator .mat-form-field-appearance-fill .mat-form-field-flex {
    border: 1px solid var(--background-base1) !important;
}

.paginator .mat-form-field-underline {
    position: absolute;
    width: 0;
    pointer-events: none;
    transform: none;
    bottom: 0em;
}

.paginator .mat-form-field-infix {
    padding: 0.25em 0 0.45em 0;
    border-top: 0.2em solid transparent !important;
}

.paginator .mat-form-field-appearance-fill .mat-select-arrow-wrapper {
    transform: translateY(-50%);
    padding-top: 1em;
}

.paginator .mat-paginator-range-actions {
    display: flex;
    align-items: center;
    // padding-bottom: 0.5rem;
}

.paginator .mat-paginator-range-label {
    margin: 0 0.1rem 0 0.1rem;
}

// paginator
.icon-eligible.mat-form-field-prefix .mat-icon,
.mat-form-field-suffix .mat-icon {
    font-size: 100%;
    line-height: 1.125;
}

.cardBorder {
    border: var(--border);
    border-radius: var(--border-radius);
}

// icon-button
.button.active[_ngcontent-qsf-c163] {
    background: none;
}

.btn1 .mat-button-ripple.mat-ripple,
.mat-button-focus-overlay {
    display: none !important;
}

// buttons
.btn-1 {
    background-color: var(--text-color2) !important;
    color: var(--button-color) !important;
    border: 1px solid var(--text-color2) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 500;
}

.btn-2 {
    background-color: var(--button-color) !important;
    color: var(--text-color2) !important;
    border: var(--save-draft-border) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 500;
}

.btn-3 {
    background-color: var(--button-color) !important;
    color: var(--text-delete) !important;
    border: var(--delete-border) !important;
    border-radius: 4px;
    padding: 0rem 1rem !important;
    font-weight: 500;
}

.btn-4 {
    color: var(--text-color2);
    background-color: var(--background-base3);
    border: 1px solid var(--background-base3);
    border-radius: 4px;
    font-weight: 500;
}

.btn-5 {
    color: var(--sing-out);
    background-color: var(--sing-out-background);
    border: 1px solid var(--sing-out-background);
    border-radius: 4px;
    font-weight: 500;
}

.btn-6 {
    color: var(--text-color1);
    background-color: var(--background-base);
    border: 1px solid var(--text-color1) !important;
    border-radius: 4px;
    font-weight: 500;
}

.btn-7 {
    color: var(--sing-out);
    background-color: var(--button-color);
    border: 1px solid var(--sing-out) !important;
    border-radius: 4px;
    font-weight: 500;
}

.btn-1.mat-button {
    line-height: 26px !important;
}

.btn-2.mat-button {
    line-height: 26px !important;
}

.btn-3.mat-button {
    line-height: 26px !important;
}

.btn-4.mat-button {
    line-height: 26px !important;
}

.btn-5.mat-button {
    line-height: 26px !important;
}

.btn-6.mat-button {
    line-height: 26px !important;
}

.btn-7.mat-button {
    line-height: 26px !important;
}

.mat-button-wrapper {
    display: contents !important;
}

.custom-bread-crumb {
    padding: 0px !important;
    list-style: none;
    background-color: transparent !important;
}

.custom-bread-crumb li a {
    text-decoration: none;
    font-size: 0.65rem;
}

.cardBorder {
    border: var(--border);
    border-radius: var(--border-radius);
}

.popUp-input .mat-form-field {
    width: 100%;
}

.mat-mini-fab .mat-button-wrapper {
    padding: 0px !important;
    text-align: -webkit-center;
}

// Mat radio button
.mat-radio-outer-circle {
    height: 14px !important;
    width: 14px !important;
}

.mat-radio-inner-circle {
    height: 14px !important;
    width: 14px !important;
}

.mat-radio-container {
    width: 14px !important;
    height: 14px !important;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--text-color2) !important;
    // width: 14px !important;
    // height: 14px !important;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--text-color2);
    // border-color: rgba(var(--color-accent), 1);
}

.mat-radio-button.mat-accent .mat-radio-inner-circle :hover {
    background-color: none !important;
    background: none;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle :hover {
    background-color: none !important;
    background: none;
}

.mat-radio-button.mat-accent .mat-radio-inner-circle :hover,
:focus {
    background-color: none !important;
    background: none;
}

.mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle,
.mat-radio-outer-circle :hover,
:focus {
    background-color: none !important;
    background: none;
}

.mat-radio-button-checked:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element,
.mat-radio-button:active:not(.mat-radio-button-disabled).mat-accent .mat-ripple-element {
    background: none !important;
    background: none;
}

.mat-radio-button .mat-ripple-element {
    background: none !important;
    background: none;
    display: none !important;
}

.mat-icon-button .mat-ripple-element {
    display: none !important;
}

// Mat radio button
// Mat Slide Toggle
.mat-slide-toggle-bar {
    width: 30px !important;
    height: 16px !important;
    border-radius: 10px !important;
}

.mat-slide-toggle-thumb-container {
    z-index: 1;
    width: 10px !important;
    height: 10px !important;
    top: 2px !important;
    left: 1px !important;
}

.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb {
    background-color: var(--text-color2);
}

.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb {
    background-color: var(--background-base);
}

.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
    background-color: var(--text-color2);
}

.mat-slide-toggle-thumb {
    height: 12px !important;
    width: 12px !important;
    border-radius: 50%;
}

.mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element {
    background-color: transparent;
}

.mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element {
    background-color: transparent;
}

.mat-slide-toggle.mat-checked .mat-ripple-element {
    background-color: transparent;
}

.mat-slide-toggle:not(.mat-checked) .mat-ripple-element {
    background-color: transparent;
}

.mat-slide-toggle:not(.mat-checked) .mat-ripple-element {
    background-color: transparent;
}

.mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element {
    background-color: transparent;
}

// Mat Slide Toggle
// Date picker
.calender .mat-form-field-wrapper {
    padding-bottom: 0em;
}

.calender .mat-date-range-input-container {
    padding: 0em;
    margin: 0em;
}

.calender .mat-input-element {
    padding-bottom: 0em !important;
}

.calendar-icons .mat-button-wrapper {
    padding-bottom: 0em !important;
}

.calender .mat-form-field-infix {
    border-top: 0.7em solid transparent;
}

.calender .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.35em 0 0.55em 0;
}

.calender1 .mat-form-field-wrapper {
    padding-bottom: 0em;
}

.calender1 .mat-form-field-infix {
    border-top: 0.5em solid transparent;
}

.calender1 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.55em 0 1em 0;
}

.calender .mat-form-field {
    margin-bottom: 0px;
    max-width: 190px;
}

.calender .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,
.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button {
    width: 1em !important;
    height: 1em !important;
    padding-top: -0.25rem !important;
}

.calender .mat-form-field-appearance-outline .mat-form-field-prefix,
.mat-form-field-appearance-outline .mat-form-field-suffix {
    top: 0.1em !important;
}

// Date picker
.matTabGroup .mat-tab-label {
    height: 38px !important;
}

.matTabGroup .mat-tab-label-content {
    font-size: 12px;
}

.eligibility-list .mat-stroked-button {
    padding: 0 12px !important;
    line-height: 27px;
    font-size: 0.65rem;
}

.eligibility-list .mat-stroked-button:not(.mat-button-disabled) {
    // border-color: var(--theader)!important;
    white-space: initial;
    text-align: initial;
}

.eligibility-list .mat-form-field-wrapper {
    padding-bottom: 0.25em;
}

.eligibility-list .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 1em 0 0.65em 0;
    border-top: 0.4em solid transparent;
}

.eligibility1 .mat-form-field-wrapper {
    padding-bottom: 1.5em !important;
}

.eligibility1 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 1em 0 0.65em 0;
    border-top: 0.4em solid transparent;
}

.eligibility-list1 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0 0.35em 0;
    border-top: 0.4em solid transparent;
}

.eligibility-list1 .mat-form-field-wrapper {
    padding-bottom: 0.2em !important;
}

.eligibility-list2 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.7em 0 1em 0;
    border-top: 0.4em solid transparent;
}

.eligibility-list3 .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.55em 0 0.65em 0;
    border-top: 0.4em solid transparent;
}

.eligibility-list3 .mat-form-field-wrapper {
    padding-bottom: 0em !important;
}

.flex-col-important {
    flex-direction: column !important;
    // width: 260vw;
    align-self: start;
}

.flex-col-important1 {
    flex-direction: column !important;
    width: 400%;
    align-self: start;
}

.elapsedTime .mat-hint {
    min-width: 7rem;
}

.elapsedTime .mat-form-field {
    width: 43.3% !important;
}

.exam-details .mat-form-field {
    width: 48.75% !important;
}

.manageUsers.mat-form-field {
    width: 31% !important;
}

.manageUsers.mat-form-field-appearance-outline .mat-form-field-flex {
    width: 100% !important;
}

.traininginstitute.mat-form-field {
    width: 50% !important;
}

.traininginstitue.mat-horizontal-stepper-header {
    padding: 0px 15px !important;
}

.customPopUp .mat-form-field-wrapper {
    padding-bottom: 1em;
}

.temporary-sidenav-class {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    width: 35%;
    height: 79vh;
    background: var(--background-base5) !important;
}

.temporary-sidenav-class1 {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    width: 35%;
    background: var(--background-base5) !important;
}

.mat-drawer-backdrop.mat-drawer-shown {
    background-color: transparent !important;
}

#dynamicComponentLoader .mat-drawer-container {
    display: inherit !important;
}

.mat-drawer-container {
    background-color: transparent;
}

.mat-dialog-container {
    padding: 12px !important;
    height: auto !important;
    background: var(--background-base);
}

.mat-drawer-inner-container {
    // background: var(--background-base1);
}

// form-builder changes
.formBuilderCard {
    border: var(--border);
    border-radius: var(--border-radius);
    overflow: auto;
    // @screen xl {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.25rem);
    // }
    // @screen lg {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.25rem);
    // }
    // @screen md {
    //     height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 6.25rem);
    // }
}

.report-details.mat-expansion-panel-header {
    color: #a7a8ac !important;
    background: #f9f9f9 !important;
    box-shadow: none;
}

.detail .mat-expansion-panel-body {
    padding: 10px 24px 16px !important;
}

.application-expansion .mat-expansion-panel-content {
    overflow: auto !important;
    @screen xl {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.2rem);
    }
    @screen lg {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.2rem);
    }
    @screen md {
        height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 12.2rem);
    }
}

.application-expansion .mat-expansion-panel-spacing {
    margin: 0px !important;
}

// form-builder changes
.addNewTitle .mat-form-field {
    width: 100% !important;
}

.addNewNote .mat-form-field {
    width: 100% !important;
}

.NgxEditor__MenuBar {
    padding: 1rem 0rem 0rem 0rem !important;
    flex-wrap: wrap !important;
}

.textEditor.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 1em 0 0.65em 0;
    border-top: 0.4em solid transparent;
    height: 30vh;
    overflow: auto;
}

.textEditor1.mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 1em 0 0.65em 0;
    border-top: 0.4em solid transparent;
    height: 30vh;
    overflow: auto;
}

.textEditor .mat-form-field-wrapper {
    padding-bottom: 0em;
    
}

.textEditor1 .mat-form-field-wrapper {
    padding-bottom: 0em;
    width: 400px !important
}

.searchFilt .mat-select-trigger {
    height: 2.6em;
    padding: 0.4rem 0rem 0.1rem 0.4rem;
}

//snack bar started
.my-custom-dialog-class .mat-dialog-container {
    overflow: hidden;
}

element.style {
    transform: scale(1);
    opacity: 1;
}

.error-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: 0.3px solid var(--snackbar-error);
    min-width: 0;
}

.warning-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: 0.3px solid var(--snackbar-warning);
    min-width: 0;
}

.success-snackbar.mat-snack-bar-container {
    color: var(--snackbar-text);
    background-color: var(--background-base) !important;
    box-shadow: none;
    border: var(--btn-border);
    min-width: max-content;
}

.success-snackbar .mat-icon-button,
.warning-snackbar .mat-icon-button,
.error-snackbar .mat-icon-button {
    padding: 0 16px;
    min-width: 0;
    width: 40px;
    height: 27px;
    flex-shrink: 0;
    line-height: 26px;
    border-radius: unset;
}

.success-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/success1.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

.warning-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/warning.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

.error-snackbar .grid-auto:first-child {
    background-image: url(../../../assets/img/error.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 36px 100px;
}

//snack bar end
.link-animation {
    color: var(--text-color2);
    font-weight: 500;
    cursor: pointer;
}

.onClickBlack {
    color: #7d7d7d !important;
}

.popup-container .mat-form-field-infix {
    border-top: 0.6em solid transparent;
}

.popup-container .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.75em 0 1em 0;
}

.popup-container
    .mat-form-field:not(.mat-form-field-appearance-legacy)
    .mat-form-field-prefix
    .mat-icon-button
    .mat-datepicker-toggle-default-icon,
.mat-form-field:not(.mat-form-field-appearance-legacy)
    .mat-form-field-suffix
    .mat-icon-button
    .mat-datepicker-toggle-default-icon {
    padding-top: 0.4rem;
}

.viewApp-Form {
    // height:calc(100vh - 47vh);
    // padding-top: 16rem!important;
    // margin-top: 1rem!important;
}

.matTabGroup .mat-tab-label {
    padding: 0 10px;
}

.add-to-cart .mat-button {
    padding: 0px 10px;
    line-height: 30px;
    min-width: 30px;
}

.appProcessLog {
    padding-top: 3rem;
    width: 12.8rem !important;
}

.width-4 {
    width: 4rem;
}

.width-5 {
    width: 5rem;
}

.width-6 {
    width: 6rem;
}

.width-65 {
    width: 6.5rem;
}

.width-7 {
    width: 7rem;
}
.width-8 {
    width: 8rem;
}

.width-85 {
    width: 8.5rem;
}


// mat-form field border sized
.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,
.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,
.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap {
    border-width: 1.5px !important;
}

.mat-tab-label:focus:not(.mat-tab-disabled) {
    opacity: 1;
    color: var(--text-color2) !important;
}

.mat-tabGroup .mat-tab-label-content {
    margin-bottom: 0.25rem !important;
}

.process-log1 {
    flex: auto;
}

.mat-tab-group.mat-primary .mat-ink-bar,
.mat-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: var(--text-color2);
}

.mat-badge-content {
    background: var(--text-color2);
}

.mat-select-panel {
    min-width: 100% !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

.edit-icon1 .mat-icon {
    font-size: 15px !important;
    cursor: pointer !important;
    text-align: center !important;
    vertical-align: sub;
    color: var(--text-color1);
}

.edit-icon2.mat-icon {
    font-size: 15px !important;
    cursor: pointer !important;
    text-align: center !important;
    vertical-align: sub;
    color: var(--text-color1);
}

.progressbar-template .mat-expansion-panel-body {
    padding: 0px;
}

.progressbar-template .mat-expansion-panel-header.mat-expanded {
    height: 45px;
}

.mat-slide-toggle-content {
    vertical-align: top;
    display: inline-block;
    line-height: 24px;
}

.eligibility-list .mat-stroked-button {
    // padding: 0 0 0 12px !important;
    line-height: 32px;
    font-size: 0.65rem;
}

.eligibility-list .mat-stroked-button:not(.mat-button-disabled) {
    border-color: var(--theader) !important;
    white-space: initial;
    text-align: initial;
}

.cloud_icon.mat-icon {
    height: 20px !important;
}

.icon-close.mat-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: right !important;
    font-size: large !important;
}

.custom-search.mat-input-element {
    border: 0.1px solid #cccccc !important;
    width: -webkit-fill-available;
}

.mat-tooltip {
    font-size: 12px!important;
    max-width: unset !important;
}
.wrap1{
    word-wrap: break-words
  }

.naetester .mat-form-field-wrapper{
    padding-bottom: 0em !important;
  }
  .naetester .mat-form-field{
    margin-bottom: auto !important;
    max-width: 199px !important;
  }

  .nst .mat-form-field{
    margin-bottom: -4px !important;
  }
  
  
  .naetester .mat-form-field-appearance-outline .mat-form-field-flex {
    max-width: 200px;
    height: 41px;
    background:#ffff;;
    border-radius: 0.65rem !important;
  }
  
  .naetester .mat-select-panel{
    max-width: 520px !important;
  }
  
  .naetester .mat-form-field-appearance-outline .mat-form-field-outline{
    top :0em !important;
  }
