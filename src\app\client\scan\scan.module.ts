import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { ScanComponent } from './scan.component';
import { ScanRoutingModule } from './scan-routing';
import { EvaluatorScanComponent } from './Evaluator-scan/Evaluator.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import {TooltipModule} from 'ng2-tooltip-directive';
import { EffectsModule } from '@ngrx/effects';
import { SharedEffects } from '../state/shared/shared.effects';
import { StoreModule } from '@ngrx/store';
import { SHARED_STATE_NAME } from '../state/shared/shared.selectors';
import { SharedReducer } from '../state/shared/shared.reducers';
import { OnboardDetailsComponent } from './onboard-details/onboard-details.component';
import { OnboardDetailsPhotoComponent } from './onboard-details-photo/onboard-details-photo.component';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { FlexLayoutModule } from '@angular/flex-layout';
import { IconModule } from '@visurel/iconify-angular';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { MatNativeDateModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSortModule } from '@angular/material/sort';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import {MatGridListModule} from '@angular/material/grid-list';
import {MatRadioModule} from '@angular/material/radio';
import { TableModule } from 'src/app/core/common-component/table/table.module';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { CommonComponentModule } from 'src/app/core/common-component/common-component.module';
import { VideoStreamComponent } from './video/video-stream/video-stream.component';

import { ClipboardModule } from '@angular/cdk/clipboard';
import { WebcamModule } from "ngx-webcam";
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import{ApplicationModule} from 'src/app/client/application/application.module';
import{AuditformComponent} from './Audit-form/audit-form'
import { ShredingService } from 'src/shared/video-provider/lib/service/shredding.service';

@NgModule({
  declarations: [
    ScanComponent,
    EvaluatorScanComponent,
    VideoStreamComponent,
    OnboardDetailsComponent,
    OnboardDetailsPhotoComponent,
    AuditformComponent
  ],
  imports: [
    CommonModule,
    ScanRoutingModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatTableModule,
    MatPaginatorModule,
    MatRadioModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    ClipboardModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRippleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    MatSnackBarModule,
    FormsModule,
    MatGridListModule,
    NgDynamicBreadcrumbModule,
    TableModule,
    MatExpansionModule,
    MatSidenavModule,
    TooltipModule,
    CommonComponentModule,
    EffectsModule.forFeature([SharedEffects]),
    StoreModule.forFeature(SHARED_STATE_NAME, SharedReducer),
    WebcamModule,
    MatProgressSpinnerModule,
    ApplicationModule

  ],
  exports:[ScanComponent],
  providers: [
    ShredingService
  ]

})
export class ScanModule {}
