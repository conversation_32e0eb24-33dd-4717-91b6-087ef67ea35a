export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://cna365api.examroom.ai/",
  reportUrl:"https://reports.examroom.ai/",
  reportUrlapi:"https://reportapiv2.examroom.ai/",
  redirectUrl: "https://cna365.examroom.ai/",
  redirectUrls: "https://cna365.examroom.ai/",
  gistUrl: "https://cna365.examroom.ai/gis/validate-gisaccess",
  redirectgsitUrl:" https://gis.examroom.ai",
  ScanUrl: "https://erlens.examroom.ai/",
  URLNotAllowedToken:'https://cna365api.examroom.ai/gisapis/api/EventOffer/GetEventOfferByEmail',
  NAERiredectorErrupt: "https://api.examroom.ai/",
  RedirecttoSkillsExams:'https://skillsreview.examroom.ai/',
  apiUrl: "https://webrtcapi.examroom.ai/",
  streamApiKey: "47095304",
  SkillsAccessKey :'cna.skilluser',
  SkillsSecrectKey:"bsjyvK85BeqL0dD",
  GisApiUrl:'https://cna365api.examroom.ai/gisapis/api/',
  superset:'https://workbench.examroom.ai/',
  candidate: "candidate",
  client: "client",
  training: "/training",
  sponsor: "/voucher",
  state:"/loader/manage-applications",
  Employees:"/loader/manage-all-users",
  Report:"/report",
  Evalutor:"https://cna365.examroom.ai/gis/validate-gisaccess",
  Finance:"/training",
  //// client ID update for paypal
  PayPalConfigurationId:"AT4WtY3pqlZReoVFkIrWngudLJhMcG9oXju25bsvZniEzsWBtpI1jYBcZO22nQaTV-CWPiroWePWljoC",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "brucewayne",
      credential: "1234567890",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};

