<div class="" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full ml-2" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1" *ngIf="(form && !form.formTypeID.includes(18))">
        <div class="" fxLayout="column" >
            <h6><strong>{{(form ? FormTypesToNames[form.formTypeID[0]].toString():'')+' Form'}}</strong></h6>
            <ng-container *ngIf="breadCrumbsArray && breadCrumbsArray.length > 0; else normalBreadCrumbs">
                <exai-dynamic-bread-crumbs [crumbs]="breadCrumbsArray" (crumbNavigate)="handleCrumbNavigation($event.crumb)">
                </exai-dynamic-bread-crumbs>
            </ng-container>
            <ng-template #normalBreadCrumbs>
                <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
                </app-ng-dynamic-breadcrumb>
            </ng-template>
        </div>
    </div>
    <div class="w-full mt-2" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
        <div class="flex justify-end add-new" fxLayout="" *ngIf="getWhetherAtleastOneEnabled && (form && !form.formTypeID.includes(11)) && (form && !form.formTypeID.includes(18));else problemReport">
            <button mat-button class="btn-3 text-xs mx-2" (click)="deleteForm()">
                {{this.lngSrvc.curLangObj.value.cancel}}
            </button>
            <button mat-button class="btn-1 text-xs ml-2" [style.display]="'none'" (click)="permanentlySaveResponse = false;" #submitButton id="submitButton">.
            </button>
            <button mat-button class="btn-2 text-xs mx-2" (click)="submit(false, true)" #submitButton id="submitButton">
                {{this.lngSrvc.curLangObj.value.saveDraft}}
            </button>
            <button  type="button" mat-button class="btn-1 text-xs ml-2" [ngClass]="{'button-disabled' : checkProgressOfSections }" (click)="submit(true, true)" #submitButton id="submitButton">
                {{this.lngSrvc.curLangObj.value.submit}}
            </button>
        </div>
        <ng-template #problemReport >
            <div class="flex justify-end add-new" fxLayout="" *ngIf="form && form.formTypeID.includes(11); else cofacilitation">
            <button mat-button class="btn-1 text-xs ml-2" (click)="submit(true, true)" #submitButton id="submitButton">
                {{this.lngSrvc.curLangObj.value.submit}}
            </button>
        </div>
        </ng-template> 
        <ng-template #cofacilitation>
            <div class="flex justify-end add-new mr-9" fxLayout="">
            <button mat-button class="btn-1 text-xs ml-2"  [ngClass]="{'button-disabled' : checkProgressOfSections }"(click)="submit(false, true)" #submitButton id="submitButton">
                {{this.lngSrvc.curLangObj.value.submit}}
            </button>
        </div>
        </ng-template>
    </div>
</div>

<div class="py-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
    <div class="shadow-none justify-start overflow-auto dashboard" [gdColumn]="!(form && !form.formTypeID.includes(2) && !form.formTypeID.includes(4) && !form.formTypeID.includes(11)) ? '1 / 11' : '1 / 9'" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="card shadow-none cardBorder" fxFlex="auto" [style.overflow-x]="'hidden!important'">
            <ng-container *ngIf="(form && !form.formTypeID.includes(11)) ;else problemReportBlock">
                <ng-container>
                    <exai-form-builder #formBuilder id="formBuilder" *ngIf="form && form.formJSON != null && !(userResponse$ | async)" [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true" (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)" (onChange)="handleChange($event)"
                        (formSubmitEvent)="saveResponse($event)" [submitButtonRef]="submitButtonRef" (initalSectionValidationEvent)="initialSectionValidationEvent($event)"  [showStatic]="form.formTypeID.includes(2)
                        || form.formTypeID.includes(4)" [element]="{eligibilityRouteId:form.eligibilityID,personEventId}">
                    </exai-form-builder>
                </ng-container>
                <ng-container>
                    <exai-form-builder #formBuilder id="formBuilder" *ngIf="form && form.formJSON != null && (userResponse$ | async)" [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true" (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)" (onChange)="handleChange($event)"
                        (formSubmitEvent)="saveResponse($event)" [submitButtonRef]="submitButtonRef" [existingUserData]="(userResponse$ | async).response" (initalSectionValidationEvent)="initialSectionValidationEvent($event)"  [showStatic]="form.formTypeID.includes(2)|| form.formTypeID.includes(4)" [element]="{eligibilityRouteId:form.eligibilityID,personEventId}" [disabled]="(userResponse$ |async).disabled">
                    </exai-form-builder>
                </ng-container>
            </ng-container>
            <ng-template #problemReportBlock>
                <exai-form-builder *ngIf="form && form.formJSON != null && (userResponse$ | async)" [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true" (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)" (onChange)="handleChange($event)"
                (formSubmitEvent)="saveResponse($event)" [submitButtonRef]="submitButtonRef" [existingUserData]="(userResponse$ | async).response" (initalSectionValidationEvent)="initialSectionValidationEvent($event)"  [element]="{eligibilityRouteId:form.eligibilityID,personEventId}">
            </exai-form-builder>
            <exai-form-builder *ngIf="form && form.formJSON != null && !(userResponse$ | async)" [existingForm]="form.formJSON" [showToolbar]="false" [showOnlyForm]="true" (sectionCompleted)="sectionsForProgress.splice($event.sectionIndex,1,$event);changeSectionValidationEvent($event)" (onChange)="handleChange($event)"
            (formSubmitEvent)="saveResponse($event)" [submitButtonRef]="submitButtonRef" (initalSectionValidationEvent)="initialSectionValidationEvent($event)"   [element]="{eligibilityRouteId:form.eligibilityID,personEventId}">
        </exai-form-builder>
                </ng-template>
            <div *ngIf="accTypeFormSysFileName.value" class="max-w-full py-2 shadow-none cardBorder mx-4 my-4 px-3 flex flex-row justify-center items-center" #accFormDownloadButton id="accFormDownloadButton">
                Based on your Accomodation Type selection, please download the following form for completion by your evaluator
                <mat-icon (click)="store.dispatch(downloadAccTypeForm({sysFileName:this.accTypeFormSysFileName.value}))" class="cursor-pointer">download</mat-icon>
            </div>
        </div>
    </div>
    <div *ngIf="form && !form.formTypeID.includes(2) && !form.formTypeID.includes(4)&& !form.formTypeID.includes(11) && !form.formTypeID.includes(FormTypes.AbuseAlligation) && form && !form.formTypeID.includes(13) && !form.formTypeID.includes(18)" class="shadow-none justify-start" gdColumn="9/ -1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <!-- <div class="card shadow-none cardBorder p-4 dashboard touch-auto overflow-auto" fxFlex="auto">
            <form-progress-bar [sections]="sectionsForProgress"></form-progress-bar>
        </div> -->
        <div>
            <mat-accordion >
                <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            Application Progress
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <form-progress-bar [sections]="sectionsForProgress"></form-progress-bar>
                    </p>
                </mat-expansion-panel>
                <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                <mat-expansion-panel class="cardBorder application-expansion mb-2" *ngIf="ApplicationPerformLogs && ApplicationPerformLogs.length">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            {{(form ? FormTypesToNames[form.formTypeID[0]].toString():'')+' Progress'}}
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <exai-progress-bar [performlogs]="ApplicationPerformLogs"></exai-progress-bar>
                    </p>
                </mat-expansion-panel>
                <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                <mat-expansion-panel class="cardBorder application-expansion1" *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                    <mat-expansion-panel-header>
                        <mat-panel-title class="text-xs font-bold">
                            {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <p class="expand">
                        <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                    </p>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>

</div>