<div *ngIf="global?.personEventId?.examCode !='NA-PR' || data?.details?.examName != 'Nurse Aide Written Exam'" [ngClass]="{hide1:showLoader(0)}" class="px-gutter" exaiContainer>
    <div class="loading-container">
        <mat-spinner></mat-spinner>
        <div class="loading">
            <div class="flex justify-center test-base fontcustom">
                We're processing...
            </div>
            <div class="flex justify-center text-xs font-bold">
                This process may take a few seconds, so please be patient.
            </div>
        </div>
    </div>
</div>

<div *ngIf="global?.personEventId?.examCode !='NA-PR'|| data?.details?.examName == 'Nurse Aide Written Exam'" [ngClass]="{hide:showLoader(1)}">
    <div class="header-container">
        <h2 mat-dialog-title class="font-bold flex mx-3 ml-6 f-medium text-sm">{{ header }}</h2>
        <div class="details">
            <span  *ngIf="details?.firstName || details?.lastName">
                <span><b>Name: </b></span> <span class="value">{{ details?.firstName + ' ' + details?.lastName }}</span>
            </span>
            <span>
                <span><b>Booklet Number: </b></span> <span class="value">{{ details?.bookletNumber }}</span>
            </span>
            <span>
                <span><b>Appointment Id:</b></span> <span class="value">{{ details?.appointmentId }}</span>
            </span>
        </div>
    </div>
    <hr class="divider">

    <div mat-dialog-content class="mat-typography">
        <div class="dialog-container">
            <div class="omr">
                <!-- <div *ngIf="isLoading " class="loader">
                <mat-progress-spinner class="calenderSpinner flex justify-center" color="primary" diameter="60"
                    mode="indeterminate">
                </mat-progress-spinner>
            </div> -->
                <div *ngIf="answerSheetPDF; else showImageAnswerSheet">
                    <pdf-viewer [src]='answerSheetPDF.url' [render-text]="true" [original-size]="false"
                        [zoom-scale]="1" style="width: 100%; height: 78vh">
                    </pdf-viewer>
                </div>
                <ng-template #showImageAnswerSheet>
                    <div class="image-container">
                        <img [src]="answerSheetIMG.url" alt="Answer Sheet" class="image">
                    </div>
                </ng-template>
            </div>
            <mat-divider [vertical]="true"></mat-divider>
            <div class="options">
                <form #candidateResponseForm="ngForm">
                    <table style="width: 100%; height: 100%;" *ngIf="questionsColumn1">
                        <tr>
                            <td>
                                <div class="option" *ngFor="let question of questionsColumn1">
                                    <mat-label class="label">
                                        <div class="label-text">{{ question.questionNumber + '.' }}</div>
                                    </mat-label>
                                    <select [id]="question.questionNumber" class="select"
                                        [(ngModel)]="question.response" [name]="question.questionNumber">
                                        <option value="">None</option>
                                        <option *ngFor="let option of options" [value]="option">{{option}}</option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <div class="option" *ngFor="let question of questionsColumn2">
                                    <mat-label class="label">
                                        <div class="label-text">{{ question.questionNumber + '.' }}</div>
                                    </mat-label>
                                    <select [id]="question.questionNumber" class="select"
                                        [(ngModel)]="question.response" [name]="question.questionNumber">
                                        <option value="">None</option>
                                        <option *ngFor="let option of options" [value]="option">{{option}}</option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <div class="option" *ngFor="let question of questionsColumn3">
                                    <mat-label class="label">
                                        <div class="label-text">{{ question.questionNumber + '.' }}</div>
                                    </mat-label>
                                    <select [id]="question.questionNumber" class="select"
                                        [(ngModel)]="question.response" [name]="question.questionNumber">
                                        <option value="">None</option>
                                        <option *ngFor="let option of options" [value]="option">{{option}}</option>
                                    </select>
                                </div>
                            </td>
                            <td>
                                <div class="option" *ngFor="let question of questionsColumn4">
                                    <mat-label class="label">
                                        <div class="label-text">{{ question.questionNumber + '.' }}</div>
                                    </mat-label>
                                    <select [id]="question.questionNumber" class="select"
                                        [(ngModel)]="question.response" [name]="question.questionNumber">
                                        <option value="">None</option>
                                        <option *ngFor="let option of options" [value]="option">{{option}}</option>
                                    </select>
                                </div>
                            </td>

                        </tr>
                    </table>
                </form>

            </div>

        </div>
    </div>

    <div mat-dialog-actions align="end">
        <button mat-button mat-dialog-close class=" btn-3 t-xs mr-2 cancel-btn">Cancel</button>
        <button class="btn-1 t-xs mr-2 re-evaluate" mat-button cdkFocusInitial
            (click)="submitUpdatedResponse()">{{ submitBtn }}</button>
    </div>
</div>

<ng-container >
<div *ngIf="global?.personEventId?.examCode =='NA-PR'"  class="shadow-none justify-start overflow-auto dashboard height ">
    <iframe [src]="($Gisturl | async) | safeResourceurl" class="w-full h-full" ></iframe>
</div>
</ng-container>


