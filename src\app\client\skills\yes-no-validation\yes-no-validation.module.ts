import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { YesNoValidationComponent } from "./yes-no-validation.component";
import { MatRadioModule } from "@angular/material/radio";

const _COMMONMODULE = [
  CommonModule,
  FormsModule,
  ReactiveFormsModule,
  MatRadioModule
];

@NgModule({
  declarations: [YesNoValidationComponent],
  imports: [..._COMMONMODULE],
  exports: [..._COMMONMODULE, YesNoValidationComponent],
})
export class YesNoValidationModule {}
