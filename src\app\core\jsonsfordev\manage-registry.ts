import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Manage Registration",
            moduleID: "manage-registry",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageRegistry",
            routeRef: "/loader",
            dataattributes:'managereg',
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ['tab-col'],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter: true,
                                    showAddButton: [false],
                                    showMatTabs: false,
                                    showSearch: true,
                                    showColumnWiseFilter: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                    isPaginationServerSide: [true],
                                    isSearchServerSide: [true],
                                    showStateSelectionDropdown:true,
                                    showSlectdropstatedataattributes:"manregisdrop",
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', 'w-4'],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "PersonId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["FullName"],
                                            name: "Candidate Name",
                                            cellType: "link",
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "manage-registry-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Registration Details",
                                            },
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["link-animation"],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "StateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "CertNumber",
                                            name: "Registration Number",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ExpirationDate",
                                            name: "Expiry Date",
                                            cellType: "onlydate",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false 
                                        },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "ActionBy",
                                        //     name: "Working Status",
                                        //     cellType: "text",
                                        //     headerType: "sort",
                                        //     headerAlign: "center",
                                        //     cellAlign: "center",
                                        //     headerCssClasses: ['db-table'],
                                        //     cellCssClasses: [],
                                        // },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "RegistryStatus",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "manage-registry-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Candidate Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["db-table", "w-12"],
                                            cellCssClasses: [],
                                        },
                                    ]],
                                requestDetails: [{
                                    fetchUrl: ['registry', 'api', "certificate","get-all"],
                                    requestBody: null,
                                    requestParams: [{
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNumber",
                                        paramValue: 1
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "stateId",
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'stateId'
                                    }
                                    ],
                                    method: 'GET'
                                },
                                ]
                            }
                        }
                    ]
                }
            ]
        }
    ]
}