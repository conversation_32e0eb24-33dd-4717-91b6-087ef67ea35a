import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Actions, act, createEffect, ofType } from "@ngrx/effects";
import { of, throwError } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { consoleLoaderJSON } from "../../dynamic-component-loader/dynamic-component-loader.types";
import {
  fetchConsoleLoderJSON,
  setErrorMessage,
  fetchedConsoleLoaderJSON,
  fetchAccountAccess,
  fetchedAccountAccess,
  reportSpinBoardList,
  gotReportSpinBoardList,
  getuser,
  gotUser,
  UpdateAbsentDetails,
  GotAbsentDetails,
  UpdateMeasurementScore,
  GotMeasurementScore,
  GetVMasterId,
  GotVMasterId,
  getOrderStatus,
  gotOrderStatus,
} from "./shared.actions";
import {
  AbsentResponse,
  Access,
  BooketOrderStatus<PERSON>son,
  MeasurementDetails,
  MeasurementSheet,
} from "./shared.state";
import { getUserDetails, gotUserDetails } from "./shared.actions";
import { URL } from "src/app/core/url";
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService } from "src/app/core/global-user.service";

@Injectable({
  providedIn: "root",
})
export class SharedEffects {
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private services: SnackbarService,
    private global: GlobalUserService
  ) {}

  effectivelyGetUserDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getUserDetails),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            this.global.rolename.roleName == "Proctor" &&
              this.global.rolename.tsmemail != ""
              ? URL.ACCOUNT_BASE_URL + `getParameters?EmailId=${action.emailId}`
              : URL.ACCOUNT_BASE_URL +
                  `get-parameters-new?personTenantRoleId=${action.personTenantRoleId}`
          )
          .pipe(
            map((data) =>
              gotUserDetails({
                userDetails: data,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyGetUser$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getuser),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL +
              `Account/getusers?personTenantRoleId=${action.personTentantId}`
          )
          .pipe(
            map((data) =>
              gotUser({
                getuserDetails: data,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );
  effectivelyGetReportSpinBoardList$ = createEffect(() =>
    this.actions$.pipe(
      ofType(reportSpinBoardList),
      switchMap((action) => {
        return this.httpClient
          .post<any>(`${environment.reportUrlapi}metadata/search`, {
            username: action.username,
            org_id: action.org_id,
            dependent_object_version: action.dependent_object_version,
            include_auto_created_objects: action.include_auto_created_objects,
            include_dependent_objects: action.include_dependent_objects,
            include_details: action.include_details,
            include_headers: action.include_headers,
            include_hidden_objects: action.include_hidden_objects,
            include_incomplete_objects: action.include_incomplete_objects,
            include_visualization_headers: action.include_visualization_headers,
            record_offset: action.record_offset,
            record_size: action.record_size,
            include_stats: action.include_stats,
          })
          .pipe(
            map((data) =>
              gotReportSpinBoardList({
                json: data,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(
                err.message.message.error.Message
              );
              return of(
                setErrorMessage({ message: err.message.message.error.Message })
              );
            })
          );
      })
    )
  );

  effectivelyFetchConsoleLoaderJSON$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchConsoleLoderJSON),
      switchMap((action) => {
        return this.httpClient
          .get<any>(`assets/jsons/console-loader-json.json`)
          .pipe(
            map((data) =>
              fetchedConsoleLoaderJSON({
                consoleLoaderJSON: data.json as consoleLoaderJSON,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err.message })))
          );
      })
    )
  );

  effectivelyFetchAccountAccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchAccountAccess),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            environment.baseUrl +
              `login/api/Access/list?personTenantRoleId=${action.personTenantRoleId}&consoleId=${action.consoleId}&stateId=${action.stateId}`
          )
          .pipe(
            map((data) =>
              fetchedAccountAccess({
                accessData: data as Array<Access>,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err.message })))
          );
      })
    )
  );

  effectiveAbsentDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UpdateAbsentDetails),
      switchMap((action) => {
        return this.httpClient
          .post<AbsentResponse>(
            environment.baseUrl + `client/api/` + `arkiv/absent`,
            action.body
          )
          .pipe(
            map((data) =>
              GotAbsentDetails({
                response: data,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );
  effectiveMeasureDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(UpdateMeasurementScore),
      switchMap((action) => {
        return this.httpClient
          .post<object>(
            environment.baseUrl +
              `schedulemsvc/api/` +
              `scheduler/GetMeasurementsDetails`,
            action.body
          )
          .pipe(
            map((data: MeasurementDetails[]) =>
              GotMeasurementScore({
                Measuredatadetails: data,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );

  effectivelyVMasterId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(GetVMasterId),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            `${environment.baseUrl}client/api/exam/GetVMasterID?personTenantRoleId=${action.personTenanatRoleId}`
          )
          .pipe(
            map((data) =>
              GotVMasterId({
                VMasterId: data,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );

  effectiveGetBookletOrderStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getOrderStatus),
      switchMap((action) => {
        if (action.text == "booklet_inventory") {
          return this.httpClient
            .post<BooketOrderStatusJson[]>(
              `${environment.baseUrl}client/api/credentia/order-status`,
              action.body
            )
            .pipe(
              map((data) =>
                gotOrderStatus({
                  response: data,
                })
              ),
              catchError((err) => {
                this.services.callSnackbaronError(err.error);
                return of(setErrorMessage({ message: err.message }));
              })
            );
        } else if (action.text == "candidate-search-slot") {
          // For candidate search slot logs, fetch issue types from API
          return this.httpClient
            .get<any>(`${environment.baseUrl}client/api/Account/GetExamStatus`)
            .pipe(
              map((data) => {
                // Transform API response to match expected format
                const issueTypes = data.map((item: any) => ({
                  orderStatusId: item.id,
                  statusDescription: item.description,
                }));
                return gotOrderStatus({ response: issueTypes });
              }),
              catchError((err) => {
                console.error("Error fetching exam status:", err);
                // Fallback to hardcoded values if API fails
                const fallbackIssueTypes = [
                  { orderStatusId: 98, statusDescription: "Slot Found" },
                  { orderStatusId: 99, statusDescription: "Slot Not Found" },
                  { orderStatusId: 66, statusDescription: "Added to cart" },
                  { orderStatusId: 67, statusDescription: "Removed From Cart" },
                  {
                    orderStatusId: 102,
                    statusDescription: "Voucher Related Issue",
                  },
                  {
                    orderStatusId: 100,
                    statusDescription: "Payment Not Processed",
                  },
                  { orderStatusId: 101, statusDescription: "PreFlight Issue" },
                  { orderStatusId: 68, statusDescription: "Scheduling Error" },
                  { orderStatusId: 1, statusDescription: "Exam Scheduled" },
                  { orderStatusId: 8, statusDescription: "Exam Completed" },
                ];
                return of(gotOrderStatus({ response: fallbackIssueTypes }));
              })
            );
        } else {
          return this.httpClient
            .get<any[]>(
              `${environment.baseUrl}client/api/form/QAC/grievance-action?indicator=S`
            )
            .pipe(
              map((data) =>
                gotOrderStatus({
                  response: data,
                })
              ),
              catchError((err) => {
                this.services.callSnackbaronError(err.error);
                return of(setErrorMessage({ message: err.message }));
              })
            );
        }
      })
    )
  );
}
