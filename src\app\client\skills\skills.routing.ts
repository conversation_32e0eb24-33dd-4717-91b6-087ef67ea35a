import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { SkillsDetailsComponent } from './skills-details.component';
import { SkillsComponent } from './skills.component';



const routes: Routes = [
  {
    path: '', component: SkillsComponent,
//     data: {
//       title: 'Voucher',
//       breadcrumb: [
//         {
//           label: 'Home',
//           url: '/training'
//         },
//         {
//           label: 'Voucher',
//           url: ''
//         },
//       ]
//     },
  },
  { path: 'skills-details', component: SkillsDetailsComponent,
  data: {
    title: 'Roast',
    // breadcrumb: [
    //   {
    //     label: 'Home',
    //     url: '/training'
    //   },
    //   {
    //     label: 'Voucher',
    //     url: '/voucher'
    //   },
    //   {
    //     label: 'Buy New Voucher',
    //     url: ''
    //   },
    // ]
  },
},

//   { path: ':personId/:stateId/:id/:personTenantRoleId', component:VoucherComponent },
//   { path: ':personId/:stateId/id/:personTenantRoleId', component:BuyNewVoucherComponent },
  { path: '**', pathMatch: 'full', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SkillsRoutingModule { }
