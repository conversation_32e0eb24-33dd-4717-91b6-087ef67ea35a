import { Component, Inject, OnInit, Renderer2 } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogConfig,
  MatDialogRef,
} from "@angular/material/dialog";
import { Router } from "@angular/router";
import { OnboardDetailsPhotoComponent } from "../onboard-details-photo/onboard-details-photo.component";
import { HttpErrorResponse } from "@angular/common/http";
import { HttpService } from "src/app/core/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { map } from "lodash";
import { ShowOnboardforParticularstate } from "src/app/core/common-component/examroom-formbuilder/form-builder.types";

@Component({
  selector: "app-onboard-details",
  templateUrl: "./onboard-details.component.html",
  styleUrls: ["./onboard-details.component.scss"],
})
export class OnboardDetailsComponent implements OnInit {
  onboardFormGroup: FormGroup;
  candidateIdFileName: string = "";
  candidateFaceFileName: string = "";
  appointmentId: string = "";
  firstName: string = "";
  lastName: string = "";
  candidateId: number;
  testCenterId: string;
  personEventId: string;
  naeUserId: number;
  candidateIdSystemFileName = "";
  candidateFaceSystemFileName = "";
  isSignatureDisabled = false;
  signatureFileName = "";
  signatureUploaded = false;
  allosignatureUpload = false;

  ctx: any;
  signaturepadID: any;
  isDrawing = false;
  isSignatureSave: boolean = false;
  isClearSignature: boolean = false;
  isConsentCheckbox: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialog: MatDialog,
    private http: HttpService,
    private services: SnackbarService,
    private dialogRef: MatDialogRef<OnboardDetailsComponent>,
    private renderer: Renderer2
  ) {
    this.appointmentId = this.data.appointmentId;
    this.firstName = this.data.firstName;
    this.lastName = this.data.lastName;
    this.candidateId = this.data.candidateId;
    this.testCenterId = this.data.testCenterId;
    this.personEventId = this.data.personEventId;
    this.naeUserId = this.data.naeUserId;
  }

  ngOnInit(): void {
    this.onboardFormGroup = this.formBuilder.group({
      notes: [""],
    });

    this.http.candidateIdSystemFileName$.subscribe((fileName) => {
      this.candidateIdSystemFileName = fileName;
    });

    this.http.candidateFaceSystemFileName$.subscribe((fileName) => {
      this.candidateFaceSystemFileName = fileName;
    });

    this.signpad();
  }

  signpad() {
    this.signaturepadID = document.getElementById(
      "Signaturepad"
    ) as HTMLCanvasElement;
    this.ctx = this.signaturepadID.getContext("2d");
    this.ctx.strokeStyle = "#00010b";
    this.ctx.fillStyle = "#ffffff";
    this.ctx.fillRect(
      0,
      0,
      this.signaturepadID.width,
      this.signaturepadID.height
    );
    this.setupTouchMouseListeners();
  }

  setupTouchMouseListeners() {
    //  touch events
    this.renderer.listen(this.signaturepadID, "touchstart", (e) =>
      this.signpadonMouseDown(e)
    );
    this.renderer.listen(this.signaturepadID, "touchmove", (e) =>
      this.signpadonMouseMove(e)
    );
    this.renderer.listen(this.signaturepadID, "touchend", (e) =>
      this.signpadonMouseUp(e)
    );
    // For mouse events
    this.renderer.listen(this.signaturepadID, "mousedown", (e) =>
      this.signpadonMouseDown(e)
    );
    this.renderer.listen(this.signaturepadID, "mousemove", (e) =>
      this.signpadonMouseMove(e)
    );
    this.renderer.listen(this.signaturepadID, "mouseup", (e) =>
      this.signpadonMouseUp(e)
    );
    this.renderer.listen(this.signaturepadID, "mouseout", (e) =>
      this.signpadonMouseUp(e)
    );
  }

  signpadonMouseDown(e) {
    this.isDrawing = true;
    this.isClearSignature = true;
    // document.getElementById("placeholder").style.display = "none";
    const coords = this.signpadrelativeCoords(e);
    this.ctx.moveTo(coords.x, coords.y);
    this.ctx.beginPath();
    this.ctx.arc(coords.x, coords.y, 1, 0, Math.PI * 1);
    this.ctx.fillStyle = "#00010b";
    this.ctx.fill();
    this.ctx.closePath();
    e.preventDefault();
  }

  signpadonMouseMove(e) {
    if (this.isDrawing) {
      this.allosignatureUpload = true;
      const coords = this.signpadrelativeCoords(e);
      this.ctx.lineTo(coords.x, coords.y);
      this.ctx.stroke();
      this.isSignatureSave = true;
      this.isClearSignature = true;
      e.preventDefault();
    }
  }

  signpadonMouseUp(e) {
    this.isDrawing = false;
    e.preventDefault();
  }

  signpadrelativeCoords(event) {
    const bounds = event.target.getBoundingClientRect();
    let x, y;

    if (event.type.startsWith("touch")) {
      x = event.touches[0].clientX - bounds.left;
      y = event.touches[0].clientY - bounds.top;
    } else {
      x = event.clientX - bounds.left;
      y = event.clientY - bounds.top;
    }

    return { x: x, y: y };
  }

  clear() {
    this.allosignatureUpload = false;
    this.signatureUploaded = false;
    this.ctx.clearRect(
      0,
      0,
      this.signaturepadID.width,
      this.signaturepadID.height
    );
    this.ctx.beginPath();
    this.isSignatureSave = false;
    this.isClearSignature = false;
  }

  signatureUpload() {
    this.isSignatureDisabled = true;
    const base64String = this.signaturepadID.toDataURL().split(",")[1];
    const byteArray = new Uint8Array(
      atob(base64String)
        .split("")
        .map((char) => char.charCodeAt(0))
    );
    const file = new File([byteArray], `${this.appointmentId}_signature.jpeg`, {
      type: "image/jpeg",
    });
    const formData = new FormData();
    formData.append("File", file);
    formData.append("SubFolderName", "OnboardingCandidate");
    formData.append("type", "1");
    this.http.postUploadImage(formData).subscribe(
      (data: any) => {
        const extractedFileName = data.systemFileName.replace(
          "OnboardingCandidate/",
          ""
        );
        this.signatureFileName = extractedFileName;
        this.signatureUploaded = true;
      },
      (err: HttpErrorResponse | any) => {
        this.services.callSnackbaronError(`${err.message.message.error}`);
      }
    );
  }

  consentCheck() {
    this.isConsentCheckbox = !this.isConsentCheckbox;
  }

  launch(): void {
    let body = {
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      appointmentId: this.data.appointmentId,
      clientEventId: this.data.clientEventId,
      email: this.data.email,
      personTenantRoleID: this.data.personTenantRoleID,
      id: this.data.clientEventId,
      photoType: "CandidateId",
    };
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = "60vw";
    dialogConfig.minHeight = "60vh";
    dialogConfig.data = body;
    this.dialog
      .open(OnboardDetailsPhotoComponent, dialogConfig)
      .afterClosed()
      .subscribe((fileName: any) => {
        this.candidateIdFileName = fileName;
      });
  }

  launchFaceId(): void {
    let body = {
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      appointmentId: this.data.appointmentId,
      clientEventId: this.data.clientEventId,
      email: this.data.email,
      personTenantRoleID: this.data.personTenantRoleID,
      id: this.data.clientEventId,
      photoType: "CandidateFace",
    };
    const dialogConfig = new MatDialogConfig();
    dialogConfig.minWidth = "60vw";
    dialogConfig.minHeight = "60vh";
    dialogConfig.data = body;
    this.dialog
      .open(OnboardDetailsPhotoComponent, dialogConfig)
      .afterClosed()
      .subscribe((fileName: any) => {
        this.candidateFaceFileName = fileName;
      });
  }

  public submit(): void {
    const notes = this.onboardFormGroup.value.notes;
    let formBody = {
      firstImageFile: this.candidateIdFileName,
      secondImageFile: this.candidateFaceFileName,
      appointmentId: this.appointmentId,
      personEventId: Number(this.personEventId),
      notes: notes,
      signatureImageFile: this.signatureFileName,
    };
    let body = {
      candidateId: this.candidateId,
      testCenterId: this.testCenterId,
      personEventId: this.personEventId,
      naeUserId: this.naeUserId,
    };
    this.http.formSaveFile(formBody).subscribe(
      (data) => {
        this.data.mode == 1 && !ShowOnboardforParticularstate.includes(this.data.state)?  this.http.UpdateBookletNumber(body).subscribe(
          (data) => {
            this.dialogRef.close({clientEventId:this.data.clientEventId});
          },
          (err: HttpErrorResponse | any) => {
            this.dialogRef.close();
            this.services.callSnackbaronError(err.message.message.error);
          }
        ): this.dialogRef.close({clientEventId:this.data.clientEventId});
       
      },

      (err: HttpErrorResponse | any) => {
        this.dialogRef.close();
        this.services.callSnackbaronError(err.message.message.error);
      }
    );
  }

  close(): void {
    this.dialogRef.close();
  }
}
