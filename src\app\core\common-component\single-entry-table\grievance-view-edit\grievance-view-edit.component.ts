import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { forkJoin, Observable, Subject, Subscription } from 'rxjs';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { EventDetails, GivanceViewService, GrievanceAction, GrievanceEvent, GrievanceEventGet, Processor } from 'src/app/core/grivance-view';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { cellClickEvent, linkClickEvent } from '../../table/dynamic-table-types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { MatDialog } from '@angular/material/dialog';
import { ImgPopUpComponent } from '../../dynamic-popup/img-pop-up/img-pop-up.component';
import { GrievanceQacListComponent } from '../grievance-qac-list/grievance-qac-list.component';
import { HttpService } from 'src/app/core/http.service';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { FileViewerComponent } from '../file-viewer/file-viewer.component';
import { PopUpComponent as popupcomponent } from "../../../../client/application/pop-up/pop-up.component";
import { LanguageService } from 'src/app/core/language.service';
import { Store } from '@ngrx/store';
import { recentLinkClickEventSelector } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormService } from '@ng-dynamic-forms/core';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';

@Component({
  selector: 'app-grievance-view-edit',
  templateUrl: './grievance-view-edit.component.html',
  styleUrls: ['./grievance-view-edit.component.scss']
})
export class GrievanceViewEditComponent implements OnInit, OnDestroy{
  fileDetails=[]
  processorFiltered: Observable<Processor[]>;
  grievanceTypeFiltered: Observable<GrievanceAction[]>;
  grievanceStatusFiltered: Observable<GrievanceAction[]>;
  formGroupUpload 
  formGroupModel
  QACGrievance:'QACGrievance'
  grievanceStatusList: GrievanceAction[];
  grievanceTypeList: GrievanceAction[];
  processorsList : Processor[];
  today = new Date();
  assignForm : FormGroup; 
  subscriptions: Subscription[] = [];
  eventDetails: EventDetails;
  grievanceDetail : GrievanceEventGet = undefined;
  private destroy$ = new Subject<void>();

  allowEdit = true;
  recentLinkClickEvent: linkClickEvent;
  isEdit = true;
  file:any;
  imageBase64:string;
  qacFileName:string;
  dueDate:any;
  allowRescore : boolean;

  fileUrl=[]
  url:any;

  @ViewChild('inputImage') inputImage: ElementRef;
  dataUpload = new DynamicFileUploadModel({
    required: false,
    id:'1',
    name:'2',
    label: 'Upload Supporting Documents',
    accept: ['.pdf', '.doc', '.docx','.jpg','.png'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList:false,
    additional: {
      appearance: 'outline'
    }
   
  })
  
  constructor(private grievanceViewService:GivanceViewService,
    private fb:FormBuilder,
    private snackbarService:SnackbarService,
    public global:GlobalUserService,
    private dialog:MatDialog,
    private http: HttpService,
    private lngSrvc:LanguageService,
    private cdr: ChangeDetectorRef,
    private store: Store,
    private dynamicFormService: DynamicFormService,
    private https:HttpClient,
    private router:Router
  ) { 
    this.assignForm = this.fb.group({
      processor:['',Validators.required],
      grievanceDetails:[''],
      date:['',Validators.required],
      grievanceType:['',Validators.required],
      grievanceStatus:['',Validators.required],
      fileName:['']
     });
  }

  ngOnInit(): void {
    this.formGroupModel = [this.dataUpload] 
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel)
    
    this.onPage();
    // this.subscriptions.push(processor ,grievanceType, grievanceStatus, naeDetails);
    this.store.select(recentLinkClickEventSelector).subscribe((x: cellClickEvent) => {
      this.dueDate = x.element.dueBy;
      this.assignForm.get('date').setValue(this.dueDate);
    })
   
  }


  async onPage(){
    
    const processor = await this.grievanceViewService.getProcessorsList('')
    //  .subscribe(data => {
    //   this.processorsList = data;
    //   this.processorFiltere();
    // },
    // error => {
    //   this.snackbarService.callSnackbaronError(error)
    // });

    const grievanceType = await this.grievanceViewService.getGrievanceActionsList('T')
    // .subscribe(data => {
    //   this.grievanceTypeList = data;
    //   this.grievanceTypeFiltere();
    // },
    // error => {
    //   this.snackbarService.callSnackbaronError(error)
    // });

    const grievanceStatus = await this.grievanceViewService.getGrievanceActionsList('S')
    // .subscribe(data => {
    //   this.grievanceStatusList = data;
    //   this.grievanceStatusFilter();
    // },
    // error => {
    //   this.snackbarService.callSnackbaronError(error)
    // });

    const naeDetails = await this.grievanceViewService.getNaeDetails(this.global.personEventId.personEventId,this.global.personEventId.personFormId).subscribe(data => {
      this.eventDetails = data;
    },
    error => {
      this.snackbarService.callSnackbaronError(error.message)
    });

    forkJoin([processor, grievanceType, grievanceStatus]).pipe(
      map(([processorList, grievanceTypeList, grievanceStatusList]) => {
        return { processorList, grievanceTypeList, grievanceStatusList };
      })
    ).subscribe(
      ({ processorList, grievanceTypeList, grievanceStatusList }) => {
        this.processorsList = processorList;
        this.processorFiltere();
        
        this.grievanceTypeList = grievanceTypeList;
        this.grievanceTypeFiltere();

        this.grievanceStatusList = grievanceStatusList;
        this.grievanceStatusFilter();

        if (processorList && grievanceTypeList && grievanceStatusList) {
          this.qacDetails(processorList ,grievanceTypeList , grievanceStatusList);
        }
      },
      (error) => {
        this.snackbarService.callSnackbaronError(error);
      }
    );
    
    
  }

  propagateChange = (_: any) => { };

  uploadFileEvt(files: FileList) {
   
  }

  getExtension(file:File) {
    return file.name.split('.').pop();
  }
  uploaddoc(event){
    if (event.$event.target.files) {
      var fileArray = Array.from(event.$event.target.files);
       fileArray.forEach((file: File) => {
        file.name.length <=50 ?this.uploaddocevent(file).subscribe((response: any) => {
          this.allowEdit = true
              this.fileDetails.push(file)
              this.global.FilesUpload.push(response.systemFileName)
              this.fileUrl.push(response.fileName)
          this.propagateChange(this.file.join(','));
          this.cdr.markForCheck();
        }, (err: any) => {
          console.error(JSON.stringify(err));
        }):this.snackbarService.callSnackbaronError(`FileName:${file.name} Max length in the Supporting Document Title must be 45 characters. Please rename the file before trying again`)
      })
    }
  }

  uploaddocevent(files){
    const file = files
    const formData = new FormData();
    formData.append("File", file);
    formData.append("SubFolderName", "QACGrievance");
    formData.append("type", "1");
   return this.https.post(`${environment.baseUrl}formmsvc/api/File/upload`, formData)
  }
  qacDetails(processorList :any, grievanceTypeList:any, grievanceStatusList:any) {
    this.grievanceViewService
      .getGrievanceEvent(this.global.personEventId.personFormId)
      .pipe(takeUntil(this.destroy$))  // To unsubscribe when the component is destroyed
      .subscribe(
        (data) => {
          if (data[0]) {
            this.grievanceDetail = data[0];

            // Ensure the lists are populated before proceeding
            const processor = processorList.find(
              (p) => p.emailId === this.grievanceDetail.assignToEmailId
            );
            const grievanceType = grievanceTypeList.find(
              (g) => g.id === this.grievanceDetail.grievanceType
            );
            const grievanceStatus = grievanceStatusList.find(
              (g) => g.id === this.grievanceDetail.grievanceStatus
            );


            // Only patch form values after the data is available
            if (processor && grievanceType && grievanceStatus) {
              this.assignForm.patchValue({
                processor: processor,
                grievanceDetails: this.grievanceDetail.notes,
                grievanceType: grievanceType,
                grievanceStatus: grievanceStatus
              });
            } else {
              console.error('Processor, Grievance Type, or Grievance Status not found.');
            }
            
            this.allowEdit = false;
            this.allowRescore = false;
            this.disableForm();
            this.cdr.detectChanges();  // Trigger change detection to reflect changes in the form
          } else {
            this.allowRescore = true;
            this.isEdit = false;
          }
        },
        (err) => {
          this.snackbarService.callSnackbaronError(err);
          this.assignForm.patchValue({
            processor: '',
            grievanceDetails: '',
            date: '',
            grievanceType: null,
            grievanceStatus: null
          });
        }
      );

      this.formGroupModel[0].value=[];
    }

  processorFiltere() {
    this.processorFiltered = this.assignForm.get('processor').valueChanges.pipe(
      startWith(''),
      map(value => {
        const emailId = typeof value === 'string' ? value : value?.emailId;
        return emailId ? this._filterProcessor(emailId as string) : this.processorsList.slice();
      }),
    );
  }

  grievanceTypeFiltere() {
    this.grievanceTypeFiltered = this.assignForm.get('grievanceType').valueChanges.pipe(
      startWith(''),
      map(value => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterGrievanceType(name as string) : this.grievanceTypeList.slice();
      }),
    );
  }

  grievanceStatusFilter() {
    this.grievanceStatusFiltered = this.assignForm.get('grievanceStatus').valueChanges.pipe(
      startWith(''),
      map(value => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterGrievanceStatus(name as string) : this.grievanceStatusList.slice();
      }),
    );
  }

  displayProcessor(processor: Processor): string {
    return processor && processor.emailId ? processor.emailId : '';
  }

  displayGrievanceType(grievanceType: GrievanceAction): string {
    return grievanceType && grievanceType.name ? grievanceType.name : '';
  }

  displayGrievanceStatus(grievanceType: GrievanceAction): string {
    return grievanceType && grievanceType.name ? grievanceType.name : '';
  }

  private _filterProcessor(emailId: string): Processor[] {
    const filterValue = emailId.toLowerCase();

    return this.processorsList.filter(option => option.emailId.toLowerCase().includes(filterValue));
  }

  private _filterGrievanceType(name: string): GrievanceAction[] {
    const filterValue = name.toLowerCase();

    return this.grievanceTypeList.filter(option => option.name.toLowerCase().includes(filterValue));
  }

  private _filterGrievanceStatus(name: string): GrievanceAction[] {
    const filterValue = name.toLowerCase();

    return this.grievanceStatusList.filter(option => option.name.toLowerCase().includes(filterValue));
  }

  createGrievanceEvent(){
    if(!this.assignForm.valid) return;

    const dialogNoshow = this.dialog.open(popupcomponent, {
      data: {
        title: `${this.global.personEventId.examName}`,
        message: this.lngSrvc.curLangObj.value.Edit_QAC,
        cancelButton: this.lngSrvc.curLangObj.value.cancel,
        OKButton: this.lngSrvc.curLangObj.value.cancel,
        OkButton: this.lngSrvc.curLangObj.value.save,
      },
    })
    dialogNoshow.afterClosed().subscribe((data: any) => {
      if(data.confirmed == true || data.confirmed.confirmed == true){
        const grievanceEvent:GrievanceEvent = {
          eventId: this.eventDetails?.eventId,
          eventDateUTC: this.eventDetails?.eventDateUTC,
          testCenterId: this.eventDetails?.testCenterCode,
          testCenterName: this.eventDetails?.testCenterName,
          evaluatorName: this.eventDetails?.responsibleForScore ? this.eventDetails?.responsibleForScore[0]?.name : null,
          evaluatorEmail: this.eventDetails?.responsibleForScore ? this.eventDetails?.responsibleForScore[0]?.email : null,
          contactNumber: this.eventDetails?.responsibleForScore ? this.eventDetails?.responsibleForScore[0]?.email : null,
          dueBy: moment(this.assignForm.get('date').value).format('YYYY-MM-DD'),
          notes: this.assignForm.get('grievanceDetails').value,
          personFormId: this.global.personEventId.personFormId,
          grievanceTypeId: this.assignForm.get('grievanceType').value.id,
          grievanceStatusId: this.assignForm.get('grievanceStatus').value.id,
          assignTo: this.assignForm.get('processor').value.personTenantRoleId,
          createdBy: this.global.userDetails.value.personTenantRoleId,
          fileName:this.fileUrl,
          fileUrl:this.global.FilesUpload
        }
    
        this.grievanceViewService.createGrievanceEvent(grievanceEvent).subscribe(data => {
          if(data.success) {
            this.global.FilesUpload =[]
            this.fileUrl =[]
            this.isEdit = true;
            this.grievanceViewService.getNaeDetails(this.global.personEventId.personEventId,this.global.personEventId.personFormId).subscribe(data => {
              this.eventDetails = data;
              this.fileDetails =[]
            },
            error => {
              this.snackbarService.callSnackbaronError(error.message)
              this.fileDetails =[]
              this.global.FilesUpload =[]
              this.fileUrl =[]
            });
            if(this.allowRescore){
              this.snackbarService.callSnackbaronSuccess(data.message);
              this.router.navigateByUrl('loader/manage-grievence');
            }
            else {
              this.snackbarService.callSnackbaronSuccess("Updated Successfully");
            }
            // this.qacDetails()
          }
        
        }, 
        err => {
          this.snackbarService.callSnackbaronError(err);
        });
        
        this.allowEdit = false;
        this.disableForm();
      }
    
    })


  }

  showresult(){
     this.dialog.open(ImgPopUpComponent,{
        data:{examName:this.global.personEventId.examName,id:this.global.personEventId.personEventId}
     })
  }

  allowFormEdit(allow:boolean){
    this.allowEdit = allow;
    if(allow) this.enableForm();
    else {
      this.disableForm();
      this.qacDetails(this.processorsList,this.grievanceTypeList,this.grievanceStatusList);
    }
  }

  disableForm() {
    this.assignForm.disable();
  }

  enableForm() {
    this.assignForm.enable();
  }

  viewList(){
    this.dialog.open(GrievanceQacListComponent);
  }

  triggerFileInput() {
    if (this.inputImage) {
      this.inputImage.nativeElement.click();
    }
  }

  uploadFile(event: any) {
    this.file = event.target.files[0];
    if(!this.file) return;
    const reader = new FileReader();

    reader.onloadend = () => {
      const base64Data = reader.result as string; // This is the Data URL (Base64-encoded)
      const base64Only = base64Data.split(',')[1]; // Extract only the Base64 part (ignore the prefix)
      this.imageBase64 = base64Only; 
      if(!this.imageBase64){
        this.snackbarService.callSnackbaronError('please reapload');
        this.inputImage.nativeElement.value = '';
      }

      const byteArray = new Uint8Array(
        atob(this.imageBase64)
          .split("")
          .map((char) => char.charCodeAt(0))
      );
      const file = new File([byteArray], `${this.file.name}`);
      const formData = new FormData();
      formData.append("File", file);
      formData.append("SubFolderName", "QACGrievance");
      formData.append("type", "1");
      this.http.postUploadImage(formData).subscribe(
        (data: any) => {
          this.qacFileName = data.fileName;
          this.fileUrl = data.systemFileName
          this.snackbarService.callSnackbaronSuccess('File Uploaded');
        },
        (err: HttpErrorResponse | any) => {
          this.snackbarService.callSnackbaronError(`${err.message.message.error}`);
        }
      );
    };

    reader.readAsDataURL(this.file); 
  }

  convertToBase64(file: File) {
    const reader = new FileReader();

    reader.onloadend = () => {
      const base64Data = reader.result as string; // This is the Data URL (Base64-encoded)
      const base64Only = base64Data.split(',')[1]; // Extract only the Base64 part (ignore the prefix)
      this.imageBase64 = base64Only; 
    };

    reader.readAsDataURL(file); // Read the image as Data URL (Base64)
  }

  removeFile(filename){
    if(this.allowEdit == false) return;
   this.fileDetails =  this.fileDetails.filter((x)=>x.name !=filename.name)
    this.file = null;
    this.qacFileName = null;
    this.inputImage.nativeElement.value = null;
  }

  viewFile(file:any){
    this.url = file;
    this.dialog.open(FileViewerComponent, {
      width: '600px',
      height:'650px',
      data:this.url
    });
  }

  viewSelectedFile(file: File) {
    if (file instanceof File) {
      // Check if the file is of type image or pdf based on its MIME type
      let img = file.type.includes('image');
      let pdf = file.type.includes('pdf');
  
      // Create a new FileReader
      const reader = new FileReader();
  
      // Set up the onload event for the FileReader
      reader.onload = () => {
        const url = reader.result; // Get the URL of the file (data URL for images, etc.)
        const obj = {
          url: url,
          img: img,
          pdf: pdf,
          fileName: file.name
        };
  
        // Open a dialog to display the file (ensure FileViewerComponent exists and works)
        this.dialog.open(FileViewerComponent, {
          width: '600px',
          height: '650px',
          data: obj
        });
      };
  
      // Read the file as Data URL (this works with both images and PDFs)
      reader.readAsDataURL(file);
    } else {
      console.error('The provided file is not a valid File object.');
    }
  }
  

  ngOnDestroy(){
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }


  

}
