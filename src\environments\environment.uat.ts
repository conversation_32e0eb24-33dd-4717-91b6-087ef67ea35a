export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://credentiapreprodapi.examroom.ai/",
  reportUrl:"https://reports.examroom.ai/",
  reportUrlapi:"https://reportapiv2.examroom.ai/",
  redirectUrl: "https://credentiapreprod.examroom.ai/",
  gistUrl: " https://gisuat.examroom.ai/validate-gisaccess",
  URLNotAllowedToken: "https://gisapi_uat.examroom.ai/api/Get_GISAccessToken",
  uplaodApi: "https://aiapi.examroom.ai/api/",
  ScanUrl: "https://erlens.examroom.ai/",
  NAERiredectorErrupt: "https://testapi.examroom.ai/",
  RedirecttoSkillsExams:'https://skillsreviewqa.examroom.ai/',
  SkillsAccessKey :'CNA.QATester',
  SkillsSecrectKey:"8cu75",
  GisApiUrl:'https://gisapiuat.examroom.ai/api/',
  apiUrl: "https://webrtcuatapi.examroom.ai/",
  streamApiKey: "47095304",

  PayPalConfigurationId:
    "AURSiJyrCRnfnU6pVeuu4IQhELaQVOTGC5JALwdCYx2AllFyOB1inK36PY8QY7rxfAiDUuKXtbJHxi3v",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "brucewayne",
      credential: "1234567890",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};

