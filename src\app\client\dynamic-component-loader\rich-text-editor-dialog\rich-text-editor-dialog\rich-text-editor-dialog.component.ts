import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from "src/environments/environment";
import { GlobalUserService, userData } from "src/app/core/global-user.service";
import { tap, take } from "rxjs/operators";
import jsPDF from "jspdf";
import domtoimage from 'dom-to-image';
//Angular Material
import { MatDialog, MatDialogConfig, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

import { DynamicFileUploadModel, DynamicFormHook, DynamicFormModel, DynamicFormService } from "@ng-dynamic-forms/core";

// Services
import { SnackbarService } from 'src/app/core/snackbar.service';
import { PersonFormNoteTypeEnum } from 'src/app/core/common-component/form-view/form-view.component';

@Component({
  selector: 'app-rich-text-editor-dialog',
  templateUrl: './rich-text-editor-dialog.component.html',
  styleUrls: ['./rich-text-editor-dialog.component.scss']
})
export class RichTextEditorDialogComponent implements OnInit {

  editorContent: string = "";
  @ViewChild('fileInput') fileInput;
  selectedFile: File | null = null;
  uploadedFileResponse: any = {};
  formGroupUpload; 
  formGroupModel;
  uploadedFiles;
  isResponseEditable: boolean = true;
  dataUpload = new DynamicFileUploadModel({
    required: false,
    id:'1',
    name:'2',
    label: 'Upload Supporting Documents',
    accept: ['.pdf', '.doc', '.docx','.jpg','.png'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList:true,
    additional: {
      appearance: 'outline'
    }
  })

  // Quill editor configuration (toolbar options)
  quillConfig = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],  // Text styling
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],  // Lists
      [{ 'align': [] }],  // Alignment options
      // ['link', 'image'],  // Add link and image
      ['clean']  // Clear formatting button
    ]
  };
  
  constructor(
    private http: HttpClient,
    private dialog: MatDialog,
    public globalUserService: GlobalUserService,
    private dynamicFormService: DynamicFormService,
    private snackbar: SnackbarService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<RichTextEditorDialogComponent>,
  ) { }

  ngOnInit(): void {
    this.isResponseEditable = !['Approved', 'Rejected'].includes(this.globalUserService?.personEventId?.statusName);
    this.getNote();
    this.formGroupModel = [this.dataUpload];
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel);
  }

  getNote() {
    var fetchUrl = environment.baseUrl + `client/api/form/GetGrievanceNotes?noteTypeId=${PersonFormNoteTypeEnum.GrievanceResponse}&candidateId=${this.globalUserService.personId}&personFormId=${this.globalUserService.personEventId.personFormId}`;
    this.http.get(fetchUrl).pipe(
      tap((data: any) => {
        this.editorContent = data?.body;
        this.uploadedFiles = data?.files;
      }),
      take(1)
    ).subscribe();
  }

  toPdf() {
    const dashboard = document.getElementsByClassName("ql-editor")[0];
    const dashboardHeight = dashboard.clientHeight;
    const dashboardWidth = dashboard.clientWidth;
    const options = {
      background: 'white', width: dashboardWidth, height: dashboardHeight, allowTaint: true,
      useCORS: true
    };

    domtoimage.toPng(dashboard, options).then((imgData) => {
      const doc = new jsPDF(dashboardWidth > dashboardHeight ? 'l' : 'p', 'pc', [dashboardWidth, dashboardHeight], false);
      doc.internal.scaleFactor = 1;
      const imgProps = doc.getImageProperties(imgData);
      const pdfWidth = doc.internal.pageSize.getWidth() * 0.3;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      doc.addImage(imgData, 'jpeg', 0, 0, pdfWidth, pdfHeight, "NONE",);
      doc.save('Notes Summary.pdf');
    });
  }

// Custom upload files
  // // Handle file selection
  // onFileSelected(event: Event): void {
  //   const input = event.target as HTMLInputElement;
  //   if (input && input.files) {
  //     this.selectedFile = input.files[0];
  //     this.uploadFile();
  //   } else {
  //     this.snackBar.open('No file selected', 'Close', {
  //       duration: 2000,
  //     });
  //   }
  // }

  // uploadFile() {
  //   const file: File = this.selectedFile;
  //   const formData = new FormData();
  //   formData.append('File', file);
  //   // simply removing commas so that later while parsing 
  //   // the response the.split is possible
  //   formData.append('SubFolderName', file.name.split(',').join(''));
  //   var url = environment.baseUrl + `formmsvc/api/File/upload`;
  //   return this.http.post(url, formData).pipe(
  //     take(1),
  //     tap((res) => {
  //       this.uploadedFileResponse = res;
  //       this.snackBar.open('File uploaded successfully', 'Close', {
  //         duration: 2000,
  //       });
  //     }, (error) => {
  //       this.snackBar.open('File upload failed', 'Close', {
  //         duration: 2000,
  //       });
  //     }
  //     )
  //   ).subscribe();
  // }

  removeFile(fileDetails) {
    const index = this.uploadedFiles.findIndex((file) => {
      return file.id === fileDetails.id;
    });

    if (index > -1) {
      this.uploadedFiles.splice(index, 1);
    }
  }

  getUploadedFileDetails() {
    const uploadedFilesDetails = this.formGroupModel[0]._value? this.formGroupModel[0]._value.split(',') : [];
    const mappedUploadedFilesDetails = uploadedFilesDetails && uploadedFilesDetails.length ?                     
    uploadedFilesDetails.map((file:string)=>{                    
      return {
        id: 0,
        name: file.split('|')[0],
        filePath: file.split('|')[1],
        mime: "",
        personFormNoteId: 0,
        deleted: false
      }
    }) : [];
    return mappedUploadedFilesDetails;
  }

  onSave() {
  const uploadedFileDetails = this.getUploadedFileDetails();
  let filesPayload = uploadedFileDetails;
  if(this.uploadedFiles && this.uploadedFiles.length) {
    filesPayload = uploadedFileDetails.concat(this.uploadedFiles);
  }

  const payload = {
          id: 0,
          title: "",
          body: this.editorContent,
          personFormId: this.globalUserService.personEventId.personFormId,
          noteTypeid: PersonFormNoteTypeEnum.GrievanceResponse,
          candidateId: this.globalUserService.personId,
          files: filesPayload,
          userId: this.globalUserService.userDetails.value.personTenantRoleId,
          userName: this.globalUserService.userDetails.value.userName
        }
      var httpObservable = this.http.post<any>(`${environment.baseUrl}client/api/form/AddGrievanceNote`, payload);
      httpObservable.pipe(
        take(1),
        tap((res) => {
          if (res) {
            this.getNote();
            this.dialogRef.close();
            this.snackbar.callSnackbaronSuccess('Grievance response saved successfully!');
          }
        }, (error) => {
          this.snackbar.callSnackbaronError('Grievance response not saved successfully!');
        }
      )
    ).subscribe();
  }
}
