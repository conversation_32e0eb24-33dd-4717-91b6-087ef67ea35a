import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import {
  Component,
  ElementRef,
  Inject,
  Input,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { URL } from "src/app/core/url";
import { GlobalUserService } from "src/app/core/global-user.service";
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from "@angular/material/dialog";
import { HttpService } from "src/app/core/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { LanguageService } from "src/app/core/language.service";
import { DatePipe } from "@angular/common";
import { environment } from "src/environments/environment";
import * as FS from 'file-saver'
import moment from "moment";
import Quagga from '@ericblade/quagga2';
import { NgZone } from '@angular/core';
import { CdkTextareaAutosize } from "@angular/cdk/text-field";
import { take } from "rxjs/operators";
import { UpdateAbsentDetails } from "src/app/client/state/shared/shared.actions";
import { Store } from "@ngrx/store";
import { updateAbsent } from "src/app/client/state/shared/shared.selectors";
import { Allonlyskills, ShowOnboardforParticularstate } from "../../examroom-formbuilder/form-builder.types";

@Component({
  selector: "app-payment-refund-popup",
  templateUrl: "./payment-refund-popup.component.html",
  styleUrls: ["./payment-refund-popup.component.scss"],
})
export class PaymentRefundPopupComponent implements OnInit {
  TextDetails: string
   PersonEventId:string
  ispayref = []
  addNewNote:FormGroup
  index: number
  DeatilsPayment: boolean = false
  radioselect = new FormControl("Export from Grid");
  personTenantRoleId: number;
  range: FormGroup
  isRefund: boolean
  IncicidentList:IncideintResponse
  isPayment: boolean
  matchBarcode:boolean = true
  Incidient:Array<IncideintResponse>=[]
  Id:string
  Date:string
  myForm = this.fb.group({
    refundAmount: [""],
    description: [""],
  });
  scanner:FormGroup
  datevalue:FormGroup
  minDate = new Date();
  DownloadOptions: Array<any> = [{ id: 1, name: "Export from Grid", checked: false }, { id: 2, name: "Create your Logic", checked: false }]
  examSubmitOptions: Array<any> = [{ id: 1, name: "Payment List", checked: false }, { id: 2, name: "Refund List", checked: false }]
  @ViewChild('barcodeScannerVideo') barcodeScannerVideo: ElementRef;
  @ViewChild('snapshotCanvas') snapshotCanvas: ElementRef;
  scanForm: FormGroup;
  scanValue: string = '';
  scanResults: string[] = [];
  isSendButtonDisabled: boolean = true;
  @ViewChild('autosize') autosize: CdkTextareaAutosize;
  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    public globalUserService: GlobalUserService,
    private services: HttpService,
    private snackbar: SnackbarService,
    public global: LanguageService,
private store:Store,
    private dialogRef: MatDialogRef<PaymentRefundPopupComponent>,
    public lngSrvc: LanguageService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,
    private ngZone:NgZone

  ) {
    this.globalUserService.personEventID = this.data.personEventID;
    this.TextDetails = this.data?.message === "RefundPaymentDownlaod" ? "Download Option" : this.globalUserService.userDetails.getValue().roleId==22 || this.globalUserService.userDetails.getValue().roleId==4 || this.globalUserService.userDetails.getValue().roleId==15 || this.globalUserService.userDetails.getValue().roleId==17 ? 
    (this.data.id == 3 && data.Confirm?.personScoreSuccesfully == true ?'Scan Booklet Id before Start Shedding.' : this.data.id == 3 && data.Confirm?.personScoreSuccesfully == false ?'Scan Booklet Id before Uploading Answer Sheet.':this.data.id == 4?'Update Application Elapsed Date ':data.id == 6?'Reason for marking an issue':data.id ==80?'Reason for refund the exam.':data.id == 1?"Refund Transaction Details":'Reason for canceling the assigned booklet Id.'):"Refund Transaction Details"
    this.Id = this.data?.event?.order !=undefined && this.data?.event?.order?.orderId !=undefined?"Order Id":"Transaction Id"
    this.Date = this.data?.event?.order !=undefined ?"Order Date":"Transaction Date"
  }

  ngOnInit() {
    this.myForm = this.fb.group({
      CandidateName: ["", [Validators.required]],
      TransactionId: ["", [Validators.required]],
      paidAmount: ["", [Validators.required]],
      TransactionDate: ["", [Validators.required]],
      Email: ["", [Validators.required]],
      refundAmount: ["", [Validators.required]],
      description: ["", [Validators.required]],
      myCheckbox: new FormControl(true),
    });

    this.range = new FormGroup({
      start: new FormControl("", [Validators.required]),
      end: new FormControl("", [Validators.required]),
    });


    this.addNewNote = new FormGroup({
      // title: new FormControl("", [Validators.required]),
      body: new FormControl("", [Validators.required]),
      Incident:new FormControl("", [Validators.required])
    });

    this.datevalue = new FormGroup({
      // title: new FormControl("", [Validators.required]),
      variable:new FormControl(this.data.elapseDateTime, [Validators.required])
    });
    
    this.loadScanner();
    this.getIncidientIssue();
   
    this.data.id == 3?this. initBarcodeScanner():null
    this.data.id == 3?this.ngZone.onStable.pipe(take(1)).subscribe(() => this.autosize.resizeToFitContent(true)):null



    this.globalUserService.userDetails.subscribe((user) => {
      if (user) {
        this.personTenantRoleId = user.personTenantRoleId;
        this.options(1)
      }
    });

    var datePipe = new DatePipe("en-US");
    (this.data?.event?.order !=undefined)?
    this.http
    .get<any>(
      URL.BASE_URL +
      `payment/paypal/orders/${this.data.event.order.orderId}/mini`
    )
    .subscribe((response: any) => {
      this.data = response;
      this.myForm.patchValue({
        TransactionId: this.data.orderId,
        TransactionDate: datePipe.transform(this.data.orderDate, 'yyyy-MM-dd'),
        Email: this.data.emailId,
        paidAmount: this.data.paidAmount,
        CandidateName: `${(this.data.firstName != null && this.data.firstName != undefined && this.data.firstName != "") ? this.data.firstName : ""}` + ' ' + `${(this.data.middleName != null && this.data.middleName != undefined && this.data.middleName != '') ? this.data.middleName : ""}` + ' ' + `${(this.data.lastName != null && this.data.lastName != undefined && this.data.lastName != "") ? this.data.lastName : ''}`,
        refundAmount: this.data.paidAmount,
      });
      this.getValues();
    },(err:HttpErrorResponse | any)=>{
      this.snackbar.callSnackbaronError(`${err.message.message.error}`)
  })
    :
    this.http
      .get<any>(
        URL.BASE_URL_PAYMENT +
        `transactions/${this.data.event.transaction.transId}/mini`
      )
      .subscribe((response: any) => {
        this.data = response;
        this.myForm.patchValue({
          TransactionId: this.data.transId,
          TransactionDate: datePipe.transform(this.data.transactionDateUtc, 'yyyy-MM-dd'),
          Email: this.data.emailId,
          paidAmount: this.data.paidAmount,
          CandidateName: `${(this.data.firstName != null && this.data.firstName != undefined && this.data.firstName != "") ? this.data.firstName : ""}` + ' ' + `${(this.data.middleName != null && this.data.middleName != undefined && this.data.middleName != '') ? this.data.middleName : ""}` + ' ' + `${(this.data.lastName != null && this.data.lastName != undefined && this.data.lastName != "") ? this.data.lastName : ''}`,
          refundAmount: this.data.paidAmount,
        });
        this.getValues();
      },(err:HttpErrorResponse | any)=>{
        this.snackbar.callSnackbaronError(`${err.message.message.error}`)
    });


    this.store.select(updateAbsent).subscribe((data) => {
      if (data) {
        this.snackbar.callSnackbaronSuccess("Updated SucessFully");
      }
    });
  }


  loadScanner(){
    this.scanner = new FormGroup({
      scanner_code:new FormControl("",[Validators.required])
    })
  }

  getValues() {
    if (this.data != null && this.data != '' && this.data != undefined) {
      this.myForm.controls['CandidateName'].disable();
      this.myForm.controls['TransactionId'].disable();
      this.myForm.controls['paidAmount'].disable();
      this.myForm.controls['TransactionDate'].disable();
      this.myForm.controls['Email'].disable();
    }
  }

  ///// barcode  ///
  initBarcodeScanner() {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment" // Use back camera
        }
      })
      .then((stream) => {
        const video: HTMLVideoElement = this.barcodeScannerVideo.nativeElement;
        video.srcObject = stream;
        video.play();
      })
      .catch((err) => {
        this.snackbar.callSnackbaronError(`${err.message}`)
        console.error("Error accessing the camera: ", err);
      });
    } else {
      console.error("getUserMedia not supported on your browser");
    }
  }


  getIncidientIssue() {
    let incidentType = 'W';
    if (this.data.eventStatusId == "8") {
      incidentType = 'A';
    } else if (
      (this.data.bookletId == null && this.data.password == null && !Allonlyskills.includes(this.data.examName) && this.data.onboard === false) ||
      (this.data.bookletId == null && this.data.password != null && !Allonlyskills.includes(this.data.examName) && this.data.onboard === false) ||
      (this.data.bookletId != null && this.data.password == null && !Allonlyskills.includes(this.data.examName) && this.data.onboard === false && ShowOnboardforParticularstate.includes(this.data.state))
    ) {
      if (this.data.eventStatusId != "8") {
        incidentType = 'B';
      }
    } else if (this.data.bookletId == null && this.data.password == null && Allonlyskills.includes(this.data.examName) && this.data.eventStatusId != "8") {
      incidentType = 'B,C';
    }
    this.http.get(`${environment.baseUrl}client/api/client/GetIncidentsList?IncidentType=${incidentType}`)
      .subscribe((data: IncideintResponse[]) => {
        if (data.length > 0) {
          this.Incidient = data;
        }
      });
  } 

  IncidentType(Incident:IncideintResponse){
      this.IncicidentList =Incident
  }

  captureSnapshot() {
    const video: HTMLVideoElement = this.barcodeScannerVideo.nativeElement;
    const canvas: HTMLCanvasElement = this.snapshotCanvas.nativeElement;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    Quagga?.decodeSingle({
      src: canvas.toDataURL("image/png"), // Convert snapshot to data URL
      numOfWorkers: 0, // Disable Web Workers to prevent CORS issues
      decoder: {
        readers: ["code_128_reader"] // Specify barcode type to read
      },
      locate: true // Try to locate the barcode
      
    }, (result) => {
      if (result && result.codeResult) {
        this.ngZone.run(() => {
          this.scanValue = result.codeResult.code;
          this.scanner.get('scanner_code').setValue(this.scanValue);
          // this.loadScanner();
          if(this.data.Confirm.bookletId == result.codeResult.code){
            this.closeBarcode();
            this.onScanResultChange();
            this.data?.name =='Answer'?this.dialogRef.close({confirmed:true}):null
            // this.displayResult(result.codeResult.code);
          }
          else{
            this.matchBarcode = this.data.Confirm.bookletId == this.scanner.value.scanner_code ?false:true;
            this.scanner.get('scanner_code').setErrors({ 'customError': true });
          }
        })
      }
      else{
        this.matchBarcode = this.data.Confirm.bookletId == this.scanner.value.scanner_code ?false:true;
        this.data.Confirm.bookletId == this.scanner.value.scanner_code? this.closeBarcode():null;
        this.data?.name =='Answer' && this.data.bookletId == this.scanner.value.scanner_code?this.dialogRef.close({confirmed:true}):null
        this.scanner.get('scanner_code').setErrors({ 'customError': true });
      }

    if(!this.matchBarcode){
      this.scanner.get('scanner_code').setErrors({ 'customError': true });
    }
    
    });
  }

  displayResult(barcode: string) {
    const resultDiv: HTMLElement = document.getElementById('result');
    resultDiv.innerHTML = "Detected Barcode: " + barcode;
  }

  onScanResultChange() {
    this.isSendButtonDisabled = !this.scanValue || this.scanValue.trim() === '';
    this.matchBarcode =  this.data.bookletId === this.scanValue?false:true;
   
  }

  closeBarcode(){
    const video: HTMLVideoElement = this.barcodeScannerVideo.nativeElement;
    const stream = video.srcObject as MediaStream;

    if (stream) {
      const tracks = stream.getTracks();

      tracks.forEach(track => {
        track.stop();
      });

      video.srcObject = null;
    }
  }


 public onValueChange(event: Event): void {
  const value = (event.target as any).value;
//  this.matchBarcode = this.data.bookletId == value?false:true
// this.matchBarcode = this.data.bookletId == value?false:true;
// alert('onValueChange() matchBarcode');\

 this.PersonEventId = this.data.personEventId;
 this.globalUserService.personEventID = this.data.personEventId
 this.services.personEventIDSubject$.next(this.data.personEventId);
}


  closeApp() {
    this.dialogRef.close({
      confirmed: false,
    });
    // alert('closeApp() ');
   
  }
  close(){
    this.closeBarcode();

  }


  options(n) {
    this.DeatilsPayment = n == 2 ? true : false
    this.index = n
  }

  showOptions($event, n) {
    if (n.id == 1) {
      this.isPayment = $event.checked
    } else {
      this.isRefund = $event.checked
    }
  }

  refund() {
    this.dialogRef.close({
      confirmed: true,
      description: this.myForm.value.description as string,
      personTenantRoleId: this.personTenantRoleId as number,
      transAmount: this.myForm.value.refundAmount as number,
      title:this.Id,
      captureId:this.data.captureId
    });
  }



  DownLoadRefundorPayment() {
    let startDate = this.range.value.start != "" ? moment(this.range.value.start).format('YYYY-MM-DD') : ""
    let endDate = this.range.value.end != "" ? moment(this.range.value.end).format('YYYY-MM-DD') : ""
    let url = this.data.tabselected === "Paypal transaction details"? "client/api/payment/paypal/orders/" : "client/api/payment/authorizenet/transactions/"
    this.index == 1 ? this.http.get(environment.baseUrl + url + `excel-download?startDate=${startDate}&endDate=${endDate}&pageNo=${this.data.pageNo}&pageSize=${this.data.pageSize} `, { observe: 'body', responseType: 'blob' as 'json' }).subscribe((data: any) => {
      var blobResponse = data as Blob;
      FS.saveAs(blobResponse, `Candidate-Payment_Page${this.data.pageNo}_Size${this.data.pageSize}.xlsx`);
    }) : this.http.get(environment.baseUrl + url + `excel-download-all?startDate=${startDate}&endDate=${endDate}&isPayment=${this.isPayment != undefined ? this.isPayment : ""}&isRefund=${this.isRefund != undefined ? this.isRefund : ''} `, { observe: 'body', responseType: 'blob' as 'json' }).subscribe((data: any) => {
      var blobResponse = data as Blob;
      let CandidateStatus = this.isPayment != undefined && this.isRefund == undefined  ? (this.data.tabselected === "Paypal transaction details"?"Candidate_OrderList_Page":"Candidate_TranscationList_Page"):this.isRefund != undefined && this.isPayment == undefined  ? "Candidate_Refund_Page" : this.isRefund != undefined && this.isPayment != undefined ?'Candidate_TranscationList_Refund_Page':""
      FS.saveAs(blobResponse, `${CandidateStatus}${this.data.pageNo}_Size${this.data.pageSize}.xlsx`);
    })

    this.dialogRef.close()
  }

  SubmitBookletAssign() {
    let body = {
      oldBookletNumber: this.data.bookletId,
      newBookletNumber: "",
      notes: this.addNewNote.value.body,
      emailId: this.globalUserService.email
    };
    switch (this.data.id) {
      case 4:
        this.dialogRef.close({
          confirmed: true,
          elapsedDate: this.datevalue.value.variable,
          notes: this.addNewNote.value.body,
        });
        break;

      case 6:
        if(this.data.eventStatusId){
          this.http.post(`${environment.baseUrl}client/api/arkiv/Mark-Issue-After-Exam`, {
            personEventId: Number(this.data.personEventId),
            incidentTypeId: this.IncicidentList.incidentId,
            incidentComments: this.addNewNote.value.body,
            incidentDescription: this.addNewNote.value.body,
            incidentStatus: 1,
            examType: 3,
            createdBy: this.personTenantRoleId,
            updatedBy: this.personTenantRoleId
          }).subscribe( 
            (data: any) => {
              this.snackbar.callSnackbaronSuccess(`${data.message}`);
              this.dialogRef.close({ clientEventId: this.data.clienteventid });
  
              setTimeout(() => {
                const scanURL = sessionStorage.getItem('scanCurrentURL');
                if (scanURL) window.location.href = scanURL;
              }, 5000);
            },
            (err: HttpErrorResponse | any) => {
              this.snackbar.callSnackbaronError(err?.error?.message || JSON.stringify(err));
            }
          );
          break;
        } else {
          this.http.post(`${environment.baseUrl}client/api/arkiv/absent`, {
            candidateId: Number(this.data.candidateId),
            personEventId: this.data.personEventId,
            testCenterId: this.data.testCenterId,
            naeUserId: this.data.naeUserId,
            IncidentId: this.IncicidentList.incidentId,
            IncidentName: this.IncicidentList.incidentName,
            IncidentType: this.IncicidentList.incidentType,
            IncidentTypeName: this.IncicidentList.incidentTypeName,
            IncidentComments: this.addNewNote.value.body,
            IncidentDescription: this.addNewNote.value.body,
            IncidentStatus: 1,
            examType: 3,
          }).subscribe(
            (data: any) => {
              this.snackbar.callSnackbaronSuccess(`${data.message}`);
              this.dialogRef.close({ clientEventId: this.data.clienteventid });
  
              setTimeout(() => {
                const scanURL = sessionStorage.getItem('scanCurrentURL');
                if (scanURL) window.location.href = scanURL;
              }, 5000);
            },
            (err: HttpErrorResponse | any) => {
              this.snackbar.callSnackbaronError(err?.error?.message || JSON.stringify(err));
            }
          );
          break;
        }

      case 80:
        this.http.post(`${environment.baseUrl}client/api/payment/cancel-refund-exam`, {
          personEventId: this.data.personEventId,
          personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
          notes: this.addNewNote.value.body
        }).subscribe(
          () => {
            this.snackbar.callSnackbaronSuccess('Refunded the exam successfully');
            this.dialogRef.close({ confirmed: true });
          },
          (err: HttpErrorResponse | any) => {
            this.dialogRef.close({ confirmed: false });
            this.snackbar.callSnackbaronError(err?.error?.message || JSON.stringify(err));
          }
        );
        break;

      default:
        this.services.CancelBookletId(body).subscribe(
          () => {
            let updateBody = {
              candidateId: this.data.candidateId,
              testCenterId: this.data.testCenterId,
              personEventId: this.data.personEventId,
              naeUserId: this.data.naeUserId,
            };

            this.services.UpdateBookletNumber(updateBody).subscribe(
              () => {
                this.dialogRef.close({ clientEventId: this.data.clienteventid });
              },
              (err: HttpErrorResponse | any) => {
                this.dialogRef.close();
                this.snackbar.callSnackbaronError(err?.error?.message || JSON.stringify(err));
              }
            );
          },
          (err: HttpErrorResponse | any) => {
            this.dialogRef.close();
            this.snackbar.callSnackbaronError(err?.error?.message || JSON.stringify(err));
          }
        );
    }
  }

}

export interface IncideintResponse{
    incidentId: number,
    incidentName: string,
    incidentType: string,
    incidentTypeName: string,
    isActive: boolean
  
}

