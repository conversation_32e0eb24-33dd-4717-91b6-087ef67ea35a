import { HttpClient } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { button, customPopup, submitEvent } from '../pop-up.types';
import { forkJoin } from 'rxjs';
import { URL } from 'src/app/core/url';
import { requestDetails, requestParam, Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { MatDialogRef } from '@angular/material/dialog';
import lodash from 'lodash';
import { HttpService } from 'src/app/core/http.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { Action, Store } from '@ngrx/store';
import { creationSuccessfull } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'exai-custom-popup-for-manage-users',
  templateUrl: './custom-popup-for-manage-users.component.html',
  styleUrls: ['./custom-popup-for-manage-users.component.scss']
})
export class CustomPopupForManageUsersComponent implements OnInit, customPopup {

  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> = new EventEmitter<submitEvent>();

  Array = Array;
  Roles = Roles
  userData:any;
  form: FormGroup;
  showAddress:boolean = false
  rolesId:boolean = false
  userRoles: Array<any> = [];
  isManageApplications;
  statesOrTrainingIns: Array<any> = [];
  CodeDetails:{
    tenantCode:string,testCenterId:number,testcenterGuid:string,testcenterName:string

  }
  testCenterId: Array<any> = [];
  stateActive: boolean = false;
  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private globalUserService: GlobalUserService,private https:HttpService,private services:SnackbarService,private store:Store) { }
  ngOnInit(): void {
    this.globalUserService.userDetails.subscribe(data=>{
      if(data){
      this.userData=data;
      if(this.userData.roleId==15||this.userData.roleId==14){
        this.form = this.formBuilder.group({
          id: [this.preResponse ? this.preResponse.personId : 0, [Validators.required]],
          email: [this.preResponse ? this.preResponse.emailId : '', [Validators.required, Validators.email,Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')]],
          firstName: [this.preResponse ? this.preResponse.firstName : '', [Validators.required]],
          middleName: [this.preResponse ? this.preResponse.middleName : ''],
          lastName: [this.preResponse ? this.preResponse.lastName : '', [Validators.required]],
         
          phoneNumber: [this.preResponse ? this.preResponse.contactNumber : '', [Validators.required]],
          role: [this.preResponse ? this.preResponse.listPersonRoleId : '', [Validators.required]],
          tenantId: [this.preResponse ? this.preResponse.tenantId : ''],
          gender: [''],
          address:[''],
          ClientStateCode: [this.userData.stateCode],
          permissionDetail:[this.preResponse ? this.preResponse.permissionDetail : false],

        });
      }
      else{
        this.form = this.formBuilder.group({
          id: [this.preResponse ? this.preResponse.personId : 0, [Validators.required]],
          email: [this.preResponse ? this.preResponse.emailId : '', [Validators.required, Validators.email,Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$')],],
          firstName: [this.preResponse ? this.preResponse.firstName : '', [Validators.required]],
          middleName: [this.preResponse ? this.preResponse.middleName : ''],
          lastName: [this.preResponse ? this.preResponse.lastName : '', [Validators.required]],
          phoneNumber: [this.preResponse ? this.preResponse.contactNumber : '', [Validators.required]],
          role: [this.preResponse ? this.preResponse.listPersonRoleId : '', [Validators.required]],
          city: [this.preResponse ? this.preResponse.city : '', [Validators.required]],
          zipcode: [this.preResponse ? this.preResponse.zipcode : '', [Validators.required]],
          tenantId: [this.preResponse ? this.preResponse.tenantId : ''],
          testCenterId: [this.preResponse ? this.preResponse.testCenterId : ''],
          gender: [''],
          ClientStateCode: [this.userData.stateCode],
          permissionDetail:[this.preResponse ? this.preResponse.permissionDetail : false],
          address:[this.preResponse ? this.preResponse.address : '', [Validators.required]],
          License:[this.preResponse ? this.preResponse.License : '', [Validators.required]],
          cantest:[this.preResponse ? this.preResponse.cantest : '', [Validators.requiredTrue]],
        });
      }
        setTimeout(() => {
          const selectedRoles: number[] = this.form.value.role || [];
          if (selectedRoles.includes(14)) {
            this.stateActive = true;
          }
          if (selectedRoles.includes(22) || selectedRoles.includes(4)) {
            this.showAddress = true;
            this.rolesId = selectedRoles.includes(4);
          }
        }, 1000); 
    }
    })
    
    // this.form = this.formBuilder.group({
    //   id: [this.preResponse ? this.preResponse.personId : 0, [Validators.required]],
    //   email: [this.preResponse ? this.preResponse.emailId : '', [Validators.required, Validators.email]],
    //   firstName: [this.preResponse ? this.preResponse.firstName : '', [Validators.required]],
    //   middleName: [this.preResponse ? this.preResponse.middleName : ''],
    //   lastName: [this.preResponse ? this.preResponse.lastName : '', [Validators.required]],
    //   phoneNumber: [this.preResponse ? this.preResponse.contactNumber : '', [Validators.required]],
    //   role: [this.preResponse ? this.preResponse.listPersonRoleId : '', [Validators.required]],
    //     tenantId: [this.preResponse ? this.preResponse.tenantId : ''],
    //   gender: [''],
    //   address:['']
    // });
    forkJoin([this.http.get<any>(URL.BASE_URL + `Account/manageuserroles?personTenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`), this.http.get<any>(URL.BASE_URL + `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`),
    ]).subscribe((responses: Array<any>) => {
      this.userRoles = responses[0];
      if(this.userRoles.length == 1){
        this.form.get('role')?.setValue([this.userRoles[0].id]);
        if(this.userRoles.find(r => r.id == 15)){
          this.stateActive=true;
        }else if(this.userRoles.find(r => (r.id == 22 || r.id ==4))){
          this.showAddress = true
          this.rolesId = this.userRoles.find((x)=>{ return x.id == 4})
        }
      }
      this.statesOrTrainingIns = responses[1];
      if(this.statesOrTrainingIns.length == 1){
        this.form.get('tenantId').setValue(this.statesOrTrainingIns[0].id);
        if(this.statesOrTrainingIns.find(r => r.id == 15)){
          this.stateActive=true;
        }
      }
    });
    if (this.preResponse)
      this.fetchStateORTrnIns(this.form.value.role)
    this.form.controls.role.valueChanges.subscribe((x: number) => {
      this.fetchStateORTrnIns(x);
    })
    if(this.preResponse && this.preResponse?.roleName=="OperationStaff"||this.preResponse?.roleName=="SupportingStaff"||this.preResponse?.roleName=="SuperAdmin"){
      this.stateActive =false;
      this.form.value.tenantId =this.globalUserService.userDetails.value.clientORtenantId
    }
    else
    this.stateActive =this.preResponse.listPersonRoleId[0] == 4? true : false;
    this.rolesId = this.preResponse.listPersonRoleId[0] == 4 ? true : false
    this.preResponse.tenantId? this.state({ value: this.preResponse.tenantId }) : null
    this.preResponse.listPersonRoleId != null ? this.selectedRole({ value: this.preResponse.listPersonRoleId }) : null
    this.form.get('tenantId').setValue(this.preResponse.tenantId);
    if(this.form.value.email.length > 0){
      this.form.controls.email.disable();
    }
    if(this.preResponse){
      this.http
      .get(URL.BASE_URL_SHORTER + `login/api/Access/list?personTenantRoleId=${this.preResponse.personTenantRoleId}&consoleId=4`)
      .subscribe((res) => {
        if (res) {
          this.isManageApplications=res;
          if(this.isManageApplications && this.isManageApplications.manageApplication==true){
            this.form.get('permissionDetail').setValue(true);
          }
        }
      }); 
    }
  }
  fetchStateORTrnIns(x:number = Roles.TrainingInstitue) {
    // if (x == Roles.TrainingInstitue || (Array.isArray(x) && x.includes(Roles.TrainingInstitue)))
    //   this.http.get<any>(URL.BASE_URL + `traininginstitute/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}`).subscribe((response: any) => {
    //     this.statesOrTrainingIns = response;
    //   })
    // else
      this.http.get<any>(URL.BASE_URL + `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`).subscribe((response: any) => {
        this.statesOrTrainingIns = response;
      })
  }
  
  submit(requestDetails: requestDetails, index: number) {
    if(this.preResponse == null){
      requestDetails?.requestParams?.forEach((x: requestParam) => {
        if (x.extractedFromGlobal){        
         x.paramValue = this.globalUserService.userDetails.value[x.elementPropertyToBeExtracted];
       }
     });
      // if ([17, 19].some(id => this.form.value.role?.includes(id))) {
      //   this.form.patchValue({ tenantId: this.globalUserService.userDetails.value.clientORtenantId });
      // }    
   
     if(this.form.value.permissionDetail==true)
     {
       this.form.value.permissionDetail = "{\"Permission\":{\"ManageApplication\":true}}"
     }
    else
    this.form.value.permissionDetail = "{\"Permission\":{\"ManageApplication\":false}}"
    if(this.preResponse){
     this.form.value.personTenantRoleId=this.preResponse.personTenantRoleId
    }
      // if ([8, 13, 17].some(id => this.form.value.role?.includes(id))) {
      //   this.form.patchValue({ tenantId: this.globalUserService.userDetails.value.clientORtenantId });
      // }    
      var formValueClone = lodash.cloneDeep(this.form.value);
      formValueClone.role = formValueClone.role
      const selectedRoles: number[] = this.form.value.role || [];
      const hasRole = (id: number) => selectedRoles.includes(id);
      formValueClone = {
        ...formValueClone,
        testcenterGuid: this.CodeDetails?.testcenterGuid?.trim() || '',
        tenantCode: this.CodeDetails?.tenantCode?.trim() || '',
        testcenterName: this.CodeDetails?.testcenterName?.trim() || '',
        ClientStateCode
          : formValueClone.tenantId && !hasRole(Roles.TrainingInstitue)
            ? !hasRole(Roles.Employer)
              ? this.statesOrTrainingIns.find((x: any) => x.id === formValueClone.tenantId)?.stateCode
              : this.globalUserService.userDetails.value.stateCode
            : this.globalUserService.userDetails.value.stateCode
      };
     
     this.closeOutputEvent.emit(
       {
         response: formValueClone,
         requestDetails,
         index
       })

       setTimeout(()=>{
           this.store.dispatch<Action>(creationSuccessfull())
       },1500)
    }
    else{
      let userDetails:any ={
        id: this.preResponse.personId,
        firstName: this.form.value.firstName,
        middleName:  this.form.value.middleName,
        lastName:  this.form.value.lastName,
        email: this.preResponse.emailId,
        ssn: this.preResponse.ssn,
        countryCode:"" ,
        country: "",
        city: this.preResponse.city,
        zipCode: this.preResponse.zipCode,
        accommodation: "",
        clientCandidateId: "",
        phoneNumber: this.form.value.phoneNumber,
        tenantId: this.preResponse.tenantId,
        profile: "",
        modifiedBy: 0,
        address: this.preResponse.address,
        addressLine1: "",
        addressLine2: "",
        dateofBirth: this.preResponse.dateofBirth,
        gender: this.preResponse.gender,
        stateName: this.preResponse.stateName,
        role:this.form.value.role,
        stateCode: this.globalUserService.userDetails.value.stateCode?this.globalUserService.userDetails.value.stateCode:this.statesOrTrainingIns.find((x: any) => x.id === this.preResponse.stateId)?.stateCode,
        personTenantRoleId: this.preResponse.personTenantRoleId,
        permissionDetail: this.form.value.permissionDetail == true?"{\"Permission\":{\"ManageApplication\":true}}": "{\"Permission\":{\"ManageApplication\":false}}",
        testCenterId:this.CodeDetails?.testCenterId !=null && this.CodeDetails?.testCenterId !=undefined?this.CodeDetails.testCenterId :null,
        testcenterGuid:this.CodeDetails?.testcenterGuid !=null && this.CodeDetails?.testcenterGuid !=undefined && this.CodeDetails?.testcenterGuid !=''?this.CodeDetails.testcenterGuid:'',
        tenantCode:this.CodeDetails?.tenantCode !='' && this.CodeDetails?.tenantCode !=null && this.CodeDetails?.tenantCode !=undefined?this.CodeDetails.tenantCode:null,
        testcenterName:this.CodeDetails?.testcenterName !='' && this.CodeDetails?.testcenterName !=undefined && this.CodeDetails?.testcenterName !=null?this.CodeDetails.testcenterName:'',
        ClientStateCode
        : this.globalUserService.userDetails.value.clientORtenantId? this.statesOrTrainingIns.find((x: any) => x.id === this.preResponse.stateId)?.stateCode
            : this.globalUserService.userDetails.value.stateCode
      }
       this.https.manageUserUpdate(this.preResponse.personTenantRoleId,userDetails).subscribe((data:any)=>{
        if(data){
          this.dialogRef.close()
           this.services.callSnackbaronSuccess("Update SuccessFully")
        }
        this.store.dispatch<Action>(creationSuccessfull())
       },(err:any)=>{
             this.services.callSnackbaronError(`${err.message.message.error}`)
       })
    }
  
  }

  selectedRole(event: Event | any) {
    debugger
    const selectedRoles: number[] = event?.value || [];
    const has = (ids: number[]) => selectedRoles.some(id => ids.includes(id));
    const hasRole = (id: number) => selectedRoles.includes(id);
    if (has([17, 13, 8, 28])) {
      this.showAddress = false;
      this.stateActive = false;
      this.form.patchValue({ address: 'null', License: 'null', cantest: true, zipcode: 'null', city: 'null', tenantId: this.globalUserService.userDetails.value.clientORtenantId, permissionDetail: "{\"Permission\":{\"ManageApplication\":false}}" });
    } else {
      this.stateActive = true;
      this.rolesId = hasRole(4);
      this.form.patchValue(
        hasRole(4) ? { address: 'null', License: 'null', cantest: true, zipcode: null, city: 'null', permissionDetail: "{\"Permission\":{\"ManageApplication\":false}}" } :
          hasRole(22) ? { address: 'null', License: 'null', cantest: false, zipcode: null, city: 'null', permissionDetail: "{\"Permission\":{\"ManageApplication\":false}}" } :
            { address: 'null', License: 'null', cantest: true, zipcode: 'null', city: 'null', permissionDetail: "{\"Permission\":{\"ManageApplication\":false}}" }
      );
    }
    this.showAddress = has([22, 4]);
    if(this.preResponse){
    }else {
      this.form.get('permissionDetail').setValue(false)
    }
 
  }

  testcenterCode(code){
    this.CodeDetails = code
  }

  state(event){
    if(this.preResponse == null){
      let states = this.statesOrTrainingIns.filter((x)=>x.id === event.value)
      this.rolesId == true? this.http.post(`${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,{}).subscribe((data:Array<object>)=>{
         if(data.length > 0){
           this.testCenterId = data
         }
      }):null
    }else{
    setTimeout(()=>{
      let states = this.statesOrTrainingIns.filter((x)=>x.id === event.value)
      this.rolesId == true? this.http.post(`${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,{}).subscribe((data:Array<object>)=>{
         if(data.length > 0){
           this.testCenterId = data
         }
      }):null
    },1500)
    }
  
  }

  // getTraningID(){
  //    this.http.get()
  // }
  
}
export interface addUserForm {
  id?: number;
  email: string;
  trainingprogram: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  role: number[];
  permissionDetail:string;
  tenantCode: string;
  trainingprogramcode:string;
  state:string;
  eligibilityroute:string[];
  expirydate:string;
  examtype:string;
  firstName:string;
  fax:string;
  website:string;
  zipcode:number;
  billingzipcode:number;
  address1:string;
  address2:string;
  billingaddress1:string;
  billingaddress2:string;
  city:string;
  billingcity:string;
}
