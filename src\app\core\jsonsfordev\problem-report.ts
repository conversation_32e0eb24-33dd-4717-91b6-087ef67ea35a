import { state } from "@angular/animations";
import { FormTypes } from "src/app/client/application/application.types";
import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  State,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Problem Reports",
      moduleID: "problem-report",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "demographicManageIcon",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: false,
                  showColumnWiseFilter: true,
                  showMatTabs: false,
                  showAddButton: [true],
                  addButtonTexts: ["Add Problem Report"],
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  //showStateSelectionDropdown:true,
                  addPopupData: [
                    {
                            isCustom: true,
                            customTemplateRef: "problem-report",
                            title: [""],
                            id: "problem-report",
                            buttons: [],
                    },
                  ],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "id",
                      name: "Report Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "candidateid",
                      name: "Candidate ID",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "candidateName",
                      name: "Candidate Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "registryNumber",
                      name: "Registry Number",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "submittedDate",
                      name: "Submitted Date",
                      cellType: "Date",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "problemType",
                      name: "Problem Type",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                     <tableTypes.column>{
                      visible: true,
                      id: "problemReportStatus",
                      name: "Problem Status",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.View,
                          tooltip: "View",
                          color: "#7d7d7d",
                          requestDetails: [
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                `form`,
                                `formsbyformtypeid`,
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "formTypeId",
                                  paramValue: FormTypes.problem_report,
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "stateId",
                                  paramValue: State.Pennsylvania,
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "clientId",
                                  paramValue: 3,
                                },
                              ],
                              method: "GET",
                            },
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                "form",
                                "personform",
                                "list",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "id",
                                },
                              ],
                              method: "GET",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "edit",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "Edit",
                          color: "#7d7d7d",
                          
                        },
                      ],
                      headerCssClasses: ["width-6"],
                      cellCssClasses: ["cursor-pointer"],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "application",
                        navigateToLabel: "Application",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [
                            
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: FormTypes.problem_report,
                              extractedFromElement: false,
                              elementPropertyToBeExtracted: "formTypeId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "candidateid",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: 0,
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: State.Pennsylvania,
                            },
                            // the below one is the proper one , the one below this is only temporary
                            // {
                            //   paramType: paramTypes.RouteParam,
                            //   paramName: null,
                            //   paramValue: null,
                            //   extractedFromElement: true,
                            //   elementPropertyToBeExtracted: "personEventId",
                            // },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: 0,
                              extractedFromElement: false,
                              elementPropertyToBeExtracted: "personEventId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "id",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: 0,
                              extractedFromElement: false,
                              elementPropertyToBeExtracted: "code",
                            },
                          ],
                          method: "GET",
                        },
                      },
                    },  
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "form", "GetProblemReports"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                    },
                    {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                    },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "formTypeId",
                        paramValue: FormTypes.problem_report,
                      }
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
