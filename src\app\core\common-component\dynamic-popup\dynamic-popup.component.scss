.button{
    background-color: var(--primary);
    border: none;
    color: white;
    padding: 3px 3px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    
  }
 
  ::ng-deep #richTextId {
    ol,ul {
      margin: 10px;
      padding: 10px;
    }
    ol {
      list-style: auto;
    }
    ul {
      list-style: disc;
    }
 }
 
 ::ng-deep .NgxEditor__Content--Disabled{
  pointer-events: auto !important;
 }

 .matheader{
  height: 42px !important;
  background-color: #F9F9F9;
  font: Roboto;
  font-weight: 500;
  size: 18px;
  color: #a7a8ac !important;
  box-shadow: none;
}
.title{
  color: #a7a8ac !important;
}

.form-cards {
  display: flex;
  flex-direction: column;
  align-items: normal;
  justify-content: center;
  box-shadow: none;
  padding: 0.65rem;
  margin-top: -25px;
}

@media screen and (min-width: 1440px) {
  #gallery-1 {width:788px}
}
.button-disabled {
  opacity: 0.4;
  pointer-events: none;
}

.INF {
  font-size: 11px;
  position: absolute;
  /* font-size: 2rem; */
  margin-top: .16rem;

}

.mat-text-header{
  color: #11263c;
  font-size: 0.75rem;
}

.INFLABEL {
  font-size: 0.55rem;
}

.INFTEXT {
  font-size: 0.7rem;
  font-color: gray;
}

.wwidth{
  width: 70%;
  margin-top: 8px;
}