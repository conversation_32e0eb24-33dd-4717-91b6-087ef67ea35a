import { Component, ElementRef, EventEmitter, OnInit, Output } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { BehaviorSubject, Observable } from 'rxjs';
import { StateLists } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types'
import { GlobalUserService } from 'src/app/core/global-user.service';
import { Roles } from '../dynamic-component-loader/dynamic-component-loader.types';
import { recentLinkClickEventSelector } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { get_userDetails } from '../state/shared/shared.selectors';
import { candidateCompleteList, getExamDetailtoDownload, getStateId, setStateIds, VoucherDetails } from './state/voucher.action';
import { downloadedVoucherByExam, gotTrainingInstStateID, listOFVoucher, selectorStateId } from './state/voucher.selector';
import { VoucherState } from './state/voucher.state';
import { VoucherService } from './voucher.service';
import { environment } from 'src/environments/environment';
import { MatDialog } from '@angular/material/dialog';
import { OMRVideoImage } from 'src/app/core/common-component/pop-up/omr-image-video/omr-image-video.component';

@Component({
  selector: 'app-voucher',
  templateUrl: './voucher.component.html',
  styleUrls: ['./voucher.component.scss']
})
export class VoucherComponent implements OnInit {
  TrainingDetails={name:'',address:'',effective_Date:''}
  tableOptions = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false, false, false, false, false],
    showMatTabs: false,
    matTabs: [],
    showSearch: true,
    verticalScroll: true,
    horizontalScroll: true,
  }

  displayedColumns = [[
    <tableTypes.column>{
      visible: true,
      id: "voucherName",
      name: "Voucher Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["link-animation"],
    },
    <tableTypes.column>{
      visible: true,
      id: "voucherCode",
      name: "Item Code",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/6"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "voucherFee",
      name: "Voucher Fee",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/12"],
      cellCssClasses: [],
    },

    <tableTypes.column>{
      visible: true,
      id: "totalVochers",
      name: "Total Vouchers",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/12"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "usedVochers",
      name: "Vouchers Used",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/12"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "action",
      name: "Action",
      cellType: "action",
      actions: [
        <tableTypes.action>{
          type: tableTypes.actionTypes.Custom,
          icon: 'download',
          tooltip: 'Download',
          color: '#7d7d7d'
        }
      ],
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-12"],
      cellCssClasses: ["cursor-pointer"],
    },
  ]]


  courseDetails: any = [];
  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();
  voucherListDetails: any;
  public dataSource: MatTableDataSource<any>;
  examType: number;
  userDetails: any;
  downloadVoucherInfo: any;
  trainingDetails
  navigate
 multipleRoleTenantId: Array<number> = [];
 personTentantRoleId: Array<number> = [];
  constructor(private route: Router, public store: Store<VoucherState>, public voucherService: VoucherService,
    public global: GlobalUserService,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private activteRoute:ActivatedRoute, private elementRef:ElementRef,
    private dialog:MatDialog) { 
    
    }

  ngOnInit(): void {
    this.store.select(get_userDetails).subscribe(data => {
      if (data) {
        data.roles.forEach(value => {
          this.multipleRoleTenantId.push(value.tenantId)
          this.personTentantRoleId.push(value.personTenantRoleId)
        })
        this.store.dispatch(candidateCompleteList({ tenantId: this.multipleRoleTenantId }))
      }
    });

    
    this.voucherService.canidateFilterName = []
      this.global.userDetails.subscribe((user) => {
        if (user == null) {
          this.route.navigateByUrl('/')
        }else{
          if ((user != null && Roles.TrainingInstitue === user.roleId) || (user != null && Roles.Sponsor === user.roleId) ) {
            if ((Roles.TrainingInstitue === user.roleId && StateLists.CA !=user.stateId) || Roles.Sponsor === user.roleId ) {
              const breadcrumb = [
                {
                  label: "Home",
                  url: "/training",
                },
                {
                  label: "Voucher",
                  url: "",
                },
              ];
              this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
            }else if(Roles.TrainingInstitue === user.roleId && StateLists.CA ==user.stateId){
              const breadcrumb = [
                {
                  label: "Home",
                  url: "/voucher",
                },
                {
                  label: "Voucher",
                  url: "",
                },
              ];
              this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
            }
            this.userDetails = user;
            this.store.dispatch(VoucherDetails({ personDetail: user }));
            this.store.dispatch(getStateId({ trainingInstituteID: user.clientORtenantId }))
          }
          else{
            this.global.details.subscribe(data=>{
              let tempuser={
                stateId:data.stateId,
                personId:data.personId,
                roleId:15,
                trainingId:data.training,
                personTenantRoleId:data.personTentantRole
              }
              this.store.dispatch(VoucherDetails({ personDetail: tempuser }));
              this.store.dispatch(setStateIds({Details:tempuser}))
            })
  
            this.store.select(selectorStateId).subscribe(data => {
              if (data != null && data) {
                this.trainingDetails = data
              } 
            })
         
          }
        }
    

      });

      
      
    this.store.select(recentLinkClickEventSelector).subscribe(data=>{
      if(data !=null && data){
        this.navigate = data.column.linkMetaData.navigateToLabel

        if(this.navigate != null && this.navigate !=undefined){
          if ((this.global.userDetails.value.roleId == Roles.OperationStaff || this.global.userDetails.value.roleId == Roles.SupportingStaff ) && this.navigate=="Training Program Details") {
            const breadcrumb = [
              {
                label: "Home",
                url: "/dashboard",
              },
              {
                label: "Manage Training Program",
                url: "/loader/manage-training-programs",
              },
              {
                label: "Training Program Details",
                url: "/loader/training-institute-details",
              },
              {
                label: "Voucher",
                url: "",
              },
          
            ];
            this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
          }
          // else if(this.global.userDetails.value.roleId == Roles.OperationStaff && this.navigate=="Candidate Details"){
          //   const breadcrumb = [
          //     {
          //       label: "Home",
          //       url: "/dashboard",
          //     },
          //     {
          //       label: "Manage Candidate",
          //       url: "/loader/candidate-management",
          //     },
          //     {
          //       label: "Candidate Details",
          //       url: "/loader/candidate-details",
          //     },
          //     {
          //       label: "payment",
          //       url: "",
          //     },
          //   ];
          //   this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
          // }
        } 
      }

       
      
   
    })
    
      this.store.select(selectorStateId).subscribe(data=>{
        if(data !=null && data){
          this.trainingDetails = data
        }
        
      })

    this.store.select(listOFVoucher).subscribe((data: any) => {
      try {
        if (data) {
          this.voucherListDetails = data;
          this.getVoucherDetails();
        }
      }
      catch (e) { }
    })

    try {
      this.store.select(gotTrainingInstStateID).subscribe(stateId => {
        if (stateId && stateId.result.length > 0) {
          this.voucherService.trainingInsitituteId = stateId.result[0].stateId
          this.TrainingDetails.address = stateId.result[0].address
          this.TrainingDetails.name = stateId.result[0].name
          this.TrainingDetails.effective_Date = stateId.result[0].expirydate
        }
      })
    }
    catch (e) {
    }

    // this.test()

  }



  getVoucherDetails() {
    this.courseDetails = [];
    this.voucherListDetails.forEach(element => {
      this.courseDetails.push({
        voucherName: element.voucherName,
        voucherCode: element.voucherCode,
        voucherFee: `$${element.price}`,
        eligibilityRoute: element.eligibilityRoute,
        totalVochers: element.totalVochers,
        usedVochers: element.usedVochers,
        examType: element.examTypeId,
        stateName: element.stateName
      })
    });
    var candidateList = this.courseDetails.reduce((unique, o) => {
      if (!unique.some(obj => obj.voucherCode === o.voucherCode)) {
        unique.push(o);
      }
      return unique;
    }, []);
    this.dataSource = new MatTableDataSource(candidateList)
  }

  addNewVoucher() {
    // if(this.global.userDetails.getValue().roleId == Roles.TrainingInstitue){
    //   const dilogRef = this.dialog.open(OMRVideoImage, {
    //     data: {id:73,name:this.TrainingDetails.name,address:this.TrainingDetails.address,effective:this.TrainingDetails.effective_Date},
    //     width: '750px',
    //     disableClose:true
    //   })
    //   dilogRef.afterClosed().subscribe((data)=>{
    //       if(data.confirmed == true){
    //          this.route.navigateByUrl('/voucher/buy-new-voucher')
    //       }
    //   })
    // }
    // else{
      this.route.navigateByUrl('/voucher/buy-new-voucher')
    // }
 
   
  }

  selectedVoucher(event) {
    if (event.column.id == 'voucherName') {
      this.examType = event.element.examType;
      this.voucherService.examTypeId = this.examType;
      this.voucherService.examCode = event.element.voucherCode;
      this.voucherService.data = [{
        voucherName: event.element.voucherName,
        voucherCode: event.element.voucherCode,
        voucherFee: `$${event.element.price}`,
        eligibilityRoute: event.element.eligibilityRoute,
        totalVochers: event.element.totalVochers,
        stateName: event.element.stateName
      }]
      this.route.navigate(['voucher', 'voucher-list'])
    }
  }

  buyVoucher() {
    this.route.navigateByUrl('/voucher/buy-new-voucher')
  }

  downloadVoucher(event) {
    if(this.global.userDetails.value.roleId == Roles.TrainingInstitue || this.global.userDetails.value.roleId == Roles.Sponsor ){
      this.downloadVoucherInfo = {
        examTypeId: event.element.examType,
        tenantId: this.multipleRoleTenantId,
        stateId: this.userDetails.stateId,
        itemCode: event.element.voucherCode,
        personTenantRoleId: this.personTentantRoleId
      }
    }else{
      this.downloadVoucherInfo = {
        examTypeId: event.element.examType,
        tenantId: [this.trainingDetails.trainingId],
        stateId: this.trainingDetails.stateId,
        itemCode: event.element.voucherCode,
        personTenantRoleId: [this.trainingDetails.personTenantRoleId]
      } 
    }
  
    this.voucherService.downloadFileName = event.element.voucherName;
    this.store.dispatch(getExamDetailtoDownload({ voucherData: this.downloadVoucherInfo }))
  }

  private test(): void{
    var s = document.createElement("script");
    s.id = "zsiqchat";
    s.type = "text/javascript";
    s.src = `${environment.redirectUrl}assets/static data/zoho.js`;
    this.elementRef.nativeElement.appendChild(s);
  }

}
