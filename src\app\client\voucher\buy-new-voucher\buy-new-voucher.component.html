  
    <div  [ngClass]="{hide1:untilPaymentProcess(0)}" class="px-gutter" exaiContainer>
        <div>
        <mat-spinner class="spinner"></mat-spinner>
        <div class="loading">
            <div class="flex justify-center test-base fontcustom">
                We're processing...
            </div>
            <div class="flex justify-center text-xs font-bold">
                This process may take a few seconds, so please be patient.
            </div>
        </div>
        </div>
        </div>    

<div [ngClass]="{hide:untilPaymentProcess(1) }" >
    <div class="text-sm">
        <h6><strong>{{this.language.curLangObj.value.voucher}}</strong></h6>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
        </app-ng-dynamic-breadcrumb>
    </div>
    
    <div class="py-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
        gdGap="12px">
        <div class="card cardBorder shadow-none p-4" gdColumn="3 / 9" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1 /-1">
            <div class="mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="12px">
                <div class="tab-header" gdColumn="1/ 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1">
                    <mat-card-header>
                        <mat-card-subtitle>Voucher Name</mat-card-subtitle>
                    </mat-card-header>
                </div>
                <div class="tab-header" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
                    <mat-card-header>
                        <mat-card-subtitle>Voucher Price</mat-card-subtitle>
                    </mat-card-header>
                </div>
            </div>
            <hr>
             <div *ngIf="this.globalService.userDetails.value.roleId == 15 || this.globalService.userDetails.value.roleId == 18">
                <div class="" *ngFor="let item of items" class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr" gdGap="12px">
                <div class="mt-3 tab-col1" gdColumn="1/ 4" gdColumn.lt-md="1 / 4" gdColumn.lt-sm="1">
                    {{item.name}}
                </div>
                <div class="mt-3 tab-col2 flex justify-end" gdColumn="4 / 5" gdColumn.lt-md="4 / 5" gdColumn.lt-sm="1">
                    <mat-card-header>
                        <mat-card-subtitle>${{item.Price}}</mat-card-subtitle>
                    </mat-card-header>
                </div>
                <div class="add-to-cart mt-3 flex justify-end ml-2" gdColumn="6/ -1" gdColumn.lt-md="6 / -1"
                    gdColumn.lt-sm="1">
                    <button class="min-brd" mat-button (click)="examMinus(item)">-</button>
                    <input onPaste="return false"  class="px-2 add-no w-6 text-center" matInput id={{item.voucherCode}} value="{{item.value}}"
                        (keyup)="keyUp($event,item)" (keypress)="keyPressNumbers($event)">
                    <!-- <span class="px-2 add-no" id={{item.name}}>{{item.value}}</span> -->
                    <button class="add-brd" mat-button (click)="examAdd(item)">+</button>
                </div>
            </div>
             </div>
             <div *ngIf="this.globalService.userDetails.value.roleId == 17 || this.globalService.userDetails.value.roleId == 8">
                <div class="" *ngFor="let item of OperationItems" class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr" gdGap="12px">
                <div class="mt-3 tab-col1" gdColumn="1/ 4" gdColumn.lt-md="1 / 4" gdColumn.lt-sm="1">
                    {{item.name}}
                </div>
                <div class="mt-3 tab-col2 flex justify-end" gdColumn="4 / 5" gdColumn.lt-md="4 / 5" gdColumn.lt-sm="1">
                    <mat-card-header>
                        <mat-card-subtitle>${{item.Price}}</mat-card-subtitle>
                    </mat-card-header>
                </div>
                <div class="add-to-cart mt-3 flex justify-end ml-2" gdColumn="6/ -1" gdColumn.lt-md="6 / -1"
                    gdColumn.lt-sm="1">
                    <button class="min-brd" mat-button (click)="examMinus(item)">-</button>
                    <input onPaste="return false"  class="px-2 add-no w-6 text-center" matInput id={{item.voucherCode}} value="{{item.value}}"
                        (keyup)="keyUp($event,item)" (keypress)="keyPressNumbers($event)">
                    <!-- <span class="px-2 add-no" id={{item.name}}>{{item.value}}</span> -->
                    <button class="add-brd" mat-button (click)="examAdd(item)">+</button>
                </div>
            </div>
             </div>
          
       
    
            <div class="pt-4" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr"
                gdColumns.lt-sm="1fr" gdGap="12px">
                <div class="w-full flex mt-3 pl-4" fxLayout="row" gdColumn="6/ -1" gdColumn.lt-md="6 / -1"
                    gdColumn.lt-sm="1">
                    <button mat-button class="btn-1 text-xs" (click)="paynow()" *ngIf="this.globalService.userDetails.value.roleId == 15 || this.globalService.userDetails.value.roleId == 18">Add to Cart</button>
                    <button mat-button class="btn-1 text-xs" *ngIf="this.globalService.userDetails.value.roleId == 17 ||this.globalService.userDetails.value.roleId == 8 " (click)="Generate(OperationItems)">Generate Voucher</button>
                </div>
            </div>
        </div>
    </div>
</div>
