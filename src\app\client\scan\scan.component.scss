


.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
}
.title {
  background: #fafafa;
  margin-left: -16px;
  margin-right: -16px;
  margin-top: -16px !important;

  .text {
    color: #11263c;
  }
}
.status {
  color: #c4c4c4;
}

.z-index{
  z-index: 1 !important;
}
.eliggilibityroutename {
  font-size: 0.57rem;
}
.status3 {
  color: var(--text-color1);
}
.status1 {
  color: #7d7d7d;
}

.endTime{
  background-color: #e95b6d;
  color: #ffff;
}

.spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: all;
}

@-webkit-keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
  }
}
@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

.buttonstatustimer{
  background-color: #0076c1 !important;
  color: #ffff;
  font-size: 0.85rem !important;
}






.v-center {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  position: absolute;
  background: #e5e5e5;
  > .config {
      width: 720px;
      margin: 0 auto;
      text-align: center;
      border: 0.3px solid #7d7d7d;
      border-radius: 4px;
      padding: 45px;
      background: #ffffff;
  }
  .example-full-width {
      width: 100%;
  }
  h1 {
      color: var(--primary);
      font-size: 20px;
      font-weight: 500;
      mat-icon {
          font-weight: 500;
          font-size: 20px;
          color: var(--primary);
          vertical-align: middle;
      }
  }
  h2 {
      float: left;
      color: #11263c;
      font-weight: 500;
      font-size: 16px;
      margin: 11px 0 8px 0px;
  }
}

ul{
  list-style-type: disc;
}

::ng-deep{
  .tooltip {
      text-align: inherit !important;
      background: #ffffff !important;
      color: #7d7d7d !important;
  }
}
 .headertest{
  color: #0076C1;
  white-space: normal;
  max-width: 7rem;
 }

 .enlargeContent{
  overflow: auto;
  white-space: nowrap;
 }

 .textenlarger{
  display: -webkit-box !important;
 }

.texter{
  color:#1a202eab;

  
}

.Completed{
  left: 0;
  position: absolute;
}
.delete{
  cursor: pointer;
  color: #7d7d7d;
  font-size: 1.15rem;
}

.welc-note {
  color: var(--text-color1);
}
.active {
  color: var(--text-color2);
  border: 1px solid var(--text-color2);
}
.active2 {
  color: var(--text-color2);
}

.minimise{
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow: hidden;
}
.submit {
  width: 147px;
}
.active1 {
  color: #ee9400;
}

.cancelonboard{
  color: red;
  font-size: 20px;
}

.content {
  padding: 14px;
}

.content1 > mat-card {
  margin-bottom: 12px;
}
.cradHeight {
  height: 300px;
}
.example-card {
  max-width: 400px;
}

.example-header-image {
  background-image: url('https://material.angular.io/assets/img/examples/shiba1.jpg');
  background-size: cover;
}
mat-card {
  max-width: 400px;
}
.add {
  background-color: var(--text-color2);
  color: #fff;
  // width: 200px;
  height: 33px;
}
.iconSize {
  width: 12px;
  height: 12px;
}
mat-card-title {
  margin-bottom: 5px;
}

.bg-color {
  background-color:#FAFAFA;;
}
.add-new {
  background-color: var(--button-background) !important;
  color: var(--button-color) !important;
  padding: 0.1rem 0.5rem;
}
.fontStyle {
  font-style: italic;
}
.addBtn {
  color: var(--text-color2);
  height: 30px;
  background-color: #e9f5f5;
}
.textColor {
  color: #6d6d6d;
  font-family: Roboto;
  font-weight: 400;
  font-style: normal;
  font-size: 16px;
  // line-height: 18.75;
  align-items: center;
}

.state-elig {
  color: #7D7D7D;
}

.buuton1{
  background-color: #E9F5F5;
  color: #0076C1;
}
.text1{
  color: #0076C1;
}

.buttonCheckinout{
  width: 116px;
  line-height: 19px;
}

.textfalse{
 color: rgba(244, 67, 54, 1);
}

.imgcorner{
  width: 17px;
}
.size {
  height: 70px;
  width: 70px;
}
.btnText {
  background: var(--sidenav-item-background-active);
  border-left-color: var(--sidenav-item-border-color-active);
}
.material-icons-round {
  font-size: 16px;
}
.icon {
  cursor: pointer;
}
::ng-deep .mat-menu-panel {
  min-height: 0px !important;
}
::ng-deep .mat-menu-content:not(:empty) {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.textColor1 {
  color: #d80027;
}

.title-hed {
  color: var(--text-dropdown);
}

.state-elig {
  color: var(--text-color1);
}

::ng-deep .mat-menu-item .mat-icon {
  margin-right: 0px !important;
}
.space {
  line-height: 40px;
}
// .cardHeight{
//   height: 130px;
// }

::ng-deep .mat-button.mat-primary.mat-button-disabled, .mat-button.mat-accent.mat-button-disabled, .mat-button.mat-warn.mat-button-disabled, .mat-button.mat-button-disabled.mat-button-disabled, .mat-icon-button.mat-primary.mat-button-disabled, .mat-icon-button.mat-accent.mat-button-disabled, .mat-icon-button.mat-warn.mat-button-disabled, .mat-icon-button.mat-button-disabled.mat-button-disabled, .mat-stroked-button.mat-primary.mat-button-disabled, .mat-stroked-button.mat-accent.mat-button-disabled, .mat-stroked-button.mat-warn.mat-button-disabled, .mat-stroked-button.mat-button-disabled.mat-button-disabled {
  /* color: rgba(0, 0, 0, 0.26); */
  background: #4444 !important;
}
::ng-deep b, strong {
  font-weight: bolder;
  font-size: 0.85em !important;
}

.button-disabled {
  opacity: 0.4;
  pointer-events: none;
}
.titleFont {
  font-size: 1.3em;
  // font-weight: bolder;
  font-family: "Roboto", sans-serif;
}

.onhover {
  cursor: pointer;
}

.text_centers{
  font-size: 0.88rem;
  color: #7d7d7d
}
::ng-deep .shadow-none{
  margin-left: 5px !important;
  margin-right: 5px !important;
}

::ng-deep .text-sm {
  margin-left: 15px !important;
  margin-top: 5px !important;
}


.hh{
  height: calc(100vh - var(--toolbar-height) - 90px);
}

.imageh{
  position: absolute;
    width: 97%;
    height: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 5px;
}

::ng-deep svg {
  width: 48px !important;
}