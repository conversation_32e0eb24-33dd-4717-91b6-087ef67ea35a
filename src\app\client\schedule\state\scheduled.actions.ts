import { createAction, props } from "@ngrx/store";
import { ActiveForm } from "./models/activeForm";
import { Cart, Details } from "./models/cart";
import { CartItem, payVoucherAmountDetails } from "./models/cartItem";
import { chargeBody, chargeResponse } from "./models/charge";
import { customerId } from "./models/customerId";
import { Exam } from "./models/Exam";
import { ExamName } from "./models/examName";
import {
  MakePaymentBody,
  MakePaymentResponse,
  Reschedule,
} from "./models/makePayment";
import { MonthlySlot } from "./models/monthlySlot";
import {
  CreatePaymentCustomerIdBody,
  CreatePaymentCustomerIdResponse,
  CreatePaymentMethodRespnse,
  paymentMethod,
} from "./models/paymentMethods";
import { Schedule } from "./models/schedule";
import { Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import {
  Vocher,
  VocherResponse,
  VochersApply,
  VocherUpdate,
  VocherUpdateResponse,
} from "./models/VocherList";

export const getTimezones = createAction("[Timezones] GET Timezones");

export const gotTimezones = createAction(
  "[Timezones] GOT Timezones",
  props<{ Timeszones: Timezone[] }>()
);

export const getTimeSlots = createAction(
  "[TimeSlots] GET TimeSlots",
  props<{
    timezone: string;
    startDate: string;
    endDate?: string;
    examId: number;
    offset: string;
  }>()
);

export const gotTimeSlots = createAction(
  "[TimeSlots] GOT TimeSlots",
  props<{ Slots: Slot[] }>()
);

export const getCart = createAction(
  "[Cart] GET Cart",
  props<{ details: Details; isPayment?: boolean }>()
);

export const gotCart = createAction(
  "[Cart] GOT Cart",
  props<{ cart: Cart; isPayment?: boolean }>()
);

export const getMonthlySlots = createAction(
  "[MonthlySlots] GET MonthlySlots",
  props<{ month: number; year: number; timezone: string }>()
);

export const gotMonthlySlots = createAction(
  "[MonthlySlots] GOT MonthlySlots",
  props<{ monthlySlots: MonthlySlot[] }>()
);

export const getExamId = createAction(
  "[ExamId] GET ExamId",
  props<{ eligibilityRouteId: number; candidateId: number }>()
);

export const gotExamId = createAction(
  "[ExamId] GOT ExamId",
  props<{ examdata: { id: number; title: string }[] }>()
);

export const getEligibilityroute = createAction(
  "[EligibilityRoute] GET EligibilityRoute",
  props<{ candidateId: number }>()
);

export const gotEligibilityRoutes = createAction(
  "[EligibilityRoute] GOT EligibilityRoute",
  props<{ route: ExamName }>()
);

export const getRegisteredExam = createAction(
  "[RegisteredExam] GET RegisteredExam",
  props<{ candidateId: number }>()
);

export const gotRegisteredExam = createAction(
  "[RegisteredExam] GOT RegisteredExam",
  props<{ registeredExams: Exam[] }>()
);

export const getMakePayment = createAction(
  "[MakePayment] GET MakePayment",
  props<{ makepaymentBody: MakePaymentBody; personTenantRoleId: number }>()
);

export const gotMakePayment = createAction(
  "[MakePayment] GOT MakePayment",
  props<{ makePaymentResponse: MakePaymentResponse }>()
);

export const getSchedule = createAction(
  "[Schedule] GET Schedule",
  props<{ schedule: Schedule }>()
);

export const gotSchedule = createAction(
  "[Schedule] GOT Schedule",
  props<{ scheduleres: any }>()
);

export const cancelExam = createAction(
  "[Cancel] cancelExam",
  props<{ examScheduleId: number; candidateId: number }>()
);

export const Examcancelled = createAction(
  "[Cancel] examCancelled",
  props<{ isCancelled: boolean }>()
);

export const removeCartItem = createAction(
  "[deleteCartitems] deleteCartitems",
  props<{ cartItemId: number; personTenantRoleId: number }>()
);
export const removeAllCartItem = createAction(
  "[deleteCartitems] deleteCartitems",
  props<{ cartId: number; personTenantRoleId: number }>()
);

export const cartItemRemoved = createAction(
  "[deleteCartitems] cart items deleted",
  props<{ isDeleted: boolean }>()
);

export const reschedule = createAction(
  "[Rescheduled] GET Rescheduled",
  props<{ rescheduleBody: Reschedule }>()
);

export const gotrescheduled = createAction(
  "[Rescheduled] GOT Rescheduled",
  props<{ rescheduleResponse: number }>()
);

export const isPayment = createAction(
  "[isPayment] is payment action",
  props<{ isPayment: boolean }>()
);

export const getCartItems = createAction(
  "[CartItems] GET CartItems",
  props<{ personTenantRoleId?: number }>()
);

export const gotCartItems = createAction(
  "[CartItems] GOT CartItems",
  props<{ cartItems: CartItem[] }>()
);

export const setCandidateId = createAction(
  "[setCandidateId] SET CandidateId",
  props<{ candidateId: number }>()
);

export const setTimezome = createAction(
  "[setTimezone] SET Timezone",
  props<{ timezone: Timezone }>()
);

export const getvoucherCartDetails = createAction(
  "pass tenantId ",
  props<{ personTentantRoleId: number }>()
);

export const vouchercartDetails = createAction(
  "get voucher Details",
  props<{ voucherCartDeatils: any }>()
);

export const getcartId = createAction(
  "pass data to delete ",
  props<{ personTentantRoleId: number; cartId: number }>()
);

export const deletedCartId = createAction(
  "get voucher Details",
  props<{ deletedCartStatus: boolean }>()
);

export const paymentDetails = createAction(
  "pass data to make Payment ",
  props<{ personTentantRoleId: number; details: chargeBody }>()
);

export const successPayment = createAction(
  "transcationDetails",
  props<{ paidCartStatus: any }>()
);

export const createVoucher = createAction(
  "pass data to generate voucher ",
  props<{ newvoucherDetails: any }>()
);

export const gotNewVoucher = createAction(
  "generated new voucher",
  props<{ successNewVoucher: any }>()
);

export const reAddData = createAction(
  "pass data to edit cart ",
  props<{ itemId: number; quantity: number }>()
);

export const reAddedSuccess = createAction(
  "data after cart is edited",
  props<{ sucessCartmessage: any }>()
);

export const makenull = createAction("make null ");

export const madenull = createAction(
  "making null...",
  props<{ paramater: any }>()
);

export const getStateId$ = createAction(
  "get state ID",
  props<{ trainingInstituteID: number }>()
);

export const trainingStateId$ = createAction(
  "get training istitute state ID",
  props<{ gotTiId: any }>()
);

export const getPaymentMethod = createAction(
  "get payment method",
  props<{ customerId: string }>()
);

export const gotPaymentMethod = createAction(
  "got payment method",
  props<{ paymentmethods: paymentMethod[] }>()
);


export const makeCharge = createAction(
  "make charge ",
  props<{ chargeBodu: chargeBody; personTenantRole?: number }>()
);

export const madeCharge = createAction(
  "made charge",
  props<{ chargeResponse: chargeResponse }>()
);

export const getPaymentCustomerId = createAction("GET Customer Id",
  props<{ PersonTenantRoleId: number }>())

export const gotPaymentCustomerId = createAction(
  "GOT Customer Id",
  props<{ customerIdObj: customerId }>()
);

export const createPaymentMethod = createAction(
  "[PaymentMethod] create payment method",
  props<{ CreatePaymentMethod }>()
);

export const paymentMethodCreated = createAction(
  "[PaymentMethod] payment method created",
  props<{ response: CreatePaymentMethodRespnse }>()
);

export const createPaymentCustomerId = createAction(
  "[Create Customer Id] create customer id",
  props<{ body: CreatePaymentCustomerIdBody }>()
);

export const paymentCustomerIdCreated = createAction(
  "[PaymentCustomerIdCreated]  customer id created",
  props<{ response: CreatePaymentCustomerIdResponse }>()
);

export const VoucherDetails = createAction(
  " Get Voucher List",
  props<{ personDetail: any }>()
);

export const getVoucherDetails = createAction(
  "Got Voucher List",
  props<{ voucherDetails: any }>()
);

// Test center
export const getTimeSlotsTestCenterFailure = createAction(
  "[TimeSlots] Get TimeSlots Test Center Failure",
  props<{ error: any }>()
);

export const getTimeSlotsTestCenter = createAction(
  "[TimeSlots] GET TimeSlots for TestCenter",
  props<{
    timezone: string;
    examId: number;
    startDate: string;
    endDate?: string;
    testCenterName: string;
    testCenterId?: number | string;
    testCenterAddress?: string;
    radius: number;
    candidateId?: number;
  }>()
);

export const gotTimeSlotsTestCenter = createAction(
  "[TimeSlots] GOT TimeSlots  for TestCenter",
  props<{ slots: any[] }>()
);

export const addCartTC = createAction(
  "[Cart] ADD Cart",
  props<{ body: any }>()
);

export const getVoucher = createAction(
  "[Voucher] GET Voucher",
  props<{ VocherValidator: Vocher }>()
);

export const gotVoucher = createAction(
  "[Voucher] GOT Voucher",
  props<{ VocherResponse: VocherResponse }>()
);
export const getVoucherApply = createAction(
  "[Voucher] GET VoucherApply",
  props<{ VocherDetails: VochersApply }>()
);
export const gotVoucherApply = createAction(
  "[Voucher] GOT VoucherApply",
  props<{ VocherApplyResponse: number }>()
);

export const getScheduled = createAction(
  "[Schedule] GET Scheduled",
  props<{ personTentantRole: number; cartId: number }>()
);

export const gotScheduled = createAction(
  "[Schedule] GOT Scheduled",
  props<{ ScheduledResponse: any }>()
);

export const voucherUpdate = createAction(
  "[Voucher update] create update vocher",
  props<{ VocherUpdateDetails: VocherUpdate }>()
);
export const voucherUpdated = createAction(
  "[Updated Vocher]  Vocher Updated",
  props<{ response: VocherUpdateResponse }>()
);

export const clearVocherResponse = createAction(
  "[VocherResponse] clear Vocher response state"
);
export const clearChargeResponse = createAction(
  "[clearChargeResponse] clear Charge Response state"
);

export const saveVocher = createAction(
  "[Save Vocher] save Vocher",
  props<{ voucherCode: string }>()
);

export const clearVoucherCode = createAction("[Clear Vocher] clear Vocher");

export const deleteCard = createAction(
  "[Delete Card] delete card",
  props<{ id: string }>()
);

export const cardDeleted = createAction("[Card Deleted]  card deleted");

export const clearTimeSlotsTestCenter = createAction(
  "[CLEAR TIMESLOTS FOR TEST CENTER] Clear Data"
);

export const ClearTimeslots = createAction(
  "[CLEAR TIMESLOTS FOR ONLINE] Clear Data"
);
