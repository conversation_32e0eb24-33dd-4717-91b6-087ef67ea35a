import { Component, Inject, OnInit } from "@angular/core";
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidatorFn, Validators } from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormService } from "@ng-dynamic-forms/core";
import {
  confirmationPopupData,
  formActionTypes,
} from "../form-view/form-view.component";
import { HelpService } from "src/app/client/help/help.service";
import { GivanceViewService, GrievanceAction } from "../../grivance-view";
import { SnackbarService } from "../../snackbar.service";
import { GlobalUserService } from "../../global-user.service";
import { cellClickEvent } from "../table/dynamic-table-types";
import { Store } from "@ngrx/store";
import { recentLinkClickEventSelector } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';



export function noWhitespaceValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': 'value is only whitespace' };
  };
}
@Component({
  selector: "app-confirmation-popup",
  templateUrl: "./confirmation-popup.component.html",
  styleUrls: ["./confirmation-popup.component.scss"],
})

export class ConfirmationPopupComponent implements OnInit {
  formActionTypes = formActionTypes;
  commentControl: FormControl;
  categoriesControl: FormControl;
  SelectedcategoriesId:number;
  recentLinkClickEvent: cellClickEvent = null;

  qacForm:FormGroup;
  resultCategoryList:GrievanceAction[];
  remeditionList:GrievanceAction[];
  categories$: {
    id: number,
    name: string,
    dataDetail: any
    active: boolean
}[]
  constructor(
    private dialogRef: MatDialogRef<ConfirmationPopupComponent>,
    private dynamicFormService: DynamicFormService,
    @Inject(MAT_DIALOG_DATA) public data: confirmationPopupData,
    private helpService:HelpService,
    private fb:FormBuilder,
    private grievanceViewService:GivanceViewService,
    private snackbarService:SnackbarService,
    public global:GlobalUserService,
    private store: Store,
  ) {
    this.commentControl = new FormControl('', [Validators.required, noWhitespaceValidator()]);
     this.helpService.getcatorgies().subscribe((data:{
      id: number,
      name: string,
      dataDetail: any
      active: boolean
  }[])=>{
        if(data){
         this.categories$ = data
        }
    });

    this.qacForm = this.fb.group({
      resultCategory:[null],
      remedition:[null],
      systemIssue:[false],
      evaluatorResponsible:[false]
     });

     if(data?.selectticket =='Reply and Close Ticket') {
      this.categoriesControl = new FormControl(null, [Validators.required]);
     }
  }

  rejectDisable = false;
  formGroupUpload 
  formGroupModel
  dataUpload = new DynamicFileUploadModel({
    required: false,
    id:'1',
    name:'2',
    label: '',
    accept: ['.pdf', '.doc', '.docx','.png','.jpg'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList:true,
    additional: {
      appearance: 'outline'
    }
  })
 
  ngOnInit(): void {
    this.store.select(recentLinkClickEventSelector).subscribe((x: cellClickEvent) => {
      if (x) {
        
        this.recentLinkClickEvent = x;
      }
    });
    this.formGroupModel = [this.dataUpload];
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel);

    
    this.grievanceViewService.getGrievanceActionsList('RS').subscribe(data => {
      this.resultCategoryList = data;
    },
    error => {
      this.snackbarService.callSnackbaronError(error);
    });

    this.grievanceViewService.getGrievanceActionsList('RM').subscribe(data => {
      this.remeditionList = data;
    },
    error => {
      this.snackbarService.callSnackbaronError(error);
    });
    

    const remedition = this.grievanceViewService.getGrievanceActionsList('RM').subscribe(data => {
      this.remeditionList = data;
    },
    error => {
      this.snackbarService.callSnackbaronError(error);
    });
  }

  onCategoryChange(event){
     this.SelectedcategoriesId = event.value.id;
     this.rejectDisable = this.commentControl.value !='' && this.commentControl.value !=null && this.SelectedcategoriesId !=undefined && this.SelectedcategoriesId!=null? false:true
  }

  confirm() {

    const isValid = this.data?.selectticket =='Reply and Close Ticket' ? this.commentControl.valid && this.categoriesControl.valid : this.commentControl.valid;

    if (isValid){
      this.dialogRef.close({
        confirmed: true,
        message: this.commentControl.value,
        doc:this.formGroupUpload.value,
        value:this.data.value,
        SelectedcategoriesId:this.SelectedcategoriesId,
        resultCategory:this.qacForm.get('resultCategory').value ? this.qacForm.get('resultCategory').value : null,
        remedition:this.qacForm.get('remedition').value ? this.qacForm.get('remedition').value : null,
        systemIssue:this.qacForm.get('systemIssue').value,
        evaluatorResponsible:this.qacForm.get('evaluatorResponsible').value
      });
    }
  }
}
