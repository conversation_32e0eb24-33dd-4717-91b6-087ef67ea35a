



import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';

import { FormControl, FormGroup } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { SafeResourceUrl } from '@angular/platform-browser';
import { SupersetService } from 'src/app/core/superset.service';


export interface Dashboard {
  id: number;
  dashboard_title: string;
  chart_count: number;
  created_by: string;
  created_on: string;
  changed_on: string;
  description: string;
  published: boolean;
  embed_url: string;
  url: string;
  slug: string;
}
@Component({
  selector: 'app-superset',
  templateUrl: './superset.component.html',
  styleUrls: ['./superset.component.scss']
})
export class SupersetDashboardComponent implements OnInit, OnDestroy,AfterViewInit {
  private subscription: Subscription | null = null;
  headerArray:Array<object>=[]
  headerArrays:Array<object>=[]
  selectedName:string
  selectedDashboardId:number
  embedUrl:SafeResourceUrl
  resourceurl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  $resourceurl: Observable<string> = this.resourceurl.asObservable();
  inputData: FormGroup;
  selectId:number
  viewSearch:boolean
  viewIsOn: boolean = false;
  
  constructor(private supersetService:SupersetService) { }

  ngOnInit(): void {
    this.loadDashboard();

    this.inputData = new FormGroup({
      data: new FormControl(""),
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  loadDashboard(): void {
    this.supersetService.getDashboards().subscribe((data:{dashboards:Array<{dashboard_title:string,id:number}>})=>{
     if(data){
      console.log(data)
       this.headerArray =[...data.dashboards];
       this.headerArrays = this.headerArray.sort((a: any, b: any) => {
         if (a.dashboard_title.replace(/\s+/g, '') && b.dashboard_title.replace(/\s+/g, '')) {
           let _a = a.dashboard_title.replace(/\s+/g, '').toUpperCase();
           let _b = b.dashboard_title.replace(/\s+/g, '').toUpperCase();
           return _a < _b ? -1 : _a > _b ? 1 : 0;
         }
       });
       console.log(this.headerArrays)
     }
    });
  }

  search(){
     console.log(this.inputData.value.data)
     if(this.inputData.value.data){
      this.headerArrays = this.headerArray.filter((x:{dashboard_title:string})=>x.dashboard_title ===this.inputData.value.data )
     }else{
      this.headerArrays = this.headerArray
     }
  }
  selectedItems(view) {
    this.selectedName = view.dashboard_title
  }

  

  loadpinboard(id){
    this.selectId = id.id
    this.viewSearch = true
    this.selectDashboard(id)
  
  }

  reportsearch(event){
   this.headerArrays=this.headerArray.filter(
    (item:{dashboard_title:string}) => item.dashboard_title.toLowerCase().includes(this.inputData.value.data.toLowerCase())
  );
  }

  selectDashboard(dashboard:Dashboard): void { 
    // Get the embedded dashboard UUID and load it
    this.supersetService.generateEmbedUrlWithFreshAuth(dashboard)


  }


  ngAfterViewInit(): void {
    setTimeout(()=>{
       console.log(document.querySelectorAll('div'))
    },5000)
  }
}

