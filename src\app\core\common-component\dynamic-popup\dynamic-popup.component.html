<ng-container>
    <div class="text-base flex  justify-end pb-2 dynamicPopUp overflow-auto" #PopupHeader>
        <div class="flex justify-end cursor-pointer" mat-dialog-close>
            <mat-icon class="text-base flex" (click)="close()">close</mat-icon>
        </div>
    </div>
    <hr class="popUpCard">
    <ng-container *ngIf="popupConfig.type == popupTypes.FormViewPopup && popupData">
        <!-- <exai-form-view-popup [data]="popupData"></exai-form-view-popup> -->
        <div class="shadow-none justify-start overflow-auto dashboard" gdColumn="1 / 9" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
            
            <div id="printMe"class="card shadow-none overflow-y-auto" fxFlex="auto" [style.overflow-x]="'hidden!important'">
                <ng-container *ngIf="formJSON && !userResponse">
                    <exai-form-builder [existingForm]="formJSON"  [showToolbar]="false" [showOnlyForm]="true" [disabled]="true" [submitButtonRef]="submitButton" [element]="element" [showStatic]="element.formTypeId == 2 || element.formTypeId == 4">
                    </exai-form-builder>
                </ng-container>
                <ng-container *ngIf="formJSON && userResponse">
                    <exai-form-builder [existingForm]="formJSON"  [openAll]="true" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse" [disabled]="true" [submitButtonRef]="submitButton" [element]="element" [showStatic]="element.formTypeId == 2 || element.formTypeId == 4">
                    </exai-form-builder>
                    <div>
                        <div class="form-cards mt-2" id='gallery-1'>
                            <mat-accordion *ngIf=" formJSON && userResponse && ShowData  && ![15,20,22463,22613].includes(recentEventSelect.element.stateId) && ![117,118,63,64,22331].includes(recentEventSelect.element.eligibilityRouteId)"   >
                                <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                                <mat-expansion-panel class="cardBorder  mb-2"  [expanded]="true">
                                    <mat-expansion-panel-header class="matheader">
                                        <mat-panel-title class="text-xs title ">
                                            Testing Preference
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <h6 class="mat-text-header">
                                        1. Are you planning to take your Nurse Aide Skills Exam at an In-facility or Regional
                                        Testing Center?</h6>
                                        <mat-radio-group [formControl]="radioselect" [disabled]="NonEditData">
                                            <ng-container *ngFor="let examType of examTypeModels; let i = index">
                                              <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
                                                (change)="getSelectedRoute(examType.id)">{{ examType.name }}
                                              </mat-radio-button>
                                            </ng-container>
                                            <mat-error class="-mt-2" *ngIf="radioselect.value == null || radioselect.value == '' || radioselect.value == undefined " >
                                                This field is required
                                              </mat-error>
                                          </mat-radio-group>
                                    <form [formGroup]="INFValue">
                                        <ng-container *ngIf="INFSelected" >
                                            <h6 class="mat-text-header">
                                                2.What In-facility are you interested in testing at? Kindly include their INF code below if you know it:</h6>
                                            <mat-form-field class="mt-2 ml-2" appearance="outline">
                    
                    
                                                <span class="INFTEXT mb-1" id="basic-addon3">INF</span>
                                                <input class="form-control INF "formControlName="infcode" matInput autocomplete="off" maxlength="5" id='testId' [disabled]="NonEditData" (input)="onKeydownMain($event.target.value)" (keypress)="onlyNumberKey($event)">
                    
                                            </mat-form-field>
                    
                                           
                                        </ng-container>
                                    </form>
                          
                                    <ng-container  *ngIf="INFSelected">
                                        <h6 class="-mt-2 mat-text-header">
                                            3. I acknowledge that I am not required to test at this location.</h6>
                                         
            
                                            <mat-radio-group [formControl]="radioselectAcceptance" [disabled]="NonEditData">
                                                <ng-container *ngFor="let examType of Acceptance; let i = index">
                                                  <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"  (change)="getSelectedAccept(examType.id)"
                                                    >{{ examType.name }}
                                                  </mat-radio-button>
                                                </ng-container>
                                                <mat-error class="-mt-2" *ngIf="radioselectAcceptance.value == null || radioselectAcceptance.value == '' || radioselectAcceptance.value == undefined " >
                                                    This field is required
                                                  </mat-error>
                                              </mat-radio-group>
                                    </ng-container>
                                </mat-expansion-panel>
                                <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                                <!-- <mat-expansion-panel class="cardBorder application-expansion1" *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                                    <mat-expansion-panel-header>
                                        <mat-panel-title class="text-xs font-bold">
                                            {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <p class="expand">
                                        <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                                    </p>
                                </mat-expansion-panel> -->
                            </mat-accordion>
                        </div>
                    </div>
                    <button mat-flat-button color="primary" class="button center mt-2" *ngIf="this.global.userDetails.value.roleId == 14 && Dowmloadbutton " (click)="toPdf()">Download Application</button>
                    <button mat-flat-button color="primary" class="button center mt-2"    *ngIf="(this.global.userDetails.value.roleId == 17 || this.global.userDetails.value.roleId == 14 ) && this.global.clickedviewIconDetails?.element.hasOwnProperty('problemType')"  (click)="toPdf()">Download Problem Report</button>
                </ng-container>
                

            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="popupConfig.type == popupTypes.RichTextView && popupData">
        <div class="shadow-none justify-start overflow-auto dashboard" gdColumn="1 / 9" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
            <div class="card shadow-none h-full overflow-y-auto" fxFlex="auto" [style.overflow-x]="'hidden!important'">
                <ngx-editor class="w-full h-full" id="richTextId" [editor]="editor" [formControl]="richText">
                </ngx-editor>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf="popupConfig.type == popupTypes.ComponentView && popupData">
        <div class="shadow-none justify-start overflow-auto dashboard">
            <exai-img-pop-up [data]="popupData.element"></exai-img-pop-up>
        </div>
    </ng-container>
    <ng-container *ngIf="popupConfig.type == popupTypes.EmulateCandidate && popupData">
        <div *ngIf="this.element" class="shadow-none justify-start overflow-auto dashboard h-full">
            <iframe [src]="($resourceurl | async) | safeResourceurl" class="w-full" style="height:90%;"></iframe>
        </div>
    </ng-container>
    <ng-container *ngIf="popupConfig.type == popupTypes.EmulateClient && popupData">
        <div *ngIf="this.element" class="shadow-none justify-start overflow-auto dashboard h-full">
            <iframe [src]="($resourceurl | async) | safeResourceurl" class="w-full" style="height:90%;"></iframe>
        </div>
    </ng-container>
</ng-container>
<button [style.display]="'none'" #hiddenUnnecessaryButton></button>
