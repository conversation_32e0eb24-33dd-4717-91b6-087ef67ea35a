import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";


export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Manage Users Access",
      moduleID: "manage-all-access-superadmin",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "managaeUserRoleIcon",
      routeRef: "/loader",
      sequence: 32,
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  showAddButton: [false],
                  addButtonTexts: ["Add User Access"],
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "customAddPopupFormManageUserAccess",
                      title: ["Add User Access", "Edit User Access"],
                      id: "editUserAccessPopup",
                      buttons: [
                        {
                          buttonText: "Update",
                          requestDetails: {
                            fetchUrl: ["login", "register"],

                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                  showMatTabs: false,
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: ["firstName", "middleName", "lastName"],
                      name: "Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "emailId",
                      name: "Email Id",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "stateName",
                      name: "State",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "userRoles",
                      name: "Role Name",
                      cellType: "array",
                      cellArrayProps: ["roleName"],
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                      statusUpdateDetails: {
                        fetchUrl: [],
                        requestBody: null,
                        requestParams: [],
                        method: "GET",
                      },
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "actions",
                      name: "Suppression",
                      cellType: "action",
                      
                      actions: [
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.suspension_List_remove,
                          tooltip: "Suspension_remove",
                          color: "#7d7d7d",
                        
                        },
                      ],
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ['width-5'],
                      cellCssClasses: ["w-12", "cursor-pointer"],
                      filterable: false,
                      isFilter: false,
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Actions",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "edit",
                          type: tableTypes.actionTypes.Edit,
                          tooltip: "Edit",
                          color: "#7d7d7d",
                          requestDetails: [
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                "account",
                                "UpdateUser",
                              ],
                              requestBody: null,
                              requestParams: [],
                              method: "PUT",
                            },
                          ],
                        },
                      ],
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-12"],
                      cellCssClasses: ["w-12", "cursor-pointer"],
                      filterable: false,
                      isFilter: false,
                    },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "account", "user-manage-role"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personRoleId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "id",
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
