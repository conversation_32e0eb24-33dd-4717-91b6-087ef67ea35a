.register {
    color: var(--text-register);
}

.highlight {
    color: var(--text-color-selected);
}

// ::ng-deep svg {
//     width: 20px !important;
// }

.exam-list {
    @screen sm {
        max-height: calc(100vh - 46.5vh);
        max-width: calc(100vw - 2.1rem);
    }

    @screen md {
        max-height: calc(100vh - 46.5vh);
        max-width: calc(100vw - 2.1rem);
    }

    @screen xl {
        max-height: calc(100vh - 46.5vh);
        max-width: calc(100vw - var(--sidenav-width) - 2.1rem);
    }

    @screen lg {
        max-height: calc(100vh - 46.5vh);
        max-width: calc(100vw - var(--sidenav-width) - 2.1rem);
    }

    // max-height: calc(100vh - 46.5vh);
    // max-width: calc(100vw - var(--sidenav-width) - 2.1rem);
}

.bg-card {
    border: var(--border-search1);
}

.column-filter {
    color: var(--text-color1);
}

.lineHeight{
    line-height: 35px;
}

.width{
    width: 27px !important;
}

.color{
    color: #7d7d7d;
}


.src-icon {
    color: var(--text-color1);
}

.mat-mini-fab {
    background-color: var(--text-color2) !important;
    width: 30px;
    height: 30px;
}

// .table-head {
//    // place-content: space-between !important;
//     background-color: transparent !important;
// }

.paginator {
    justify-content: space-between !important;
}

.mat-row.hovered {
    background: var(--highlight-bgcolor);
    border-radius: var(--border-radius);
}

.filter {
    padding: 0.35rem 0.5rem 0.35rem 0.5rem;
    border-radius: var(--border-radius);
    border: var(--border);
    color: var(--text-register);
}

.paginator {
    color: var(--text-toggle);
}

.paginators {
    color: var(--text-toggle);
}

.status-text {
    vertical-align: super !important;
    width: 75px !important;
}

.statusIcon {
    width: 15px;
}

.mat-tabGroup {
    justify-content: flex-end !important;
    margin-bottom: -2px;
}

.mat-tab-label-container {
    display: flex;
    flex-grow: 1;
    overflow: hidden;
    z-index: 1;
    overflow-x: scroll !important;
}

// .mat-tab-header-pagination.mat-tab-header-pagination-disabled {
//     display: none !important;
// }

.add-New {
    top: 2px;
}

::ng-deep {
    .mat-tab-label {
        min-width: 90px !important;
    }
}

.action-icons {
    font-size: 1.25rem;
}

.page-border {
    border-bottom: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

.tableBackground-page {
    background-color: var(--background-base);
}

::ng-deep .customize {
    background-color: rgb(253, 249, 249);
}

.tableBackground-col {
    background-color: transparent;
}

.disabled {
    cursor: default;
    pointer-events: none !important;
    opacity: 0.4 !important;
}

.pag-button {
    height: 24px !important;
}

.pag-buttons {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
}

::ng-deep .mat-icon-button {
    color: #7d7d7d;
}

.alignCenter {
    align-items: center !important;
}



::ng-deep .mat-datepicker-content .mat-calendar {
    width: 240px !important;
    height: 290px !important;
}

::ng-deep .addclass {
    /* box-sizing: border-box; */
    right: 0 !important;
    bottom: 0 !important;
    left: auto !important;
    top: auto !important;
    width: 40% !important;
    height: 50% !important;
}


.clear_cart {
    background-color: var(--button-color) !important;
    color: var(--text-color2) !important;
    border: var(--border-search1) !important;
    padding: 7px 7px !important;
    line-height: 10px !important;
    font-weight: 500;
    border-radius: 4px;
    margin-left: -4px !important;
}

// .withSerachFlex{
//     flex: 550px;
//     width: 227px
// }

.withoutSerachFlex {
    flex: 150px;
    width: 100%;
}

.bgColor {
    background-color: #d9d1d161;
}

.close {
    height: 18px !important;
    width: 18px !important;
    font-size: 18px !important;
}