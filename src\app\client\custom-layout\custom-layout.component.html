<ng-template #sidenavRef>
    <exai-sidenav [collapsed]="sidenavCollapsed$ | async"></exai-sidenav>
</ng-template>

<ng-template #toolbarRef>
    <exai-toolbar  [mobileQuery]="!(isDesktop$ | async)" class="exai-toolbar">
    </exai-toolbar>
</ng-template>
<ng-template #loading>
    <ng-container *ngIf="loadingObs | async">
        <div class="card-div">
            <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
        </div>

    </ng-container>

</ng-template>

<ng-template #footerRef>
    <exai-footer *ngIf="isFooterVisible$ | async" class="exai-footer"></exai-footer>
</ng-template>

<ng-template #quickpanelRef>
    <exai-quickpanel></exai-quickpanel>
</ng-template>

<exai-layout [footerRef]="footerRef" [quickpanelRef]="quickpanelRef" [sidenavRef]="sidenavRef" [toolbarRef]="toolbarRef" [loading]="loading"></exai-layout>

<exai-config-panel-toggle *ngIf="global.userDetails.value && global.userDetails.value.roleId == Roles.SupportingStaff" [currentlyConnectedChats]="currentlyConnectedChats" (openConfig)="chatPanel.open()"></exai-config-panel-toggle>

<exai-sidebar #configpanel [invisibleBackdrop]="true" position="right">
    <exai-config-panel></exai-config-panel>
</exai-sidebar>

<exai-sidebar #chatPanel [invisibleBackdrop]="true" position="right" [sidenavWidth]="'78%'">
    <exai-support-chat class="relative" *ngIf="global.userDetails.value && global.userDetails.value.roleId == Roles.SupportingStaff" (newChatConnected)="currentlyConnectedChats = $event.currentNumChats" [sideNavRef]="chatPanel">
    </exai-support-chat>
</exai-sidebar>

<!-- END CONFIGPANEL -->