import { Injectable } from "@angular/core";
import {
  HttpRequest,
  Http<PERSON><PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor,
  HttpResponse,
} from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { catchError, filter, map } from "rxjs/operators";
import { Action, Store } from "@ngrx/store";
import { URL } from 'src/app/core/url';
import { GlobalUserService } from './global-user.service';
import {
  setErrorMessage,
  setLoadingSpinner,
} from "../client/state/shared/shared.actions";
import jwt_decode from 'jwt-decode';
import { UpdateIdAndAvailability } from "../client/support-chat/store/support-chat.actions";
import { SupportChatComponent } from "../client/support-chat/support-chat.component";
import { Roles } from "../client/dynamic-component-loader/dynamic-component-loader.types";
import { SnackbarService } from "./snackbar.service";
import { ReplaySessionService } from "./openreplay.services";
import { environment } from "src/environments/environment";
import { UrlAllowed } from "./common-component/examroom-formbuilder/form-builder.types";
@Injectable({
  providedIn: "root",
})
export class TokenInterceptorService {

  constructor(private store: Store, private globalUserService: GlobalUserService,
  private snackbar:SnackbarService,private openReplayService:ReplaySessionService) { }

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    const token =sessionStorage.getItem("token")
    this.snackbar.EmailVerified = jwt_decode(sessionStorage.getItem("token"))
    sessionStorage.setItem('email', this.snackbar.EmailVerified.email)

    if(!request.url.includes('ProviderSessionApi/CreateSession')){
      this.store.dispatch<Action>(setLoadingSpinner({ status: true }));
    }
  if(request.url.includes('webrtcdevapi') || request.url.includes('webrtcuatapi') || request.url.includes('webrtcapi') || request.url.includes('GetEventsByGISLoggedUser') || request.url.includes('GetEventOfferByLoggedUser') || request.url.includes('HistoricalEventOfferByLoggedUser') ){
    request = request.clone({
      setHeaders: { 'Content-Type': 'application/json' },

    });
  }
  else if( UrlAllowed.includes(request.url)){
    request = request.clone({
      setHeaders: { 'Content-Type': 'application/json',Authorization: `Bearer ${token}` },

    });
  }
   else if(!this.openReplayService.UrlNotAllowedToken.includes(request.url)){
    request = request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
   }


    const handleResponse = (request: HttpRequest<any>, response: HttpResponse<any>, event: string) => {
      //we forward our data to the service, which will create the custom event and send it
     this.openReplayService.sendEventToReplaySession(event, { request, response })
   }
    return next
      .handle(request)
      .pipe(
        catchError((err) => {
          var currentUrl = request.url;
          if(!currentUrl.includes('StratArchive')) {
            this.snackbar.callSnackbaronError(`${(err.error !='' && err.error !=undefined && err.error !=null)?err.error:(err.message.error !='' &&err.message.error !=undefined && err.message.error !=null )?err.message.error:err.message.error.error}`);
          }

          if (err.status == 401) {
            this.snackbar.callSnackbaronError('Your login has expired! Please login again....')
            sessionStorage.removeItem("userData");
            sessionStorage.removeItem("token");
            if (this.globalUserService.userDetails.value.roleId == Roles.SupportingStaff) {
              this.store.dispatch(UpdateIdAndAvailability({
                personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
                token: SupportChatComponent.agentID,
                isOnline: false
              }))
            }
            window.location.href = URL.REDIRECT_URL;
          } else if(err.status == 500) {   
            this.store.dispatch<Action>(setLoadingSpinner({ status: false })); 
            if(err.error) {
              if(!currentUrl.includes('StratArchive')){
                this.snackbar.callSnackbaronError(err.error);
              }
            } else {
              this.snackbar.callSnackbaronError('Something went wrong! Please contact administrator')
            }            
          }
          else {
            // this.store.dispatch<Action>(setErrorMessage({ message: err }));
            this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          }
          return throwError({ message: err });
        })
      )
      .pipe(
        filter( (event: any) => event instanceof HttpResponse),
        map( (resp: HttpResponse<any>) => { //for each response, call handleResponse
          handleResponse(request, resp, `${request.url}`)
          return resp
        }),
        map<HttpEvent<any>, any>((evt: HttpEvent<any>) => {
          if (evt instanceof HttpResponse) {
            this.store.dispatch<Action>(setLoadingSpinner({ status: false }));
          }
          return evt;
        })
      );
  }
}
