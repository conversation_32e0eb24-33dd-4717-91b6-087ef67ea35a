// import { Injectable } from '@angular/core';
import * as OT from '@opentok/client';
import { ProviderService } from './provider.service';
// import { IVideoProvider } from '../interfaces/Ivideo-provider';
import {
  CallBackEvent,
  SessionEvent,
  SignalType,
  UserSessionEvent,
  UserSignalEvent,
} from '../enum';
import {
  IPublisherProperties,
  ISubscriberProperties,
  IVideoProvider,
} from '../interfaces/Ivideo-provider';
import {
  AudioStatsDetails,
  ResponseModel,
  SignalResponseModel,
  StatsDetails,
  StreamDetails,
  UserSessionStream,
  VideoStatsDetails,
} from '../models';
import { WebRTCBlackboxService } from './web-rtc-balckbox.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { HttpErrorResponse } from '@angular/common/http';

// @Injectable({
//   providedIn: 'root'
// })
export class TokboxService implements IVideoProvider {
  sessionTokbox: OT.Session | undefined;
  selfProvider: ProviderService | undefined;
  selfTokbox: TokboxService | undefined;
  publisher: OT.Publisher | undefined;
  screenPublisher: any = null;
  canvasPublisher: any = null;
  screen360Publisher: any = null;
  constructor(private webRTCBlackboxService: WebRTCBlackboxService, public snackBar: SnackbarService,) {}

  initService(parentThis: any) {
    this.selfProvider = parentThis;
    this.selfTokbox = this;
  }

  initializeSession() {
    if (this.selfProvider) {
      this.sessionTokbox = OT.initSession(
        this.selfProvider.WR_apiKey,
        this.selfProvider?.WR_sessionId
      );

      this.sessionTokbox.on('streamCreated', (event: any) => {

        const tempObject: StreamDetails = {
          name: event.stream.name,
          stream: event.stream,
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.SUBSCRIBER_STREAM_CREATED,
          tempObject
        );

        //this.webRTCBlackboxService.sendDataToEventCallBack('streamCreated', event);
      });

      this.sessionTokbox.on('streamDestroyed', (event: any) => {
        try {
          const userObject: UserSessionStream = {
            userId: event.stream.name,
            reason: 'Stream has been stopped.',
          };
          this.webRTCBlackboxService.sendEventData(
            SessionEvent.SUBSCRIBER_STREAM_DESTROYED,
            userObject
          );
        } catch (error) {
          this.selfProvider?.WR_handleError(error);
        }
      });

      // this.sessionTokbox.on('streamPropertyChanged', (event) => {
      //   //var subscribers = session.getSubscribersForStream(event.stream);
      //   this.webRTCBlackboxService.sendDataToEventCallBack('streamPropertyChanged', event);
      // });

      this.sessionTokbox.on('connectionCreated', (event: any) => {
        try {
          const jsonObject: any = JSON.parse(event.connection.data);
          const userObject: UserSessionStream = {
            userId: jsonObject.name,
            reason: 'User Connected.',
          };
          this.webRTCBlackboxService.sendEventData(
            UserSessionEvent.USER_CONNECTED,
            userObject
          );
        } catch (error) {
          this.selfProvider?.WR_handleError(error);
        }
      });

      this.sessionTokbox.on('connectionDestroyed', (event) => {
        try {
          const jsonObject: any = JSON.parse(event.connection.data);
          const userObject: UserSessionStream = {
            userId: jsonObject.name,
            reason: 'User Disconnected.',
          };
          this.webRTCBlackboxService.sendEventData(
            UserSessionEvent.USER_DISCONNECTED,
            userObject
          );
        } catch (error) {
          this.selfProvider?.WR_handleError(error);
        }
      });

      this.sessionTokbox.on('sessionDisconnected', (event) => {
        this.selfTokbox?.unsubscribe();
        const userObject: UserSessionStream = {
          userId: '',
          reason: event.reason,
        };
        this.webRTCBlackboxService.sendEventData(
          UserSessionEvent.USER_DISCONNECTED,
          userObject
        );
      });

      // Connect to the session
      this.sessionTokbox.connect(this.selfProvider?.WR_token, (error) => {
        // If the connection is successful, initialize a publisher and publish to the session
        if (error) {
          this.selfProvider?.WR_handleError(error);
        } else {
          this.selfTokbox?.reconnectSession();
          // sessionTokbox.publish(publisher, handleError);
        }
      });

      this.sessionTokbox.on('signal:' + SignalType.MESSAGE, (event: any) => {
        if (event.data) {
          const resp: SignalResponseModel = JSON.parse(event.data);
          resp.sentAt = event.from.creationTime;
          if (
            resp.to == 'All' ||
            resp.to == this.selfProvider?.WR_currentUserId
          ) {
            this.webRTCBlackboxService.sendEventData(
              UserSignalEvent.SIGNAL,
              resp
            );
          }
        }
      });
    }
  }

  reconnectSession() {
    if (!this.sessionTokbox) {
      this.selfTokbox?.initializeSession();
      return;
    }
    this.publishMedia();
  }

  publishMedia(publisherProperties?: IPublisherProperties) {
    if (!publisherProperties) {
      publisherProperties = {};
    }
    publisherProperties.name = this.selfProvider?.WR_currentUserId;
    publisherProperties.insertMode = 'append';
    publisherProperties.width = '0px';
    publisherProperties.height = '0px';

    if (!this.selfProvider?.WR_isPublisher) {
      publisherProperties.publishAudio = false;
      publisherProperties.publishVideo = false;
    }
    this.publisher = OT.initPublisher(
      '',
      publisherProperties,
      this.selfProvider?.WR_handleError
    );

    this.sessionTokbox?.publish(this.publisher, (err: any) => {
      if (err) {
        switch (err.name) {
          case 'OT_NOT_CONNECTED':
            this.selfProvider?.WR_handleError(
              'Publishing your video failed. You are not connected to the internet.'
            );
            break;
          case 'OT_CREATE_PEER_CONNECTION_FAILED':
            this.selfProvider?.WR_handleError(
              'Publishing your video failed. This could be due to a restrictive firewall.'
            );
            break;
          case 'OT_USER_MEDIA_ACCESS_DENIED':
            this.selfProvider?.WR_handleError(
              'Please allow access to the Camera and Microphone and try publishing again.'
            );
            break;
          default:
            this.selfProvider?.WR_handleError(
              'An unknown error occurred while trying to publish your video. Please try again later.'
            );
        }
        this.publisher?.destroy();
      }
    });
    // if (!this.selfProvider?.WR_isPublisher) {
    //   this.webRTCBlackboxService.sendDataToEventCallBack(
    //     'publishMedia',
    //     'Publishing media set to false.'
    //   );
    //   return;
    // }
    this.publisher.on('streamCreated', (event: any) => {
      const tempModel: any = {
        UserId: this.selfProvider?.WR_userId,
        TenantId: this.selfProvider?.WR_tenantId,
        WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
        StreamId: event.stream.streamId,
        ConnectionId: event.stream.connection.connectionId,
        TokenId: this.selfProvider?.WR_token,
        StreamType:
          this.selfProvider?.WR_StreamTypeEnum[event.stream.videoType],
      };
      const ajaxUrl: string =
        this.selfProvider?.WR_SERVER_BASE_URL_API + 'AddStreamApi';
      this.selfProvider?.callWebApi(
        ajaxUrl,
        tempModel,
        this.ApiCallBack,
        'streamCreated'
      );
      const tempObject: StreamDetails = {
        name: event.stream.name,
        stream: event.stream,
      };
      this.webRTCBlackboxService.sendEventData(
        SessionEvent.PUBLISHER_STREAM_CREATED,
        tempObject
      );
    });
    this.publisher.on('streamDestroyed', (event: any) => {
      // this.publisher?.off('streamCreated');
      // this.publisher?.off('streamDestroyed');
      // this.publisher = undefined;
      const tempObject: UserSessionStream = {
        userId: event.stream.name,
        reason: 'Stream has been destroyed.',
      };
      this.webRTCBlackboxService.sendEventData(
        SessionEvent.PUBLISHER_STREAM_DESTROYED,
        tempObject
      );
    });
  }

  getStats(): void {
    this.publisher?.getStats(
      (error?: OT.OTError, stats?: OT.PublisherStatsArr) => {
        let publisherStatContainer: OT.PublisherStatContainer | undefined =
          undefined;
        let publisherStats: OT.PublisherStats | undefined = undefined;
        let audioStatsDetails: AudioStatsDetails | undefined = undefined;
        let videoStatsDetails: VideoStatsDetails | undefined = undefined;
        let statsDetails: StatsDetails | undefined = undefined;
        if (stats && stats[0]) {
          publisherStatContainer = stats[0] as OT.PublisherStatContainer;
          if (publisherStatContainer) {
            publisherStats = publisherStatContainer?.stats;
          }
        }
        if (publisherStats) {
          audioStatsDetails = {
            bytesSent: publisherStats?.audio?.bytesSent,
            packetsLost: publisherStats?.audio?.packetsLost,
            packetsSent: publisherStats?.audio?.packetsSent,
          };
          videoStatsDetails = {
            bytesSent: publisherStats?.video?.bytesSent,
            packetsLost: publisherStats?.video?.packetsLost,
            packetsSent: publisherStats?.video?.packetsSent,
            frameRate: publisherStats?.video?.frameRate,
          };
          statsDetails = {
            audio: audioStatsDetails,
            video: videoStatsDetails,
          };
        }

        this.webRTCBlackboxService.sendEventData(
          SessionEvent.STATS_DETAILS,
          statsDetails
        );
      }
    );
  }

  changeBackground(src: string): void {
    if (this.publisher) {
      this.publisher.applyVideoFilter({
        type: 'backgroundReplacement',
        backgroundImgUrl: src,
      });
    }
  }

  clearVideoFilter(): void {
    if (this.publisher) {
      this.publisher.clearVideoFilter();
    }
  }

  backgroundBlur(): void {
    if (this.publisher) {
      this.publisher.applyVideoFilter({
        type: 'backgroundBlur',
        blurStrength: 'low',
      });
    }
  }

  publishVideo(isStream: boolean) {
    if (this.publisher) {
      this.publisher.publishVideo(isStream);
    }
  }

  publishAudio(isStream: boolean) {
    if (this.publisher) {
      this.publisher.publishAudio(isStream);
    }
  }

  subscribeVideo(subscriber: any, isStream: boolean) {
    subscriber.subscribeToVideo(isStream);
  }

  subscribeAudio(subscriber: any, isStream: boolean) {
    subscriber.subscribeToAudio(isStream);
  }

  ApiCallBack = (event: string, data: any) => {
    const resp: ResponseModel = new ResponseModel(event, data);
    this.webRTCBlackboxService.sendEventData(
      CallBackEvent.CALLBACK_EVENT,
      resp
    );
  };

  startRecord() {
    try{
      const tempModel: any = {
        UserId: this.selfProvider?.WR_userId,
        TenantId: this.selfProvider?.WR_tenantId,
        UserType: this.selfProvider?.WR_userType,
        ApplicationId: this.selfProvider?.WR_applicationId,
        WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
      };
      const ajaxUrl: string =
        this.selfProvider?.WR_SERVER_BASE_URL_API + 'ArchiveApi/StratArchive';
      this.selfProvider?.callWebApi(
        ajaxUrl,
        tempModel,
        this.ApiCallBack,
        'startRecord'
      );

    }catch(err:HttpErrorResponse | any){
      this.snackBar.callSnackbaronError(`${err.message}`)
    }
  }

  stopRecord() {
    try{

      const tempModel: any = {
        UserId: this.selfProvider?.WR_userId,
        TenantId: this.selfProvider?.WR_tenantId,
        UserType: this.selfProvider?.WR_userType,
        ApplicationId: this.selfProvider?.WR_applicationId,
        WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
      };
      const ajaxUrl: string =
        this.selfProvider?.WR_SERVER_BASE_URL_API + 'ArchiveApi/StopArchive';
      this.selfProvider?.callWebApi(
        ajaxUrl,
        tempModel,
        this.ApiCallBack,
        'stopRecord'
      );
    }catch(err:HttpErrorResponse | any){
      this.snackBar.callSnackbaronError(`${err.message}`)
    }
  }

  leaveMeeting() {
    if (this.screenPublisher) {
      this.sessionTokbox?.unpublish(this.screenPublisher);
    }
    if (this.screen360Publisher) {
      this.sessionTokbox?.unpublish(this.screen360Publisher);
    }
    if (this.publisher) {
      this.sessionTokbox?.unpublish(this.publisher);
    }
    if (this.canvasPublisher) {
      this.sessionTokbox?.unpublish(this.canvasPublisher);
    }
    this.sessionTokbox?.disconnect();
    //this.webRTCBlackboxService.sendDataToEventCallBack('leaveMeeting', 'Left meeting successfully.');
  }

  sendSignal(json: string) {
    if (this.sessionTokbox && json) {
      const signalOptions: any = {
        type: SignalType.MESSAGE,
        data: json,
      };
      this.sessionTokbox.signal(signalOptions, (error) => {
        if (error) {
          this.selfProvider?.WR_handleError(error);
        } else {
          this.webRTCBlackboxService.sendEventData(
            UserSignalEvent.SENT_SIGNAL,
            json
          );
        }
      });
    }
  }

  disconnectUser() {
    const tempModel: any = {
      UserId: this.selfProvider?.WR_userId,
      TenantId: this.selfProvider?.WR_tenantId,
      UserType: this.selfProvider?.WR_userType,
      ApplicationId: this.selfProvider?.WR_applicationId,
      WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
    };
    const ajaxUrl: any =
      this.selfProvider?.WR_SERVER_BASE_URL_API + 'DisconnectSessionApi';
    this.selfProvider?.callWebApi(
      ajaxUrl,
      tempModel,
      this.ApiCallBack,
      'disconnectUser'
    );
  }

  shareScreen() {
    if (this.sessionTokbox) {
      this.screenPublisher = OT.initPublisher(
        this.selfProvider?.WR_screenShareId,
        {
          name: this.selfProvider?.WR_currentUserId + '_screen',
          videoSource: 'screen',
        },
        this.selfProvider?.WR_handleError
      );
      this.screenPublisher.on('streamCreated', (event: any) => {
        const tempModel: any = {
          UserId: this.selfProvider?.WR_userId,
          TenantId: this.selfProvider?.WR_tenantId,
          WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
          StreamId: event.stream.streamId,
          ConnectionId: event.stream.connection.connectionId,
          TokenId: this.selfProvider?.WR_token,
          StreamType:
            this.selfProvider?.WR_StreamTypeEnum[event.stream.videoType],
        };
        const ajaxUrl: string =
          this.selfProvider?.WR_SERVER_BASE_URL_API + 'AddStreamApi';
        this.selfProvider?.callWebApi(
          ajaxUrl,
          tempModel,
          this.ApiCallBack,
          'shareScreen'
        );

        const tempObject: StreamDetails = {
          name: event.stream.name,
          stream: event.stream,
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.SCREEN_SHARE_STREAM_CREATED,
          tempObject
        );
      });

      this.screenPublisher.on('streamDestroyed', (event: any) => {
        this.screenPublisher.off('streamCreated');
        this.screenPublisher.off('streamDestroyed');
        this.screenPublisher = null;
        const tempObject: UserSessionStream = {
          userId: event.stream.name,
          reason: 'Stream has been destroyed.',
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.SCREEN_SHARE_STREAM_DESTROYED,
          tempObject
        );
      });

      this.sessionTokbox.publish(this.screenPublisher, (err: any) => {
        if (err) {
          switch (err.name) {
            case 'OT_NOT_CONNECTED':
              this.selfProvider?.WR_handleError(
                'Publishing your video failed. You are not connected to the internet.'
              );
              break;
            case 'OT_CREATE_PEER_CONNECTION_FAILED':
              this.selfProvider?.WR_handleError(
                'Publishing your video failed. This could be due to a restrictive firewall.'
              );
              break;
            case 'OT_USER_MEDIA_ACCESS_DENIED':
              this.selfProvider?.WR_handleError(
                'Please allow access to the Camera and Microphone and try publishing again.'
              );
              break;
            default:
              this.selfProvider?.WR_handleError(
                'An unknown error occurred while trying to publish your video. Please try again later.'
              );
          }
          this.publisher?.destroy();
        }
      });
    } else {
      const tempObject: UserSessionStream = {
        userId: '',
        reason: 'Something went wrong while share screen.',
      };
      this.webRTCBlackboxService.sendEventData(
        SessionEvent.SCREEN_SHARE_STREAM_DESTROYED,
        tempObject
      );
    }
  }

  share360Camera(
    webRTCSessionID: string,
    audioDeviceId: string,
    videoDeviceId: string
  ) {
    if (this.selfProvider) {
      this.sessionTokbox = OT.initSession(
        this.selfProvider?.WR_apiKey,
        this.selfProvider?.WR_sessionId
      );

      if (this.sessionTokbox) {
        this.screen360Publisher = OT.initPublisher(
          '',
          {
            audioSource: audioDeviceId,
            videoSource: videoDeviceId,
            insertMode: 'append',
            name: this.selfProvider?.WR_currentUserId,
            facingMode: 'environment',
            publishAudio: true,
            publishVideo: true,
            width: '0px',
            height: '0px',
            echoCancellation: true,
          },
          this.selfProvider?.WR_handleError
        );

        this.screen360Publisher.on('streamCreated', (event: any) => {
          const tempModel: any = {
            UserId: this.selfProvider?.WR_userId,
            TenantId: this.selfProvider?.WR_tenantId,
            WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
            StreamId: event.stream.streamId,
            ConnectionId: event.stream.connection.connectionId,
            TokenId: this.selfProvider?.WR_token,
            StreamType:
              this.selfProvider?.WR_StreamTypeEnum[event.stream.videoType],
          };
          const ajaxUrl: string =
            this.selfProvider?.WR_SERVER_BASE_URL_API + 'AddStreamApi';
          this.selfProvider?.callWebApi(
            ajaxUrl,
            tempModel,
            this.ApiCallBack,
            'share360Screen'
          );
          const tempObject: StreamDetails = {
            name: event.stream.name,
            stream: event.stream,
          };
          this.webRTCBlackboxService.sendEventData(
            SessionEvent.CAMERA_360_STREAM_CREATED,
            tempObject
          );
        });

        this.screen360Publisher.on('streamDestroyed', (event: any) => {
          this.screen360Publisher.off('streamCreated');
          this.screen360Publisher.off('streamDestroyed');
          this.screen360Publisher = null;
          const tempObject: UserSessionStream = {
            userId: event.stream.name,
            reason: 'Stream has been destroyed.',
          };
          this.webRTCBlackboxService.sendEventData(
            SessionEvent.CAMERA_360_STREAM_DESTROYED,
            tempObject
          );
        });

        this.sessionTokbox.connect(this.selfProvider?.WR_token, (error) => {
          // If the connection is successful, initialize a publisher and publish to the session
          if (error) {
            this.selfProvider?.WR_handleError(error);
          } else {
            this.sessionTokbox?.publish(this.screen360Publisher, (err: any) => {
              if (err) {
                switch (err.name) {
                  case 'OT_NOT_CONNECTED':
                    this.selfProvider?.WR_handleError(
                      'Publishing your video failed. You are not connected to the internet.'
                    );
                    break;
                  case 'OT_CREATE_PEER_CONNECTION_FAILED':
                    this.selfProvider?.WR_handleError(
                      'Publishing your video failed. This could be due to a restrictive firewall.'
                    );
                    break;
                  case 'OT_USER_MEDIA_ACCESS_DENIED':
                    this.selfProvider?.WR_handleError(
                      'Please allow access to the Camera and Microphone and try publishing again.'
                    );
                    break;
                  default:
                    this.selfProvider?.WR_handleError(
                      'An unknown error occurred while trying to publish your video. Please try again later.'
                    );
                }
                this.publisher?.destroy();
              }
            });
          }
        });
      } else {
        const tempObject: UserSessionStream = {
          userId: '',
          reason: 'Something went wrong while share 360 screen.',
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.CAMERA_360_STREAM_DESTROYED,
          tempObject
        );
      }
    }
  }

  unsubscribe() {
    this.sessionTokbox?.off();
    // this.sessionTokbox?.off('streamCreated');
    // this.sessionTokbox?.off('streamDestroyed');
    // this.sessionTokbox?.off('connectionCreated');
    // this.sessionTokbox?.off('connectionDestroyed');
    // this.sessionTokbox?.off('sessionDisconnected');
    // this.sessionTokbox?.off('streamPropertyChanged');
    // this.sessionTokbox?.off('signal:' + SignalType.MESSAGE);
    if (this.publisher) {
      this.publisher?.off();
      // this.publisher?.off('streamCreated');
      // this.publisher?.off('streamDestroyed');
    }
    if (this.screenPublisher) {
      this.screenPublisher?.off();
      // this.screenPublisher?.off('streamCreated');
      // this.screenPublisher?.off('streamDestroyed');
    }
    if (this.screen360Publisher) {
      this.screen360Publisher?.off();
      // this.screen360Publisher?.off('streamCreated');
      // this.screen360Publisher?.off('streamDestroyed');
    }
    if (this.canvasPublisher) {
      this.canvasPublisher?.off();
      // this.screen360Publisher?.off('streamCreated');
      // this.screen360Publisher?.off('streamDestroyed');
    }
    this.sessionTokbox = undefined;
    this.publisher = undefined;
    this.screenPublisher = undefined;
    this.screen360Publisher = undefined;
    this.canvasPublisher = undefined;
  }

  subscribeStream(
    stream: any,
    subscriberElementId: any,
    subscriberProperties?: ISubscriberProperties
  ) {
    if (!subscriberProperties) {
      subscriberProperties = {};
    }
    subscriberProperties.insertMode = 'append';
    subscriberProperties.width = '100%';
    subscriberProperties.height = '100%';
    subscriberProperties.showControls = false;
    // subscriberProperties.subscribeToAudio = false;
    // subscriberProperties.subscribeToVideo = true;

    if (this.selfProvider?.WR_isSubscriber) {
      const subscriber: OT.Subscriber | undefined =
        this.selfTokbox?.sessionTokbox?.subscribe(
          stream,
          subscriberElementId,
          subscriberProperties,
          this.selfProvider.WR_handleError
        );

      const tempObject: StreamDetails = {
        name: stream.name,
        stream: subscriber,
      };
      this.webRTCBlackboxService.sendEventData(
        SessionEvent.SUBSCRIBER_DETAILS,
        tempObject
      );
    }
  }

  cycleVideo(): void {
    if (this.sessionTokbox) {
      this.publisher.cycleVideo().then();
    }
  }

  shareAImodel(canvas: any) {
    if (this.sessionTokbox) {
      const publisherOptions: any = {
        // insertMode: 'append',
        name: this.selfProvider?.WR_currentUserId + '_ai_model',
        width: '0px',
        height: '0px',
        videoSource: canvas.captureStream(30).getVideoTracks()[0], // Use canvas.captureStream at 30 fps and pass the video track to the Publisher
      };
      this.canvasPublisher = OT.initPublisher(
        '',
        publisherOptions,
        this.selfProvider?.WR_handleError
      );
      this.sessionTokbox?.publish(this.canvasPublisher);
      this.canvasPublisher.on('streamCreated', (event: any) => {
        const tempObject: StreamDetails = {
          name: event.stream.name,
          stream: event.stream,
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.AI_PUBLISHER_STREAM_CREATED,
          tempObject
        );
        // const tempModel = {
        //   UserId: this.selfProvider?.WR_userId,
        //   TenantId: this.selfProvider?.WR_tenantId,
        //   WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
        //   StreamId: event.stream.streamId,
        //   ConnectionId: event.stream.connection.connectionId,
        //   TokenId: this.selfProvider?.WR_token,
        //   StreamType:
        //     this.selfProvider?.WR_StreamTypeEnum[event.stream.videoType],
        // };
        // const ajaxUrl =
        //   this.selfProvider?.WR_SERVER_BASE_URL_API + 'AddStreamApi';
        // this.selfProvider?.callWebApi(
        //   ajaxUrl,
        //   tempModel,
        //   this.ApiCallBack,
        //   'shareCanvasScreen'
        // );
      });

      this.canvasPublisher.on('streamDestroyed', (event: any) => {
        this.canvasPublisher.off();
        this.canvasPublisher = undefined;
        const tempObject: UserSessionStream = {
          userId: event.stream.name,
          reason: 'Stream has been destroyed.',
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.AI_PUBLISHER_STREAM_DESTROYED,
          tempObject
        );
      });
    }
  }

  shareAImodel360(canvas: any) {
    if (this.sessionTokbox) {
      const publisherOptions: any = {
        // insertMode: 'append',
        name: this.selfProvider?.WR_currentUserId + '_360ai_model',
        width: '0px',
        height: '0px',
        videoSource: canvas.captureStream(30).getVideoTracks()[0], // Use canvas.captureStream at 30 fps and pass the video track to the Publisher
      };
      this.canvasPublisher = OT.initPublisher(
        '',
        publisherOptions,
        this.selfProvider?.WR_handleError
      );
      this.sessionTokbox?.publish(this.canvasPublisher);
      this.canvasPublisher.on('streamCreated', (event: any) => {
        const tempObject: StreamDetails = {
          name: event.stream.name,
          stream: event.stream,
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.AI_PUBLISHER_STREAM_CREATED,
          tempObject
        );
        // const tempModel = {
        //   UserId: this.selfProvider?.WR_userId,
        //   TenantId: this.selfProvider?.WR_tenantId,
        //   WebRTCSessionId: this.selfProvider?.WR_webRTCsessionId,
        //   StreamId: event.stream.streamId,
        //   ConnectionId: event.stream.connection.connectionId,
        //   TokenId: this.selfProvider?.WR_token,
        //   StreamType:
        //     this.selfProvider?.WR_StreamTypeEnum[event.stream.videoType],
        // };
        // const ajaxUrl =
        //   this.selfProvider?.WR_SERVER_BASE_URL_API + 'AddStreamApi';
        // this.selfProvider?.callWebApi(
        //   ajaxUrl,
        //   tempModel,
        //   this.ApiCallBack,
        //   'shareCanvasScreen'
        // );
      });

      this.canvasPublisher.on('streamDestroyed', (event: any) => {
        this.canvasPublisher.off();
        this.canvasPublisher = undefined;
        const tempObject: UserSessionStream = {
          userId: event.stream.name,
          reason: 'Stream has been destroyed.',
        };
        this.webRTCBlackboxService.sendEventData(
          SessionEvent.AI_PUBLISHER_STREAM_DESTROYED,
          tempObject
        );
      });
    }
  }
}
