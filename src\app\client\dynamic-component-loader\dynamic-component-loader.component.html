<ng-container #dynamicComponentLoader>
    <mat-sidenav-container hasBackdrop="false">
        <mat-sidenav-content class="sidenav-container">
            <ng-container *ngFor="let module of consoleLoaderJson.modules; let moduleIndex = index">
                <ng-container *ngIf="module.moduleID == curModuleId">
                    <ng-container *ngIf="module.nestingType === nestingTypes.ColumnsInRows">
                        <ng-container *ngIf="module.actions">
                            <ng-container *ngFor="let action of module.actions">
                                <button *ngIf="action.type != actionTypes.Custom" mat-button class="btn-2 t-xs mr-2">
                                    {{action.Tooltip}}
                                </button>
                                <ng-container *ngIf="action.type == actionTypes.Custom"
                                    [ngTemplateOutlet]="addViewNotesButton">
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        <div [ngClass]="module.class" [gdColumn]="module.gdColumn"
                            [gdColumn.lt-lg]="module.gdColumnltlg" [gdColumn.lt-md]="module.gdColumnltmd"
                            [gdColumn.lt-sm]="module.gdColumnsltsm" [fxLayout]="module.fxLayout"
                            [fxFlex]="module.fxFlex" [fxFlex.lt-md]="module.fxFlexltmd"
                            [fxLayoutAlign]="module.fxLayoutAlign" [fxHide.xs]="module.fxHidexs"
                            [fxHide.sm]="module.fxHidesm" [gdColumns]="module.gdColumns"
                            [gdColumns.lt-lg]="module.gdColumnsltlg" [gdColumns.lt-md]="module.gdColumnsltmd"
                            [gdColumns.lt-sm]="module.gdColumnsltsm" [gdGap]="module.gdGap" exaiContainer>
                            <div class="my-0 ltr:pr-4 rtl:pl-4 ltr:mr-4 rtl:ml-4 text-sm font-semibold">
                                <span class="heading">{{module.name}}</span>
                            </div>
                            <ng-container>
                                <exai-dynamic-bread-crumbs [crumbs]="breadCrumbsArray"
                                    (crumbNavigate)="handleCrumbNavigation($event.crumb)">
                                </exai-dynamic-bread-crumbs>
                            </ng-container>
                            <div *ngFor="let row of module.rows;let rowIndex = index" [ngClass]="row.class"
                                [gdColumn]="row.gdColumn" [gdColumn.lt-lg]="row.gdColumnltlg"
                                [gdColumn.lt-md]="row.gdColumnltmd" [gdColumn.lt-sm]="row.gdColumnsltsm"
                                [fxLayout]="row.fxLayout" [fxFlex]="row.fxFlex" [fxFlex.lt-md]="row.fxFlexltmd"
                                [fxLayoutAlign]="row.fxLayoutAlign" [fxHide.xs]="row.fxHidexs"
                                [fxHide.sm]="row.fxHidesm" [gdColumns]="row.gdColumns"
                                [gdColumns.lt-lg]="row.gdColumnsltlg" [gdColumns.lt-md]="row.gdColumnsltmd"
                                [gdColumns.lt-sm]="row.gdColumnsltsm" [gdGap]="row.gdGap">
                                <div *ngFor="let column of row.columns;let columnIndex = index" [ngClass]="column.class"
                                    [gdColumn]="column.gdColumn" [gdColumn.lt-lg]="column.gdColumnltlg"
                                    [gdColumn.lt-md]="column.gdColumnltmd" [gdColumn.lt-sm]="column.gdColumnsltsm"
                                    [fxFlex.lt-md]="column.fxFlexltmd" [fxLayoutAlign]="column.fxLayoutAlign"
                                    [fxHide.xs]="column.fxHidexs" [fxHide.sm]="column.fxHidesm"
                                    [gdColumns]="column.gdColumns" [gdColumns.lt-lg]="column.gdColumnsltlg"
                                    [gdColumns.lt-md]="column.gdColumnsltmd" [gdColumns.lt-sm]="column.gdColumnsltsm"
                                    [gdGap]="column.gdGap">
                                    <ng-container *ngIf="column.widget.type == widgetTypes.DynamicTable ">
                                        <exai-table *ngIf="tableData" class="w-full pt-2"
                                            [tableOptions]="column.widget.tableOptions" [curModuleId]="curModuleId"
                                            [displayedColumns]="column.widget.displayedColumns"
                                            [totalRecords]="tableDataTotalRecords" [dataSource]="tableData"
                                            [selectedIndexObs]="$tabIndex"
                                            (actionPerformed)="handleAction($event,rowIndex,columnIndex)"
                                            (cellClickEvent)="print($event)" (headerClicked)="print($event)"
                                            (add)="openAdd(column.widget.tableOptions.addPopupData,rowIndex,columnIndex)"
                                            (headerDropdownSelection)="print($event)"
                                            (tabSelected)="handleTabChange($event,rowIndex,columnIndex,moduleIndex)"
                                            (checkboxClicked)="print($event)" (toggled)="toggleHandler($event)"
                                            (linkClicked)="handleLinkClick($event)"
                                            [accessData]="column.widget.accessData"
                                            (nextPageDataFetchEvent)="handleServerSidePagination($event,rowIndex,columnIndex)"
                                            (searched)="prevSearchKey = $event.searchKey"
                                            [prevSearchKey]="breadCrumbsArray.length == 2 ? prevSearchKey : null"
                                            [scores]="scores" [states]="states"
                                            (scoreSelected)="handleScoreSelection($event,rowIndex,columnIndex)"
                                            (stateSelected)="handleStateSelection($event,rowIndex,columnIndex)"
                                            (dateSelected)="handleDateSelection($event,rowIndex,columnIndex)"
                                            [prevStateKey]="selectedStateId" (mergeRequested)="mergeRequested($event)"
                                            (certificateSelect)="BulkCertificate($event)"
                                            (preselectedIndex)="storeSelectedPagination($event)"
                                            [preSelecetedIndex]="breadCrumbsArray.length == 2 ? preSelecetedIndex : null"
                                            [breadCrumbsArray]="breadCrumbsArray"
                                            (bookletorderStatusSelect)="bookletorderStatusSelect($event)"
                                            (examModeSelect)="examModeSelect($event)"
                                            (testCenterPreferenceSelect)="testCenterPreferenceSelect($event)">
                                        </exai-table>
                                    </ng-container>
                                    <ng-container *ngIf="column.widget.type == widgetTypes.DynamicSingleEntryTables">
                                        <exai-single-entry-table *ngIf="singleEntryTableData" class="w-full mt-2"
                                            [requestDetails]="column.widget.requestDetails"
                                            [displayedColumns]="column.widget.displayedColumns[0]"
                                            [data]="singleEntryTableData" [accessData]="column.widget.accessData">
                                        </exai-single-entry-table>
                                    </ng-container>
                                    <ng-container *ngIf="column.widget.type == widgetTypes.DynamicProcessLogs">
                                        <exai-progress-bar
                                            class="w-full card cardBorder progressBar shadow-none overflow-auto"
                                            [performlogs]="processLogData" [header]="column.widget.widgetHeader">
                                        </exai-progress-bar>
                                    </ng-container>
                                    <ng-container *ngIf="column.widget.type == widgetTypes.DynamicForms">
                                        <exai-form-view class="w-full" [formJSON]="formJSON"
                                            [userResponse]="userResponse" [formOptions]="column.widget.formOptions"
                                            (openSidenav)="sidenav.toggle()" [element]="recentLinkClickEvent.element">
                                        </exai-form-view>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="module.nestingType === nestingTypes.RowsInColumns">
                        <ng-container *ngIf="module.actions">
                            <ng-container *ngFor="let action of module.actions">
                                <button *ngIf="action.type != actionTypes.Custom" mat-button class="btn-2 t-xs mr-2">
                                    {{action.Tooltip}}
                                </button>
                                <ng-container *ngIf="action.type == actionTypes.Custom"
                                    [ngTemplateOutlet]="addViewNotesButton">
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        <div [ngClass]="module.class" [gdColumn]="module.gdColumn"
                            [gdColumn.lt-lg]="module.gdColumnltlg" [gdColumn.lt-md]="module.gdColumnltmd"
                            [gdColumn.lt-sm]="module.gdColumnsltsm" [fxLayout]="module.fxLayout"
                            [fxFlex]="module.fxFlex" [fxFlex.lt-md]="module.fxFlexltmd"
                            [fxLayoutAlign]="module.fxLayoutAlign" [fxHide.xs]="module.fxHidexs"
                            [fxHide.sm]="module.fxHidesm" [gdColumns]="module.gdColumns"
                            [gdColumns.lt-lg]="module.gdColumnsltlg" [gdColumns.lt-md]="module.gdColumnsltmd"
                            [gdColumns.lt-sm]="module.gdColumnsltsm" [gdGap]="module.gdGap" exaiContainer>
                            <div *ngFor="let column of module.columns;let columnIndex = index" [ngClass]="column.class"
                                [gdColumn]="column.gdColumn" [gdColumn.lt-lg]="column.gdColumnltlg"
                                [gdColumn.lt-md]="column.gdColumnltmd" [gdColumn.lt-sm]="column.gdColumnsltsm"
                                [fxLayout]="column.fxLayout" [fxFlex]="column.fxFlex" [fxFlex.lt-md]="column.fxFlexltmd"
                                [fxLayoutAlign]="column.fxLayoutAlign" [fxHide.xs]="column.fxHidexs"
                                [fxHide.sm]="column.fxHidesm" [gdColumns]="column.gdColumns"
                                [gdColumns.lt-lg]="column.gdColumnsltlg" [gdColumns.lt-md]="column.gdColumnsltmd"
                                [gdColumns.lt-sm]="column.gdColumnsltsm" [gdGap]="column.gdGap">
                                <ng-container *ngIf="columnIndex == 0">
                                    <div class="my-0 ltr:pr-4 rtl:pl-4 ltr:mr-4 rtl:ml-4 text-sm font-semibold">
                                        <span class="heading">{{module.name}}</span>
                                    </div>
                                    <ng-container>
                                        <exai-dynamic-bread-crumbs [crumbs]="breadCrumbsArray"
                                            (crumbNavigate)="handleCrumbNavigation($event.crumb)">
                                        </exai-dynamic-bread-crumbs>
                                    </ng-container>
                                </ng-container>
                                <div *ngFor="let row of column.rows;let rowIndex = index" [ngClass]="row.class"
                                    [gdColumn]="row.gdColumn" [gdColumn.lt-lg]="row.gdColumnltlg"
                                    [gdColumn.lt-md]="row.gdColumnltmd" [gdColumn.lt-sm]="row.gdColumnsltsm"
                                    [fxLayout]="row.fxLayout" [fxFlex]="row.fxFlex" [fxFlex.lt-md]="row.fxFlexltmd"
                                    [fxLayoutAlign]="row.fxLayoutAlign" [fxHide.xs]="row.fxHidexs"
                                    [fxHide.sm]="row.fxHidesm" [gdColumns]="row.gdColumns"
                                    [gdColumns.lt-lg]="row.gdColumnsltlg" [gdColumns.lt-md]="row.gdColumnsltmd"
                                    [gdColumns.lt-sm]="row.gdColumnsltsm" [gdGap]="row.gdGap">
                                    <ng-container *ngIf="row.widget.type == widgetTypes.DynamicTable">
                                        <exai-table *ngIf="tableData" class="w-full pt-2"
                                            [tableOptions]="row.widget.tableOptions" [curModuleId]="curModuleId"
                                            [totalRecords]="tableDataTotalRecords"
                                            [displayedColumns]="row.widget.displayedColumns" [dataSource]="tableData"
                                            [selectedIndexObs]="$tabIndex"
                                            (actionPerformed)="handleAction($event,rowIndex,columnIndex)"
                                            (cellClickEvent)="print($event)" (headerClicked)="print($event)"
                                            (add)="openAdd(row.widget.tableOptions.addPopupData,rowIndex,columnIndex)"
                                            (headerDropdownSelection)="print($event)"
                                            (tabSelected)="handleTabChange($event,rowIndex,columnIndex,moduleIndex)"
                                            (checkboxClicked)="print($event)" (toggled)="toggleHandler($event)"
                                            (linkClicked)="handleLinkClick($event)" [accessData]="row.widget.accessData"
                                            (nextPageDataFetchEvent)="handleServerSidePagination($event,rowIndex,columnIndex)"
                                            (searched)="prevSearchKey = $event.searchKey"
                                            [prevSearchKey]="breadCrumbsArray.length == 2 ? prevSearchKey : null"
                                            [states]="states"
                                            (stateSelected)="handleStateSelection($event,rowIndex,columnIndex)"
                                            (dateSelected)="handleDateSelection($event,rowIndex,columnIndex)"
                                            (mergeRequested)="mergeRequested($event)"
                                            (certificateSelect)="BulkCertificate($event)"
                                            (preselectedIndexEvent)="preSelecetedIndex = $event.preSelectedIndex"
                                            [preSelecetedIndex]="breadCrumbsArray.length == 2 ? preSelecetedIndex : null"
                                            [breadCrumbsArray]="breadCrumbsArray">
                                        </exai-table>
                                    </ng-container>
                                    <ng-container *ngIf="row.widget.type == widgetTypes.DynamicSingleEntryTables">
                                        <exai-single-entry-table *ngIf="singleEntryTableData" class="w-full mt-2"
                                            [displayedColumns]="row.widget.displayedColumns[0]"
                                            [data]="singleEntryTableData" [requestDetails]="row.widget.requestDetails"
                                            [accessData]="row.widget.accessData">
                                        </exai-single-entry-table>
                                    </ng-container>
                                    <ng-container *ngIf="row.widget.type == widgetTypes.DynamicProcessLogs">
                                        <app-prg-bar-template *ngIf="processLogData"
                                            class="w-full mt-4 card cardBorder progressBar shadow-none overflow-auto"
                                            [performlogs]="processLogData" [header]="row.widget.widgetHeader">
                                        </app-prg-bar-template>
                                    </ng-container>
                                    <ng-container *ngIf="row.widget.type == widgetTypes.DynamicForms">
                                        <exai-form-view *ngIf="formJSON" class="w-full" [formJSON]="formJSON"
                                            [userResponse]="userResponse" [formOptions]="row.widget.formOptions"
                                            [registerForExam]="module.moduleID == 'schedule-accommodation-details'"
                                            (openSidenav)="openSidenavAndSetViewId($event)"
                                            [element]="recentLinkClickEvent.element">
                                        </exai-form-view>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </ng-container>
            </ng-container>
        </mat-sidenav-content>
        <mat-sidenav #sidenav mode="over" class="temporary-sidenav-class1 cardBorder" fixedBottomGap="0" fixedTopGap="0"
            fixedInViewport="true" position='end'>
            <ng-container [ngTemplateOutlet]="notesSideBar" [ngTemplateOutletContext]="{viewId:notesSidebarViewId}"
                *ngIf="(curWidgetTypes$ | async).includes(widgetTypes.DynamicForms) || curModuleId == 'candidate-details' ||curModuleId == 'view-misconduct' ">
            </ng-container>
        </mat-sidenav>

    </mat-sidenav-container>
</ng-container>
<ng-template #notesSideBar let-viewId="viewId">
    <exai-notes-sidebar [viewId]="viewId" [sidenavRef]="sidenav">
    </exai-notes-sidebar>
</ng-template>
<ng-template #addViewNotesButton>
    <button
        *ngIf="globalUserService.userDetails.value && [Roles.OperationStaff,Roles.SupportingStaff].includes(globalUserService.userDetails.value.roleId) && (this.globalUserService.email !=null && this.globalUserService.email!='' ) && this.globalUserService.migrated != 'Migrated' && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom6" (click)="checkStatus()">
        Check Status
    </button>
    <button
        *ngIf="globalUserService.userDetails.value && [Roles.StateClient].includes(globalUserService.userDetails.value.roleId) && (this.globalUserService.email !=null && this.globalUserService.email!='' ) && this.globalUserService.migrated != 'Migrated' && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom1" (click)="candidateRecord()">
        Download Candidate Record
    </button>
    <button
        *ngIf="globalUserService.userDetails.value && [Roles.OperationStaff,Roles.SupportingStaff].includes(globalUserService.userDetails.value.roleId)&& (this.globalUserService.email !=null && this.globalUserService.email!='' ) && this.globalUserService.migrated != 'Migrated' && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom5" (click)="emulateCandidate()">
        Emulate
    </button>
    <button *ngIf="MisconductStatus !='View Misconduct Details'" mat-button class="btn-2 t-xs mr-2 absolute custom"
        (click)="openSidenavAndSetViewId({whatToOpen:5})">
        Add/View Notes
    </button>

    <button
        *ngIf="(this.globalUserService.personEventId?.misconductStatus == 2 ||this.globalUserService.personEventId?.misconductStatus == null  ) && MisconductStatus =='View Misconduct Details'"
        class="btn-2 t-xs mr-2 absolute misconduct1" (click)="openSidenavAndSetViewId({whatToOpen:10})" mat-button>
        Misconduct Response
    </button>
    <button
        *ngIf="(this.globalUserService.personEventId?.misconductStatus == 2 || this.globalUserService.personEventId?.misconductStatus == null ) && MisconductStatus =='View Misconduct Details'"
        class="btn-3 t-xs mr-2 absolute misconduct width-6" (click)="openConfirmation('Rejected')" mat-button>
        Invalid
    </button>
    <button
        *ngIf="(this.globalUserService.personEventId?.misconductStatus == 2 || this.globalUserService.personEventId?.misconductStatus == null ) && MisconductStatus =='View Misconduct Details'"
        class="btn-1 t-xs mr-2 absolute custom4 width-6" (click)="openConfirmation('Approved')" mat-button>
        Valid
    </button>



    <button
        *ngIf="globalUserService.userDetails.value && globalUserService.userDetails.value.roleId==Roles.SupportingStaff && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom1" (click)="raiseTickets()">
        Raise Ticket For Operation Staff
    </button>

    <button
        *ngIf="globalUserService.userDetails.value && globalUserService.userDetails.value.roleId==Roles.OperationStaff && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom1" (click)="raiseTickets()">
        Raise Ticket For Candidate
    </button>
    <button
        *ngIf="globalUserService.userDetails.value && globalUserService.userDetails.value.roleId==Roles.OperationStaff && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom3" (click)="disableCandidiate()">
        Disable Candidate
    </button>
    <button
        *ngIf="globalUserService.userDetails.value && [Roles.OperationStaff,Roles.SupportingStaff].includes(globalUserService.userDetails.value.roleId) && curModuleId == 'candidate-details' && ($tabIndex | async)== 1 && MisconductStatus !='View Misconduct Details'"
        mat-button class="btn-2 t-xs mr-2 absolute custom4" (click)="getclear()">
        Clear Cart
    </button>
</ng-template>


<!-- the below line has to be commented out when latest json from app.component has to be taken to be put in the console-loader-json.json -->
<!-- <pre style="margin-bottom: 4rem;">
    <mat-icon style="position: absolute; top:20px; right:50px;font-size:1.4rem;cursor:pointer;z-index:100000000"
        [matTooltip]="'Secret Dev button, Click to Copy the entire \'ConsoleLoaderJson\' to Clipboard'" mat-button [cdkCopyToClipboard]="consoleLoaderJson | json">
        file_copy
    </mat-icon>
</pre> -->