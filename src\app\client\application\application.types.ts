export interface eligibilityRoute {
  id: number;
  eligibilityName?: string;
  tenantCode?: string;
  eligibilityRouteDetails?: eligibilityRouteDetails;
}

export interface eligibilityRouteDetails {
  eligibilityRouteDetail: string;
  mandatoryInfo: string;
  optionalInfo: string,
}

export enum FormTypes {
  "Accomodation" = 1,
  "Grievance" = 2,
  "Application" = 3,
  "Demographic" = 4,
  "Certificate_Renewal" = 5,
  "Certificate_Reciprocity" = 6,
  "Certificate_Duplicate" = 7,
  "AbuseAlligation"=9,
  "PreRegistry"=10,
  "problem_report"=11,
  "Excused_Absence" =13,
  "MACEReciporating"= 15,
  "MACERenewal"= 14,
  "SCrenewalReinstate"=17,
  "Cofacilitation" = 18
  
}

export const FormTypesToNames = {
  1: "Accomodation",
  2: "Grievance",
  3: "Application",
  4: "Demographic",
  5: "Certificate Renewal",
  6: "Certificate Reciprocity",
  7: "Certificate Duplicate",
  8: "Survey",
  9: "AbuseAlligation",
  10:"PreRegistry",
  11:"Problem Report",
  13:"Excused Absence",
  18:"NURSE AIDE EVALUATOR QUALITY OBSERVATION"
}

export interface Form {
  formID?: Array<number>,
  formTypeID: Array<number>,
  stateID?: number,
  eligibilityID: number,
  formJSON?: any;
  isSubmitAllowed?: Array<boolean>;
}

export interface PersonFormLog {
  personFormId: number;
  comment: string;
  name: string;
  actionOn: Date;
  reviewer: string;
  formTypeId:number;
}

export interface PersonForm{
  personFormId: number;
  name: string;
  state: string;
  eligiblityRoute: string;
  eligibilityRouteId?: number;
  formCode?: string;
  submittedDate: Date;
  lastUpdatedDate: Date;
  iconUrl: string;
  status: string;
  waitingTime?: any;
}

export interface response {
  response: any;
  formTypeId: Array<FormTypes>;
  personFormId: Array<number>;
  disabled?: Array<boolean>;
  code: string;
}

export const stateAllowstosavedata=[7,5,6]

export const FormStatusToNames={
  1:"Approved",
  2:"Rejected",
  3:"Cancelled",
  4: "Change Request",
  5:"YetToRecieve",
  6:"Saved",
  7:"Submitted",
  8:"Expired",
  9:"Completed"

}