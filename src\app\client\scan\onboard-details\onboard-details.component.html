<!-- <div class="border border-indigo-600"> -->

<div>
  <div class="onBoardingHeader flex justify-between border border-indigo-600">
    <h5 class="font-semibold text-sm" style="color: #0076c1">
      Onboard Candidate Details
    </h5>
    <div
      class="flex justify-end cursor-pointer text-red"
      fxLayout="row"
      mat-dialog-close
    >
      <mat-icon class="text-sm pt-1 justify-end">close</mat-icon>
    </div>
  </div>

  <div
    class="onboardingContent pl-3 pt-3 flex flex-col border border-indigo-600"
  >
    <form action="" #userForm="ngForm" [formGroup]="onboardFormGroup">
      <div class="flex flex-row mb-4">
        <mat-checkbox
          class="mr-1"
          color="primary"
          (change)="consentCheck()"
        ></mat-checkbox>
        <span class="font-weight-bold signatureConsentText">
          I acknowledge that the information below is correct and for the purposes of testing, I give my consent for my photo and identification to be scanned and captured for verification.
        </span>
      </div>

      <div class="flex flex-row mb-4">
        <div class="col">
          <canvas
            id="Signaturepad"
            class="signaturebox mt-2"
            (mousedown)="signpadonMouseDown($event)"
            width="400"
            height="75"
            (mousemove)="signpadonMouseMove($event)"
          ></canvas>
        </div>
        <div class="flex items-center" *ngIf="signatureUploaded">
          <mat-icon class="text-2xl signatureDone">check_circle_outline</mat-icon>
        </div>
      </div>
      <div class="flex flex-row mb-4">
        <div>
          <button
            class="btn-1 text-xs"
            mat-button
            type="button"
            (click)="clear()"
          >
          <mat-icon>refresh</mat-icon>
          </button>
          <button
            class="btn-1 text-xs"
            mat-button
            type="button"
            (click)="signatureUpload()"
            *ngIf="allosignatureUpload"
          >
            Upload
          </button>
        </div>
      </div>
      <div class="flex flex-row mb-4">
        <span class="mr-2" fxLayout="column">Appointment Id:</span>
        <span class="spanMargin bold font-bold" fxLayout="column">{{
          appointmentId
        }}</span>
      </div>

      <div class="flex flex-row mb-4">
        <span class="mr-2">First Name : </span>
        <span class="spanMargin bold ml-5 font-bold">{{ firstName }}</span>
      </div>

      <div class="flex flex-row mb-4">
        <span class="mr-2">Last Name:</span>
        <span class="spanMargin bold ml-6 font-bold">{{ lastName }}</span>
      </div>

      <div class="flex flex-row mb-4">
        <span class="mr-2">Candidate Id <span class="mandatory">*</span>:</span>
        <div class="spanMargin ml-6">
          <button
            *ngIf="!candidateIdFileName"
            class="btn-1 text-xs"
            mat-button
            type="button"
            (click)="launch()"
          >
            Upload
          </button>
          <span class="bold ml-6 font-bold" *ngIf="candidateIdFileName">{{
            candidateIdSystemFileName
          }}</span>
        </div>
      </div>

      <div class="flex flex-row mb-4">
        <span class="mr-2"
          >Candidate Photo <span class="mandatory">*</span> :</span
        >
        <div class="spanMargin">
          <button
            *ngIf="!candidateFaceFileName"
            class="btn-1 text-xs"
            mat-button
            type="button"
            (click)="launchFaceId()"
          >
            Upload
          </button>
          <span class="bold font-bold" *ngIf="candidateFaceFileName">{{
            candidateFaceSystemFileName
          }}</span>
        </div>
      </div>

      <div class="flex flex-row mb-4">
        <span class="mr-2">Notes:</span>
        <div>
          <textarea
            class="border-slate-400 ml-2"
            matInput
            formControlName="notes"
            placeholder="Notes"
            cols="25"
            rows="7"
          >
          </textarea>
        </div>
      </div>
      <div class="flex flex-row justify-center">
        <button
          class="btn-1 text-xs"
          mat-button
          type="button"
          [disabled]="
            !candidateFaceFileName ||
            !candidateIdFileName ||
            !isConsentCheckbox ||
            !signatureUploaded ||
            !isSignatureDisabled
          "
          (click)="submit()"
        >
          Submit
        </button>
      </div>
    </form>
  </div>
</div>

<!-- </div> -->
<!-- 1st Card Eligibility Route -->
<!-- <div class="justify-start" gdColumn="1 / 1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
      <div class="card shadow-none cardBorder overflow-auto" fxFlex="auto">
        <form #userForm="ngForm" [formGroup]="onboardFormGroup">         
          <div class=" state eligibility1 edit eligible" fxLayout="column" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr">
            <div fxLayout="row" class="w-full items-baseline mrg">
               <span class="mrg-right" fxLayout="column">Appointment Id:</span>
               <span class="bold ml-2" fxLayout="column">{{appointmentId}}</span>
            </div>
            <div fxLayout="row"  gdColumns.lt-sm="1fr" class="w-full items-baseline mrg columns-2">
                <span class="mrg-right" fxLayout="column">First Name : </span>
                <span class="bold ml-2" fxLayout="column">{{firstName}}</span>
             </div>
             <div fxLayout="row" class="w-full items-baseline mrg columns-2">
                <span class="mrg-right" fxLayout="column">Last Name:</span>
                <span class="bold ml-2" fxLayout="column">{{lastName}}</span>
             </div>

             <div fxLayout="row" class="w-full items-baseline mrg columns-2">
                <span class="mrg-right" fxLayout="column">Candidate Id Proof:</span>
                <div fxLayout="column">
                  <button *ngIf="!candidateIdFileName" class=" btn-1 text-xs" mat-button type="button" (click)="launch()">
                    Upload
                  </button>
                  <span class="bold ml-2" *ngIf="candidateIdFileName">{{candidateIdFileName}}</span>
                </div>
               
             </div>

             <div fxLayout="row" class="w-full items-baseline mrg">
                <span class="mrg-right" fxLayout="column">Candidate Photo:</span>
                <div fxLayout="column">
                  <button *ngIf="!candidateFaceFileName" class=" btn-1 text-xs" mat-button type="button" (click)="launchFaceId()">
                    Upload
                  </button>
                  <span class="bold ml-2" *ngIf="candidateFaceFileName">{{candidateFaceFileName}}</span>
                </div>               
             </div>

             <div fxLayout="row" class="w-full items-baseline mrg">
              <span class="mrg-right" fxLayout="column">Notes:</span>
              <div fxLayout="column">
                <textarea class="editor flex-wrap" matInput formControlName="notes" placeholder="Notes"
                                cols="30" rows="8">
                </textarea>
              </div>               
           </div>
           </div>
          
             <div class="px-6 eligibility1 edit textbox eligible" fxLayout="column">
              <div class="flex justify-center mb-2">
                <button class=" btn-1 text-xs" mat-button type="button" (click)="submit()">
                  Submit
                </button>
              </div> 
            </div>
        </form>
      </div>
      
      <div class="px-6 eligibility1 edit textbox eligible" fxLayout="column">
          <div class="flex justify-end mb-2">
            <button class=" btn-1 text-xs" mat-button type="button" (click)="submit()">
              Submit
            </button>
          </div> 
        </div>
    </form>
  </div>
</div> -->
