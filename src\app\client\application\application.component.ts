import {
  Component,
  <PERSON>ement<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
  ViewChildren,
  Input
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { ActivatedRoute, ParamMap, Router } from "@angular/router";
import { DynamicFormControlEvent } from "@ng-dynamic-forms/core";
import { Action, Store } from "@ngrx/store";
import { BehaviorSubject, Observable, Subject, Subscription } from "rxjs";
import {
  sectionCompleteEvent,
  validatedSubmission,
} from "src/app/core/common-component/examroom-formbuilder/form-builder.types";
import { LanguageService } from "src/app/core/language.service";
import { Form, FormTypes, response } from "./application.types";
import { selectCandidateId, selectForm, selectLatestPersonFormId, selectPersonEventId, selectPersonFormLogs, selectUserResponse } from "./state/application.selectors";
import { ApplicationState } from "./state/application.state";
import { PopUpComponent } from "./pop-up/pop-up.component";
import { v4 as uuidv4 } from "uuid";
import { GlobalUserService, userData } from "src/app/core/global-user.service";
import { getFormJson, setBasicDetails, saveUserResponse, deleteUserResponse, getUserResponse, clearApplicationState, getPersonFormLogs, downloadAccTypeForm } from './state/application.actions';
import lodash from "lodash";
import { ChangeDetectionStrategy } from '@angular/core';
import { debounceTime, takeUntil } from "rxjs/operators";
import { PopFromBreadCrumbUptoIndex, PushIntoBreadCrumb, setRecentLinkClickEvent } from "../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import { crumb } from "src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types";
import { moduleTypes } from "../dynamic-component-loader/dynamic-component-loader.types";
import { cellClickEvent } from "src/app/core/common-component/table/dynamic-table-types";
import { breadCrumbsSelector, recentLinkClickEventSelector } from "../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import { PersonFormLog } from "src/app/core/common-component/progress-bar/progress-bar.types";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { SnackbarService } from "src/app/core/snackbar.service";
import { statuses } from "../dynamic-component-loader/dynamic-component-loader.types";
import { FormTypesToNames } from './application.types';
import { AccomodationFormAccTypeFieldIds, AccomodationFormsSectionOneIds, AccomodationYesOrNoFieldIds, ProblemReport } from "./application-static-data";
import { FormBuilderComponent } from "src/app/core/common-component/examroom-formbuilder/form-builder.component";
import { FormBuilderService } from "src/app/core/common-component/examroom-formbuilder/form-builder.service";
import { Location } from '@angular/common';
import { HttpService } from "src/app/core/http.service";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { MatAutocompleteTrigger } from "@angular/material/autocomplete";
@Component({
  selector: "exai-application",
  templateUrl: "./application.component.html",
  styleUrls: ["./application.component.scss"],
  changeDetection: ChangeDetectionStrategy.Default,
})
export class ApplicationComponent implements OnInit, OnDestroy {
  formTypeId:number
  typeOfForm:any;
  filteredOptions:Array<any> =[];
  SearchDetails:any
  FormTypes = FormTypes;
  options:Array<any>=[]
  TestReferenceId:number
  TestRefrenceName:string
  NonEditData:boolean 
  downloadAccTypeForm = downloadAccTypeForm;
  FormTypesToNames = FormTypesToNames;
  private unsubscribe: Subject<any> = new Subject();
  AcceptanceName:boolean
  radioselect = new FormControl("");
  radioselectAcceptance = new FormControl("");
  INFValue:FormGroup
  examTypeModels=[{id: 1, name: 'INF (In-facility)',checked: false},{id: 2, name: 'RTS (Regional Testing Center)',checked: false,}]
  Acceptance=[{id: 1, name: 'Yes',checked: false}]
   INFSelected :boolean = false
  AcceptanceValue:boolean 

  constructor(
    private dialog: MatDialog,
    public lngSrvc: LanguageService,
    public store: Store<ApplicationState>,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public globalUserService: GlobalUserService,
    private http: HttpClient,
    private snackbar: SnackbarService,
    private services:FormBuilderService,
    private httpService :HttpService,
    private location: Location,
    private fb:FormBuilder
  ) { }
  sectionsForProgress: Array<sectionCompleteEvent> = [];
  validation_messages = {
    testId: [{ type: "pattern", message: this.globalUserService.INF_Validation }],
  };
  checkProgressOfSections: boolean = false;


  checkProgressOfSectionsfn(): void {
    if (this.form) {
      switch (this.form.formTypeID[0]) {
        case FormTypes.Application:
        case FormTypes.Certificate_Renewal:
        case FormTypes.Certificate_Duplicate:
        case FormTypes.Certificate_Reciprocity:
        case FormTypes.Accomodation:

          this.checkProgressOfSections = !!this.sectionsForProgress.find(x =>{
            return this.form.formTypeID[0] ==FormTypes.Application &&  ![15,20,22463,22613].includes(this.SearchDetails.stateId) && !['117','118','63','64','22331'].includes(this.SearchDetails.eligibilityRouteId)?  (x.status == "INVALID" || x.status == 'UNTOUCHED' || this.INFValue.get('infcode')?.value.length < 8 || !this.radioselectAcceptance.value) : (x.status == "INVALID" || x.status == 'UNTOUCHED') 
          });
          break;
        case FormTypes.Grievance:
        case FormTypes.Demographic:
        case FormTypes.Excused_Absence:
          this.checkProgressOfSections = !!this.sectionsForProgress.find(x => x.status == 'INVALID' || x.status == 'UNTOUCHED');
          break;
          case FormTypes.Cofacilitation:
            this.checkProgressOfSections = !!this.sectionsForProgress.find(x => x.status == 'INVALID' || x.status == 'UNTOUCHED');
            break;
        case FormTypes.problem_report:
        // cases for reciprocity and renewal form can be put here
        default:
          this.checkProgressOfSections = true;
      }
    }
  }

  form: Form = null;
  userResponse: BehaviorSubject<response> = new BehaviorSubject<response>(null);
  userResponse$: Observable<response> = this.userResponse.asObservable();
  fetchedUserResponse: any = null;
  breadCrumbsArray: crumb[] = null;
  recentLinkClickEvent: cellClickEvent = null;
  userDetailsSub: Subscription;

  isAccomodationSelected: boolean = false;
  isSubmit: boolean = false;
  permanentlySaveResponse: boolean = false;
  stateCode:string
  candidateId: number = null;
  personEventId: number = null;
  personformId:number;
  code: string = null;
  eventSku;

  ApplicationPerformLogs: PersonFormLog[] = [];
  AccomodationPerformLogs: PersonFormLog[] = [];
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger: MatAutocompleteTrigger;
  accTypeFormSysFileName: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  @ViewChild("submitApplicationButton") submitApplicationButton: ElementRef;
  @ViewChildren("submitButton") submitButtonRef: Array<ElementRef>;
  @ViewChild("accFormDownloadButton", { static: false }) accFormDownloadButton: ElementRef<HTMLDivElement>;

  @ViewChild("formBuilder") formBuilder: FormBuilderComponent;

  ngOnInit(): void {
    this.store.dispatch(clearApplicationState());
    this.store.dispatch(PushIntoBreadCrumb({
      crumb: {
        label: 'Application',
        navigationMetaData: {
          moduleID: 'application',
          linkClickEvent: null,
          navigateToType: moduleTypes.Independent,
        }
      }
    }));
    this.activatedRoute.paramMap.pipe(takeUntil(this.unsubscribe)).subscribe((paramMap: any) => {
   // var params =Object.keys(paramMap.params ).length !== 0 ? paramMap.params : customParam.customParams;
      var params = paramMap.params
      this.eventSku = params.eventSku
      if(params.formTypeId == "18"){
        this.httpService.getEventDetail(this.eventSku).subscribe((data) =>{
          let naeData = data
          sessionStorage.setItem('naeData', JSON.stringify(naeData));
        })
      }
      this.SearchDetails = paramMap.params;
      if (params.formTypeId && params.candidateId && params.eligibilityRouteId && params.stateId) {
        this.formTypeId =Number(params.formTypeId)
        this.store.dispatch(setBasicDetails({
          formTypeId: Number(params.formTypeId),
          candidateId: Number(params.candidateId)?Number(params.candidateId) :null,
          eligibilityRouteId: Number(params.eligibilityRouteId),
          stateId: Number(params.stateId),
          personFormId: params.personFormId ? Number(params.personFormId) : null,
          code: params.code ? params.code : null,
          personEventId: params.personEventId ? Number(params.personEventId) : null
        }));
        if ((params.code || params.personFormId !=undefined )&& this.formTypeId != 18 ) {
          // this area handles editing of an existing form
          this.code = params.code;
          this.store.dispatch(getUserResponse({ code: this.code, personFormId: params.personFormId }));
          this.store.dispatch<Action>(getPersonFormLogs({ code: paramMap.params.code, personFormId: params.personFormId }));
        }
        // this.router.navigateByUrl('/application',{skipLocationChange: true});
      }
    })
    this.store.select(selectPersonFormLogs).subscribe((performlogs: any) => {
      if (performlogs && performlogs.length > 0) {
        performlogs.forEach((ele: PersonFormLog) => {
          if (ele.formTypeId == FormTypes.Accomodation) {
            this.AccomodationPerformLogs.push(ele)
          }
          else {
            this.ApplicationPerformLogs.push(ele)
          }
        });
      }
    });

    this.INFValue = this.fb.group({
      infcode:['',[Validators.required ,Validators.maxLength(8)]],

    
   })


    this.store.select(recentLinkClickEventSelector).subscribe(data=>{
   
      this.recentLinkClickEvent = data

      if( this.recentLinkClickEvent !=null){
        if( this.recentLinkClickEvent.element.status !='Drafted' && this.recentLinkClickEvent.element.status !='Change Request' && this.recentLinkClickEvent.element.status !='Training Program Change Request'){
          this.radioselect.setValue(this.recentLinkClickEvent.element.testingPreferenceName)
          let isTestCenterAcknowledge = this.recentLinkClickEvent.element.isTestCenterAcknowledge == false?"No":'Yes'
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(this.recentLinkClickEvent.element.testingPreferenceId,isTestCenterAcknowledge)
          this.INFValue = this.fb.group({
            infcode:[this.recentLinkClickEvent.element.testCenterId?this.recentLinkClickEvent.element.testCenterId:'',[Validators.required ,Validators.maxLength(8)]],
         })
          this.NonEditData = true
          this.INFValue.get('infcode').disable()
        }else{
          this.NonEditData = false
          this.radioselect.setValue(this.recentLinkClickEvent.element.testingPreferenceName)
          let isTestCenterAcknowledge = this.recentLinkClickEvent.element.isTestCenterAcknowledge == false && this.recentLinkClickEvent.element.isTestCenterAcknowledge !=null?"No":this.recentLinkClickEvent.element.isTestCenterAcknowledge !=null?'Yes':null
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(this.recentLinkClickEvent.element.testingPreferenceId,isTestCenterAcknowledge)
          this.INFValue = this.fb.group({
            infcode:[this.recentLinkClickEvent.element.testCenterId?this.recentLinkClickEvent.element.testCenterId:'',[Validators.required ,Validators.maxLength(8)]],
         })
        }
      }else{
        this.INFValue = new FormGroup({
          infcode:new FormControl("INF", [Validators.required,Validators.maxLength(8)]),
       })
      }
     
    })
    this.store.select(selectForm).pipe(takeUntil(this.unsubscribe)).subscribe((x: Form) => {
      if (x) {
        if (!this.form || !this.form.formJSON) {
          this.form = x;
          if (!this.form.formJSON) {
            this.userDetailsSub = this.globalUserService.userDetails.subscribe((value: userData) => {
              if (value) {
                
                this.typeOfForm=this.form.formTypeID[0]
                this.services.editFormValue=this.typeOfForm;
                
                this.store.dispatch(getFormJson(this.form));
               
              }
            })
          }
        }
        else if (x.formJSON && this.form.formID != x.formID) {
          if (this.userResponse.value == null || this.userResponse.value.response.length == this.form.formJSON.sections.length)
            (document.querySelectorAll('#submitButton')[0] as any).click();
          this.modifyFormJson(null, x);
        }
        if (this.form && FormTypes.AbuseAlligation == this.form.formTypeID[0]) {
          this.lngSrvc.curLangObj.value.saveDraft = "Submit";
        }
        else
          this.lngSrvc.curLangObj.value.saveDraft = "Save as Draft";
      }
    });
    this.store.select(selectPersonEventId).subscribe((x: number) => this.personEventId = x);
    this.store.select(selectCandidateId).pipe(takeUntil(this.unsubscribe)).subscribe((x: number) => this.candidateId = x);
    this.store.select(selectLatestPersonFormId).pipe(takeUntil(this.unsubscribe)).subscribe((x: number) => {
      if (x) this.userResponse.next({
        formTypeId: this.userResponse.value.formTypeId,
        personFormId: this.getUnique([...this.userResponse.value.personFormId, x]),
        response: this.userResponse.value.response,
        disabled: this.userResponse.value.disabled,
        code: this.code
      })
    });
    this.store.select(selectUserResponse).pipe(takeUntil(this.unsubscribe)).subscribe((x: Array<any>) => {
      if (x) {
        this.code = x[0].code;
        this.fetchedUserResponse = x;
        if (x.findIndex((x: any) => { return x.formTypeId == FormTypes.Accomodation }) > -1) {
          this.isAccomodationSelected = true;
          this.store.dispatch(getFormJson({
            formTypeID: [FormTypes.Accomodation],
            eligibilityID: this.form.eligibilityID,
            stateID: this.form.stateID,
            isSubmitAllowed: [true],
          }))
        }
        if (x[0].personEventId) this.personEventId = x[0].personEventId;
        this.userResponse.next({
          formTypeId: [...x.map((x: any) => { return x.formTypeId })],
          code: this.code,
          personFormId: [...x.map((x: any) => { return x.id })],
          disabled: this.getDisabled(x),
          response: this.parseResponse(x)
        })
      }
    });
    this.store.select(breadCrumbsSelector).pipe(takeUntil(this.unsubscribe)).subscribe((x: any) => {
        this.breadCrumbsArray = x
      }
      );
    this.accTypeFormSysFileName.pipe(debounceTime(0), takeUntil(this.unsubscribe)).subscribe((value: string) => {
      if (value) this.accFormDownloadButton.nativeElement.scrollIntoView({ behavior: 'smooth' });
    })

    this._filter(this.INFValue.value.infcode)
  }




  submit(submit: boolean, savePermanently: boolean) {
    this.isSubmit = submit;
    this.permanentlySaveResponse = savePermanently;
  
  if (this.checkProgressOfSections && this.formTypeId !=FormTypes.problem_report){
        alert('Some step at the process are missing, please verify and try again.');
     }      
     else
     this.forceSubmitValidation();
  }

  private _filter(value: string) {
    const filterValue = value.toLowerCase();
     this.filteredOptions = this.options.filter(option => option.testCenterId.toLowerCase().includes(filterValue));
     console.log(this.filteredOptions)
  }

  forceSubmitValidation() {
    this.formBuilder.forceSubmitWithValidation();
  }

  getDisabled(x: Array<any>) {
    if (x.length == 1) {
      // only one form is there
      var parsedRes = JSON.parse(x[0].formResponse);
      return Array.isArray(parsedRes) ? getMappedDisabled(x[0], parsedRes) : getMappedDisabled(x[0], parsedRes.formValue);
    }
    else {
      var parsedRes1 = JSON.parse(x[0].formResponse);
      var retValue = getMappedDisabled(x[0], parsedRes1);
      if (x[1].formTypeId == FormTypes.Accomodation) {
        retValue[retValue.length - 1] = !(x[1].statusId == statuses.Drafted || x[1].statusId == statuses.ChangeRequest)
      }
      return retValue;
    }
    function getMappedDisabled(parent: any, response: Array<any>) {
      return response.map((x: any) => { return !(parent.statusId == statuses.Drafted || parent.statusId == statuses.ChangeRequest) });
    }
  }
  get getWhetherAtleastOneEnabled() {
    if (this.userResponse.value)
      return this.userResponse.value.disabled.findIndex((x: boolean) => { return !x }) > -1;
    else return true
  }
  parseResponse(x: Array<any>) {
    if (x.length == 1) {
      // only one form is there
      var parsedRes = JSON.parse(x[0].formResponse);
      return Array.isArray(parsedRes) ? parsedRes : parsedRes.formValue;
    }
    else {
      // we have both application and accomodation
      var parsedRes1 = JSON.parse(x[0].formResponse);
      var parsedRes2 = JSON.parse(x[1].formResponse);
      if (lodash.isEqual(parsedRes1, parsedRes2)) {
        // this means the form was filled by operation staff
        return parsedRes1;
      }
      else {
        // change request problem
        // parsedRes2[parsedRes.length-1] contains the latest accomodation from
        // and the parsedRes1[0:parsedRes1.length-2] contains the latest application form
        parsedRes1[parsedRes1.length - 1] = parsedRes2[parsedRes2.length - 1];
        return parsedRes1;
      }
    }

  }
  deleteForm() {
    this.dialog
      .open(PopUpComponent, {
        data: {
          title: this.lngSrvc.curLangObj.value.deletePage,
          message: this.lngSrvc.curLangObj.value.deleteMsg,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,
          OkButton: this.lngSrvc.curLangObj.value.delete,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        if (confirmed.confirmed == true || this.personformId != undefined) {
          //
          this.code ?
            this.store.dispatch(deleteUserResponse({
              candidateId: this.candidateId,
              personFormId: this.userResponse.value.personFormId[0],
              route: true,
              formTypeID: this.userResponse.value.formTypeId[0],
            }))
            :
            this.navigateToParentUsingBreadCrumb();
          // this.router.navigate(['loader', 'candidate-management']);
         
        }
        else{
          this.navigateToParentUsingBreadCrumb();
        }
      })
  }
  navigateToParentUsingBreadCrumb() {
    // extracting the second last 
    let crumb = this.breadCrumbsArray[this.breadCrumbsArray.length - 1];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.store.dispatch(setRecentLinkClickEvent({ event: this.recentLinkClickEvent }));
      this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
    }
    // this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
  }
  handleCrumbNavigation(crumb: crumb) {
    if (!(this.breadCrumbsArray.indexOf(crumb) == this.breadCrumbsArray.length - 1)) {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
      else {
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
          this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
          this.store.dispatch(setRecentLinkClickEvent({ event: this.recentLinkClickEvent }));
        }
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Independent) {
          this.router.navigate([crumb.navigationMetaData.moduleID]);
        }
        else
          this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
      }
      this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
    }
  }
  saveResponse($event: validatedSubmission) {
    if (!this.code) this.code = uuidv4();
    this.userResponse.next({
      formTypeId: this.form.formTypeID,
      personFormId: this.userResponse.value && this.userResponse.value.personFormId ? this.userResponse.value.personFormId : [],
      disabled: this.userResponse.value && this.userResponse.value.disabled ? this.userResponse.value.disabled : [false, false],
      response: $event.formValue,
      code: this.code
    })
    if (this.permanentlySaveResponse) {
  
      if (this.isSubmit) {
        if (!this.checkProgressOfSections || this.checkValidity($event)) {
          if(Number(this.SearchDetails.formTypeId)== 5 && Number(this.SearchDetails.stateId ) == 4 && (this.services.SearchValue !=null && this.services.SearchValue !='')){
            this.dialog
            .open(PopUpComponent, {
              data: {
                title: this.lngSrvc.curLangObj.value.leavePage,
                message: this.lngSrvc.curLangObj.value.leavePageMsg,
                cancelButton: this.lngSrvc.curLangObj.value.cancel,
                OkButton: this.lngSrvc.curLangObj.value.save,
              },
            })
            .afterClosed()
            .subscribe((confirmed: any) => {
            
              if (confirmed == true || confirmed.confirmed ==  true) {
               
                this.dispatchSaveResponseActions();
              }
            })
          }else if(this.SearchDetails.formTypeId !='5'){
            this.dialog
            .open(PopUpComponent, {
              data: {
                title: this.lngSrvc.curLangObj.value.leavePage,
                message: this.lngSrvc.curLangObj.value.leavePageMsg,
                cancelButton: this.lngSrvc.curLangObj.value.cancel,
                OkButton: this.lngSrvc.curLangObj.value.save,
              },
            })
            .afterClosed()
            .subscribe((confirmed: any) => {
            
              if (confirmed == true || confirmed.confirmed ==  true) {
               
                this.dispatchSaveResponseActions();
              }
            })
          }
          else this.snackbar.callSnackbaronError('Please fill all required fields and submit.')
        }
      
      }
      else this.dispatchSaveResponseActions();
    }

  }
  handleChange($event: DynamicFormControlEvent) {

    // the below comparision has to be changed to using identifiers
    if (AccomodationYesOrNoFieldIds.includes($event.model.id)
    ) {
      if (["true", "yes"].includes(($event.control.value as string).toLowerCase())) {
        this.isAccomodationSelected = true;
        if (!this.form.formTypeID.includes(FormTypes.Accomodation))
          this.store.dispatch(
            getFormJson({
              formTypeID: [FormTypes.Accomodation],
              stateID: this.form.stateID,
              eligibilityID: this.form.eligibilityID,
              isSubmitAllowed: [true]
            })
          );
      } else {
        // i.e accomodation form is also saved
        this.isAccomodationSelected = false;
        this.accTypeFormSysFileName.next(null);
        let accIndex = this.form.formJSON.sections.findIndex((x: any) => {
          return AccomodationFormsSectionOneIds.includes(x.id)
        })
        if (accIndex > 0) {
          (document.querySelectorAll('#submitButton')[0] as any).click();
          this.form.formTypeID = this.form.formTypeID.filter((x: any) => { return x != FormTypes.Accomodation });
          this.modifyFormJson(accIndex);
        }
      }
    }
    // the below is the id for AccomodationTypeField
    if (AccomodationFormAccTypeFieldIds.includes($event.model.id)) {
      this.http.get(environment.baseUrl + "candidate/api/Form/accommodationtype").subscribe((response: any) => {
        this.accTypeFormSysFileName.next(response.find((x: any) => { return x.value == $event.control.value }).systemFileName);
      })
    }
    this.checkProgressOfSectionsfn();
  }
  modifyFormJson(indexToRemove: number = null, extraForm = null) {
    var modifiedFormJson = lodash.cloneDeep(this.form.formJSON);
    if (indexToRemove != null)
      modifiedFormJson.sections.splice(indexToRemove, 1);
    if (extraForm != null) {
      modifiedFormJson.sections = [...modifiedFormJson.sections, ...extraForm.formJSON.sections];
      modifiedFormJson.formLayout = { ...modifiedFormJson.formLayout, ...extraForm.formJSON.formLayout };
    }
    var modifiedFormClone = {
      formID: [...this.form.formID],
      formTypeID: [...this.form.formTypeID],
      eligibilityID: this.form.eligibilityID,
      stateID: this.form.stateID,
      formJSON: modifiedFormJson,
      isSubmitAllowed: this.fetchedUserResponse ? this.fetchedUserResponse.map((res: any) => { return (res.statusId == statuses.Drafted || res.statusId == statuses.ChangeRequest) }) : [...this.form.isSubmitAllowed]
    }
    if (extraForm != null) {
      modifiedFormClone.formTypeID = this.getUnique<number>([...this.form.formTypeID, ...extraForm.formTypeID]);
      modifiedFormClone.formID = this.getUnique<number>([...this.form.formID, ...extraForm.formID]);
      if (!this.fetchedUserResponse || (this.fetchedUserResponse && this.fetchedUserResponse.length < 2))
        modifiedFormClone.isSubmitAllowed = [...modifiedFormClone.isSubmitAllowed, ...extraForm.isSubmitAllowed];
    }
    // this definitely needs to be fixed , a lot of changes required in formBuilder itself
    // probably ChangeDetectionStrategy.OnPush needs to be used or something like that
    this.form = null;
    setTimeout(() => {
      this.form = modifiedFormClone;
    })
  }

  getUnique<T>(arr: Array<T>): Array<T> {
    return arr.filter((value, index, self) => {
      return self.indexOf(value) === index
    })
  }

  checkValidity($event: validatedSubmission) {
    if (this.form.formTypeID.includes(FormTypes.Grievance) && $event.formValue) {
      let parentResponse = $event.formValue[0]["grievance__grievance_form_q2"]['c8e558db99684e9ebb6d061b6ad28b10'];
      for (let id of GrievanceFormFieldIds[parentResponse]) {
        let curVal = $event.formValue[0]["grievance__grievance_form_q2"][id];
        if (curVal == null || curVal == "" || curVal == undefined) {
          return false;
        }
      }
      return true;
    }
    return $event.valid;
  }

  dispatchSaveResponseActions() {
    if(this.formTypeId == 18){
      let body = {
        formName : "NURSE AIDE EVALUATOR QUALITY OBSERVATION",
        eventSku : this.eventSku,
        jsonBody :JSON.stringify(this.userResponse.value.response[0])
      }
      this.httpService.submitAuditForm(body).subscribe((data) =>{
        if(data){
            window.parent.postMessage('Success', '*');
          this.snackbar.callSnackbaronSuccess("Form Submitted Successfully")
        }
      },(err)=>{
         if(err){
          window.parent.postMessage('Success', '*');
         }
      })
    }

   else if (this.form.isSubmitAllowed[0]) {
      let useresponsecandidateId;
      let candidateSection=this.userResponse.value.response[0]?.problem_report_section_1_q1s1;
      let summarysection=this.userResponse.value.response[2]?.problem_report_section_1_q1s1;
      if( this.form.formTypeID[0]==11 && (candidateSection[ProblemReport.problem_report_section_1_q1s1]==null || summarysection[ProblemReport.problem_report_section_1_q1s1_type]==null || summarysection[ProblemReport.problem_report_section_1_q1s1_status]==null ) ){
        candidateSection[ProblemReport.problem_report_section_1_q1s1]==null?this.snackbar.callSnackbaronWarning("Please enter the candidate details"):
        summarysection[ProblemReport.problem_report_section_1_q1s1_status]==null?this.snackbar.callSnackbaronWarning("Please enter the report status"):
        this.snackbar.callSnackbaronWarning("Please enter the report type")
      }
      else if(this.form.formTypeID[0]==11 && this.userResponse.value.response[0].problem_report_section_1_q1s1[ProblemReport.problem_report_section_1_q1s1]!=null){
        useresponsecandidateId=Number(this.userResponse.value.response[0].problem_report_section_1_q1s1[ProblemReport.problem_report_section_1_q1s1].split(',')[1].split(':')[1].split('/')[0].split('"')[1]);
        this.store.dispatch(saveUserResponse({
          formTypeID: this.userResponse.value.formTypeId[0],
          userResponse: {
            id: this.userResponse.value.personFormId.length > 0 ? this.userResponse.value.personFormId[0] : 0,
            personTenantRoleId: this.candidateId?this.candidateId:useresponsecandidateId,
            formResponse: JSON.stringify(this.userResponse.value.response),
            isSubmit: this.isSubmit,
            formId: this.form.formID[0],
            code: this.code,
            personEventId: this.personEventId ? this.personEventId : 0,
            actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          }
        }));
        let personFormId=this.store.select(selectLatestPersonFormId).subscribe(data=>{
          if(data){
            this.router.navigateByUrl('loader/problem-report')
            personFormId.unsubscribe();
          }
        })
      }
      else{
         this.store.dispatch(saveUserResponse({
          formTypeID: this.userResponse.value.formTypeId[0],
          userResponse: {
            id: this.userResponse.value.personFormId.length > 0 ? this.userResponse.value.personFormId[0] : 0,
            personTenantRoleId: this.candidateId!=0?this.candidateId:useresponsecandidateId,
            formResponse: JSON.stringify(this.userResponse.value.response),
            isSubmit: this.isSubmit,
            formId: this.form.formID[0],
            code: this.code,
            personEventId: this.personEventId ? this.personEventId : 0,
            actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
            version:1,
            StateCode:this.SearchDetails.stateCode,
            testingPreferenceId: this.TestReferenceId,
            testCenterId:this.TestReferenceId == 1?`${this.INFValue.value.infcode}`:null,
            isTestCenterAcknowledge:this.TestReferenceId == 1?this.AcceptanceName:null
          }
        }));
      }
    
    }
    if (this.isAccomodationSelected && this.form.isSubmitAllowed[1]) {
     
      this.store.dispatch(saveUserResponse({
        formTypeID: this.userResponse.value.formTypeId[1],
        userResponse: {
          id: this.userResponse.value.personFormId.length > 1 ? this.userResponse.value.personFormId[1] : 0,
          personTenantRoleId: this.candidateId,
          formResponse: JSON.stringify(this.userResponse.value.response),
          isSubmit: this.isSubmit,
          formId: this.form.formID[1],
          code: this.code,
          personEventId: this.personEventId ? this.personEventId : 0,
          actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
          testingPreferenceId: this.TestReferenceId,
          testCenterId:this.TestReferenceId == 1?`${this.INFValue.value.infcode}`:null,
          isTestCenterAcknowledge:this.TestReferenceId == 1?this.AcceptanceName:null
        }
      }));
    }
    else if (!this.isAccomodationSelected && this.userResponse.value.personFormId.length == 2) {
      this.store.dispatch(deleteUserResponse({
        candidateId: this.candidateId,
        personFormId: this.userResponse.value.personFormId[1],
        route: false,
        formTypeID: FormTypes.Accomodation,
      }))
    }
  }

  paste(event: any) {
    const pastedData = event.clipboardData.getData("text/plain");
    const onlyNumbers = this.extractNumbersFromString(pastedData);
    event.target.value = onlyNumbers;
  }

  extractNumbersFromString(input: string): string {
    // Use regular expression to extract only numeric characters
    return input.replace(/\D/g, "");
  }

  initialSectionValidationEvent(event: any) {
    setTimeout(() => {
      this.sectionsForProgress = event
      this.checkProgressOfSectionsfn()
    }, 1200);
  }

  changeSectionValidationEvent(event: any) {
    this.sectionsForProgress.map(elem => {
      if (elem.sectionName === event.sectionName)
        return event

      return elem
    })
    this.checkProgressOfSectionsfn()
  }

  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }



  onKeydownMain(event){
   this.checkProgressOfSectionsfn()
  }



    getSelectedRoute(event,isTestCenterAcknowledge?:string) {
      if (event == 1) {
        this.INFSelected = true
        this.TestReferenceId = event
       isTestCenterAcknowledge !=null?this.radioselectAcceptance.setValue(isTestCenterAcknowledge):this.radioselectAcceptance.setValue(null)
           this.checkProgressOfSectionsfn()
      } else if (event == 2) {
        this.INFSelected = false
        this.TestReferenceId = event
        this.INFValue.setValue({infcode:'INF77373'})
        this.radioselectAcceptance.setValue('No')
        this.checkProgressOfSectionsfn()
      }
    }

  getSelectedAccept(event){
    this.AcceptanceName = event ==1?true:false
    event ===1?this.radioselectAcceptance.setValue('Yes'):this.radioselectAcceptance.setValue('No')
    this.checkProgressOfSectionsfn()

  }

  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}

export const GrievanceFormFieldIds = {
  "skills": [
    "289e9a27b0b14a55894c84a8cd44d780",
    "0ac07e47e7ce45f8937f87eddd518ad2",
    "5cf74d48eb76452c8101336aaae8afb1",
    "e5ad83e8fd4f4486bba529a8d523b5e1",
    "0b8f8e0e823d45e6a0ed78f5883364e4",
    "d252c8418f414fed96e236fe9779c529",
    "7d8b16e179394cd9aee5e5e1a1f58f8f",
    "250274aeca544ef4ac4f1ee407d3e3c7",
    // "9aac4001738446dd8cb7d7152d041b2e",
  ],
  "ada": [
    "b8aac6fc535a4164a09e2b634858c70c",
    "fb99089e5ed74d95ad1748e0a4a14ace",
    "86cf190f61e849b4bfa3bc20a1fd969b",
    "696d8b0c796e43078d79467e7ac98cab",
    "20e39b0e77594ca5af92c9268ec5ab77",
    // "5418390e278e4328ace4290d7bc2f00c",
  ],
  "turned": [
    "3321d06a714843a4a3d3229510f1d498",
    "e434da614b854636916c296540468a23",
    "f481a11ac6fd4cf692c0c60fa3861412",
    "4419513d668b4a76ac8f13221e58c696",
    "e784b182428844bb84fefdbca57df21c",
    // "c88b005b9f6c420696db6cf7ee300f84"
  ]
}

