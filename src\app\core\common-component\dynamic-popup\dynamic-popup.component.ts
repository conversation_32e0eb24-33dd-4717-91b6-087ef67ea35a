import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { State, Store } from '@ngrx/store';
import { logout } from '@thoughtspot/visual-embed-sdk';
import { Editor } from 'ngx-editor';
import { BehaviorSubject, Observable } from 'rxjs';
import { genericPopupConfig, popupTypes, Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { recentLinkClickEventSelector } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { environment } from 'src/environments/environment';
import { GlobalUserService } from '../../global-user.service';
import { getObject } from '../form-view/form-view.component';
import { state } from '../table/dynamic-table-types';
import { SnackbarService } from '../../snackbar.service';

@Component({
  selector: 'app-dynamic-popup',
  templateUrl: './dynamic-popup.component.html',
  styleUrls: ['./dynamic-popup.component.scss']
})
export class DynamicPopupComponent implements OnInit {

  popupTypes = popupTypes;
  popupData: any = null;
  formJSON: any;
  userResponse: any;
  richText: FormControl;
  element: any = null;
  editor: Editor;
  Dowmloadbutton :boolean = false
 stateId
  resourceurl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  $resourceurl: Observable<string> = this.resourceurl.asObservable();

  @ViewChild('hiddenUnnecessaryButton') submitButton:any;
  constructor(@Inject(MAT_DIALOG_DATA) public popupConfig: genericPopupConfig,public global:GlobalUserService,private store:Store,private snackbar:SnackbarService) {
    this.popupData = popupConfig.popupData;
    this.formJSON = this.popupData.formJSON;
    this.userResponse = this.popupData.userResponse;
    this.element = this.popupData.element;
    this.element = {
      ...this.element,
      candidateId:this.element.personTenantRoleId,
      personEventId: this.element.personEventRegistryId ? this.element.personEventRegistryId : this.element.personEventId
    }
    if (this.popupConfig.type == popupTypes.EmulateCandidate) {
      
      let Email  =this.element.element.emailId?this.element.element.emailId:this.global.email
      let firstName =this.element.element.firstName?this.element.element.firstName:this.global.firstName
      let lastName =this.element.element.lastName?this.element.element.lastName:this.global.lastName
      this.resourceurl
        .next((!environment.production ? 'http://localhost:8011' : (environment.redirectUrl + 'candidate')) + '/dashboard/?token=' + sessionStorage.getItem('token') + '&email=' + encodeURIComponent(Email) + '&firstName=' + firstName + '&lastName=' + lastName);
    }
    if (this.popupConfig.type == popupTypes.EmulateClient) {
      let Email  =this.element.emailId
      let EmployerEmail = this.element.emailid
      let TypeEmualte = "Client"
      let EmailId = (Email !=null && Email !=undefined)?Email:EmployerEmail
      this.resourceurl
        .next((!environment.production ? 'http://localhost:8002' : (environment.redirectUrl + 'client')) + '/dashboard/?token=' + sessionStorage.getItem('token') + '&email=' + encodeURIComponent(EmailId) + '&firstName=' + this.element.firstName + '&lastName=' + this.element.lastName + "&personTenantroleId=" + this.element?.personTenantRoleId + '&TypeValue=' + TypeEmualte) ;    }
    this.richText = new FormControl({ disabled: true, value: this.popupData.richText });
    this.editor = new Editor();
  }

  ngOnInit(): void {
    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      this.stateId = data.element.stateId
      if ((this.stateId == state.District_of_Columbia || this.stateId == state.Colorado) && (this.global.column.linkMetaData.navigateTo === "candidate Details-sc") || (this.global.column.linkMetaData.navigateTo === "candidate-details-sc")) {
        this.Dowmloadbutton = true
      }
    })
  }
  toPdf() {
    var html = '<HTML>\n<HEAD>\n';
    if (document.getElementsByTagName != null) {
      var headTags = document.getElementsByTagName("head");
      if (headTags.length > 0) html += headTags[0].innerHTML;
    }
    html += '\n</HE' + 'AD>\n<BODY>\n';
    var printReadyElem = document.getElementById("printMe");

    if (printReadyElem != null) html += printReadyElem.innerHTML;
    html += '\n</BO' + 'DY>\n</HT' + 'ML>';
    var printWin = window.open("", "processPrint");
    printWin.document.open();
    printWin.document.write(html);
    printWin.document.close();
    //logic to manually put values into text and date fields
    
    var inputElements = printWin.document.getElementsByTagName("input");
    var dateElemens = printWin.document.getElementsByClassName("mat-datepicker-input");
    var textAreaElements = printWin.document.getElementsByTagName("textarea");

    for(let i = 0; i<inputElements.length; i++){
        let inputId = inputElements[i].id;
        let value = getObject(this.userResponse,inputId,false);
        if(value){
          printWin.document.getElementById(inputId).setAttribute("value",value);
        }
    }
    for(let i = 0; i<dateElemens.length; i++){
      let inputId = dateElemens[i].id;
      let value = getObject(this.userResponse,inputId,false);
      let date = new Date(value);
      if(value !=null && value !="" && value !=undefined){
        value = date.getUTCMonth()+1 + '/'+date.getDate()+ '/'+date.getFullYear();
        if(value){
          printWin.document.getElementById(inputId).setAttribute("value",value);
        }
      }
   
    }
    for(let i = 0; i<textAreaElements.length; i++){
        let inputId = textAreaElements[i].id;
        let value = getObject(this.userResponse,inputId,false);
        if(value){
          printWin.document.getElementById(inputId).textContent = value;
        }
    }
    setTimeout(()=>{
      printWin.print();
      printWin.close()
    },500)
  }
  close(){
    logout()
  }
}
