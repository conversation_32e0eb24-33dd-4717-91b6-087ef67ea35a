import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { Subject } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { recentLinkClickEventSelector } from '../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { ExamType } from '../state/models/examType';
import { Slot } from '../state/models/slot';
import { SlotModel } from '../state/models/slot.model';
import { TimeSlotRange } from '../state/models/timeSlotRange';
import { Timezone } from '../state/models/timezone.model';
import { getCart, getCartItems, getTimeSlots, getTimezones, setCandidateId, setTimezome } from '../state/scheduled.actions';
import { get_cartItems, get_timeslots, get_timezones } from '../state/scheduled.selectors';

@Component({
  selector: 'app-rescheduled',
  templateUrl: './rescheduled.component.html',
  styleUrls: ['./rescheduled.component.scss']
})
export class RescheduledComponent implements OnInit {
  timezones
  timeZoneSelected
  timeslots: Slot[] = [];
  dateValue
  dateSelected: Date;
  candidateId: number;
  scheduleId:number;
  isSelect: boolean = false;
  scheduleEvent:Subject<any> = new Subject<any>();
  timeSlotRange: TimeSlotRange[];
  slots: TimeSlotRange = new TimeSlotRange("", 0, [],"","");
  slotsAvaiable: SlotModel[] = [];
  selectedExamType:any
  disableDateRange:boolean = true;
  disableTimeSlot:boolean = false;
  selectedTimeSlot: Slot;
  cartPayment:boolean = false;
  cartItemList
  constructor(@Inject(MAT_DIALOG_DATA) public data: any,private store:Store,public dialogRef: MatDialogRef<RescheduledComponent>,private router:Router,  private snackbar:SnackbarService,private global:GlobalUserService) {
    this.store.dispatch<Action>(getTimezones());
   }

  ngOnInit(): void {
    this.scheduleEvent.next(true)
    this.store.select(get_timezones).subscribe((timezones: Timezone[]) => {
      if (timezones) {
        this.timezones = timezones;
      }
    });

    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      if (data != null && data) {
        this.candidateId = data.element.personTenantRoleId;
      }
    })

    this.store.select(get_cartItems).subscribe((cartItems) => {
      if(cartItems!=null){
        this.cartItemList=cartItems
      }
    })
    this.store.select(get_timeslots).subscribe((timeslots) => {
      if (timeslots) {
        this.slotsAvaiable = [];
        if (timeslots.length > 0) {
          this.timeslots = timeslots;
          this.timeslots.forEach((ele: Slot) => {
            let slotstring = `${ele.strSlotDate} ${ele.strSlotTime}`;
            let slotDate = new Date(Date.parse(slotstring));
            let slotmodel = new SlotModel({
              slotId: ele.slotId,
              availableSlots: ele.availableSlots,
              bookedSlots: ele.bookedSlots,
              slotDate: ele.slotDate,
              strSlotDate: ele.strSlotDate,
              strSlotTime: ele.strSlotTime,
              totalSlots: ele.totalSlots,
              slotDateTime: slotDate,
              slotDateUtc: ele.slotDateUtc
            })
            this.slotsAvaiable.push(slotmodel);
          });
          this.slotsAvaiable = this.slotsAvaiable.sort((a, b) => new Date(a.slotDateTime).getTime() - new Date(b.slotDateTime).getTime());
         
          this.slotsAvaiable.forEach((ele) => {
            let slotDate = ele.slotDateTime;
            if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 00:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 04:00 AM`))) {
              this.timeSlotRange[0].data.push(ele);
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 04:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 08:00 AM`))) {
              this.timeSlotRange[1].data.push(ele);
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 08:00 AM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 12:00 PM`))) {
              this.timeSlotRange[2].data.push(ele);
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 12:00 PM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 04:00 PM`))) {
              this.timeSlotRange[3].data.push(ele);
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 04:00 PM`)) && slotDate < new Date(Date.parse(`${ele.strSlotDate} 08:00 PM`))) {
              this.timeSlotRange[4].data.push(ele);
            } else if (slotDate >= new Date(Date.parse(`${ele.strSlotDate} 08:00 PM`)) && slotDate <= new Date(Date.parse(`${ele.strSlotDate} 11:59 PM`))) {
              this.timeSlotRange[5].data.push(ele);
            }
          });          
          this.slots = this.timeSlotRange[this.slots.id];
          this.isSelect = true;
        } else if (this.slotsAvaiable.length == 0) {          
          this.snackbar.callSnackbaronWarning("No slots available for the selected date");
          this.isSelect = false;
        }
      }
    });

    // this.activatedRoute.paramMap.subscribe((x: any) => {
    //   if (x) {
    //     if (x.params.scheduleId) {
    //       this.scheduleId = x.params.scheduleId;
    //     }
    //     this.candidateId = Number(x.params.candidateId);
    //     this.store.dispatch<Action>(
    //       setCandidateId({ candidateId: this.candidateId })
    //     );
    //   }
    // });
  }
  dateSelectedEvent($event) {
    if ($event.value) {
      this.setSlot()
      this.dateValue = $event.value;
      if (this.dateValue)
        this.store.dispatch<Action>(
          getTimeSlots({
            timezone: this.timeZoneSelected.id,
            startDate: moment(this.dateValue).format("YYYY-MM-DD"),
            examId: this.selectedExamType.id,
            offset: this.timeZoneSelected.offset
          })
        );
    }

  }
  timeZoneSelectedEvent($event: Timezone) {
    this.setSlot();
    this.timeZoneSelected = $event;
    this.store.dispatch<Action>(
      setTimezome({ timezone: this.timeZoneSelected })
    );
    if (this.dateValue)
      this.store.dispatch<Action>(
        getTimeSlots({
          timezone: this.timeZoneSelected.id,
          startDate: moment(this.dateValue).format("YYYY-MM-DD"),
          examId: this.selectedExamType.id,
          offset: this.timeZoneSelected.offset
        })
      );
  }

  slotIdSelected($event) {
    this.cartPayment=true
    this.selectedTimeSlot = $event;
  }
  getData(item,date){
    this.timeZoneSelected=item
    this.setSlot();
    
    if(date!=null && item.examModeId ===1){
      this.dateValue = date.value;
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: item.timeZoneCode, startDate: this.dateValue.toDateString(), examId: item.examId, offset: item.timeZoneOffset })
    );
    // this.disableDateRange=true;
    // this.disableTimeSlot=false;
    // this.cartPayment=false;
    }
    if(date==null && item.examModeId ===1){
    this.store.dispatch<Action>(
      getTimeSlots({ timezone: item.timeZoneCode, startDate:moment(item.eventDateUtc).format("YYYY-MM-DD"), examId: item.examId, offset: item.timeZoneOffset })
    );
    // this.disableDateRange=true
    }
    this.selectedExamType={
      id:item.examId,
      price:item.amount,
      title:item.examName,
      page:"PopPage",
      personEventId: item.personEventId,
      voucherCode: item.voucherCode
    }
  }
  onNoClick(item){
    if(item==0){
    this.dialogRef.close({confirmed:true});
    this.router.navigateByUrl('/loader/candidate-details')
    }
    else if(item==1){
      this.dialogRef.close({confirmed:true});
    this.router.navigateByUrl('/exam-scheduled/payment')
    }
  }
  addToCart(list){
    this.store.dispatch<Action>(
      getCart({
        details: {
          personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
          amount: list.amount,
          cartItemTypeId: 1,
          currencyId: 1,
          examDetail: {
            candidateId: this.candidateId,
            examId: this.selectedExamType.id,
            slotId: this.selectedTimeSlot.slotId,
            timeZone: this.timeZoneSelected.id,
            offSet: this.timeZoneSelected.offset,
            examModeId: 1,
            personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
            examDateTime: this.selectedTimeSlot.slotDateUtc
          },
          personEventId: list.personEventId?list.personEventId:null,
          voucherCode: list.voucherCode?list.voucherCode:null
        },
      })
    );
  }
  payNow(){
   
    this.store.dispatch<Action>(
      getCartItems({ personTenantRoleId: this.global.userDetails.value.personTenantRoleId })
    )
    if(this.cartItemList?.length>0){
     this.onNoClick(1);
     this.router.navigateByUrl(`scheduleExam/${this.candidateId}/payment/page`);
    }
  }
  slotRangeSelected($event) {
    this.disableTimeSlot=true
    this.slots = $event;
  }

  setSlot() { 
    var t0 = new TimeSlotRange("12 AM - 04 AM", 0, [],"MIDNIGHT","green");
    var t1 = new TimeSlotRange("04 AM - 08 AM", 1, [],"EARLY MORNING","blue");
    var t2 = new TimeSlotRange("08 AM - 12 PM", 2, [],"MORNING","blue");
    var t3 = new TimeSlotRange("12 PM - 04 PM", 3, [],"AFTERNOON","blue");
    var t4 = new TimeSlotRange("04 PM - 08 PM", 4, [],"EVENING","green");
    var t5 = new TimeSlotRange("08 PM - 11:59 PM", 5, [],"NIGHT",'green');
    this.timeSlotRange = [t0, t1, t2, t3, t4, t5];
  }

}
