import { state } from "@angular/animations";
//import { State } from "@ngrx/store";

import {
  consoleLoaderJSON,
  moduleTypes,
  State,
  nestingTypes,
  paramTypes,
  Roles,

  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";
export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Manage State Client",
      moduleID: "Manage State Client",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "ticket",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showAddButton: [false],
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "",
                      title: [""],
                      id: "",
                      buttons: [
                        {
                          buttonText: "Submit",
                          requestDetails: {
                            fetchUrl: ["client", "api", "user"],
                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                  showMatTabs: false,
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  showColumnFilter: true,
                  showStateSelectionDropdown: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [false],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: ["firstName", 'middleName', 'lastName'],
                      name: "User Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: 'contactNumber',
                      name: "Contact Number",
                      cellType: "text",
                      headerDisable: true,
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,



                      visibilityRule: [
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                          shouldHaveValue: State.North_Carolina,
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                          shouldHaveValue: State.Virginia,
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                          shouldHaveValue: State.Washington,
                        },
                        {
                          operator: tableTypes.Operators.AND,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.StateClient,
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.OperationStaff,
                        }

                      ],



                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "emailId",
                      name: "Email Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-1/12"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "stateName",
                      name: "State",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-1/12"],
                      cellCssClasses: [],

                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "lastVerifiedDate",
                      name: "Created Date/Time",
                      cellType: "date",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],

                    },



                    <tableTypes.column>{
                      visible: true,
                      id: "active",
                      name: "Enable/Disable",
                      showOnlyData: true,
                      cellType: "toggle",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [""],

                      statusUpdateDetails: {
                        fetchUrl: ["client", "api", "state", "status"],
                        requestBody: null,
                        requestParams: null,
                        method: "PUT",
                      },
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "active",
                      name: "Enable/Disable",
                      showOnlyData: true,
                      cellType: "toggle",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [""],

                      statusUpdateDetails: {
                        fetchUrl: ["client", "api", "state", "status"],
                        requestBody: null,
                        requestParams: null,
                        method: "PUT",
                      },
                    },


                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 1769176

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2111856

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2111855

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2056784

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2036106

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2034258

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2102699

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2132153

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2003387

                            }



                          ]
                        },
                        <tableTypes.action>{
                          icon: "person",
                          type: tableTypes.actionTypes.StateemulatePerson,
                          tooltip: "Emulate",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "roleId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff

                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "personId",
                              extractedFromGlobal: true,
                              compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                              shouldHaveValue: 2119915

                            }



                          ]
                        }
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Offspring,
                        navigateTo: "training-institute-details",
                        eventDataUseful: false,
                        navigateToLabel: "Training Program Details",
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },
                    // <tableTypes.column>{
                    //   visible: true,
                    //   id: "action",
                    //   name: "Action",
                    //   cellType: "link",
                    //   linkMetaData: {
                    //     navigateToType: moduleTypes.Offspring,
                    //     navigateTo: "training-institute-details",
                    //     eventDataUseful: false,
                    //     navigateToLabel: "Training Institute",
                    //   },
                    //   headerType: "normal",
                    //   headerAlign: "center",
                    //   cellAlign: "center",
                    //   headerCssClasses: [],
                    //   cellCssClasses: [],
                    // },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "Account", "getusers"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },

                      {
                        paramType: paramTypes.Optional,
                        paramName: "personRoleId",
                        paramValue: 14,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "stateId",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: 'stateId'
                      }
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
