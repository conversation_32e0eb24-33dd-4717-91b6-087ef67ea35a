import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';
import { HelpService } from '../../help.service';
import { Category } from '../../interfaces/category';
import { Ticket } from '../../interfaces/ticket';

@Component({
  selector: 'exai-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss']
})
export class CardComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    private helpService: HelpService,
  ) { }

  categoryName: string

  @Input() selectedCategoryName
  @Input() name
  @Input() eligibilityRouteName
  @Input() id
  @Input() applicationId
  @Input() status
  @Input() createdDate
  @Input() changedDate
  @Input() getHelp
  @Input() ticketRaised

  selectedCategory$: Observable<Category[]>
  selectedCategory: Category
  selectedTicket$: Observable<Ticket[]>
  selectedCategorySub: Subscription;
  ngOnInit(): void {


    if(this.id==undefined) {
      this.id="1";
    }
    this.helpService.selectedTicketId.next(this.id)
    this.selectedCategorySub = this.helpService.selectedCategory.pipe(tap(data => {
      data.dataDetail.FAQ.forEach(el => {
        el.ShowAnswer = false
      })
    })).subscribe(data => this.selectedCategory = data)
    
    this.selectedTicket$ = this.helpService.getTicket(this.id).pipe(tap(data => {}
    ))
  }

  navigateToRaiseTicket() {
    // this.router.navigate(['/help', this.id, 'raise-ticket'])
    this.helpService.selectedCategoryOutput.next('id');
  }

  
  navigateToSubmit() {
    // this.router.navigate(['/help', this.id, 'raise-ticket'])
    this.helpService.selectedCategoryOutput.next('details');
  }
}
