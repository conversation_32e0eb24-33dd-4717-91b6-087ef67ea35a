
<mat-icon class="text-base flex close" (click)="onNoClick()">close</mat-icon>
<div *ngIf="disableSendButton && SelectedValue.tabtype !=5 && SelectedValue.tabtype !=6  ">
<exai-table class="w-full" [tableOptions]="tableOptions"  [displayedColumns]="displayedColumns"  [dataSource]="dataSource" [selectedIndexObs]="$tabIndex" (cellClickEvent)="candidateSelected($event)">
</exai-table>
</div>

<div *ngIf="enableSendButton && SelectedValue.tabtype !=5">
    <exai-table class="w-full" [tableOptions]="tableOptionsassign"  [displayedColumns]="displayedColumnsassign"  [dataSource]="dataSourceassign" [selectedIndexObs]="$tabIndex" (cellClickEvent)="assignVoucher($event)">
    </exai-table>
    </div>

  

    <div *ngIf="SelectedValue.tabtype ==5 && SelectedValue.tabtype !=6">
      <div class="px-2" fxLayout="column" fxLayoutAlign="" >
        <div fxLayout="row" class="justify-between -ml-4">
          <mat-radio-group [formControl]="radioTrainingselect">
            <ng-container *ngFor="let examType of TrainingSelectOptions; let i = index">
                <mat-radio-button class="-ml-1 px-5 mb-3 pt-1 t-xs" [checked]="examType.checked"  [value]="examType.name" (change)="options(examType.id)" >{{ examType.name }}
                </mat-radio-button>
            </ng-container>

        </mat-radio-group>
        </div>
        </div>

        
<form [formGroup]="candiateDetails" *ngIf="radioTrainingselect.value =='Search by email Id'">   
    <input type="text" formControlName="registryNumber" class="p-2" placeholder="Search..">
    <button mat-button class="btn-1 text-xs ml-2"  (click)="serachresult()">Search</button>

</form>


            <mat-form-field class="w-full" appearance="outline" *ngIf="radioTrainingselect.value =='Select by user'"  >
                <mat-label class="text-xs  fontColor2">Select User</mat-label>
                <mat-select  placeholder="Issue"
                class=" border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                <mat-option *ngFor="let itemValue of UserList" [value]=itemValue (click)="User(itemValue)"  >
                  {{itemValue.firstName}} {{itemValue.middleName}} {{itemValue.lastName}} | {{itemValue.emailId}}
                </mat-option>
            </mat-select>
            </mat-form-field>

            <mat-form-field class="w-full" appearance="outline" *ngIf="radioTrainingselect.value =='Search by email Id' && TrainingInstituteList.length > 0 "  >
              <mat-label class="text-xs  fontColor2">Select User</mat-label>
              <mat-select  placeholder="Issue"
              class=" border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
              <mat-option *ngFor="let itemValue of TrainingInstituteList" [value]=itemValue (click)="User(itemValue)"  >
                {{itemValue.trainingProgramName}} | {{itemValue.trainingProgramId}}
              </mat-option>
          </mat-select>
          </mat-form-field>
       
        <exai-table class="w-full" [tableOptions]="tableOptionsassign"  [displayedColumns]="displayedColumnsTransfer"  [dataSource]="dataSourceassigntransfer" [selectedIndexObs]="$tabIndex" (cellClickEvent)="assignVoucher($event)" (actionPerformed)="downloadVoucher($event)" >
        </exai-table>  

           <div class="float-right">
             <span class="color">Vouchers Remaining : {{this.SelectedValue.totalVoucher - this.SelectedListValue.length}}</span>
           </div>

        <div fxLayout="row" class="w-full flex justify-between mb-2 pt-2"  fxLayoutAlign="end end" >
            <button mat-dialog-close class="btn-3" mat-button (click)="Cancel()" > Cancel</button>
            <button  class="btn-1 ml-2" mat-button color=primary (click)="SubmitTransferVoucher()" > Submit</button>
        </div>
    </div>

    <form [formGroup]="candiateDetailsbyemail" *ngIf="SelectedValue.tabtype ==6 && !enableSendButton">   
      <div fxLayout="row" class="px-2 trainingUploadCandidate" fxLayout.lt-sm="column" fxLayoutGap="16px">
        <mat-form-field fxFlex="auto"  appearance="outline">
            <input class="text-xs" id="email" matInput  placeholder="Email" formControlName="email">
            <mat-error *ngFor="let validation of validation_messages._email">
              <mat-error class="error-message"
                  *ngIf="candiateDetailsbyemail.get('email').hasError(validation.type) && (candiateDetailsbyemail.get('email').dirty || candiateDetailsbyemail.get('email').touched)">
                  {{validation.message}}</mat-error>
          </mat-error>
        </mat-form-field>
     
    </div>
     
  
  </form>

  <button *ngIf="!enableSendButton && SelectedValue.tabtype ==6 " [disabled]="!candiateDetailsbyemail.valid"  mat-button class="btn-1 text-xs ml-2" (click)="serachresultbyEmail()"  >Search</button>


    <div *ngIf="!disableSendButton && SelectedValue.tabtype ==6  ">
      <exai-table class="w-full" [tableOptions]="tableOptionsPractice"  [displayedColumns]="displayedColumns"  [dataSource]="dataSource" [selectedIndexObs]="$tabIndex" (cellClickEvent)="candidateSelected($event)">
      </exai-table>
      </div>