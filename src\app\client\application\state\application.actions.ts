import { createAction, props } from "@ngrx/store";
import { PersonFormLog } from "src/app/core/common-component/progress-bar/progress-bar.types";
import { Form, FormTypes } from '../application.types';

export const getFormJson = createAction('[FORM JSON] Getting Form Json',
  props<Form>()
);
export const gotFormJson = createAction('[FORM JSON] Got Form Json',
  props<Form>()
);
export const clearApplicationState = createAction('[CLEAR APPLICATION STATE] Cleared Application State');

export const getPersonFormLogs = createAction('[personformlogs] GET personformlogs',
  props<{ personFormId?: string, code?: string }>())

export const gotPersonFormLogs = createAction('[personformlogs] GOT personformlogs',
  props<{ personFormLogs: PersonFormLog[] }>())

export const settingEligilityRouteOnLoad = createAction('[SettingEligiblityRoute] Setting Eligibility Route', props<{
  id: number,
  personTenantRoleId: number,
  eligibilityRouteId: number,
  actionBy: number
}>());

export const setEligilityRouteOnLoad = createAction('[SetEligiblityRoute] Set Eligibility Route');

export const setBasicDetails = createAction('[BASIC DETAILS] Setting basic details into store to be able to laod a form',
  props<{
    formTypeId: FormTypes,
    candidateId: number;
    eligibilityRouteId: number;
    stateId: number;
    personFormId: number;
    code: string;
    personEventId: number;
  }>())

export const saveUserResponse = createAction('[UserResponse] SAVE response to current form',
  props<{
    formTypeID: FormTypes;
    userResponse: {
      // id here is the personFormId
      id: number,
      personTenantRoleId: number,
      formId: number,
      isSubmit: boolean,
      code: string,
      formResponse: any,
      personEventId: number,
      actionBy: number;
      version?:number;
      StateCode?:string
      testingPreferenceId?: number;
      testCenterId?: string;
      isTestCenterAcknowledge?:boolean,
    }
  }>());

export const savedUserResponse = createAction('[SAVED RESPONSE] Saved User Response to current form',
  props<{ personFormId: number }>());

export const deleteUserResponse = createAction('[DELETE RESPONSE] Delete Saved Response if it exists', props<{
  candidateId: number,
  personFormId: number,
  route?: boolean,
  formTypeID: FormTypes
}>())

export const deletedUserResponse = createAction('[DELETE RESPONSE] Deleted Saved Response')

export const getUserResponse = createAction('[GET RESPONSE] Get existing response',
  props<{
    code?: string, 
    personFormId?: number
  }>());

export const gotUserResponse = createAction('[GOT RESPONSE] Got existing response',
  props<{ userResponse: any }>());

export const downloadAccTypeForm = createAction('[DOWNLOAD] Accomodation Type Accompaniment Form',
  props<{ sysFileName: any }>());
export const downloadedAccTypeForm = createAction('[DOWNLOAD] Accomodation Type Accompaniment Form',props<{url:string}>());