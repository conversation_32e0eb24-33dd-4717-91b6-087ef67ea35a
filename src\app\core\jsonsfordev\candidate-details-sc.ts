import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, State, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from 'src/app/core/common-component/single-entry-table/single-entry-table.types';
import { FormTypes } from "src/app/client/application/application.types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Offspring,
      name: "View  Details ",
      moduleID: "candidate-details-sc",
      nestingType: nestingTypes.RowsInColumns,
      iconRef: "",
      routeRef: "/loader",
      class: "",
      gdColumns: "1fr",
      gdColumnsltlg: "1fr",
      gdColumnsltmd: "1fr",
      gdColumnsltsm: "1fr",
      gdGap: "12px",
      columns: [
        {
          class: "flex-col-important w-full",
          gdColumn: "1/1",
          gdColumnltlg: "1/1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "space-between stretch",
          fxFlex: "auto",
          rows: [
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicSingleEntryTables,
                displayedColumns: [
                  [
                    <singleEntryTableTypes.column>{
                      id: ["firstName", "middleName", "lastName"],
                      name: "Candidate Name",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "address",
                      name: "Address",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "phoneNumber",
                      name: "Contact Number",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "ssn",
                      name: "Last 4 SSN",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "emailId",
                      name: "Email Id",
                      editable: true,
                    },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "account", "getuser"],
                    requestBody: null,
                    requestParams: [
                    {
                      paramType: paramTypes.Optional,
                      paramName: 'personTenantRoleId',
                      paramValue: null,
                      extractedFromElement: true,
                      elementPropertyToBeExtracted: "personTenantRoleId",
                      
                    },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: "manage-applications",
              },
            },
            {
                class: "",
                gdColumn: "1 / 1",
                gdColumnltlg: "1 / 1",
                gdColumnltmd: "1 / 1",
                gdColumnltsm: "1",
                fxLayout: "",
                fxLayoutAlign: "start center",
                fxFlex: "none",
                fxFlexltmd: "auto",
                fxHidexs: "false",
                fxHidesm: "false",
                widget: {
                  type: widgetTypes.DynamicTable,
                  tableOptions: {
                    headerRowCssClasses: [],
                    cellRowCssClasses: ["my-3 shadow"],
                    showPaginator: false,
                    pageSizeOptions: [8, 20, 100],
                    pageSize: 8,
                    showColumnFilter: true,
                    showAddButton: [false, false, false],
                    addButtonTexts: ["Add Application", "Register For Exam", ""],
                    showMatTabs: true,
                    matTabs: [
                      { id: 0, name: "Application Details" },
                      { id: 1, name: "Exam Details" }
                    ],
                    showSearch: false,
                    verticalScroll: true,
                    horizontalScroll: true,
                    addPopupData: [
                      {
                        isCustom: true,
                        customTemplateRef: "addApplicationForCandidate",
                        title: ["Add Application for Candidate"],
                        id: "addApplicationForCandidate",
                        buttons: [],
                      },
                      {
                        isCustom: true,
                        customTemplateRef: "register",
                        title: [""],
                        id: "register",
                        buttons: [],
                      },
                    ],
                  },
                  displayedColumns: [
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "formTypeName",
                        name: "Application Name",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "personFormId",
                        name: "Application ID",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "status",
                        name: "Status",
                        cellType: "status",
                        statuses: tableTypes.tableStatuses,
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: ["text-xs"],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "currentAssigneeRoleName",
                        name: "Currently Working",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        headerType: "normal",
                        headerAlign: "center",
                        actions: [
                          <tableTypes.action>{
                            icon: "visibility",
                            type: tableTypes.actionTypes.View,
                            tooltip: "View",
                            color: "#7d7d7d",
                            requestDetails: [
                              {
                                fetchUrl: [
                                  "client",
                                  "api",
                                  `form`,
                                  `formsbyformtypeid`,
                                ],
                                requestBody: null,
                                requestParams: [
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "formTypeId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted: "formTypeId",
                                  },
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "eligibilityId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted:
                                      "eligibilityRouteId",
                                  },
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "stateId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted: "stateId",
                                  },
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "clientId",
                                    paramValue: null,
                                    extractedFromGlobal: true,
                                    elementPropertyToBeExtracted: "clientId",
                                  },
                                ],
                                method: "GET",
                              },
                              {
                                fetchUrl: [
                                  "client",
                                  "api",
                                  "form",
                                  "personform",
                                  "list",
                                ],
                                requestBody: null,
                                requestParams: [
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "personFormId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted: "personFormId",
                                  },
                                ],
                                method: "GET",
                              },
                            ],
                            backwardsDataDependency: true,
                            grandParentReference: new Map([
                              [Roles.SuperAdmin, "manage-states"],
                              [Roles.OperationStaff, "candidate-management"],
                              [Roles.StateClient, "candidate-management"],
                              [Roles.SupportingStaff, "candidate-management"],
                            ]),
                          },
                          <tableTypes.action>{
                            icon: "edit",
                            type: tableTypes.actionTypes.Link,
                            tooltip: "Edit",
                            color: "#7d7d7d",
                            visibilityRule: [
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: "formTypeId",
                                compareWithOperator:
                                  comparisionOperators.NOTEQUAL,
                                shouldHaveValue: FormTypes.Accomodation,
                              },
                              //   {
                              //   operator:tableTypes.Operators.AND,
                              //   elementProperty: 'status',
                              //   compareWithOperator: comparisionOperators.NOTEQUAL,
                              //   shouldHaveValue:'Approved'
                              // },
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: "status",
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: "Drafted",
                              },
                            ],
                          },
                        ],
                        headerCssClasses: ["width-6"],
                        cellCssClasses: ["cursor-pointer"],
                        linkMetaData: {
                          navigateToType: moduleTypes.Independent,
                          navigateTo: "application",
                          navigateToLabel: "Application",
                          requestDetails: {
                            fetchUrl: [],
                            requestBody: null,
                            requestParams: [
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "formTypeId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted:
                                  "personTenantRoleId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted:
                                  "eligibilityRouteId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "stateId",
                              },
                              // the below one is the proper one , the one below this is only temporary
                              // {
                              //   paramType: paramTypes.RouteParam,
                              //   paramName: null,
                              //   paramValue: null,
                              //   extractedFromElement: true,
                              //   elementPropertyToBeExtracted: "personEventId",
                              // },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: 0,
                                extractedFromElement: false,
                                elementPropertyToBeExtracted: "personEventId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "personFormId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "code",
                              },
                            ],
                            method: "GET",
                          },
                        },
                      },
                    ],
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "checkbox",
                        name: "checkbox",
                        cellType: "text",
                        headerType: "checkbox",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent", "w-4"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "examName",
                        name: "Exam Name",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "mode",
                        name: "Mode",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "testCenterDetails",
                        name: "Test Center Details",
                        cellType: "json",
                        cellArrayProps: ["testCenterId","testCenterName"],
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "examDateTime",
                        name: "Exam Date/Time",
                        cellType: "dobtype",
                        dateCellProps:'medium',
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "timeZoneAbbreviation",
                        name: "Timezone",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "examStatus",
                        name: "Exam Status",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        actions: [
                          <tableTypes.action>{
                            icon: "schedule",
                            type: tableTypes.actionTypes.Link,
                            tooltip: "reschedule",
                            color: "#7d7d7d",
                            visibilityRule: [
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'allowReschedule',
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: true,
                              },
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.NOTEQUAL,
                                shouldHaveValue: Roles.StateClient
                              },
                              // { 
                              //   operator:tableTypes.Operators.AND,
                              //   elementProperty: 'examStatus',
                              //   compareWithOperator: comparisionOperators.EQUAL,
                              //   shouldHaveValue:'Event Scheduled'
                              // }
                            ],
                          },
                          <tableTypes.action>{
                            icon: "cancel",
                            type: tableTypes.actionTypes.Delete,
                            tooltip: "cancel",
                            color: "#7d7d7d",
                            visibilityRule: [
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'allowCancel',
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: true,
                              },
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal:true,
                                compareWithOperator: comparisionOperators.NOTEQUAL,
                                shouldHaveValue: Roles.StateClient
                              },
                              // { 
                              //   operator:tableTypes.Operators.AND,
                              //   elementProperty: 'examStatus',
                              //   compareWithOperator: comparisionOperators.EQUAL,
                              //   shouldHaveValue:'Event Scheduled'
                              // }
                            ],
  
                            requestDetails: [
                              {
                                fetchUrl: [
                                  "client",
                                  "api",
                                  `operationstaff`,
                                  "exam",
                                  "cancel",
                                ],
                                requestBody: null,
                                requestParams: [
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "examScheduleId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted: "id",
                                  },
                                  {
                                    paramType: paramTypes.Optional,
                                    paramName: "candidateId",
                                    paramValue: null,
                                    extractedFromElement: true,
                                    elementPropertyToBeExtracted: "candidateId",
                                  },
                                ],
                                method: "DELETE",
                              },
                            ],
                          },
                          <tableTypes.action>{
                            icon: "visibility",
                            type: tableTypes.actionTypes.ComponentView,
                            tooltip: "Show Result",
                            color: "#7d7d7d",
                            visibilityRule: [
                              {
                                operator: tableTypes.Operators.OR,
                                elementProperty: "allowShowResult",
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: true,
                              },
                            ],
                          },
                        ],
                        linkMetaData: {
                          navigateToType: moduleTypes.Independent,
                          navigateTo: "scheduleExam",
                          eventDataUseful: false,
                          navigateToLabel: "Training Program Details",
                          requestDetails: {
                            fetchUrl: [],
                            requestBody: null,
                            requestParams: [
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "candidateId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "id",
                              },
                            ],
                          },
                        },
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [""],
                      },
                    ],
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "checkbox",
                        name: "checkbox",
                        cellType: "text",
                        headerType: "checkbox",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["w-4"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "RegistryName",
                        name: "Registry Name",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "EffectiveDate",
                        name: "Start Date",
                        cellType: "date",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "ExpirationDate",
                        name: "End Date",
                        cellType: "date",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
  
                      <tableTypes.column>{
                        visible: true,
                        id: "RegistryStatus",
                        name: "Status",
                        cellType: "status",
                        statuses: tableTypes.tableStatuses,
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        headerType: "normal",
                        headerAlign: "center",
                        actions: [
                          <tableTypes.action>{
                            icon: "visibility",
                            type: tableTypes.actionTypes.Link,
                            tooltip: "View",
                            color: "#7d7d7d",
                          },
                        ],
                        headerCssClasses: ["w-12"],
                        cellCssClasses: [""],
                        linkMetaData: {
                          navigateToType: moduleTypes.Independent,
                          navigateTo: "certificate",
                          eventDataUseful: false,
                          navigateToLabel: "certification",
                          requestDetails: {
                            fetchUrl: [],
                            requestBody: null,
                            requestParams: [],
                            method: "GET",
                          },
                        },
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "renewalAction",
                        name: "Renewal",
                        cellType: "action",
                        headerType: "normal",
                        headerAlign: "center",
                        actions: [
                          <tableTypes.action>{
                            icon: "schedule",
                            type: tableTypes.actionTypes.Link,
                            tooltip: "Renewal",
                            color: "#7d7d7d",
                          },
                        ],
                        headerCssClasses: ["w-12"],
                        cellCssClasses: [""],
                        linkMetaData: {
                          navigateToType: moduleTypes.Independent,
                          navigateTo: "application",
                          navigateToLabel: "Application",
                          requestDetails: {
                            fetchUrl: [],
                            requestBody: null,
                            requestParams: [
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: FormTypes.Certificate_Renewal,
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted:
                                  "PersonTenantRoleId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted:
                                  "EligibilityRouteId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "StateId",
                              },
                              {
                                paramType: paramTypes.RouteParam,
                                paramName: null,
                                paramValue: null,
                                extractedFromElement: true,
                                elementPropertyToBeExtracted: "Id",
                              },
                            ],
                            method: "GET",
                          },
                        },
                      },
                    ],
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "assignedTo",
                        name: "Assigned To",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "description",
                        name: "Description",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "statusName",
                        name: "Status",
                        cellType: "status",
                        statuses: tableTypes.tableStatuses,
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: ["text-xs"],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "subject",
                        name: "Subject",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "createdDate",
                        name: "Created Date",
                        cellType: "date",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["bg-transparent"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                            actions: [
                                <tableTypes.action>{
                                    icon: "visibility",
                                    type: tableTypes.actionTypes.Link,
                                    tooltip: "View",
                                    color: '#7d7d7d',
                                },
                            ],
                        linkMetaData: {
                            navigateToType: moduleTypes.Custom,
                            navigateTo: 'support-ticket/support-ticket-details',
                            eventDataUseful: false,
                            navigateToLabel: 'Ticket Details',
                        },
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: ["cursor-pointer"],
                    },
                    ],
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "to",
                        name: "To",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "cc",
                        name: "Cc",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "bcc",
                        name: "Bcc",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "createdOn",
                        name: "Created On",
                        cellType: "date",
                        dateCellProps:'medium',
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "subject",
                        name: "Subject",
                        cellType: "text",
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        headerType: "normal",
                        headerAlign: "center",
                        actions: [
                          <tableTypes.action>{
                            icon: "visibility",
                            type: tableTypes.actionTypes.RichTextView,
                            tooltip: "View",
                            color: "#7d7d7d",
                          },
                        ],
                        headerCssClasses: ["w-12"],
                        cellCssClasses: [""],
                      },
                    ],
                  ],
                  requestDetails: [
                    {
                      fetchUrl: ["client", "api", "Candidate", "forms-filled"],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Required,
                          paramName: null,
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted: "personTenantRoleId",
                          position: 3,
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "actionBy",
                          paramValue: null,
                          extractedFromGlobal: true,
                          elementPropertyToBeExtracted: "personTenantRoleId",
                        },
                      ],
                      method: "GET",
                    },
                    {
                      fetchUrl: [
                        "client",
                        "api",
                        "Candidate",
                        "registered-exams",
                      ],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Required,
                          paramName: null,
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted: "personTenantRoleId",
                          position: 3,
                        },
                      ],
                      method: "GET",
                    },
                    {
                      fetchUrl: ['client', 'api', 'registry', 'get-all'],
                      requestBody: null,
  
                      requestParams: [
                        {
                          paramType: paramTypes.Optional,
                          paramName: "pageNumber",
                          paramValue: 1,
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "pageSize",
                          paramValue: 1000,
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "personId",
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted: "personId",
                          position: 3,
                        },
                      ],
                      method: "GET",
                    },
                    {
                      fetchUrl: ["customerservice", "api", "tickets"],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Optional,
                          paramName: "roleId",
                          paramValue: null,
                          extractedFromGlobal: true,
                          elementPropertyToBeExtracted: "roleId",
                        },
                         {
                          paramType: paramTypes.Optional,
                          paramName: "candidateId",
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted: "personTenantRoleId",
                        },
                      ],
                      method: "GET",
                    },
                    {
                      fetchUrl: [
                        "customerservice",
                        "api",
                        "tickets",
                        "user-notifications",
                      ],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Optional,
                          paramName: "personId",
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted: "personId",
                          position: 4,
                        },
                      ],
                      method: "GET",
                    },
                  ],
                  backwardsDataDependency: true,
                  grandParentReference: new Map([
                    [Roles.SuperAdmin, "manage-states"],
                    [Roles.OperationStaff, "candidate-management"],
                    [Roles.StateClient, "candidate-management"],
                    [Roles.SupportingStaff, "candidate-management"],
                  ]),
                },
              },
              {
                class: "overflow-auto viewApp-Form",
                gdColumn: "1 / 1",
                gdColumnltlg: "1 / 1",
                gdColumnltmd: "1 / 1",
                gdColumnltsm: "1",
                fxLayout: "",
                fxLayoutAlign: "start center",
                fxFlex: "none",
                fxFlexltmd: "auto",
                fxHidexs: "false",
                fxHidesm: "false",
                widget: {
                  type: widgetTypes.DynamicForms,
                  formOptions: {
                    formDisabled: true,
                    approveRejectAllowed: true,
                    changeRequestAllowed: true,
                    addORviewNotesAllowed: true,
                    hideForm:true
                  },
                  requestDetails: [
                    {
                      fetchUrl: [
                        "client",
                        "api",
                        `form`,
                        `formsbyformtypeid`,
                      ],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Optional,
                          paramName: "formTypeId",
                          // since its manage-application i.e. manage "Application"
                          paramValue: FormTypes.PreRegistry,
                        //   extractedFromElement: true,
                        //   elementPropertyToBeExtracted: "formTypeId",
                        },
                        // hard-coding client id for now
                        {
                          paramType: paramTypes.Optional,
                          paramName: "clientId",
                          paramValue: null,
                          extractedFromGlobal: true,
                          elementPropertyToBeExtracted: 'clientORtenantId'
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "eligibilityId",
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted:
                            "eligibilityRouteId",
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "stateId",
                          paramValue: null,
                          extractedFromElement: true,
                          elementPropertyToBeExtracted:
                            "stateId",
                        },
                      ],
                      method: "GET",
                    },
                    {
                      fetchUrl: null,
                      requestBody: null,
                      requestParams: null,
                      method: "GET",
                    },
                  ],
                  backwardsDataDependency: true,
                  grandParentReference: "manage-applications",
                },
              },
          ],
        },
        {
          class: "grid process-log1",
          gdColumn: "1 / 1",
          gdColumnltlg: "1 / 1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "start center",
          fxFlex: "",
          rows: [
            {
              class: "appProcessLog w-full h-full items-initial",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start",
              fxFlex: "none",
              fxFlexltmd: "",

              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicProcessLogs,
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", `form`, `personformlogs`],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personFormId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted:
                          "personFormId",
                      },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: "manage-applications",
                widgetHeader: "Pre-Registry Process",
              },
            },
          ],
        },
      ],
    }
  ]
}