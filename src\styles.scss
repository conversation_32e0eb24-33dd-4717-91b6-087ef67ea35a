@import "~simplebar/dist/simplebar.css";
@import "~highlight.js/styles/androidstudio.css";
// exai Core
@import "./@exai/styles/core";
@import "./app/core/common-component/examroom-formbuilder/form-builder-styles.scss";

/* Quill’s default theme */
@import "~quill/dist/quill.snow.css"; /* For Snow theme */


/*
  You can override any CSS Variable or style here
  Example:

  :root {
   --sidenav-background: #333333;
  }

  All possible variables can be found in @exai/styles/core or by simply inspecting the element you want to change
  in Chrome/Firefox DevTools
*/


/**
  Uncomment the below code and adjust the values to fit your colors, the application will automatically adjust
 */

.blue-snackbar {
    color: var(--text-color2);
}

.red-snackbar {
    color: #f44336;
}

* {
    font-family: "Roboto", sans-serif;
}

:root {
    // --color-primary: blue;
    // --color-primary-contrast: white;
    // --color-accent: yellow;
    // --color-accent-contrast: black;
    // --color-warn: yellow;
    // --color-warn-contrast: black;
}

table {
    table-layout: fixed;
    word-break: break-all;
    word-wrap: break-word;
}

  :host ::ng-deep {
    .OT_publisher .OT_edge-bar-item.OT_edge-bottom.OT_mode-on {
      -webkit-transform: scaleX(-1) !important;
      display: none !important;
    }
  }
   
  .OT_mirrored {
    display: none !important;
  }
   
  body > .OT_publisher {
    display: none !important;
  }
   
  body > .OT_root {
    display: none !important;
  }
   
  body {
    --primary-button-border-color: #209e91;
  }
