<div *ngIf="global.userDetails.value.roleId == 17 || global.userDetails.value.roleId == 8 " class="dashBoard" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px" exaiContainer>

    <div class="text-sm" gdColumn="1/ 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 /-1">
        <b>Roster</b>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
        </app-ng-dynamic-breadcrumb>
    </div>

    <div class="w-full" gdColumn="2/ -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1 /-1">
        <div class="t-xs overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
            <form [formGroup]="form" >
                <div class="justify-end"   gdColumn="1/-1 " gdColumn.lt-md="1/-1" gdColumn.lt-sm="1/-1">
                    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" fxLayoutAlign="end center" class=" exam naetester " fxLayoutGap.lt-sm="0">
                        <mat-form-field class="pt-2" fxFlex="50%" appearance="outline"
                        >
                        <mat-label class="text-xs fontColor2" >Search by email</mat-label>
                        <input type="text" (input)="userMetrics($event.target.value)"class="text-xs"
                            matInput  >
                    </mat-form-field>
        
                        <mat-form-field class="pt-2" appearance="outline" fxFlex="50%" >
                            <mat-label class="text-xs fontColor2">Select Your  Date Range</mat-label>
                            <mat-date-range-input  [rangePicker]="picker">
                              <input class="text-xs" matStartDate formControlName="start" autocomplete="off" required
                                placeholder="Start date">
                              <input class="text-xs" matEndDate formControlName="end" autocomplete="off" required
                                placeholder="End date">
                            </mat-date-range-input>
                            <mat-datepicker-toggle matSuffix [for]="picker" ></mat-datepicker-toggle>
                            <mat-date-range-picker #picker></mat-date-range-picker>
                        </mat-form-field>

                        <mat-form-field class="pt-2 pb-1" appearance="outline" fxFlex="50%" >
                            <mat-label class="text-xs  fontColor2">Select Event ID</mat-label>
                            <mat-select [formControl]="stateControl" placeholder="Select Event ID"
                            class=" border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                         
                            <mat-option *ngFor="let state of ExamTypes " (click)="EventType(state)" [value]=state>
                                {{state.eventSKU}}
                            </mat-option>
                        </mat-select>
                        </mat-form-field>
        
       
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<exai-table *ngIf="global.userDetails.value.roleId == 17 ||global.userDetails.value.roleId == 8 " class="w-full" [tableOptions]="tableOptions" [displayedColumns]="displayedColumns"  [dataSource]="dataSource"   (actionPerformed)="downloadVoucher($event)">
</exai-table>

<app-scan *ngIf="global.userDetails.value.roleId == 15 &&  this.global.userDetails.value.cantest == true"></app-scan>


