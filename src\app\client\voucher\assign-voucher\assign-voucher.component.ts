import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Observable } from 'rxjs';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { get_userDetails } from '../../state/shared/shared.selectors';
import { assignVoucher, candidateCompleteList, getStateId, selectedVoucherList } from '../state/voucher.action';
import { candidateFullList, detailVoucher, gotTrainingInstStateID, voucherAssignMessage } from '../state/voucher.selector';
import { VoucherState } from '../state/voucher.state';
import { voucherList } from '../state/voucher.types';

import { VoucherService } from '../voucher.service';
import { HttpService } from 'src/app/core/http.service';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-assign-voucher',
  templateUrl: './assign-voucher.component.html',
  styleUrls: ['./assign-voucher.component.scss']
})
export class AssignVoucherComponent implements OnInit {
  TrainingSelectOptions: Array<any> = [{ id: 1, name: "Search by email Id", checked: false }, { id: 2, name: "Select by user", checked: false }]
  radioTrainingselect = new FormControl("");
  candiateDetails: FormGroup;
  candiateDetailsbyemail: FormGroup;
  FilterCandidateDetails:Array<any>=[]
  tableOptions = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [5],
    pageSize: 8,
    showAddButton: [false],
    showMatTabs: false,
    matTabs: [],
    showSearch: true,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: false,
    showHeaderRow: false
  }

  tableOptionsPractice = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false],
    showMatTabs: false,
    matTabs: [],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: false,
    showHeaderRow: false
  }

  tableOptionsassign = {
    tableName: "",
    showPaginator: false,
    pageSizeOptions: [5],
    pageSize: 8,
    showAddButton: [false],
    showMatTabs: false,
    matTabs: [],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: false,
    showHeaderRow: false
  }

  displayedColumnsassign = [[
    <tableTypes.column>{
      visible: true,
      id: "candidateName",
      name: "Candidate Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "assignCandidate",
      name: "",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["link-animation"],
    },
  ]]

  displayedColumns = [[
    <tableTypes.column>{
      visible: true,
      id: "candidateName",
      name: "Candidate Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["link-animation"],
    },
    <tableTypes.column>{
      visible: true,
      id: "examCode",
      name: "Course Completion Date",
      cellType: "date",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "TrainingInstituteName",
      name: "Training Institute Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
  ]]

  displayedColumnsTransfer = [[
    <tableTypes.column>{
      visible: true,
      id: "voucherCode",
      name: "Voucher Code",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "examcode",
      name: "Exam Code",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "price",
      name: "Voucher Price",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "action",
      name: "Action",
      cellType: "action",
      actions: [
        <tableTypes.action>{
          type: tableTypes.actionTypes.Custom,
          icon: 'delete',
          tooltip: 'Delete',
          color: "#7d7d7d",
        }
      ],
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["cursor-pointer"],
    }
  ]]
  validation_messages = {
    '_email': [
      { type: 'required', message: this.global.Email_is_required },
      { type: 'pattern', message: this.global.Enter_a_valid_email }
    ],

  };
  public dataSource: MatTableDataSource<any>;
  public dataSourceassign: MatTableDataSource<any>;
  public dataSourceassigntransfer: MatTableDataSource<any>;
  public dataSourceassignCandidateDetails: MatTableDataSource<any>;
  candidateDetails: any = [];
  assignCandidateName: any[];
  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();
  enableSendButton: boolean = false;
  disableSendButton: boolean = true;
  getVoucherList: voucherList;
  voucherList: any;
  VoucherCodes:Array<string>=[]
  data: any;
  TrainingInstituteList:Array<any>=[]
  candidateforSerachList: Array<any> = []
  multipleRoleTenantId: Array<number> = [];
  UserList: Array<object> = []
  SelectedListValue: Array<object> = []
  UserDetailsPersontenantroleId ={personTenantRoleId:0,trainingInstitueId:0,transferToPersonTenantRoleId:0,trainingProgramId:0}
  constructor(public dialogRef: MatDialogRef<AssignVoucherComponent>,
    @Inject(MAT_DIALOG_DATA)
    public SelectedValue: any,
    public store: Store<VoucherState>,
    public global: GlobalUserService,
    private voucherService: VoucherService, private https: HttpService,private snackbar:SnackbarService,private formBuilder:FormBuilder,private http:HttpClient,private cdr:ChangeDetectorRef)
  //@Inject(MAT_DIALOG_DATA) public data: DialogData
  {
    console.log(this.SelectedValue)
  //  this.SelectedValue.VoucherName.subscribe((data)=>console.log(data))
    this.SelectedListValue = this.SelectedValue.selectedVoucherList
    this.SelectedValue.tabtype == 5 ? this.dataSourceassigntransfer = new MatTableDataSource(this.SelectedListValue) : null
  }

  ngOnInit(): void {

    this.candiateDetails = this.formBuilder.group({
      registryNumber: new FormControl("")
    });
    this.candiateDetailsbyemail = this.formBuilder.group({
      email: new FormControl('', Validators.compose([Validators.required, Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,10}$')])),
    });
    this.data = this.voucherService.data
    this.store.dispatch(getStateId({ trainingInstituteID: this.global.userDetails.value.clientORtenantId }))
    let filteredcandidate = [...new Map(this.voucherService.canidateFilterName.map(item => [item['firstName'], item])).values()]

    this.store.select(get_userDetails).subscribe(data => {
      if (data) {
        data.roles.forEach(value => {
          this.multipleRoleTenantId.push(value.tenantId)
        })
        this.SelectedValue.tabtype != 5 ? this.store.dispatch(candidateCompleteList({ tenantId: this.multipleRoleTenantId })) : null
      }
    });
    this.SelectedValue.tabtype != 5 ?
     this.store.select(candidateFullList).subscribe((data: any) => {
      try {
        if (data) {
          // Filtering all candidates who have already been assigned a voucher
          this.candidateDetails = [];
          const dataMap = new Map(data.map(item => [item.firstName, item]));
          const filteredMap = new Map(filteredcandidate.map(item => [item.firstName, item]));
          const result = [
              ...data.filter(({ firstName }) => !filteredMap.has(firstName)),
              ...filteredcandidate.filter(({ firstName }) => !dataMap.has(firstName))
          ];
          this.candidateDetails = result.map(element => ({
              candidateName: `${element.firstName || ''} ${element.middleName || ''} ${element.lastName || ''}`.trim().replace(/\s+/g, ' '),
              examCode: element.trainingInstituteCompletedDate,
              personTenantRoleId: element.personTenantRoleId,
              TrainingInstituteName: element.trainingInstituteName,
              emailId:element.emailId
          }));
        }      
      }
      catch (e) { }
    }) : null
    this.store.select(gotTrainingInstStateID).subscribe((data) => {
      data.result.forEach((x) => {
        this.UserList = x.users.filter((y) => y.emailId != this.global.email)
      })

    })
    this.SelectedValue.tabtype != 5 ? this.dataSource = new MatTableDataSource(this.candidateDetails) : null
  }

  options(event){

  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  candidateSelected(event) {
    if (event.column.id = "candidateName") {
      this.enableSendButton = !this.enableSendButton;
      this.disableSendButton = !this.disableSendButton;
    }
    this.assignCandidateName = [];
    this.assignCandidateName.push({
      candidateName: event.element.candidateName,
      assignCandidate: "Assign",





      personTenantRoleId: event.element.personTenantRoleId,
      TrainingInstituteName: event.element.trainingInstituteName
    })
    this.dataSourceassign = new MatTableDataSource(this.assignCandidateName)
    console.log(this.assignCandidateName)
  }

  assignVoucher(event) {
    debugger
    if (event.column.id == "assignCandidate") {
      this.store.dispatch(assignVoucher({ voucherId: this.voucherService.voucherId, tenantroleId: event.element.personTenantRoleId }));
      this.store.select(voucherAssignMessage).subscribe((data: any) => {
        try {
          if (data == true) {
            this.dialogRef.close();
          }
          this.getVoucher();
        }
        catch (e) {

        }
      })

    }
  }
  downloadVoucher(event){
     if(event.action.icon === "delete"){
      let DeleteStatus =this.SelectedListValue.filter((x:{voucherId:string})=>x.voucherId !=event.element.voucherId)
        this.SelectedListValue = DeleteStatus
        this.dataSourceassigntransfer =new MatTableDataSource(this.SelectedListValue)
        this.cdr.detectChanges()
     }
  }

  User(Users) {
   this.UserDetailsPersontenantroleId = Users
  }

  serachresult() {
    this.filterUsers(this.candiateDetails.value.registryNumber)
  }

  serachresultbyEmail() {
    this.filterByEmail(this.candiateDetailsbyemail.value.email)
  }

  filterByEmail(emailId){
    this.http.get(`${environment.baseUrl}login/getParameters?EmailId=${emailId}`).subscribe((data:{roles:Array<{personTenantRoleId:number}>})=>{
       if(data){
           this.http.get(`${environment.baseUrl}client/api/account/getusers?personTenantRoleId=${data.roles[0].personTenantRoleId}`).subscribe((resp:Array<{firstName:string,middleName:string,lastName:string,personTenantRoleId:number}>)=>{
              if(resp){
                this.FilterCandidateDetails.push({candidateName:`${resp[0].firstName} ${resp[0].middleName} ${resp[0].lastName}`,personTenantRoleId:resp[0].personTenantRoleId })
                this.disableSendButton = false
                this.dataSource = new MatTableDataSource(this.FilterCandidateDetails)
              }
           })
       }
    })
  
    

  }

  filterUsers(emailId:string){
    this.http.get(`${environment.baseUrl}sponsor/api/Voucher/training-program-user?emailId=${emailId}`).subscribe((data: any) => {
      if (data) {
          this.TrainingInstituteList = data
      }
    })
  }


  SubmitTransferVoucher() {
    this.SelectedListValue.forEach((x:{voucherCode:string})=>{
      this.VoucherCodes.push(x.voucherCode)
    })
    let body = {
      personTenantRoleId: this.global.userDetails.getValue().personTenantRoleId,
      transferToPersonTenantRoleId: this.UserDetailsPersontenantroleId.personTenantRoleId?this.UserDetailsPersontenantroleId.personTenantRoleId:this.UserDetailsPersontenantroleId.transferToPersonTenantRoleId,
      transferTrainingProgramId:this.UserDetailsPersontenantroleId.trainingInstitueId?this.UserDetailsPersontenantroleId.trainingInstitueId:this.UserDetailsPersontenantroleId.trainingProgramId,
      voucherCode:this.VoucherCodes
    }
    this.https.TransferVoucher(body).subscribe((data:{message:string})=>{
          if(data){
            this.snackbar.callSnackbaronSuccess(`${data.message}`)
            this.dialogRef.close({id:5})
          }
    },(err:HttpErrorResponse |any)=>{
        this.snackbar.callSnackbaronError(`${err.message.message.error.
          errors[0]
          }`)
    })
  }


  getVoucher() {
    let trainingInstituteId;
    this.store.dispatch(getStateId({ trainingInstituteID: this.global.userDetails.value.clientORtenantId }))
    this.store.select(gotTrainingInstStateID).subscribe(stateId => {
      if (stateId) {
        trainingInstituteId = stateId[0].stateId
      }
    })

    this.getVoucherList = {
      examTypeId: this.voucherService.examTypeId,
      tenantId: this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId : [this.global.userDetails.value.stateId],
      stateId: this.global.userDetails.value.stateId,
      voucherStatusId: 1,
      itemCode: this.voucherService.examCode,
      personTenantRoleId: [this.global.userDetails.value.personTenantRoleId]
    }
    this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))

  }

  Cancel() {
    this.dialogRef.close();
  }

}


