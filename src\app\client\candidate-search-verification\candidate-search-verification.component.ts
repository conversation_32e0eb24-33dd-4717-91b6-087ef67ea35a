import { DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";
import { StateLists } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { environment } from 'src/environments/environment';
import { recentLinkClickEventSelector } from '../../client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { candidateStatus, courseDetails, makenull, reviewPersonForm } from '../training/state/training-program.action';
import { formSuccess } from '../training/state/training-program.selector';
import { TrainingProgramState } from '../training/state/training-program.state';
import { TrainingService } from '../training/training.service';
import { reviewForm } from '../training/training.types';

@Component({
  selector: 'app-candidate-search-verification',
  templateUrl: './candidate-search-verification.component.html',
  styleUrls: ['./candidate-search-verification.component.scss']
})
export class CandidateSearchVerificationComponent implements OnInit {
  submittrue:boolean = false
  @Input() candidateName: any;
  @Input() candidateListName: any;
  updateData: string;
  enable: boolean = false;
  updatedDate;
  changedDate: string = null;
  ticketdata=[]
  date;
  minDate
  personFormReviewId: Array<any> = [];
  disableVerification: boolean = false;
  disableVerificationpage: boolean = true;
  reasons: string = null;
  ICNA:string = null
  dataFromCandidateSearch: any = null;
  submitPersonForm: reviewForm;
  maxDate = new Date();
  displayedCandidateDetailsColumns = [
    <singleEntryTableTypes.column>{
      id: 'candidateName',
      name: 'Full Name',
      editable: false,

    },
    <singleEntryTableTypes.column>{
      id: "candidateId",
      name: "Candidate Id",
      editable: false,
      

    },
    <singleEntryTableTypes.column>{
      id: "ssn",
      name: "Last 4 SSN",
      editable: false,
 
    },
    <singleEntryTableTypes.column>{
      id: "stateName",
      name: "State",
      editable: false,
  
    },
    <singleEntryTableTypes.column>{
      id: "trainingProgramName",
      name: "Training Program Name",
      editable: false,
  
    },
    <singleEntryTableTypes.column>{
      id: "completionDate",
      name: "Completion Date",
      editable: false,
   
    },
    <singleEntryTableTypes.column>{
      id: "emailId",
      name: "Email",
      editable: false,
   
    },
    <singleEntryTableTypes.column>{
      id: "phoneNumber",
      name: "Contact Number",
      editable: false,
   
    },
  ]
  data: string[] = ['Update Completion Date', 'No Changes'];
  enableSubmit: boolean = true;
  enablesubmit:boolean = false
  counter =0

  
  constructor(public store: Store<TrainingProgramState>,
    public trainingService: TrainingService,
    public global: GlobalUserService,
    private snack:SnackbarService,
    public route: Router,private http :HttpClient) {

     
     }

  ngOnChanges(changes: SimpleChanges): void {
    
    this.candidateName = changes.candidateName.currentValue
    this.updateData = null;
    this.disableVerification = false;
    this.disableVerificationpage = true;
    this.enable = false;
    this.ngOnInit();
  }

  ngOnInit(): void {
    this.reasons = null;
    this.ICNA =null
    try {
      this.store.select(recentLinkClickEventSelector).subscribe((data) => {
        if (data != null && data.element.status == "Approved" || data.element.status == "Rejected") {
          this.route.navigateByUrl('loader/candidate-search')
        }
        this.dataFromCandidateSearch = data;
        this.ticketdata.push(data.element)
      })
    }
    catch (e) {
    }
    if (this.dataFromCandidateSearch != null && (this.candidateName == undefined)) {
      this.date = new FormControl(this.configurateDate(this.dataFromCandidateSearch.element.completionDate));
    }
    else {
      if (this.candidateListName.length == 0) {
        this.date = new FormControl(this.configurateDate(this.candidateName.completedDate));
        this.dataFromCandidateSearch = null;
      }
      else {
        this.date = new FormControl(new Date());
        this.dataFromCandidateSearch = null;
      }

  
    }
    this.minDate = this.global.userDetails.getValue().stateId == StateLists.VA?new Date():null
   
  }

  configurateDate(dateParam): Date {
    const date = new Date(dateParam);
    const dateTransformed = new Date(`${date.getUTCFullYear()}-${date.getUTCMonth()+1}-${date.getUTCDate()}`);
    return dateTransformed;
  }

  updatedname(event) {
    if (event.value == 'No Changes') {
      this.enable = true;
      this.disableVerification = true;
      this.dataFromCandidateSearch.element.completionDate != undefined ? this.date.setValue(this.configurateDate(this.dataFromCandidateSearch.element.completionDate)) : null;
      this.updatedDate = this.date.value

    }
    if (event.value == 'Update Completion Date') {
      this.counter ++
        if(this.counter == 1 && this.global.userDetails.getValue().stateId == StateLists.VA){
          const d = new Date();
          let month = d.getDate();
          this.minDate.setDate(month - 730);
        }
      this.enable = true;
      this.disableVerification = false;
    }
  }

  OnDate(event) {
    var datePipe = new DatePipe("en-US");
    var test = datePipe.transform(event, 'yyyy-MM-dd');
    this.updatedDate = test;
  }

  onChange(event) {
    this.reasons = event;
    this.ICNA =event
   
  }

  validate(event) {
    if(this.global.userDetails.getValue().stateId == StateLists.NV && ((this.ICNA !="" && this.ICNA.replace(/\s+/g, '') !="") && (this.reasons !="" && this.reasons.replace(/\s+/g, '') !="") && this.ICNA !=null && this.reasons !=null)){
      if (event.target.value && (event.target.value.trim().length)) {
        this.enableSubmit = false;
    
      } else {
        this.enableSubmit = true;
      }
    }else if(this.global.userDetails.getValue().stateId != StateLists.NV){
      if (event.target.value && (event.target.value.trim().length)) {
        this.enableSubmit = false;
      } else {
        this.enableSubmit = true;
      }
    }else{
      this.enableSubmit = true
    }
   
  }

  submit() {
    this.personFormReviewId = [];
    try {
      if (this.candidateListName.length == 0) {
        this.personFormReviewId.push(this.candidateName.personFormReviewId);
      }
      else {
        this.candidateListName.forEach(element => {
          this.personFormReviewId.push(
            element.personFormReviewId
          )
        });
      }
    }
    catch (e) {
      this.personFormReviewId.push(this.dataFromCandidateSearch.element.personFormReviewId);
    }
    if (this.updatedDate != null) {
      this.submitPersonForm = {
        personFormReviewId: this.personFormReviewId,
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        isApproved: true,
        comment: this.reasons,
        completionDate: this.updatedDate,
        personId:this.global.userDetails.value.personId,
        icnaLicense:this.global.userDetails.getValue().stateId == StateLists.NV && ((this.ICNA !="" && this.ICNA !=null && this.ICNA.replace(/\s+/g, '') !=""))?this.ICNA:null
      }

      // this.store.dispatch(reviewPersonForm({ formSubmission: this.submitPersonForm }))
      // this.store.select(formSuccess).subscribe((data: any) => {
        this.http.post(`${environment.baseUrl}client/api/client/review-personform`,this.submitPersonForm).subscribe((data:any)=>{
           if(data){
            this.global.userDetails.subscribe((user) => {
              if (user != null) {
                // this.store.dispatch(candidateStatus({ personDetail: user,erId:this.trainingService.eligibilityRoute }));
                // this.store.dispatch<Action>(courseDetails({ date: this.trainingService.createdOnDate, status: this.trainingService.currentStatus, personDetail: user,erId:this.trainingService.eligibilityRoute }))
                this.store.dispatch(candidateStatus({ personDetail: user }));
                //this.store.dispatch<Action>(courseDetails({ date: this.trainingService.createdOnDate, status: this.trainingService.currentStatus, personDetail: user}))
              }
            })
            this.disableVerificationpage = false;
            this.store.dispatch(makenull())
            if (this.dataFromCandidateSearch != null) {
            }
            this.route.navigateByUrl('/loader/candidate-search')
            this.snack.callSnackbaronSuccess(
              "Submitted Successfully"
            );
           }
        },(err:any)=>{
             this.snack.callSnackbaronError(`${err.message.message.error}`)
        }) 
      // })

    }
    else {
      this.submitPersonForm = {
        personFormReviewId: this.personFormReviewId,
        isApproved: true,
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        comment: this.reasons,
        completionDate: this.date.value,
        personId:this.global.userDetails.value.personId,
        icnaLicense:this.global.userDetails.getValue().stateId == StateLists.NV && ((this.ICNA !=null && this.ICNA !="" && this.ICNA.replace(/\s+/g, '') !=""))?this.ICNA:null   
         }

      this.http.post(`${environment.baseUrl}client/api/client/review-personform`,this.submitPersonForm).subscribe((data:any)=>{
        if(data){
          this.disableVerificationpage = false;
          this.store.dispatch(makenull())
          if (this.dataFromCandidateSearch != null) {
          }
          this.route.navigateByUrl('/loader/candidate-search')
          this.snack.callSnackbaronSuccess(
            "Submitted Successfully"
          );
        }
     },(err:any)=>{
          this.snack.callSnackbaronError(`${err.message.message.error}`)
     }) 
     
    }


  }


  reject() {
    this.personFormReviewId = [];
    try {
      if (this.candidateListName.length == 0) {
        this.personFormReviewId.push(this.candidateName.personFormReviewId);
      }
      else {
        this.candidateListName.forEach(element => {
          this.personFormReviewId.push(
            element.personFormReviewId
          )
        });
      }
    }
    catch (e) {
      this.personFormReviewId.push(this.dataFromCandidateSearch.element.personFormReviewId);
    }
    if (this.updatedDate != null) {
      this.submitPersonForm = {
        personFormReviewId: this.personFormReviewId,
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        isApproved: false,
        comment: this.reasons,
        completionDate: this.updatedDate,
        personId:this.global.userDetails.value.personId,
        icnaLicense:this.global.userDetails.getValue().stateId ==StateLists.NV?this.ICNA:null 
      }
      this.store.dispatch(reviewPersonForm({ formSubmission: this.submitPersonForm }))
      this.store.select(formSuccess).subscribe((data: any) => {
        if (data === 1) {
          this.disableVerificationpage = false;
          this.store.dispatch(makenull())
          if (this.dataFromCandidateSearch != null) {
            this.route.navigateByUrl('loader/candidate-search')
          }
        }
      })
    }
    else {
      this.submitPersonForm = {
        personFormReviewId: this.personFormReviewId,
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        isApproved: false,
        comment: this.reasons,
        completionDate: this.date.value,
        personId:this.global.userDetails.value.personId,
        icnaLicense:this.global.userDetails.getValue().stateId ==StateLists.NV?this.ICNA:null
      }
      this.store.dispatch(reviewPersonForm({ formSubmission: this.submitPersonForm }))
      this.store.select(formSuccess).subscribe((data: any) => {
        if (data === 1) {
          this.disableVerificationpage = false;
          this.store.dispatch(makenull())
          if (this.dataFromCandidateSearch != null) {
            this.route.navigateByUrl('loader/candidate-search')
          }
        }
      })
    }
    
    setTimeout(() => {
      this.route.navigateByUrl('loader/candidate-search')
    }, 3000);
  }

}
