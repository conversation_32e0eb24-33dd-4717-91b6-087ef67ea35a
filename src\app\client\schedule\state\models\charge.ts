import { Customer, paymentMethod, ResponseMeta, User } from "./paymentMethods";

export interface LineItem {
  id: string;
  item: string;
  details: string;
  quantity: number;
  price: number;
}

export interface InputMeta {
  tax?:number;
  subtotal: number;
  lineItems: LineItem[];
  reference: string;
}

export interface chargeBody {
  cartId:string;
  personTenantRoleId?:number;
  personId : string;
  firstname: string;
  lastname: string;
  email: string;
  method: string;
  card_number?: string;
  card_cvv?: string;
  card_exp?: string;
  customer_id: string;
  payment_method_id: string;
  meta: InputMeta;
  total: number;
  pre_auth: number;
  bank_account?: string;
  bank_routing?: string;
  bank_name?: string;
  bank_type?: string;
  bank_holder_type?: string;
}


export interface chargeResponse {
  id: string;
  invoice_id: string;
  reference_id: string;
  recurring_transaction_id: string;
  type: string;
  source?: any;
  merchant_id: string;
  user_id: string;
  customer_id: string;
  payment_method_id: string;
  is_manual?: any;
  success: boolean;
  message?: any;
  meta: ResponseMeta;
  total: number;
  method: string;
  pre_auth: boolean;
  last_four: string;
  receipt_email_at?: any;
  receipt_sms_at?: any;
  settled_at?: any;
  created_at: string;
  updated_at: string;
  total_refunded: number;
  issuer_auth_code?: any;
  is_refundable: boolean;
  is_voided: boolean;
  is_voidable: boolean;
  is_settling?: any;
  schedule_id?: any;
  customer: Customer;
  child_transactions: any[];
  files: any[];
  payment_method: paymentMethod;
  user: User;
}
export interface AchChargeBody{
  personTenantRoleId: number,
  personId: string,
  firstname: string,
  lastname: string,
  email: string,
  accountType: string,
  routingNumber: string,
  accountNumber: string,
  nameOnAccount: string,
  bankName: string,
  meta:InputMeta,
}

export interface AchResponceBody{
  success: boolean,
  message: string
}





