import { Component, EventEmitter, Input, Inject, OnInit, Output, ViewChildren, ViewChild } from '@angular/core';
import { formOptions, moduleTypes, statuses } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { HttpClient } from '@angular/common/http';
import { breadCrumbsSelector, recentLinkClickEventSelector } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { Store } from '@ngrx/store';
import { cellClickEvent } from '../table/dynamic-table-types';
import { Router } from '@angular/router';
import { SnackbarService } from '../../snackbar.service';
import { ConfirmationPopupComponent } from '../confirmation-popup/confirmation-popup.component';
import { MatDialog, MatDialogConfig, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BreakpointObserver } from '@angular/cdk/layout';
import { URL } from 'src/app/core/url';
import { GlobalUserService } from '../../global-user.service';
import { crumb } from '../dynamic-bread-crumbs/dynamic-bread-crumbs.types';
import { PopFromBreadCrumbUptoIndex } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { environment } from 'src/environments/environment';
import { act } from '@ngrx/effects';
import { PopUpComponent as popupcomponent } from "../../../client/application/pop-up/pop-up.component";

// Components
import { ReEvaluationDialogComponent } from '../re-evaluation-dialog/re-evaluation-dialog.component';

// RXJS
import { tap, take, takeUntil } from 'rxjs/operators';

// Shared
import { LanguageService } from '../../language.service';
import { PopUpComponent } from "../../../client/application/pop-up/pop-up.component";

// Services
import { ReEvaluationService } from '../re-evaluation.service';
import { DynamicFormService } from '@ng-dynamic-forms/core';
import { FormBuilderComponent } from '../examroom-formbuilder/form-builder.component';
import { Subject } from 'rxjs';
import { QacAccomodationComponent } from '../pop-up/qac-accomodation/qac-accomodation.component';
import { FormTypes } from '../examroom-formbuilder/form-builder.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'exai-form-view',
  templateUrl: './form-view.component.html',
  styleUrls: ['./form-view.component.scss']
})
export class FormViewComponent implements OnInit {

  formActionTypes = formActionTypes;
  statuses = statuses;
  PersonFormNoteTypeEnum = PersonFormNoteTypeEnum;
  takeValue: number
  isSubmit: boolean = false;
  permanentlySaveResponse: boolean = false;
  @Input() formJSON: any;
  @Input() userResponse: any;
  @Input() element: any;
  @Input() formOptions: formOptions;
  @Input() registerForExam?: boolean = false;
  @Output('openSidenav') notesAddView: EventEmitter<sidenavOpenEvent> = new EventEmitter<sidenavOpenEvent>();
  recentLinkClickEvent: cellClickEvent = null;
  currentFormResponseStatus: statuses;
  formtypeId: number;
  isGrievance: boolean = false;
  isRejected: boolean = false;
  isManageCandidate: boolean = true;
  grievanceData;
  @ViewChildren('hiddenUnnecessaryButton') submitButton;
  @ViewChild("formBuilder") formBuilder: FormBuilderComponent;
  formId: Array<{ id: number, formId: number }> = []
  breadCrumbArray: crumb[] = null;
  downloadReady: boolean = false;
  answerSheetPDF: any;
  isRescoreEnable : boolean;
  examTypeModels=[{id: 1, name: 'INF (In-facility)',checked: false},{id: 2, name: 'RTS (Regional Testing Center)',checked: false,}]
  Acceptance=[{id: 1, name: 'Yes',checked: false}]
   INFSelected :boolean = false
   TestReferenceId:number
  testreference:boolean= false
  AcceptanceName:boolean
  radioselect = new FormControl("");
  radioselectAcceptance = new FormControl("");
  AcceptanceValue:boolean 
  INFValue:FormGroup
  NonEditData:boolean 
  constructor(private httpClient: HttpClient,
    private store: Store,
    public router: Router,
    private lngSrvc: LanguageService,
    private snackbar: SnackbarService,
    private dialog: MatDialog,
    private reEvaluationService: ReEvaluationService,
    private breakpointObserver: BreakpointObserver,
    public globalUserService: GlobalUserService,
    public formService: DynamicFormService,
    private fb:FormBuilder
  ) {
  }

  ngOnInit(): void {
    this.store.select(recentLinkClickEventSelector).subscribe((x: cellClickEvent) => {
      if (x) {
        this.recentLinkClickEvent = x;
        this.currentFormResponseStatus = x.element.statusId?x.element.statusId:x.element?.status =='Approved'?7:''
        this.isRescoreEnable = x.element.processor ? true : false;
        if(x.element.status !='Drafted' && x.element.status !='Change Request'){
          this.radioselect.setValue(x.element.testingPreferenceName)
          let isTestCenterAcknowledge = x.element.isTestCenterAcknowledge == false && x.element.isTestCenterAcknowledge !=null?"No":x.element.isTestCenterAcknowledge !=null?'Yes':null
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(x.element.testingPreferenceId)
          this.INFValue = this.fb.group({
            infcode:[x.element.testCenterId?this.cleanINFString(x.element.testCenterId):'',[Validators.required ,Validators.maxLength(8)]],
         })
          this.NonEditData = true
          this.INFValue.get('infcode').disable()
        }else{
          this.NonEditData = false
          this.radioselect.setValue(x.element.testingPreferenceName)
          let isTestCenterAcknowledge = x.element.isTestCenterAcknowledge == false && x.element.isTestCenterAcknowledge !=null?"No":x.element.isTestCenterAcknowledge !=null?'Yes':null
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
          this.getSelectedRoute(x.element.testingPreferenceId)
          this.INFValue = this.fb.group({
            infcode:[x.element.testCenterId?this.cleanINFString(x.element.testCenterId):'',[Validators.required,Validators.maxLength(8)]],
         })
        }
        // the key should come as the same in both candidate and client api, but that is not the case
        // so ..... cheap fix 
        this.element = {
          ...this.element,
          personEventId: this.element.personFormRegistryId,
          candidateId: this.element.personTenantRoleId,
          certStatusId: this.element.statusId
        }
        this.formtypeId = x.element.formTypeId;
        if (this.formtypeId == 2 || this.formtypeId == 16) {
          this.isGrievance = true;
        }
      }
    })
    this.store.select(breadCrumbsSelector).subscribe((x: crumb[]) => this.breadCrumbArray = x)



    // this.store.select(selectLatestPersonFormId).pipe(takeUntil(this.unsubscribe)).subscribe((x: number) => {
    //   if (x) 
    //     this.takeValue = null
    // });


  }

  formAction(action: formActionTypes, comment: string = "", resultCategory?: number, remedition?: number,technicalIssue?:boolean,evaluatorResponsible?:boolean) {
    var requestBody: formActionRequest = {
      personFormId: this.recentLinkClickEvent.element.personFormId,
      personFormReviewId: this.recentLinkClickEvent.element.personFormReviewId,
      comment: comment,
      statusId: this.globalUserService?.personEventId?.statusId === statuses.Appeal && action === 'Rejected' ? 13 : statuses[action],
      personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
      resultCategoryId: resultCategory,
      remediationId: remedition,
      technicalIssue: technicalIssue,
      evaluatorResponsible:evaluatorResponsible
    }
    this.httpClient.post(URL.BASE_URL_SHORTER + `client/api/form/personform/action`, requestBody).subscribe((x: any) => {
      //Renewel
      if (this.formtypeId == 5 && x.isReviewApproved == true) {
        // this.httpClient.get(environment.baseUrl+`registry/api/certificate/get-details?registryId=${this.recentLinkClickEvent.element.personFormRegistryId}`).subscribe((data2:any)=>{
        //   if(data2){
        //     var resposity={}
        //     resposity={
        //       "certNumber":data2.CertNumber,
        //       "certType":2,
        //       "actionBy":this.globalUserService.userDetails.value.personTenantRoleId,
        //       "stateId":this.recentLinkClickEvent.element.stateId
        //     }
        //     this.httpClient.post(environment.baseUrl+`registry/api/Certificate/generate-registry`,resposity).subscribe(data1=>{
        //       if(data1){

        //       }
        //     });
        //   }
        // })
      }

      //resproctiy
      if (this.formtypeId == 6 && x.isReviewApproved == true) {
        // this.httpClient.get(environment.baseUrl+`client/api/form/personform/list?personFormId=${this.recentLinkClickEvent.element.personFormId}`).subscribe(data1=>{
        //       if(data1){
        //         let a= JSON.parse(data1[0].dataDetail);
        //         var resposity={}
        //         resposity={
        //           "certNumber":a.CertificateNumber,
        //           "certType":4,
        //           "stateId": this.recentLinkClickEvent.element.stateId,
        //           "actionBy":this.globalUserService.userDetails.value.personTenantRoleId,
        //           "personId":this.recentLinkClickEvent.element.personId
        //         }
        //         this.httpClient.post(environment.baseUrl+`registry/api/Certificate/generate-registry`,resposity).subscribe(data1=>{
        //           if(data1){

        //           }
        //         });
        //       }
        //     })

      }

      if (statuses[action] == statuses.Approved || statuses[action] == statuses.Rejected || statuses[action] == statuses.ChangeRequest) {
        this.snackbar.callSnackbaronSuccess(formActionTypes[action] + ' Application Successfully');
        // process backward navigation action using breadcrumbs
        this.navigateToParentUsingBreadCrumb();
      }

      if(action === 'Approved' || action === 'Rejected'){
      this.formtypeId === FormTypes.Grievance && this.globalUserService?.personEventId?.vMasterId!=null && this.globalUserService?.personEventId?.vMasterId !='' && this.globalUserService?.personEventId?.vMasterId !=undefined?this.reEvaluationService.generateScore({
          vMasterid: this.globalUserService.personEventId.vMasterId,
          reviewerId: this.globalUserService.userDetails.getValue().personId,
          isRescore: true,
          callReScoreAPI: true
        }).subscribe(res => {
          
        }):null
      }
    }, (err: any) => {
      this.snackbar.callSnackbaronError(err.message.error);
    })
  }

  navigateToParentUsingBreadCrumb() {

    // extracting the second last 
    let crumb = this.breadCrumbArray[this.breadCrumbArray.length - 2];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
    }
    this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbArray.indexOf(crumb) }));
  }

  submit(submit: boolean, savePermanently: boolean) {
    this.isSubmit = submit;
    this.permanentlySaveResponse = savePermanently;
    this.forceSubmitValidation();
  }

  forceSubmitValidation() {
    this.formBuilder.forceSubmitWithValidation();
  }

  openConfirmation(status: formActionTypes) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointObserver
      .observe(['(min-width : 1024px)'])
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = '40vw';
          dialogConfig.minHeight = 'fit-content';
          dialogConfig.maxWidth = '40vw';
        } else {
          dialogConfig.minWidth = '90vw';
          dialogConfig.minHeight = '90vh';
        }
      });
    dialogConfig.data = {
      type: formActionTypes[status],
      message: confirmationMessage[status],
      value: this.recentLinkClickEvent.element.statusId == 12 ? 9 : 3
    };

    const dialogRef = this.dialog.open(ConfirmationPopupComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((data) => {
      const value = data;
      if (data && data.confirmed) {
        if (status === 'Rejected') {
          this.isRejected = true;
        }
        if (this.formtypeId != 2 && this.formtypeId != 16) {
          if (status == 'Rejected' && data.message == '') {
            this.snackbar.callSnackbaronError('Please Add Reason');
          }
          else {
            this.formAction(status, data.message);
          }
        }
        else if (this.formtypeId == 2 || this.formtypeId == 16) {
          //devaccountapi.examroom.ai/client/api/form/getnotes?personFormId=110&noteTypeId=3
          //testaccountapi.examroom.ai/client/api/form/getnotes?noteTypeId=3&personFormId=110&candidateId=67"
          this.httpClient
            .get(
              environment.baseUrl + `client/api/form/getnotes?noteTypeId=${value.value}&personFormId=${this.recentLinkClickEvent.element.personFormId}`
            )
            .subscribe((data) => {
              this.grievanceData = data;
              if (this.grievanceData && this.grievanceData.length > 0 && (this.formtypeId == 2 || this.formtypeId == 16)) {
                this.formAction(status, value.message, value.resultCategory, value.remedition);
              }
              else {
                this.snackbar.callSnackbaronError(value.value === 3 && this.formtypeId == 16 ? 'Please Add Appeal Response' : 'Please Add Grievance Response');
              }
            });
        }
        // else if(this.formtypeId==6){
        //   if(!this.isRejected){
        //     }
        //     else{
        //       this.formAction(status,data.message);
        //     }
        // }
        //Renewal
        // else if(this.formtypeId==5){
        //   if(!this.isRejected){
        //   }
        //   else{
        //     this.formAction(status,data.message);
        //   }
        // }
      }
    });
  }

  changeForm() {
    this.getDisable(false)
    this.takeValue = 2
  }

  cancelForm() {
    this.takeValue = null
    this.getDisable(true)

  }

  saveResponse($event) {
    const dialogNoshow = this.dialog.open(popupcomponent, {
      data: {
        title: `${this.globalUserService.personEventId.examName}`,
        message: this.lngSrvc.curLangObj.value.Edit_grievance,
        cancelButton: this.lngSrvc.curLangObj.value.cancel,
        OKButton: this.lngSrvc.curLangObj.value.cancel,
        OkButton: this.lngSrvc.curLangObj.value.save,
      },
    })
    dialogNoshow.afterClosed().subscribe((data: any) => {
      if ((data.confirmed == true || data.confirmed.confirmed == true)) {
        this.httpClient.get(`${environment.baseUrl}client/api/form/personform/list?personFormId=${this.globalUserService.personEventId.personFormId}`).subscribe((data: any) => {
          if (data.length > 0) {
            this.formId = data
            var requestBody = {
              personFormId: this.recentLinkClickEvent.element.personFormId,
              personFormReviewId: this.recentLinkClickEvent.element.personFormReviewId,
              comment: "",
              statusId: 4,
              personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
            }
    
            this.httpClient.post(URL.BASE_URL_SHORTER + `client/api/form/personform/action`, requestBody).subscribe((x: any) => {
              if (x) {
                this.httpClient.post(`${environment.baseUrl}client/api/form/savepersonform`, {
                  formTypeID: this.formtypeId,
                  id: this.formId[0].id,
                  personTenantRoleId: this.globalUserService.personEventId.personTenantRoleId,
                  formResponse: JSON.stringify($event.formValue),
                  isSubmit: this.isSubmit,
                  formId: this.formId[0].formId,
                  code: this.globalUserService.personEventId.code,
                  personEventId: this.globalUserService.personEventId.personEventId ? Number(this.globalUserService.personEventId.personEventId) : 0,
                  actionBy: this.globalUserService.userDetails.value.personTenantRoleId,
                }).subscribe((data) => {
                  if (data) {
                    this.userResponse = $event.formValue
                    this.takeValue = null
                    this.snackbar.callSnackbaronSuccess("Form Updated Successfully")
    
    
                  }
                }, (err) => {
                  this.snackbar.callSnackbaronError(err.message.message.error.slice(err.message.message.error.search("Content"), -1));
    
                })
              }
    
    
    
            }, (err: any) => {
              this.snackbar.callSnackbaronError(err.message.message.error.slice(err.message.message.error.search("Content"), -1));
            })
    
    
          }
        }, (err) => {
          this.snackbar.callSnackbaronError(err.message.message.error.slice(err.message.message.error.search("Content"), -1));
    
        })
      }
    })
    


  }

  getDisable(value) {
    if (this.takeValue && value) {
      return false
    } else {
      return true
    }
  }

  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }

    cleanINFString(str) {
    const match = str.match(/^INF(\d+)$/);
    return match ? match[1] : null;
  }

  getVideoImage() {
    const personEventId = this.globalUserService.personEventId.personEventId;

    // this.http.get(`${environment.baseUrl}client/api/form/GetArkivDocument/`)
    this.globalUserService.personEventId.examCode != 'NA-PR' ? this.reEvaluationService.getVideoImage(personEventId).pipe(
      tap((data: any) => {
        if (data.arkivOMRSheets.length > 0) {
          this.answerSheetPDF = (data.arkivOMRSheets.filter((x) => x.extension == 'pdf'))[0];
          this.openDialogToReEvaluate();
        }
        else {
          const openDialog = this.dialog.open(PopUpComponent, {
            data: {
              title: "Re-Evaluate",
              message: this.lngSrvc.curLangObj.value.noScore,
              cancelButton: this.lngSrvc.curLangObj.value.cancel,
              OKButton: this.lngSrvc.curLangObj.value.cancel,
              OkButton: this.lngSrvc.curLangObj.value.ok,
            },
          });
          openDialog.afterClosed().subscribe();
        }
      }),
      take(1)
    ).subscribe() : this.openDialogToReEvaluate()

  }

  openDialogToReEvaluate(): void {

    const dialogRef = this.dialog.open(ReEvaluationDialogComponent, {
      width: '95vw',
      height: 'fit-content',
      minWidth: '95vw',
      id: 'reEvaluationDialogComponent',
      panelClass: 'full-width-dialog',
      data: this.answerSheetPDF
    });

    dialogRef.afterClosed().subscribe(result => {
    });
  }

  toPdf() {
    var html = '<HTML>\n<HEAD>\n';
    if (document.getElementsByTagName != null) {
      var headTags = document.getElementsByTagName("head");
      if (headTags.length > 0) html += headTags[0].innerHTML;
    }
    html += '\n</HE' + 'AD>\n<BODY>\n';
    var printReadyElem = document.getElementById("printMe");

    if (printReadyElem != null) html += printReadyElem.innerHTML;
    html += '\n</BO' + 'DY>\n</HT' + 'ML>';
    var printWin = window.open("", "processPrint");
    printWin.document.open();
    printWin.document.write(html);
    printWin.document.close();
    //logic to manually put values into text and date fields

    var inputElements = printWin.document.getElementsByTagName("input");
    var dateElemens = printWin.document.getElementsByClassName("mat-datepicker-input");
    var textAreaElements = printWin.document.getElementsByTagName("textarea");

    for (let i = 0; i < inputElements.length; i++) {
      let inputId = inputElements[i].id;
      let value = getObject(this.userResponse, inputId, false);
      value = inputId == "985a249fec204cf197fd75f71a01818c" ? (this.globalUserService.personEventId.certificateNumber) : value
      if (value) {
        printWin.document.getElementById(inputId).setAttribute("value", value);
      }
    }
    for (let i = 0; i < dateElemens.length; i++) {
      let inputId = dateElemens[i].id;
      let value = getObject(this.userResponse, inputId, false);
      let date = new Date(value);
      value = date.getUTCMonth() + 1 + '/' + date.getDate() + '/' + date.getFullYear();
      value = inputId == "0e3c7f2f8ea649f6bc06c858088b0cd2" ? new Date(this.globalUserService.personEventId.expirationDate).getUTCMonth() + 1 + '/' + new Date(this.globalUserService.personEventId.expirationDate).getDate() + '/' + new Date(this.globalUserService.personEventId.expirationDate).getFullYear() : value
      if (value) {
        printWin.document.getElementById(inputId).setAttribute("value", value);
      }
    }
    for (let i = 0; i < textAreaElements.length; i++) {
      let inputId = textAreaElements[i].id;
      let value = getObject(this.userResponse, inputId, false);
      if (value) {
        printWin.document.getElementById(inputId).textContent = value;
      }
    }
    setTimeout(() => {
      printWin.print();
      printWin.close()
    }, 1500)
  }

  qacRespose(){
    this.dialog.open(QacAccomodationComponent,{data:{id:2}});
  }

  getSelectedRoute(event) {
    if (event == 1) {
      this.INFSelected = true
      this.TestReferenceId = event
        //  this.checkProgressOfSectionsfn()
    } else if (event == 2) {
    

    }
  }

  onKeydownMain(event){
    debugger
  //  this.checkProgressOfSectionsfn()
  }

  getSelectedAccept(event){
    this.AcceptanceName = event ==1?true:false
    event ===1?this.radioselectAcceptance.setValue('Yes'):this.radioselectAcceptance.setValue('No')
    // this.checkProgressOfSectionsfn()

  }
}

export function getObject(theObject: any, key: string, setValueToNull: boolean) {
  var result = null;
  if (theObject instanceof Array) {
    for (var i = 0; i < theObject.length; i++) {
      result = getObject(theObject[i], key, setValueToNull);
      if (result)
        break;
    }
  }
  else {
    for (var prop in theObject) {
      if (prop == key) {
        result = Array.isArray(theObject[prop]) ? theObject[prop][0] : theObject[prop];
        if (setValueToNull)
          theObject[prop] = null;
        break;
      }
      else if (theObject[prop] instanceof Object || theObject[prop] instanceof Array) {
        result = getObject(theObject[prop], key, setValueToNull);
        if (result) break;
      }
    }
  }
  return result;
}
export interface formActionRequest {
  personFormId: number;
  personFormReviewId: number,
  comment: string,
  statusId: statuses,
  personTenantRoleId: number,
  resultCategoryId?: number,
  remediationId?: number,
  technicalIssue?:boolean,
  evaluatorResponsible?:boolean
}
export interface confirmationPopupData {
  type: formActionTypes,
  message: string,
  id?: number
  title?: string,
  value?: number
}
export enum formActionTypes {
  Approved = 'Approved',
  Rejected = 'Rejected',
  ChangeRequest = 'Change Request'
}
export enum confirmationMessage {
  Approved = 'Are you sure you want to approve this application?',
  Rejected = 'Are you sure you want to reject this application?',
  ChangeRequest = 'Are you sure you want to create a Change Request',
}
export interface sidenavOpenEvent {
  whatToOpen: PersonFormNoteTypeEnum;
  value?: string;
}

export enum PersonFormNoteTypeEnum {
  Note = 1,
  ChangeRequest = 2,
  GrievanceResponse = 3,
  AppealResponse = 9,
  NAEResponse = 12,
  Evaluator_Result = 14
}