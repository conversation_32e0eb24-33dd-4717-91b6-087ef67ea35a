import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { HttpService } from './http.service';
import {linkClickEvent} from "./common-component/table/dynamic-table-types";
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GlobalUserService {
 
  userDetails: BehaviorSubject<userData> = new BehaviorSubject<userData>(null);
  refreshData: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  cart: Array<any> = [];
  ScheduledStatusId = 1;
  PaymentPendingStatusId = 66;
  isEnable:boolean
  scheduling_error = 68;
  personEventID
  stateIds:number = 16
  Exam_Cancelled=9;
  payment_Completed= 70
  Exam_Completed = 8
  StateName:string
  Email_is_required = "Email is required";
  Enter_a_valid_email = "Please enter valid email";
  personId:number
  Treport:any;
  tenantStateId = 1;
  tentantId
  PersonId:number
  id:any;
  traningInstitute:number
  employerId:number
  score:BehaviorSubject<any> = new BehaviorSubject<any>('');
  state:BehaviorSubject<any> = new BehaviorSubject<any>(null);
  details:BehaviorSubject<any> = new BehaviorSubject<any>(null);
  addedToCartInfo:any
  selecetedPageSize:number=null;
  clickedviewIconDetails:any;
  problemReportCandidateId:number=null;
  email:string;
  migrated:string;
  active:boolean;
  trainingId:number;
  rolename={tsmemail:'',roleName:''}
  stateId:number;
  firstName:string;
  lastName:string;
  PersonTentantRoleId:number
  column:any
  lastClickEvents:BehaviorSubject<linkClickEvent[]> = new BehaviorSubject<linkClickEvent[]>([]);
  remainigFiles:string;
  personEventId:personTenantRoleId
  FilesUpload:Array<any>=[]
  navigate:string
  TypeValue
  icon:string;
  personFormId:number;
  formTypeId:number;
  No_slots_Avaiable=new BehaviorSubject<any>(null);
  RegistryID:number;
  roleId:number
  userstatus;
  GrievamceEvaluator : BehaviorSubject<any> = new BehaviorSubject<any>(null);
  Grievanceroute:string; 
  GrievamceEvaluatorresponse:BehaviorSubject<any> = new BehaviorSubject<any>(null);
  INF_Validation_message="Test center code must start with the letters INF followed by 5 digits"
  PublishEmail:string
  GistRoleId = 4
  Apikey='';
  SessionID='';
  Token='';
  //video start archive 
  userId: "string";
  userType: 0;
  applicationId: 0;
  tenantId: "string";
  webRTCSessionId: "string";
  currentUserId: "string";
  GIStoken:string
  Candidate_RoleID: number = 1;
  Client_RoleID: number = 2;
  SuperAdmin_RoleID: number = 13;
  OperationStaff_RoleID: number = 17;
  StateClient_RoleID: number = 14;
  TrainingInstitute_RoleID: number = 15;
  Sponser_RoleID: number = 18;
  SupportStaff: number = 8;
  Employer: number = 19
  FinanaceRole:number = 21
  PayRole:number = 26
  Hrms:number = 25
  operationaccount:number = 27
  Evalator:number = 22
  testsite:number = 23
  Proctor:number =4
  QAC:number=28
  constructor(private http:HttpClient) {

  }

  getFilterData(){
    var url = `${environment.baseUrl}formmsvc/api/form/get-candidate-import-files`
    return this.http.get(url)
  }

  getProductFruitsKey(emailId:string){
    var url =`${environment.baseUrl}client/api/Account/ProductFruit?emailId=${emailId}`
    return this.http.post(url,{});
  }

  SelectRole(role, idToken, stateId): void {
    switch (role.roleId) {
      case this.Candidate_RoleID: {
        window.location.href = environment.redirectUrls + environment.candidate + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.SuperAdmin_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.OperationStaff_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.StateClient_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + environment.state +`/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.TrainingInstitute_RoleID: {
        let roleId = stateId ==this.stateIds?environment.sponsor:environment.training
        window.location.href = environment.redirectUrls + environment.client + roleId + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Sponser_RoleID: {
        window.location.href = environment.redirectUrls + environment.client + environment.sponsor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.SupportStaff: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Employer: {
        window.location.href = environment.redirectUrls + environment.client + environment.Employees + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.FinanaceRole: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Evalator: {
        window.location.href = environment.Evalutor + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}&personId=${role.personId}`;
        break;
      }
      case this.testsite: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Proctor: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.Hrms: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.operationaccount: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.PayRole: {
        window.location.href =  environment.Evalutor  + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      case this.QAC: {
        window.location.href = environment.redirectUrls + environment.client + `/?token=${idToken}&personTenantroleId=${role.personTenantRoleId}`;
        break;
      }
      default: {
        // this.services.callSnackbaronError(this.global.Please_check_your_credentials);
        break;
      }
    }
  }
  
}
export interface userData {
  personTenantRoleId: number
  roleId: number;
  roleName: string;
  clientORtenantId: number;
  personId: number;
  stateId:number;
  clientId: number;
  stateName:string;
  stateCode:string;
  userName:string;
  role?:number;
  roles?:number
  clientStateCode?:string,
  cantest?:boolean
}
export interface personTenantRoleId{
  personTenantRoleId?:number;
  status:string,
  candidateId?:number;
  formTypeId?:number;
  stateId?:number;
  id?:number;
  PersonTenantRoleId?:number;
  PersonId?:number;
  StateId?:number;
  statusName?:string;
  personFormVersion?:string 
  certificateNumber?:string,
  expirationDate?:string,
  eligibilityRouteId?:string,
  statusId?:number,
  misconductStatus?:boolean ,
  personEventId?:Number
  stateName?:string,
  grievanceType?:string,
  examCode?:string,
  vMasterId?:string,
  personId?:number,
  naePersonId?:number,
  isRescore?:boolean,
  personFormId?:number,
  examName?:string,
  isEvent?:boolean,
  code?:string,
  registryDetail?:{expirationDate:string,personFormRegistryId:number,certType:number,registryStatusId:number,certificateNumber:string}
  registryId?:number
  isViewResultOnly?:number

}



export enum PaymentOptions
{
  PayPal = 'PayPal',
  Credit = 'Credits/Debit Cards',
  ACH = 'ACH Bank'
}
