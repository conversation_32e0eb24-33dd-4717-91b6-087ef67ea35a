<div *ngIf="!registerForExam" class="flex flex-row absolute top-0 viewBtns">
    
    <button class="btn-2 t-xs re-evaluate" (click)="getVideoImage()" mat-button *ngIf="isGrievance && [statuses.Pending, statuses.Approved].includes(currentFormResponseStatus) && isRescoreEnable">
        Rescoring
    </button>

    <button  class="btn-2 t-xs custom1 -ml-5" (click)="toPdf()" mat-button *ngIf="downloadReady && globalUserService.userDetails.value.roleId != 15">
        Download Application
    </button>
    <button *ngIf="formOptions.changeRequestAllowed && !isGrievance && globalUserService.userDetails.value.roleId == 15" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.ChangeRequest})" mat-button>
        Add Change Request
    </button>
    <button *ngIf="recentLinkClickEvent.column.linkMetaData.navigateToLabel =='View Accommodation Details'" class="btn-2 t-xs mr-2" (click)="qacRespose()" mat-button>
        QAC Response
    </button>
    <ng-container *ngIf="![statuses.Approved,statuses.Rejected,statuses.ChangeRequest,statuses.Completed].includes(currentFormResponseStatus)">
        <button *ngIf="formOptions.addORviewNotesAllowed" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Note})">
            Add/View Notes
        </button>
        <button *ngIf="formOptions.changeRequestAllowed && !isGrievance" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.ChangeRequest})" mat-button>
            Add Change Request
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-1 t-xs mr-2" (click)="openConfirmation(formActionTypes.Approved)" mat-button>
            Approve
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-3 t-xs mr-2" (click)="openConfirmation(formActionTypes.Rejected)" mat-button>
            Reject
        </button>
    </ng-container>
</div> 
<div *ngIf="registerForExam" class="flex flex-row absolute top-0 viewBtns">
    <button class="btn-1 t-xs" (click)="router.navigate(['scheduleExam',this.recentLinkClickEvent.element.personTenantRoleId])" *ngIf="formOptions.approveRejectAllowed" mat-button>
        Register For Exam
    </button>
</div> 

<div *ngIf="!registerForExam" class="flex flex-row absolute top-0 viewBtns">
    <ng-container *ngIf="isGrievance">
        <button *ngIf="![statuses.Pending,statuses.ChangeRequest].includes(currentFormResponseStatus)" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.AppealResponse})" mat-button>
            Appeal Response
        </button>
        <button  class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.GrievanceResponse})" mat-button>
            Grievance Response
        </button>
        <button class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.NAEResponse})" mat-button>
            NAE Response
        </button>
        <button class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Evaluator_Result})" mat-button>
            Measurement Details
        </button>
        <button *ngIf="formOptions.changeRequestAllowed" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Note})">
            Add/View Notes
        </button>
    </ng-container>
    <ng-container *ngIf="isGrievance && ![statuses.Approved,statuses.Rejected,statuses.ChangeRequest].includes(currentFormResponseStatus)">
        <!-- <button *ngIf="recentLinkClickEvent.element.statusId === 12" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.AppealResponse})" mat-button>
            Appeal Response
        </button> -->
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-1 t-xs mr-2" (click)="openConfirmation(formActionTypes.Approved)" mat-button>
            Approve
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-3 t-xs mr-2" (click)="openConfirmation(formActionTypes.Rejected)" mat-button>
            Reject
        </button>
    </ng-container>

</div> 


<div id="printMe" *ngIf="!formOptions.hideForm && formJSON" class="card shadow-none cardBorder mt-4" fxFlex="auto" [style.overflow-x]="'hidden!important'">
    <div  class="float-right ">
        <button class=" t-xs textsave" matTooltip="Edit"  *ngIf="takeValue ==null && takeValue ==undefined && globalUserService.personEventId.statusId == 2 && isGrievance" mat-button (click)="changeForm()">
            <mat-icon>edit</mat-icon>
        </button>
    </div> 

    <div class="float-right">
        <div class="ml-auto" *ngIf="takeValue !=null && takeValue !=undefined" >
            <button class="text-xs lh textcancel" mat-button type="button" (click)="cancelForm()" matTooltip=
            "Cancel" ><mat-icon>cancel</mat-icon></button>
            <button class=" text-xs  textsave"  mat-button type="button"  (click)="submit(true, true)" matTooltip=
            "Update form changes"><mat-icon>save</mat-icon></button>
        </div>
    </div>
    <ng-container *ngIf=" formJSON && !userResponse">
        <exai-form-builder [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [submitButtonRef]="submitButton" [disabled]="formOptions.formDisabled" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4" [openAll]="false">
        </exai-form-builder>
    </ng-container>
    <ng-container *ngIf=" formJSON && userResponse && takeValue ==null && takeValue ==undefined">
        <exai-form-builder [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse" [submitButtonRef]="submitButton" [disabled]="getDisable(formOptions.formDisabled)" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4"
            [openAll]="true" (initalSectionValidationEvent)="downloadReady = true">
        </exai-form-builder>
    </ng-container>
    <ng-container *ngIf=" formJSON && userResponse && takeValue">
        <exai-form-builder #formBuilder id="formBuilder" [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse" [submitButtonRef]="submitButton" [disabled]="getDisable(formOptions.formDisabled)" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4"
        (formSubmitEvent)="saveResponse($event)"   [openAll]="true" (initalSectionValidationEvent)="downloadReady = true" >
        </exai-form-builder>
    </ng-container>
   
</div>




<button [style.display]="'none'" #hiddenUnnecessaryButton></button> 