<div class="dashBoard" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px" exaiContainer>

    <div class="text-sm" gdColumn="1/ 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 /-1">
        <b>Voucher</b>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
    </div>

    <div class="w-full" gdColumn="2/ -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1 /-1">
        <div class="flex justify-end">
            <button class="add-new text-xs" mat-button (click)="addNewVoucher()" *ngIf="this.global.userDetails.value.roleId === 15  ||this.global.userDetails.value.roleId === 18 "> 
                <mat-icon class="text-base pt-1">add</mat-icon>Buy New Voucher</button>
                <button class="add-new text-xs" mat-button (click)="addNewVoucher()" *ngIf="this.global.userDetails.value.roleId === 17 || this.global.userDetails.value.roleId === 8 "> 
                    <mat-icon class="text-base pt-1">add</mat-icon>Add Voucher</button>
        </div>
    </div>
</div>


<exai-table class="w-full" [tableOptions]="tableOptions" [displayedColumns]="displayedColumns" [selectedIndexObs]="$tabIndex" [dataSource]="dataSource" (cellClickEvent)="selectedVoucher($event)" (add)="buyVoucher()" (actionPerformed)="downloadVoucher($event)">
</exai-table>