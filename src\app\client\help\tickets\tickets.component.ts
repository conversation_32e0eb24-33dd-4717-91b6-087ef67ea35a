import { Component, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { HelpService } from '../help.service';
import { Category } from '../interfaces/category';
import { TicketExam } from '../interfaces/ticket-exam';
import { TicketForm } from '../interfaces/ticketForm';

@Component({
  selector: 'exai-tickets',
  templateUrl: './tickets.component.html',
  styleUrls: ['./tickets.component.scss']
})
export class TicketsComponent implements OnInit, OnDestroy {
  selectedCategoryId =CategoryId

  constructor(
    private helpService: HelpService,
  ) { }

  selectedCategory: Category
  tickets$: Observable<TicketForm[] | TicketExam[]>
  selectedCategorySub: Subscription
  TicketList:Array<object>

  ngOnInit(): void {
    this.selectedCategorySub = this.helpService.selectedCategory.subscribe(data => {
      this.selectedCategory = data
    })
    this.tickets$ = this.helpService.getTickets()
    this.helpService.tickets.subscribe((data)=>{
      if(data){
        this.TicketList = data
      }else{
        this.TicketList=[]
      }
      
    })
  }

  ngOnDestroy() {
    
    this.selectedCategorySub.unsubscribe()
  }
  
}

export const CategoryId=[2,4]
