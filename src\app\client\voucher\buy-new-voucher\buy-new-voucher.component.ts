import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import { result } from 'lodash';
import { NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { Subscription } from 'rxjs';
import { count, elementAt } from 'rxjs/operators';
import { StateLists } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { GlobalUserService, userData } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http.service';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { environment } from 'src/environments/environment';
import { PopUpComponent } from '../../application/pop-up/pop-up.component';
import { Roles } from '../../dynamic-component-loader/dynamic-component-loader.types';
import { recentLinkClickEventSelector } from '../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { getCart, getvoucherCartDetails } from '../../schedule/state/scheduled.actions';
import { gotVoucherList } from '../../schedule/state/scheduled.selectors';
import { getLoading } from '../../state/shared/shared.selectors';
import { examVoucherDetails, fetchvoucherCartDetails, passDataToCart } from '../state/voucher.action';
import { cartInfo, fetchVoucherList, selectorStateId, voucherDetailsStatus } from '../state/voucher.selector';
import { VoucherState } from '../state/voucher.state';
import { CartItems } from '../state/voucher.types';


@Component({
  selector: 'app-buy-new-voucher',
  templateUrl: './buy-new-voucher.component.html',
  styleUrls: ['./buy-new-voucher.component.scss']
})
export class BuyNewVoucherComponent implements OnInit {
  items: Array<any> = [];
  OperationItems: Array<any> = [];
  vouchercount: number;
  checkPrice: any;
  finalcheckout: any = [];
  Operationcheckout: any = [];
  cartItemEventId: any = [];
  voucherList: Array<any> = [];
  passDataToCart: CartItems;
  keyUpValue: any = 0;
  keyItem: any;
  globlUserDetail: any;
  hideScreen:boolean =false;
  navigate
  event
  disablePaybutton
  trainingDetails
  constructor(public language: LanguageService, public store: Store<VoucherState>,
    public router: Router, public globalService: GlobalUserService, private http: HttpClient, private dialog: MatDialog, private HttpServices: HttpService, private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,private snackbar : SnackbarService) { }

  ngOnInit(): void {

    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      this.navigate = data.column.linkMetaData.navigateToType
    })
    if (this.navigate != null) {
      if ((this.globalService.userDetails.value.roleId == Roles.OperationStaff || this.globalService.userDetails.value.roleId == Roles.SupportingStaff) && this.navigate == "offspring") {
        const breadcrumb = [
          {
            label: "Home",
            url: "/dashboard",
          },
          {
            label: "Manage Training Program",
            url: "/loader/manage-training-programs",
          },
          {
            label: "Training Program Details",
            url: "/loader/training-institute-details",
          },
          {
            label: "Voucher",
            url: "/voucher",
          },
          {
            label: "Buy new Voucher",
            url: "",
          },
        ];
        this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
      }
    }

    this.store.select(selectorStateId).subscribe(data => {
      if (data != null && data) {
        this.trainingDetails = data
      }
    })

    this.globalService.userDetails.subscribe((user) => {
      if (user == null) {
        this.router.navigateByUrl('/voucher')
      } else {
        if ((user != null && Roles.TrainingInstitue === user.roleId) || (user != null && Roles.Sponsor === user.roleId)) {
          this.globlUserDetail = user;
          if ((Roles.TrainingInstitue === user.roleId && StateLists.CA != user.stateId) || Roles.Sponsor === user.roleId) {
            const breadcrumb = [
              {
                label: "Home",
                url: "/training",
              },
              {
                label: "Voucher",
                url: "/voucher",
              },
              {
                label: " Buy new Voucher",
                url: "",
              },
            ];
            this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
          }else if(Roles.TrainingInstitue === user.roleId && StateLists.CA == user.stateId){
            const breadcrumb = [
              {
                label: "Home",
                url: "/voucher",
              },
              {
                label: "Voucher",
                url: "/voucher",
              },
              {
                label: " Buy new Voucher",
                url: "",
              },
            ];
            this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
          }
          this.store.dispatch<Action>(examVoucherDetails({ personDetail: user }))
          this.store.dispatch<Action>(fetchvoucherCartDetails({ personTentantRoleId: this.globlUserDetail.personTenantRoleId }))
        } else {
          let tempuser = {
            stateId: this.trainingDetails.stateId,
            personId: this.trainingDetails.personId,
            roleId:15,
          }
          this.store.dispatch<Action>(examVoucherDetails({ personDetail: tempuser }))
          // this.store.dispatch(VoucherDetails({ personDetail: tempuser }));
        }
      }

    });


    this.store.select(voucherDetailsStatus).subscribe((data: any) => {
      try {
        this.items = [];
        this.OperationItems = []

        ///WA state removing combination exams and skills ///
        let WAallowedVoucherItems =['Nurse Aide Written Exam',"Nurse Aide Oral Exam"]
        let WAState = data.filter((x)=>WAallowedVoucherItems.includes(x.voucherName))
        let VoucherDetails = data;
        ///WA state removing combination exams and skills ///
        var result = VoucherDetails.reduce((unique, o) => {
          if (!unique.some(obj => obj.price === o.price && obj.voucherCode === o.voucherCode)) {
            unique.push(o);
          }
          return unique;
        }, []);

        result.forEach(element => {
          this.items.push({ 'name': element.voucherName, 'Price': element.price, 'value': 0, 'examCode': element.voucherCode })
          this.OperationItems.push({ 'name': element.voucherName, 'Price': element.price, 'value': 0, 'examCode': element.voucherCode, "voucherItemId": element.voucherItemId, "examtypeId": element.examTypeId })
        });
      }
      catch (e) { }
    })
    this.store.select(fetchVoucherList).subscribe((data: any) => {
      try {
        if (data != null) {
          this.voucherList = data;
        }
      }
      catch (e) { }
    });

  }

  // Only Integer Numbers for ssn
  keyPressNumbers(event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    // Only Numbers 0-9
    if ((charCode < 48 || charCode > 57)) {
      event.preventDefault();
      return false;
    } else {
      return true;
    }
  }

  keyUp(event, item) {
    this.keyUpValue = event.target.value;
    item.value = Number(this.keyUpValue);

  }

  examAdd(event) {
    event.value = event.value + 1;
    this.keyUpValue = event.value;
    (document.getElementById(`${event.voucherCode}`) as HTMLInputElement).value = event.value;
  }

  examMinus(event) {
    if (event.value > 0) {
      event.value = event.value - 1;
      (document.getElementById(`${event.voucherCode}`) as HTMLInputElement).value = event.value;
    }
  }
  paynow() {

    let check: boolean;
    this.items.forEach(value => {
      if (value.value > 0) {
        check = true;
      }
    })
    if (check == true && check != undefined) {
      this.getCartEventId();
      // let temp = {},
      // countItems = [];
      // this.items.forEach(data=>{
      //   if(!temp[data.examTypeId]){
      //     temp[data.examTypeId]={name:data.name,Price:data.Price,value:0,examTypeId:data.examTypeId}
      //     countItems.push(temp[data.examTypeId])
      //   }
      //   temp[data.examTypeId].value+=data.value;
      // })

      var result = this.items.reduce((unique, o) => {
        if (!unique.some(obj => obj.value === o.value && obj.examCode === o.examCode)) {
          unique.push(o);
        }
        return unique;
      }, []);

      // result.forEach(finalPrice => {
      //   this.items.forEach(async listofVoucher => {
      //     if (finalPrice.Price == listofVoucher.Price && listofVoucher.value > 0) {
      //       value+=listofVoucher.value*listofVoucher.Price;       
      //       this.finalcheckout.push({
      //         amount: Number(listofVoucher.Price),
      //         cartItemTypeId: 2,
      //         examCode: listofVoucher.examCode,
      //         quantity: listofVoucher.value,
      //         personEventCartId: this.cartItemEventId[listofVoucher.examCode] != undefined ? Number(this.cartItemEventId[listofVoucher.examCode].personEventCartId) : 0
      //       })
      //     }
      //   });
      // })

      result.forEach(element => {
        if (element.value > 0) {
          this.finalcheckout.push({
            amount: Number(element.Price),
            cartItemTypeId: 2,
            examCode: element.examCode,
            quantity: element.value,
            personEventCartId: this.cartItemEventId[element.examCode] != undefined ? Number(this.cartItemEventId[element.examCode].personEventCartId) : 0
          })
        }
      });

      this.passDataToCart = {
        personTenantRoleId: this.globalService.userDetails.value.personTenantRoleId,
        currencyId: 1,
        cartItems: this.finalcheckout
      }

      this.store.dispatch(passDataToCart({ cartDetails: this.passDataToCart }))

      this.store.select(cartInfo).subscribe((data) => {
        if (data != null) {
          this.router.navigateByUrl(`scheduleExam/${this.globalService.userDetails.value.personTenantRoleId}/payment/page`)
          let subs: Subscription = this.http.get(`${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.globalService.userDetails.value.personTenantRoleId}`).subscribe((cart: any) => {
            this.globalService.cart = cart;
            subs?.unsubscribe();
          })
        }
      })

    }

  }

  Generate(event) {
    let voucherData = {};
    var Operationresults = this.OperationItems.reduce((unique, o) => {
      if (!unique.some(obj => obj.value === o.value && obj.examCode === o.examCode)) {
        unique.push(o);
      }
      return unique;
    }, []);

    Operationresults.forEach(element => {
      if (element.value > 0) {
        this.Operationcheckout.push({
          price: element.Price,
          voucherName: element.name,
          examCode: element.examCode,
          count: element.value,
          voucherItemId: element.voucherItemId,
          examTypeId: element.examtypeId,
        })

      }


    });

    let examsTypes = this.OperationItems.filter((x) =>
      this.Operationcheckout.find((y) => y.examCode === x.examCode)
    );

    this.Operationcheckout.forEach((voucherDetails: any) => {
      examsTypes.forEach((item) => {
        if (item.examCode === voucherDetails.examCode) {
          voucherData[voucherDetails.examCode] = {
            price: voucherDetails.price,
            count: String(voucherDetails.count),
            voucherName: voucherDetails.voucherName,
            examTypeId: item.examtypeId,
            voucherItemId: item.voucherItemId,
          };
        }
      });


    });
    const dialogRef = this.dialog.open(PopUpComponent, {
      data: {
        title: this.language.curLangObj.value.Voucher,
        message: "",
        cancelButton: this.language.curLangObj.value.no,
        OkButton: this.language.curLangObj.value.yes,

      },
    })
    dialogRef.afterClosed().subscribe(data => {
      if (data.confirmed == true || data.confirmed?.confirmed == true) {
        let body = {
          personTenantRoleId:this.trainingDetails.personTenantRoleId,
          stateId: this.trainingDetails.stateId,
          traniningInsId: this.trainingDetails.trainingId,
          voucherData,
          generatedby:this.globalService.userDetails.value.personId
        }
        this.event=this.store.select(getLoading).subscribe(data=>{
          this.disablePaybutton=data
        })
        this.HttpServices.getVoucherGenerate(body).subscribe((data:any)=>{
          if(data){
            this.event?.unsubscribe();
            this.snackbar.callSnackbaronSuccess("Voucher Generate Successfully")
            this.router.navigateByUrl('/voucher')
            let tempuser = {
              stateId: this.trainingDetails.stateId,
              personId: this.trainingDetails.personId,
              roleId:15,
            }
            this.store.dispatch<Action>(examVoucherDetails({ personDetail: tempuser }))
          }
        })
      }
    })

  }

  untilPaymentProcess(item){
    if(item==0){
      if(this.disablePaybutton){
        this.hideScreen=true
        return false;
      }
      else{
        return true;
      }
      
    }
    else if(item==1){
      if(this.disablePaybutton){
        return this.disablePaybutton;
      }
      else{
        return false;
      }
    //   // return true
    }
  }
  getCartEventId() {
    let cartItemDetails = {}
    this.store.dispatch<Action>(fetchvoucherCartDetails({ personTentantRoleId: this.globalService.userDetails.value.personTenantRoleId }))
    this.items.forEach(fromPersonList => {
      this.voucherList.forEach(fromCartList => {

        if (fromCartList.examName == fromPersonList.name && fromPersonList.value != 0) {
          cartItemDetails[fromCartList.examCode] = {
            examName: fromCartList.examName,
            personEventCartId: fromCartList.personEventCartId,
            examType: fromCartList.examCode
          }
          this.cartItemEventId[fromCartList.examCode] = (cartItemDetails[fromCartList.examCode])
        }
      });
    });
  }

}

export const WAstate=[15]





