export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://devaccountapi.examroom.ai/",
  
  reportUrl:"https://reports.examroom.ai/",
  reportUrlapi:"https://reportapi.examroom.ai/",
  redirectUrl: "https://devaccount.examroom.ai/",
  config: {
    iceServers: [{
      urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
    },
    {
      username: "bruce<PERSON>",
      credential: "**********",
      urls: [
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
        "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
      ]
    }
    ]
  }
};

