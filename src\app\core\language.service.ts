import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type language = {
  dashboard: string;
  application: string;
  eligibility:string;
  voucher:string;
  examscheduled: string;
  grievanceform: string;
  manageprofile: string;
  deletemsg2:string
  help: string;
  DeactivateMessage:string;
  // Application component
  applicationProcess: string;
  accommodationProcess: string;
  currentStatus: string;
  submitDate: string;
  waitTime: string;
  reason: string;
  summary: string;
  apply: string;
  appForm: string;
  cancel: string;
  saveDraft: string;
  submit: string;
  save: string;
  leavePage: string;
  leavePageMsg: string;
  selectApp: string;
  state: string;
  stateError: string;
  selectEligibility: string;
  eligibilityDesc: string;
  inivite_email:string
  start: string;
  welcome: string;
  welMsg: string;
  appId: string;
  examMode: string;
  examDate: string;
  examTime: string;
  registerExam: string;
  addNewApp: string;
  fillApp: string;
  startExam: string;
  checkExamScore: string;
  getCertified: string;
  systemCheck: string;
  deletePage: string;
  deleteMsg: string;
  delete: string;
  correctionProcess:string;
  manageVoucherCodes:string;
  cancelExam: string;
  cancelonboard: string;
  cancelExamTitle:string;
  delete_employee:string
  No_show:string;
  Edit_grievance:string
  Confirm_Booklet:string
  Refund_Payment:string
  Re_evaluate:string;
  noScore:string;
  Comment:string
  deletemsg:string;
  No_slot_avaiable:string
  yes:string;
  no:string;
  status:string;
  ok:string;
  user:string;
  users:string;
  emulateMessage:string;
  emulateTitle:string;
  Voucher:string
  editEmail:string
  editPhoneNumber:string
  confirmed:string;
  StatusUpdate : string;
  Absent:string;
  GIS:string;
  TSM:string;
  EventComplete:string,
  triningerror:string,
  Edit_QAC:string
  remove_suspension:string
};

@Injectable({
  providedIn: 'root'
})

export class LanguageService {

  english: language = {
    dashboard: 'Dashboard',
    No_slot_avaiable:'There are no slots available at a test center for that date, would you like to schedule an online exam instead?',
    eligibility: 'Manage Eligibility Routes',
    voucher:'Voucher',
    application: "Application",
    examscheduled: "Exam Schedule",
    grievanceform: "Grievance Form",
    manageprofile: "Manage Profile",
    help: "Help",
    ok:"OK",
    inivite_email:"Are you sure you want to send invite email link?",
    delete_employee:"Are you sure you want to delete this Employee?",
    remove_suspension:"Are you sure you want to remove the email from Suppression list?",    applicationProcess: "Application Process",
    accommodationProcess: "Accommodation Process",
    correctionProcess:"Correction Form",
    currentStatus: "Current Status",
    submitDate: "Submitted Date",
    Absent:"Do you want to mark the candidate absent?",
    waitTime: "Waiting Time",
    reason: "Reason for rejection",
    Edit_grievance:'Are you sure you want to edit the greivance?',
    summary: "Summary",
    apply: "Apply Again",
    appForm: "Application Form",
    cancel: "Cancel",
    saveDraft: "Save as Draft",
    triningerror:"Please contact Credentia Support Team",
    submit: "Submit",
    save: "Yes",
    noScore: "No Record Found!",
    Re_evaluate:"Are you sure you want to re-evaluate the score?",
    Voucher:"Are you sure want to generate the Voucher?",
    Refund_Payment:"Are you sure want to refund this exam?",
    leavePage: "Submit Application",
    leavePageMsg: "Are you sure you want to submit this application?",
    Confirm_Booklet:"Are you sure you want to confirm booklet?",
    selectApp: "Select Application",
    state: "Pennsylvania",
    stateError: "State is Required",
    selectEligibility: "Select Your Eligibility Route",
    eligibilityDesc: "Select an eligibility route to start your application",
    start: "Start",
    welcome: "Welcome,",
    welMsg: "Phasellus viverra nulla ut metus varius laoreet.",
    appId: "Application ID",
    addNewApp: "Start New Application",
    fillApp: "Fill Application form",
    registerExam: "Register For Exam",
    startExam: "Start Exam",
    checkExamScore: "Check Exam Scores",
    getCertified: "Get Certified",
    systemCheck: "System Check",
    examMode: "Exam Mode",
    examDate: "Exam Date",
    examTime: "Exam Time",
    deletePage: "Delete Application",
    Edit_QAC:"Are you sure you want to edit this QAC details?",
    deleteMsg: "Are you sure you want to delete this application?",
    DeactivateMessage: "Are you sure you want to deactive this candidate?",
    status:'Are you sure you want to confirm the user?',
    delete: "Delete",
    manageVoucherCodes: 'Manage Voucher Codes',
    cancelExam: "Are you sure you want to cancel the Exam ?",
    cancelonboard:"Are you sure you want to cancel the Onboard ",
    cancelExamTitle:"Cancel Exam",
    deletemsg:"You already have a different candidate records in your cart. Click Yes to clear existing cart items!",
    deletemsg2:"Are you sure you want to add this to cart?",
    yes:'Yes',
    no:'NO',
    user:'USER IS ALREADY ',
    EventComplete:"Do you want to Complete the event?",
    GIS:"Do you want to visit event management console?",
    TSM:"Do you want to visit slot management console?",
    users:'USER IS NOT CONFIRMED ',
    emulateTitle:'User is not confirmed',
    emulateMessage:'Emulate feature is not available',
    editEmail:'Are you sure you want to update the Email Id?',
    editPhoneNumber:'Are you sure you want to update the Phone Number ?',
    confirmed:"CONFIRMED",
    No_show:'Are you sure you want to change status from No Show to Cancel ?',
    Comment:"Changing status from no show to cancel",
    StatusUpdate:"<div>Disabling the Training Program will cancel all <br>applications  still  pending the Training Program’s review.<br> Please select Yes to confirm the profile should be disabled</div>"
   
   
  };

  globalLanguage: BehaviorSubject<string> = new BehaviorSubject<string>('english');

  curLangObj: BehaviorSubject<language> = new BehaviorSubject<language>(this.english);
  $curLangObj: Observable<language> = this.curLangObj.asObservable();

  constructor() {
    this.globalLanguage.subscribe((value: string) => {
      switch (value) {
        case 'english': this.curLangObj.next(this.english);
          break;
        default: this.curLangObj.next(this.english);
      }
    })
  }


  
  // login
  email: string = "Email";
  password: string = "Password";
  login: string = "Login";
  forgot_Pwd: string = "Forgot Password?";
  Or_SignIn_With: string = "or sign in with";
  signUp: string = "Sign up";
  Dont_Have_an_Account: string = "Don't have an account?";
  Please_change_your_pwd: string = "Please change your password"

  // login validation msg's
  Email_is_required = "Email is required";
  Enter_a_valid_email = "Enter a valid email";
  Password_is_required = "Password is required";
  Please_enter_valid_password = "Please enter valid password";

  // login snackbar msg's
  Invalid_credentials_Login_failed = "Invalid credentials, Login failed";
  Please_check_your_credentials = "Please check your credentials";
  // clientRoleID
  Candidate_RoleID: number = 1;
  Client_RoleID: number = 2;
  SuperAdmin_RoleID: number = 13;
  OperationStaff_RoleID: number = 17;
  StateClient_RoleID: number = 14;
  TrainingInstitute_RoleID: number = 15;
  Sponser_RoleID: number = 18;
  // register
  Create_Your_Account: string = "Create Your Account";
  General_Information: string = "General Information";
  First_name: string = "First name";
  Middle_name: string = "Middle name";
  Last_name: string = "Last name";
  Organization_Code: string = "Organization Code";
  Other_Information: string = "Other Information";
  Date_of_Birth: string = "Date of Birth";
  Gender: string = "Gender";
  Address: string = "Address (Number and Street)";
  City: string = "City";
  Zip_Code: string = "Zip Code";
  State: string = "State";
  SSN: string = "SSN";
  Phone_number: string = "Phone number";
  Account_Setup: string = "Account Setup";
  Enter_Password: string = "Enter Password";
  Confirm_Password: string = "Confirm Password";
  By_clicking_SignUp: string = "By clicking Sign Up, you agree to our";
  Terms: string = "Terms";
  Data_Policy: string = "Data Policy";
  Cookie_Policy: string = "Cookie Policy.";
  and: string = "and";
  Sign_Up: string = "Sign Up";
  Sign_In: string = "Sign In";
  Already_have_an_account: string = "Already have an account?";
  Terms_and_DataPolicy: string = "Terms and Data Policy";
  Passwords_not_matched: string = "Passwords didn't matched.";

  // register form validation msg's
  // First name
  First_name_is_required: string = "First name is required";
  Maximum_15_characters_are_allowed = "Maximum 15 characters are allowed";
  Please_enter_valid_first_name = "Please enter valid first name";
  // Last name
  Please_enter_valid_last_name = "Please enter valid last name";
  Last_name_is_required = "Last name is required";
  // OrgID
  Please_enter_your_organization_ID = "Please enter your organization ID";
  // DOB
  Please_select_your_DOB = "Please select your DOB";
  // gender
  Please_select_your_gender = "Please select your gender";
  // address
  Please_enter_your_address = "Please enter your address";
  Maximum_225_characters_are_allowed = "Maximum 225 characters are allowed";
  // city
  Please_enter_your_city = "Please enter your city";
  Please_enter_valid_City_name = "Please enter valid City name";
  // zipcode
  Zip_code_is_required = "Zip code is required";
  Maximum_5_numbers_are_allowed = "Maximum 5 numbers are allowed";
  // state
  Please_enter_your_state = "Please enter your state";
  Maximum_25_characters_are_allowed = "Maximum 25 characters are allowed";
  Please_enter_valid_state_name = "Please enter valid state name";
  // ssn
  Please_enter_your_Social_Security_number = "Please enter your Social Security number";
  Please_enter_valid_SSN = "Please enter valid SSN";
  // phNo
  Phone_number_is_required = "Phone number is required";
  Enter_a_valid_phone_number = "Enter a valid phone number";
  // password
  Password_should_not_exceed_99_characters = "Password should not exceed 99 characters";
  Use_8_or_more_characters = "Use 8 or more characters with a mix of letters, numbers & symbols";

  // register form snackbar msg's
  Registered_Successfully = "Registered Successfully";
  Created_Successfully = "Created Successfully";
  Updated_Successfully ="Updated Successfully"
  Registration_failed = "Registration failed";
  Please_enter_your_correct_emailID = "Please enter your correct email ID";
  Please_check_your_email_to_verify_the_link = "Please check your email to verify the link";

  // verify email
  Verify_your_email: string = "Verify your email";
  You_will_need_to_verify_your_email_to_complete_registration: string = "You will need to verify your email to complete registration";
  Incorrect_Email: string = "Incorrect Email ?";
  Resend_Email: string = "Resend Email";
  Contact_Support: string = "Contact Support";
  Verification_Completed: string = "Verification Completed";
  dummy_terms1: string = "Lorem ipsum dolor sit amet, consectetuer adipiscing elit.Aeneancommodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla consequat massa quis enim. Donec pede justo, fringilla vel, aliquet nec, vulputate eget, arcu. In enim justo, rhoncus ut, imperdiet a, venenatis vitae, justo. Nullam dictum felis eu pede mollis pretium. Integer tincidunt. Cras dapibus. Vivamus elementum semper nisi. Aenean vulputate eleifend tellus. Aenean leo ligula, porttitor eu, consequat vitae, eleifend ac, enim. Aliquam lorem ante, dapibus in, viverra quis, feugiat a, tellus. Phasellus viverra nulla ut metus varius laoreet. Quisque rutrum. Aenean imperdiet. Etiam ultricies nisi vel augue. Curabitur ullamcorper ultricies nisi."
  dummy_terms2: string = "Nam pretium turpis et arcu. Duis arcu tortor, suscipit eget, imperdiet nec, imperdiet iaculis, ipsum. Sed aliquam ultrices mauris. Integer ante arcu, accumsan a, consectetuer eget, posuere ut, mauris. Praesent adipiscing.Phasellus ullamcorper ipsum rutrum nunc. Nunc nonummy metus. Vestibulum volutpat pretium libero.Cras id dui. Aenean ut eros et nisl sagittis vestibulum. Nullam nulla eros, ultricies sit amet,nonummy id, imperdiet feugiat, pede. Sed lectus. Donec mollis hendrerit risus. Phasellus nec sem in justo pellentesque facilisis. Etiam imperdiet imperdiet orci. Nunc nec neque. Phasellus leo dolor, tempus non, auctor et, hendrerit quis, nisi. Curabitur ligula sapien, tincidunt non, euismod vitae, posuere imperdiet, leo. Maecenas malesuada. Praesent congue erat at massa. Sed cursus turpis vitae tortor. Donec posuere vulputate arcu. Phasellus accumsan cursus velit."
  dummy_terms3: string = "Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Sed aliquam, nisi quis porttitor congue, elit erat euismod orci, ac placerat dolor lectus quis orci.Phasellus consectetuer vestibulum elit. Aenean tellus metus, bibendum sed, posuere ac, mattis non, nunc. Vestibulum fringilla pede sit amet augue. In turpis. Pellentesque posuere. Praesent turpis.Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc, eu sollicitudin urna dolor sagittis lacus. Donec elit libero, sodales nec, volutpat a, suscipit non, turpis. Nullam sagittis. Suspendisse pulvinar, augue ac venenatis condimentum, sem libero volutpat nibh, nec pellentesque velit pede quis nunc. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae; Fusce id purus.Ut varius tincidunt libero.Phasellus dolor.Maecenas vestibulum mollis diam.Pellentesque ut neque.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.In dui magna, posuere eget, vestibulum et, tempor auctor, justo. In ac felis quis tortor malesuada pretium.Pellentesque auctor neque nec urna.Proin sapien ipsum, porta a, auctor quis, euismod ut, mi.Aenean viverra rhoncus pede.Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Phasellus accumsan cursus velit."
  Back_to_Login: string = "Back to Login";

  // verified email
  Your_email_is_Verified: string = "Your email is Verified";
  Your_email_id_is_successfully_verified: string = "Your email id is successfully verified";
  Now_you_can_Login: string = "Now you can Login";
  Continue_to_Login: string = " Continue to Login";
  Your_email_is_already_verified_Continue_Login: string = "Your email is already verified, please click on Continue to Login button";

  // forget
  Enter_your_MailID_to_recieve_resetlink: string = "Enter your Mail ID to recieve a reset link";
  Submit: string = "Submit";
  Back_to_Sign_In: string = "Back to Sign In";

  // reset
  Reset_Password: string = "Reset Password?";
  Access_code: string = "Access code";
  access_code_placeholder: string = "253436";
  email_placeholder: string = "<EMAIL>";
  pwd_placeholder: string = "********";
  Back: string = "Back";
  Reset_Password_btn: string = "Reset Password";
  New_Password: string = "New Password";
  // reset form validation msg's
  access_code_is_required: string = "access code is required"
  // reset form snackbar msg's
  Password_updated_successfully: string = "Password updated successfully";
  Please_try_again: string = "Please try again";
  // terms and conditions
  Terms_and_conditions: string = "Terms and conditions";
  Last_Updated_Date: string = "Last Updated June 24, 2021";
  Summary: string = "Summary:";
  General_Terms_Conditions: string = "General Terms and Conditions";
  Acceptable_UsePolicy: string = "Acceptable Use Policy";
  Cancellation_Policy: string = "Cancellation Policy";
  Privacy_Policy: string = "Privacy Policy";

  // forgot pwd
  Please_check_your_mail_for_accesscode: string = "Please check your mail for the access code";
  Invalid_emailID: string = "Invalid emailID";

  // Change pwd
  Change_Password: string = "Change Password";
  Old_Password: string = "Old Password";
  Password_changed_successfully: string = "Password changed successfully";
//edit email
emaile_edit:string=""
}
