import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes, statuses } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Offspring,
            name: "Registration Details",
            moduleID: "manage-registry-details",
            nestingType: nestingTypes.RowsInColumns,
            iconRef: "",
            routeRef: "/loader",
            class: "",
            gdColumns: "1fr",
            gdColumnsltlg: "1fr",
            gdColumnsltmd: "1fr",
            gdColumnsltsm: "1fr",
            gdGap: "12px",
            columns: [
                {
                    class: "flex-col-important",
                    gdColumn: "1/1",
                    gdColumnltlg: "1/1",
                    gdColumnltmd: "1/1",
                    gdColumnltsm: "1",
                    fxLayoutAlign: "space-between stretch",
                    fxFlex: "auto",
                    rows: [
                        {
                            class: "andjela",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicSingleEntryTables,
                                widgetHeader: "Candidate details",
                                displayedColumns: [
                                    [
                                        <singleEntryTableTypes.column>{
                                            id: ["firstName", "middleName", "lastName"],
                                            name: "Candidate Name",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "ssn",
                                            name: "Security Number",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "id",
                                            name: "CandidateId",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "address",
                                            name: "Address",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "emailId",
                                            name: "Email Id",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "phoneNumber",
                                            name: "Contact Number",
                                            editable: false,
                                        },
                                    ],
                                ],
                                requestDetails: [

                                    {
                                        fetchUrl: ["client", "api", "account", "getuser"],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "Id",
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "PersonId",
                                                //   paramName: "personTenantRoleId",
                                                //   paramValue: null,
                                                //   extractedFromElement: true,
                                                //   elementPropertyToBeExtracted: "personTenantRoleId",
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ],
                                backwardsDataDependency: true,
                                grandParentReference: "manage-registry",
                            }
                        },
                        {
                            class: "",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: [],
                                    cellRowCssClasses: ["my-3 shadow"],
                                    showPaginator: false,
                                    showColumnFilter: false,
                                    showAddButton: [false],
                                    showMatTabs: false,
                                    showSearch: false,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "RegistryName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "CertNumber",
                                            name: "Registration Number",
                                            cellType: "text",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "EffectiveDate",
                                            name: "Issue Date",
                                            cellType: "date",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ExpirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "RegistryStatus",
                                            name: "Status",
                                            cellType: "status",
                                            statuses: tableTypes.tableStatuses,
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent"],
                                            cellCssClasses: ["text-xs"],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                    requestDetails: [
                                                        {
                                                            fetchUrl: [
                                                                "client",
                                                                "api",
                                                                `registry`,
                                                                `get-url`,
                                                            ],
                                                            requestBody: null,
                                                            requestParams: [
                                                                {
                                                                    paramType: paramTypes.Optional,
                                                                    paramName: 'certPath',
                                                                    paramValue: null,
                                                                    extractedFromElement: true,
                                                                    elementPropertyToBeExtracted: "Certpath"
                                                                },
                                                                {
                                                                    paramType: paramTypes.Optional,
                                                                    paramName: 'certNo',
                                                                    paramValue: null,
                                                                    extractedFromElement: true,
                                                                    elementPropertyToBeExtracted: "CertNumber"
                                                                }
                                                            ],
                                                            method: "GET",
                                                        },
                                                    ],
                                                    backwardsDataDependency: true,
                                                    grandParentReference: "manage-registry",
                                                },
                                            ],
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12",],
                                            cellCssClasses: ["cursor-pointer"],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Independent,
                                                navigateTo: 'certificate',
                                                eventDataUseful: false,
                                                navigateToLabel: 'certification',
                                                requestDetails: {
                                                    fetchUrl: [],
                                                    requestBody: null,
                                                    requestParams: [],
                                                    method: "GET",
                                                },
                                            },
                                        },
                                    ],
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: ["client", "api", 'registry', 'get-details'],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'registryId',
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "Id",
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ],
                                backwardsDataDependency: true,
                                grandParentReference: "manage-registry",
                            },
                        }
                    ],
                },
                {
                    class: "grid",
                    gdColumn: "1 / 1",
                    gdColumnltlg: "1 / 1",
                    gdColumnltmd: "1/1",
                    gdColumnltsm: "1",
                    fxLayoutAlign: "start center",
                    fxFlex: "flex-grow",
                    rows: [
                        {
                            class: "appProcessLog w-full h-full items-initial",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicProcessLogs,
                                requestDetails: [
                                    {
                                        fetchUrl: ["client", "api", "form", "personformlogs"],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "personFormId",
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "personFormId",
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ],
                                widgetHeader: "Registration Process",
                                backwardsDataDependency: true,
                                grandParentReference: "manage-registry",
                            },
                        },
                    ],
                }
            ],
        }
    ]
}