import { Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Observable } from 'rxjs';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types'
import * as singleEntryTableTypes from 'src/app/core/common-component/single-entry-table/single-entry-table.types';
import { candidateCompleteList, candidateCompleteListSponsor, candidateDetails, getExamDetailtoDownload, getinduvialExamDetailtoDownload, getStateId, selectedVoucherList, tempselectedVoucherList } from '../state/voucher.action';
import { candidateFullList, candidateIdList, detailVoucher, gotTrainingInstStateID, selectorStateId, sponsorCandidateDetails, tempDetails } from '../state/voucher.selector';
import { VoucherState } from '../state/voucher.state';
import { VoucherService } from '../voucher.service';
import { MatDialog } from '@angular/material/dialog';
import { AssignVoucherComponent } from '../assign-voucher/assign-voucher.component';
import { paramTypes, requestDetails, Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { voucherList } from '../state/voucher.types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { Router } from '@angular/router';
import { sub } from 'date-fns';
import { get_userDetails } from '../../state/shared/shared.selectors';
import {  NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { recentLinkClickEventSelector } from '../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { HttpService } from 'src/app/core/http.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { StateLists } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';


@Component({
  selector: 'app-voucher-list',
  templateUrl: './voucher-list.component.html',
  styleUrls: ['./voucher-list.component.scss']
})
export class VoucherListComponent implements OnInit {
tabIndex:number
VoucherName:string
  tableOptions = {
    tableName: "Voucher List",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false, false, false, false, false],
    showMatTabs: true,
    matTabs: [
      { id: 1, name: "Available" },
      { id: 2, name: "All" },
      { id: 3, name: "Used" },
      { id: 4, name: "Expired" },
      { id: 5, name: "Assigned" },
      { id: 6, name: "Transfer Voucher" },
    ],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
  }

  tableSponsorOption = {
    tableName: "Voucher List",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false, false, false, false, false],
    showMatTabs: true,
    matTabs: [
      { id: 1, name: "Available" },
      { id: 2, name: "All" },
      { id: 3, name: "Used" },
      { id: 4, name: "Expired" },
    ],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
  }
  tableOperationOption = {
    tableName: "Voucher List",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false, false, false, false, false],
    showMatTabs: true,
    matTabs: [
      { id: 1, name: "Available" },
      { id: 2, name: "All" },
      { id: 3, name: "Used" },
      { id: 4, name: "Expired" },
    ],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
  }

  displayedColumns1 = [
    <singleEntryTableTypes.column>{
      id: 'voucherName',
      name: 'Exam Name',
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "voucherCode",
      name: "Exam Code",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "stateName",
      name: "State Name",
      editable: false,
    },
    // <singleEntryTableTypes.column>{
    //   id: "eligibilityRoute",
    //   name: "Eligibity Route",
    //   editable: false,
    // },
    <singleEntryTableTypes.column>{
      id: "totalVochers",
      name: "Total Vouchers",
      editable: false,
    },
  ]

  displayedColumns = [

    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["link-animation"],
      },

      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "action",
        name: "Action",
        cellType: "action",
        actions: [
          <tableTypes.action>{
            type: tableTypes.actionTypes.Custom,
            icon: 'download',
            tooltip: 'Download',
            color: "#7d7d7d",
          }
        ],
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["cursor-pointer"],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["link-animation"],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["link-animation"],
      },

      <tableTypes.column>{
        visible: true,
        id: "statusSinceDate",
        name: "Used Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["link-animation"],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["link-animation"],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "action",
        name: "Action",
        cellType: "action",
        actions: [
          <tableTypes.action>{
            type: tableTypes.actionTypes.Custom,
            icon: 'remove_from_queue',
            tooltip: 'Unassigned',
            color: "#7d7d7d",
          }
        ],
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["cursor-pointer"],
      },
      
    ],
    [
      <tableTypes.column>{
        visible: true,
        id: "checkbox",
        name: "checkbox",
        cellType: "text",
        headerType: "checkbox",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: ["w-4"],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      // <tableTypes.column>{
      //   visible: true,
      //   id: "description",
      //   name: "Candidate Name",
      //   cellType: "text",
      //   headerType: "normal",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: [],
      //   cellCssClasses: ["link-animation"],
      // },

      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "action",
        name: "Action",
        cellType: "action",
        actions: [
          // <tableTypes.action>{
          //   type: tableTypes.actionTypes.Custom,
          //   icon: 'download',
          //   tooltip: 'Download',
          //   color: "#7d7d7d",
          // }
        ],
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["cursor-pointer"],
      },
    ]
  ]

  operationsdisplayedColumns = [

    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      // <tableTypes.column>{
      //   visible: true,
      //   id: "description",
      //   name: "Candidate Name",
      //   cellType: "text",
      //   headerType: "normal",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: [],
      //   cellCssClasses: ["link-animation"],
      // },

      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "action",
        name: "Action",
        cellType: "action",
        actions: [
          <tableTypes.action>{
            type: tableTypes.actionTypes.Custom,
            icon: 'download',
            tooltip: 'Download',
            color: "#7d7d7d",
          }
        ],
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["cursor-pointer"],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
  
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      // <tableTypes.column>{
      //   visible: true,
      //   id: "description",
      //   name: "Candidate Name",
      //   cellType: "text",
      //   headerType: "normal",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: [],
      //   cellCssClasses: ["link-animation"],
      // },

      <tableTypes.column>{
        visible: true,
        id: "statusSinceDate",
        name: "Used Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      // <tableTypes.column>{
      //   visible: true,
      //   id: "description",
      //   name: "Candidate Name",
      //   cellType: "text",
      //   headerType: "normal",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: [],
      //   cellCssClasses: ["link-animation"],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      // <tableTypes.column>{
      //   visible: true,
      //   id: "description",
      //   name: "Candidate Name",
      //   cellType: "text",
      //   headerType: "normal",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: [],
      //   cellCssClasses: ["link-animation"],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ]
  ]


  sponsorTable = [
    [
      // <tableTypes.column>{
      //   visible: true,
      //   id: "checkbox",
      //   name: "checkbox",
      //   cellType: "text",
      //   headerType: "checkbox",
      //   headerAlign: "center",
      //   cellAlign: "center",
      //   headerCssClasses: ["w-4"],
      //   cellCssClasses: [],
      // },
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },

      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "action",
        name: "Action",
        cellType: "action",
        actions: [
          <tableTypes.action>{
            type: tableTypes.actionTypes.Custom,
            icon: 'download',
            tooltip: 'Download',
            color: "#7d7d7d",
          }
        ],
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: ["cursor-pointer"],
      },
    ],
    // [
    //   <tableTypes.column>{
    //     visible: true,
    //     id: "voucherCode",
    //     name: "Voucher Code",
    //     cellType: "text",
    //     headerType: "normal",
    //     headerAlign: "center",
    //     cellAlign: "center",
    //     headerCssClasses: [],
    //     cellCssClasses: [],
    //   },
    //   <tableTypes.column>{
    //     visible: true,
    //     id: "expirationDate",
    //     name: "Expiration Date",
    //     cellType: "date",
    //     headerType: "normal",
    //     headerAlign: "center",
    //     cellAlign: "center",
    //     headerCssClasses: [],
    //     cellCssClasses: [],
    //   },
    //   <tableTypes.column>{
    //     visible: true,
    //     id: "action",
    //     name: "Action",
    //     cellType: "action",
    //     actions: [
    //       <tableTypes.action>{
    //         type: tableTypes.actionTypes.Custom,
    //         icon: 'download',
    //         tooltip: 'Download',
    //         color: '#7d7d7d',
    //       }
    //     ],
    //     headerType: "normal",
    //     headerAlign: "center",
    //     cellAlign: "center",
    //     headerCssClasses: [],
    //     cellCssClasses: ["cursor-pointer"],
    //   },
    // ],
    [
      <tableTypes.column>{
        visible: true,
        id: "voucherCode",
        name: "Item Code",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "expirationDate",
        name: "Expiration Date",
        cellType: "date",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      
      <tableTypes.column>{
        visible: true,
        id: "description",
        name: "Candidate Name",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
      <tableTypes.column>{
        visible: true,
        id: "voucherStatus",
        name: "Voucher Status",
        cellType: "text",
        headerType: "normal",
        headerAlign: "center",
        cellAlign: "center",
        headerCssClasses: [],
        cellCssClasses: [],
      },
    ],
    [<tableTypes.column>{
      visible: true,
      id: "voucherCode",
      name: "Item Code",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "description",
      name: "Candidate Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },

    <tableTypes.column>{
      visible: true,
      id: "statusSinceDate",
      name: "Used Date",
      cellType: "date",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "expirationDate",
      name: "Expiration Date",
      cellType: "date",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },

    ],
    [<tableTypes.column>{
      visible: true,
      id: "voucherCode",
      name: "Item Code",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "expirationDate",
      name: "Expiration Date",
      cellType: "date",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "description",
      name: "Candidate Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },

    ],
  ]

  voucherDetails: any = [];
  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();
  public dataSource: MatTableDataSource<any>;
  voucherList: any;
   SelectedCheckboxValues:Array<object>=[]
  addpersonMessage: string = "+Add Candidate";
  sponsorMessage: string = "Not Assigned"
  candidateName: Array<any>;
  getVoucherList: voucherList;
  data: any;
  userroleId:any;
  requestDetails: requestDetails;
  sponorRoleId: number;
  trainingInstituteId: number;
  downloadVoucherInfo: any;
  navigate
  trainingDetails
  multipleRoleTenantId: Array<number> = [];
  personTentantRoleId: Array<number> = [];

  constructor(public store: Store<VoucherState>,
    public voucherservice: VoucherService,
    public dialog: MatDialog,
    public global: GlobalUserService,
    private ngDynamicBreadcrumbService:NgDynamicBreadcrumbService,
    public route: Router,private http:HttpService,private snackbar:SnackbarService) { }

  ngOnInit(): void {

    this.store.select(recentLinkClickEventSelector).subscribe(data => {

      this.navigate = data.column.linkMetaData.navigateTo
      this.VoucherName = data.element.voucherName
    })
    if (this.navigate != null) {
      if ((this.global.userDetails.value.roleId == Roles.OperationStaff || this.global.userDetails.value.roleId == Roles.SupportingStaff) && this.navigate == "training-institute-details") {
        const breadcrumb = [
          {
            label: "Home",
            url: "/dashboard",
          },
          {
            label: "Voucher",
            url: "/voucher",
          },
          {
            label: "VoucherList",
            url: "",
          },
        ];
        this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
      }
    }
  
    this.store.select(get_userDetails).subscribe(data => {
      if (data) {
        data.roles.forEach(value => {
          this.multipleRoleTenantId.push(value.tenantId)
          this.personTentantRoleId.push(value.personTenantRoleId)
        })
        this.store.dispatch(candidateCompleteList({ tenantId: this.multipleRoleTenantId }))
      }
    });

    this.store.select(selectorStateId).subscribe(data => {
      if (data != null && data) {
        this.trainingDetails = data
      }
    })
    
    this.global.userDetails.subscribe(data=>{
      if(data!=null){
        this.userroleId=data;
        if ((Roles.TrainingInstitue === data.roleId &&  StateLists.CA != data.stateId) || Roles.Sponsor === data.roleId ) {
          const breadcrumb = [
            {
              label: "Home",
              url: "/training",
            },
            {
              label: "Voucher",
              url: "/voucher",
            },
            {
              label: "VoucherList",
              url: "",
            },
          ];
          this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
        }else if(StateLists.CA === data.stateId && Roles.TrainingInstitue === data.roleId){
          const breadcrumb = [
            {
              label: "Home",
              url: "/voucher",
            },
            {
              label: "Voucher",
              url: "/voucher",
            },
            {
              label: "VoucherList",
              url: "",
            },
          ];
          this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
        }
        (data.stateId !=null && data.stateId !=undefined)?this.store.dispatch(candidateCompleteListSponsor({tenantId:data})):null
        //this.store.dispatch(getStateId({trainingInstituteID:data.tenantId}))
      }
    })
    try {
      this.store.select(gotTrainingInstStateID).subscribe(stateId => {
        if (stateId) {
          this.trainingInstituteId = stateId[0].stateId
        }
      })
      this.sponorRoleId = this.global.userDetails.value.roleId;
      this.data = this.voucherservice.data;
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 1,
          clickedMatTab:true,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId: [this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          voucherStatusId: 1,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }
  
      this.voucherservice.examTypeId !=null ?this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList })):this.route.navigateByUrl('/voucher')
      this.store.select(detailVoucher).subscribe((data: any) => {
        try {
          this.voucherList = data;
          this.getcourse()
        }
        catch (e) { }
      })
    }
    catch (e) {
      this.route.navigateByUrl('/voucher')
    }
  }


  mergeRequested(event){
    this.SelectedCheckboxValues = event.selection 

  }

  getcourse() {
    this.voucherservice.canidateFilterName = [];
    this.voucherDetails = [];
    if(this.userroleId.roleId==15){

    this.voucherList.forEach(element => {
      if (element.assigntoPersonId == null && (element.voucherStatus == 'Available' ||element.voucherStatus == 'Transfer Voucher')) {

        this.voucherDetails.push(
          {
            voucherCode: element.voucherCode,
            expirationDate: element.expirationDate,
            description: this.global.userDetails.value.roleId == 15 ? this.addpersonMessage : this.sponsorMessage,
            voucherStatus: element.voucherStatus,
            voucherId: element.id,
            statusSinceDate: element.statusSinceDate,
            examcode:element.itemCode,
            price:element.price
          },
        )  
        this.getVoucherList.clickedMatTab==true?this.getcandidateFilter():null
      }
      else if ((element.voucherStatus == 'Used' || element.voucherStatus == 'Expired' )) {
        if (element.usedBy != null) {
          this.store.select(candidateFullList).subscribe((data: any) => {
            if (data != null) {
              data.forEach(candidatelist => {
                if (element.usedBy == candidatelist.personId) {
                  this.voucherDetails.push(
                    {
                      voucherCode: element.voucherCode,
                      expirationDate: element.expirationDate,
                      description: `${candidatelist.firstName} ${candidatelist.lastName}`,
                      voucherStatus: element.voucherStatus,
                      voucherId: element.id,
                      statusSinceDate: element.statusSinceDate
                    },
                  )
                }
              });
            }
          });
        }
      }
      else {
        const subs = this.store.select(candidateFullList).subscribe((data: any) => {
          if (data != null && element.assigntoPersonId!=null) {
            data.forEach(candidatelist => {
              if (element.assigntoPersonId == candidatelist.personId) {
                this.voucherDetails.push(
                  {
                    voucherCode: element.voucherCode,
                    expirationDate: element.expirationDate,
                    description: `${candidatelist.firstName} ${candidatelist.lastName}`,
                    voucherStatus: element.voucherStatus,
                    voucherId: element.id,
                    statusSinceDate: element.statusSinceDate,
                    examcode:element.itemCode,
                    price:element.price
                  },
                )
                this.voucherservice.canidateFilterName.push({
                  firstName: candidatelist.firstName
                })

              }
            });
          }
        })
        subs?.unsubscribe()
      }
    });
  }
  else{
      

    this.voucherList.forEach(element => {
      if (element.assigntoPersonId == null && (element.voucherStatus == 'Available' ||element.voucherStatus == 'Transfer Voucher'  )) {
        this.voucherDetails.push(
          {
            voucherCode: element.voucherCode,
            expirationDate: element.expirationDate,
            description: this.global.userDetails.value.roleId == 15 ? this.addpersonMessage : this.sponsorMessage,
            voucherStatus: element.voucherStatus,
            voucherId: element.id,
            statusSinceDate: element.statusSinceDate,
            examcode:element.itemCode,
            price:element.price
          },
        )     
        this.getVoucherList.clickedMatTab==true?this.getcandidateFilter():null
      }
      else if ((element.voucherStatus == 'Used' || element.voucherStatus == 'Expired')) {
        if (element.usedBy != null) {
          this.store.select(sponsorCandidateDetails).subscribe((data: any) => {
            if (data != null) {

              data.forEach(candidatelist => {
                if (element.usedBy == candidatelist.personId) {
                  this.voucherDetails.push(
                    {
                      voucherCode: element.voucherCode,
                      expirationDate: element.expirationDate,
                      description: `${candidatelist.firstName} ${candidatelist.lastName}`,
                      voucherStatus: element.voucherStatus,
                      voucherId: element.id,
                      statusSinceDate: element.statusSinceDate
                    },
                  )
                }
              });
            }
          });
        }
      }
      else {
        const subs = this.store.select(sponsorCandidateDetails).subscribe((data: any) => {
          if (data != null && element.assigntoPersonId!=null) {
            data.forEach(candidatelist => {
              if (element.assigntoPersonId == candidatelist.personId) {
                this.voucherDetails.push(
                  {
                    voucherCode: element.voucherCode,
                    expirationDate: element.expirationDate,
                    description: `${candidatelist.firstName} ${candidatelist.lastName}`,
                    voucherStatus: element.voucherStatus,
                    voucherId: element.id,
                    statusSinceDate: element.statusSinceDate,
                    examcode:element.itemCode,
                    price:element.price
                  },
                )
                this.voucherservice.canidateFilterName.push({
                  firstName: candidatelist.firstName
                })

              }
            });
          }
        })
        subs?.unsubscribe()
      }
    });

  }
  //The above logic is when assignedto person id is null and
  //when usedby is null and
  //when candidate used voucher is from some other TP
  const results=this.voucherList.filter(({voucherCode : id1 }) => !this.voucherDetails.some(({ voucherCode: id2 }) => id2 === id1));
  results.forEach(element => {
    this.voucherDetails.push(
      {
        voucherCode: element.voucherCode,
        expirationDate: element.expirationDate,
        description: ``,
        voucherStatus: element.voucherStatus,
        voucherId: element.id,
        statusSinceDate: element.statusSinceDate,
        examcode:element.itemCode,
        price:element.price
      },
    );

  })
    this.dataSource = new MatTableDataSource(this.voucherDetails)
  }


  getevent(event) {

    this.tabIndex = event.tabIndex
    if (event.tabIndex == 0) {
      this.voucherDetails = [];
 
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 1,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId: [this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          voucherStatusId: 1,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
        if(data){
          this.voucherDetails =[]
          data.forEach((x)=>{
             this.voucherDetails.push({
              voucherCode: x.voucherCode,
              expirationDate: x.expirationDate,
              description:  x.assigntoPerson !=""? x.assigntoPerson :this.global.userDetails.value.roleId == 15 && x.voucherStatus !="Used" ? this.addpersonMessage : this.sponsorMessage && x.voucherStatus !="Used"?this.sponsorMessage:null,
              voucherStatus: x.voucherStatus,
              voucherId: x.id,
              statusSinceDate: x.statusSinceDate,
              examcode:x.itemCode,
              price:x.price
             })
          })
          
          this.dataSource = new MatTableDataSource(this.voucherDetails)
        

         
         
        }
    })
    }
    else if (event.tabIndex == 1) {
      this.voucherDetails = [];
   
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 0,
          clickedMatTab:true,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId: [this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          itemCode: this.voucherservice.examCode,
          voucherStatusId: 0,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }     
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
        if(data){
          this.voucherDetails =[]
          data.forEach((x)=>{
            this.voucherDetails.push({
             voucherCode: x.voucherCode,
             expirationDate: x.expirationDate,
             description: x.assigntoPerson !=""? x.assigntoPerson :this.global.userDetails.value.roleId == 15 && x.voucherStatus !="Used" ? this.addpersonMessage : this.sponsorMessage && x.voucherStatus !="Used"?this.sponsorMessage:null,
             voucherStatus: x.voucherStatus,
             voucherId: x.id,
             statusSinceDate: x.statusSinceDate
            })
         })
          this.dataSource = new MatTableDataSource(this.voucherDetails)
        }
    })
    }

    else if (event.tabIndex == 2) {
      this.voucherDetails = [];
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 3,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId:this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId: [this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          voucherStatusId: 3,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
        if(data){
           this.voucherDetails =[]
           data.forEach((x)=>{

            this.voucherDetails.push({
             voucherCode: x.voucherCode,
             expirationDate: x.expirationDate,
             description: x.assigntoPerson,
             voucherStatus: x.voucherStatus,
             voucherId: x.id,
             statusSinceDate: x.statusSinceDate
            })
         })
          this.dataSource = new MatTableDataSource(this.voucherDetails)
        }
    })
    }
    else if (event.tabIndex == 4) {
      this.voucherDetails = [];
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ?this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 5,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:[this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          voucherStatusId: 5,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
        if(data){
           this.voucherDetails =[]
           data.forEach((x)=>{

            this.voucherDetails.push({
             voucherCode: x.voucherCode,
             expirationDate: x.expirationDate,
             description: x.assigntoPerson ,
             voucherStatus: x.voucherStatus,
             voucherId: x.id,
             statusSinceDate: x.statusSinceDate
            })
         })
          this.dataSource = new MatTableDataSource(this.voucherDetails)
        }
    })
    }
    else if(event.tabIndex == 5){
      this.getVoucherList = {
        examTypeId: this.voucherservice.examTypeId,
        tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
        stateId:this.global.userDetails.value.stateId,
        voucherStatusId: 1,
        clickedMatTab:true,
        itemCode: this.voucherservice.examCode,
        personTenantRoleId: this.personTentantRoleId
      }
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
        if(data){

          this.voucherDetails =[]
          data.forEach((x)=>{
            this.voucherDetails.push({
             voucherCode: x.voucherCode,
             expirationDate: x.expirationDate,
             description: x.assigntoPerson !=""? x.assigntoPerson :this.global.userDetails.value.roleId == 15 && x.voucherStatus !="Used" ? this.addpersonMessage : this.sponsorMessage && x.voucherStatus !="Used"?this.sponsorMessage:null,
             voucherStatus: x.voucherStatus,
             voucherId: x.id,
             statusSinceDate: x.statusSinceDate,
             examcode:x.itemCode,
              price:x.price
            })
         })
          this.dataSource = new MatTableDataSource(this.voucherDetails)
        }
    })
    }
    else {
      this.voucherDetails = [];
      if(this.global.userDetails.value.roleId == 15 || this.global.userDetails.value.roleId == 18 ){
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId : [this.global.userDetails.value.stateId],
          stateId:this.global.userDetails.value.stateId,
          voucherStatusId: 2,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: this.personTentantRoleId
        }
      }else {
        this.getVoucherList = {
          examTypeId: this.voucherservice.examTypeId,
          tenantId:[this.trainingDetails.trainingId],
          stateId:Number(this.trainingDetails.stateId),
          voucherStatusId: 2,
          itemCode: this.voucherservice.examCode,
          personTenantRoleId: [Number(this.trainingDetails.personTenantRoleId)]
        }
      }
      this.store.dispatch(selectedVoucherList({ examTypeId: this.getVoucherList }))
      this.store.select(detailVoucher).subscribe(data=>{
          if(data){
             this.voucherDetails =[]
             data.forEach((x)=>{
              this.voucherDetails.push({
               voucherCode: x.voucherCode,
               expirationDate: x.expirationDate,
               description: x.assigntoPerson,
               voucherStatus: x.voucherStatus,
               voucherId: x.id,
               statusSinceDate: x.statusSinceDate,
               examcode:x.itemCode,
              price:x.price
              })
           })
            this.dataSource = new MatTableDataSource(this.voucherDetails)
          }
      })
    }

  

    this.tabIndexStream.next(event.tabIndex)
  }

  openDialog(event): void {
    debugger
    if (event.column.id=="description"&&event.element.description == "+Add Candidate" && !VoucherCode.includes(event.element.examcode) ) {
      this.voucherservice.voucherId = event.element.voucherId;
      const dialogRef = this.dialog.open(AssignVoucherComponent, {
        width: '600px',
        data:{tabtype:2,VoucherName:event.element.examcode}
      });
    }else if(event.column.id=="description"&&event.element.description == "+Add Candidate" && VoucherCode.includes(event.element.examcode)){
      this.voucherservice.voucherId = event.element.voucherId;
      const dialogRef = this.dialog.open(AssignVoucherComponent, {
        width: '600px',
        data:{tabtype:6,VoucherName:event.element.examcode}
      });
    }
  }

  Transfer(){
    const dialogRef = this.dialog.open(AssignVoucherComponent, {
      width: '600px',
      data:{selectedVoucherList:this.SelectedCheckboxValues,tabtype:this.tabIndex,totalVoucher:this.voucherservice.data[0].totalVochers}
    });
    dialogRef.afterClosed().subscribe((data) => {
      this.SelectedCheckboxValues =[]
        this.getevent({tabIndex:data.id})
    })
  }

  downloadVoucher(event) {
    if(event.column.actions[0].icon !="remove_from_queue"){
      this.downloadVoucherInfo = {
        voucherCode: event.element.voucherCode,
        validity: event.element.expirationDate,
        fileType: "xlsx"
      }
      this.voucherservice.VoucherFileName = event.element.voucherCode;
      this.store.dispatch(getinduvialExamDetailtoDownload({ induvialvoucherData: this.downloadVoucherInfo }))
    }else{
      this.http.Unassigned(event.element.voucherId).subscribe(data=>{
        if(data){
          this.snackbar.callSnackbaronSuccess("Voucher Unassigned")
          this.getevent({tabIndex: 4})
        }
      },(error:any)=>{
        this.snackbar.callSnackbaronError(`${error.message.message.error}`)
      })
    }
  
  }

  getcandidateFilter(){
    this.getVoucherList = {
      examTypeId: this.voucherservice.examTypeId,
      tenantId:this.global.userDetails.value.roleId == 15 ? this.multipleRoleTenantId  : [this.global.userDetails.value.stateId],
      stateId:this.global.userDetails.value.stateId,
      voucherStatusId: 0,//all voucher
      itemCode: this.voucherservice.examCode,
      personTenantRoleId: this.personTentantRoleId
    }
    this.store.dispatch(tempselectedVoucherList({ tempexamTypeId: this.getVoucherList }))
    const candidateSub=this.store.select(tempDetails).subscribe((x: any) => {
      if(x){
        x.forEach(y => {
          const subs = this.store.select(candidateFullList).subscribe((data: any) => {
            if (data != null) {
              data.forEach(candidatelist => {
                if (y.assigntoPersonId == candidatelist.personId) {
                  this.voucherDetails.push(
                    {
                      voucherCode: y.voucherCode,
                      expirationDate: y.expirationDate,
                      description: `${candidatelist.firstName} ${candidatelist.lastName}`,
                      voucherStatus: y.voucherStatus,
                      voucherId: y.id,
                      statusSinceDate: y.statusSinceDate
                    },
                  )
                  this.voucherservice.canidateFilterName.push({
                    firstName: candidatelist.firstName
                  })
  
                }
              });
            }
          })
          subs.unsubscribe();
        });
      }
      });
  }

}
export const VoucherCode=['NA-PWR','NA-PWR2','NA-PWR3']
