import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { createEffect, Actions, ofType } from "@ngrx/effects";
import { Observable, of } from "rxjs";
import {
  switchMap,
  map,
  catchError,
  tap,
  mergeMap,
  exhaustMap,
  flatMap,
  concatMap,
  take,
} from "rxjs/operators";
import { Router } from "@angular/router";
import { setErrorMessage } from "../../state/shared/shared.actions";
import { URL } from "src/app/core/url";
import {
  addCartTC,
  cancelExam,
  createPaymentCustomerId,
  createPaymentMethod,
  createVoucher,
  deletedCartId,
  Examcancelled,
  getCart,
  getcartId,
  getCartItems,
  // getCartItems,
  getEligibilityroute,
  getExamId,
  getMakePayment,
  getMonthlySlots,
  getPaymentCustomerId,
  getPaymentMethod,
  getRegisteredExam,
  getSchedule,
  getStateId$,
  getTimeSlots,
  getTimezones,
  getvoucherCartDetails,
  getVoucherDetails,
  gotCart,
  gotCartItems,
  // gotCartItems,
  gotEligibilityRoutes,
  gotExamId,
  gotMakePayment,
  gotMonthlySlots,
  gotNewVoucher,
  gotPaymentCustomerId,
  gotPaymentMethod,
  gotRegisteredExam,
  gotrescheduled,
  gotTimeSlots,
  gotTimezones,
  madeCharge,
  madenull,
  makeCharge,
  makenull,
  paymentCustomerIdCreated,
  paymentDetails,
  paymentMethodCreated,
  reAddData,
  reAddedSuccess,
  removeCartItem,
  removeAllCartItem,
  reschedule,
  successPayment,
  trainingStateId$,
  vouchercartDetails,
  VoucherDetails,
  getTimeSlotsTestCenter,
  gotTimeSlotsTestCenter,
  getVoucher,
  gotVoucher,
  getVoucherApply,
  gotVoucherApply,
  getScheduled,
  gotScheduled,
  voucherUpdate,
  voucherUpdated,
  cardDeleted,
  deleteCard,
  getTimeSlotsTestCenterFailure,
} from "./scheduled.actions";
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService } from "src/app/core/global-user.service";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class ScheduledEffects {
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private router: Router,
    private services: SnackbarService,
    private global: GlobalUserService
  ) { }

  effectivelyGetTimeZones$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimezones),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER + "client/api/operationstaff" + `/timezones`
          )
          .pipe(
            map((timezones) =>
              gotTimezones({
                Timeszones: timezones,
              })
            )
            // catchError(err => this.services.callSnackbaronError(err.error))
          );
      })
    )
  );

  effectivelyGetTimeSlots$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimeSlots),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `client/api/operationstaff/slots-online?examId=${action.examId}&slotDate=${action.startDate}&timeZone=${action.timezone}&offSet=${action.offset}`
          )
          .pipe(
            map((timeslots) =>
              gotTimeSlots({
                Slots: timeslots,
              })
            )
          );
      })
    )
  );

  effectivelyGetCart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getCart),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER + "client/api/operationstaff" + "/cart/add",
            action.details
          )
          .pipe(
            map((cart) => {
              if (cart) {
                if (action.isPayment) {
                  this.router.navigateByUrl(
                    `scheduleExam/${action.details.examDetail.candidateId}/payment/page`
                  );
                }
                this.services.callSnackbaronSuccess(
                  "Added to cart successfully"
                );
              }

              return gotCart({
                cart: cart,
              });
            }),

            catchError((err) => {
            
              this.services.callSnackbaronError(err.message.message.error);
              return of(setErrorMessage({ message: err.message.message.error }));
            })
          );
      })
    )
  );
  effectivelyScheduled$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getScheduled),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.CANDIDATE_URL +
              `Exam/makepaymentwithzero?personTenantRoleId=${action.personTentantRole}&cartId=${action.cartId}`,
            null
          )
          .pipe(
            map((rescheduled) => {
              return gotScheduled({
                ScheduledResponse: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyVocher$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucher),
      switchMap((action) => {
        //
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SPONSOR + `Voucher/validate-voucher`,
            action.VocherValidator
          )
          .pipe(
            map((rescheduled) => {
              return gotVoucher({
                VocherResponse: rescheduled,
              });
            }),
            take(1),
            catchError((err) => {
              this.services.callSnackbaronError(err.message.message.error)
              return of(setErrorMessage({ message: err.message.message.error }))
            })
          );
      })
    )
  );

  effectivelyVocherApply$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getVoucherApply),
      switchMap((action) => {
        return this.httpClient
          .put<any>(
            URL.CANDIDATE_URL + `Exam/cartitem/applyvoucher`,
            action.VocherDetails
          )
          .pipe(
            map((vocher) => {
              return gotVoucherApply({
                VocherApplyResponse: vocher,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyAddCart$ = createEffect(() =>
    this.actions$.pipe(
      ofType(addCartTC),
      concatMap((action) => {
        //
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER + "client/api/operationstaff/cart/add",
            action.body
          )
          .pipe(
            map((cart) => {
              if (cart) {
                this.services.callSnackbaronSuccess(
                  "Added to cart successfully"
                );
              }
              return gotCart({
                cart: cart,
              });
            }),

            catchError((err) => {
              //
              this.services.callSnackbaronError(err.message.message.error);
              return of(
                setErrorMessage({ message: err.message.message.error })
              );
            })
            // catchError((err) => of(setErrorMessage({ message: err.error })))
          );
      })
    )
  );

  effectivelyGetTimeSlotsTestCenter$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getTimeSlotsTestCenter),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `client/api/operationstaff/slots-testCenter?timezoneId=${action.timezone}&examId=${action.examId}&startDate=${action.startDate}&endDate=${action.endDate}&testCenterName=${action.testCenterName}&testCenterId=${action.testCenterId}&testCenterAddress=${action.testCenterAddress}&radius=${action.radius}&candidateId=${action.candidateId}`
          )
          .pipe(
            map((timeSlots) =>
              gotTimeSlotsTestCenter({
                slots: timeSlots,
              })
            ),
            catchError((error) => {
              this.services.callSnackbaronError(
                "Failed to fetch time slots. Please try again."
              );
              return of(getTimeSlotsTestCenterFailure({ error }));
            }),
            take(1)
          );
      })
    )
  );
  F;

  effectivelyGetMonthlySlots$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getMonthlySlots),
      mergeMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/monthlySlots?month=${action.month}&year=${action.year}&timeZone=${action.timezone}`
          )
          .pipe(
            map((monthlySlots) =>
              gotMonthlySlots({
                monthlySlots: monthlySlots,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );
  effectivelyGetExamId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getExamId),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/exams?eligibilityRouteId=${action.eligibilityRouteId}&personTenantRoleId=${action.candidateId}`
          )
          .pipe(
            map((examdata) =>
              gotExamId({
                examdata: examdata,
              })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );

  effectivelyGetEligibilityRoute$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getEligibilityroute),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/active-eligibility-route?candidateId=${action.candidateId}`
          )
          .pipe(
            map((route) =>
              gotEligibilityRoutes({
                route: route,
              })
            )
          );
      })
    )
  );
  effectivelyGetRegisteredExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getRegisteredExam),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/registeredexams?candidateId=${action.candidateId}`
          )
          .pipe(
            map((registeredexams) =>
              gotRegisteredExam({
                registeredExams: registeredexams,
              })
            )
          );
      })
    )
  );

  effectivelyMakePayment$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getMakePayment),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff/" +
              `cart/makepayment?personTenantRoleId=${action.personTenantRoleId}`,
            action.makepaymentBody
          )
          .pipe(
            map(
              (makepaymentresponse) =>
                gotMakePayment({
                  makePaymentResponse: makepaymentresponse,
                })
              // this.services.callSnackbaronSuccess("Added to cart successfully.")
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );

  effectivelyReschedule$ = createEffect(() =>
    this.actions$.pipe(
      ofType(reschedule),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              "/exam/reschedule",
            action.rescheduleBody
          )
          .pipe(
            map((rescheduled) => {
              if (reschedule) {
                this.services.callSnackbaronSuccess(
                  " Rescheduled Successfully"
                );
              }
              return gotrescheduled({
                rescheduleResponse: rescheduled,
              });
            }),
            catchError((err) => {
              this.services.callSnackbaronError(err.message.message.error);
              return of(
                setErrorMessage({ message: err.message.message.error })
              );
            })
          );
      })
    )
  );

  // effectivelyGetActiveForm$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(getActiveForm),
  //     switchMap((action) => {
  //       return this.httpClient
  //         .get<any>(
  //           URL.BASE_URL_SHORTER +"client/api/operationstaff"+ `form/activeform?candidateId=1&formTypeId=3`,
  //         )
  //         .pipe(
  //           map((activeForm) =>
  //             gotActiveForm({activeForm: activeForm})
  //           ),
  //           catchError((err) => of(setErrorMessage({ message: err.message })))
  //         );
  //     })
  //   )
  // );

  effectivelyCancelExam$ = createEffect(() =>
    this.actions$.pipe(
      ofType(cancelExam),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL_SHORTER +
            "client/api/operationstaff" +
            `exam/cancel?examScheduleId=${action.examScheduleId}&candidateId=${action.candidateId}`
          )
          .pipe(
            map((isCancelled) => {
              if (isCancelled) {
                this.services.callSnackbaronSuccess(
                  "Exam cancelled successfully"
                );
              }
              return Examcancelled({ isCancelled: isCancelled });
            }),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
          );
      })
    )
  );

  effectivelyDeleteCartItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(removeCartItem),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/cart-item?personTenantRoleId=${action.personTenantRoleId}&cartItemId=${action.cartItemId}`
          )
          .pipe(
            map((cartItemRemoved) => {
              if (cartItemRemoved) {
                this.services.callSnackbaronSuccess(
                  "Cart item deleted successfully!"
                );
              }
              return cartItemRemoved({ isDeleted: cartItemRemoved });
            })
          );
      })
    )
  );

  effectivelyDeleteAllCartItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(removeAllCartItem),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/cart-items?personTenantRoleId=${action.personTenantRoleId}&cartId=${action.cartId}`
          )
          .pipe(
            map((cartItemRemoved) => {
              if (cartItemRemoved) {
                this.services.callSnackbaronSuccess(
                  "Cart item deleted successfully!"
                );
              }
              return cartItemRemoved({ isDeleted: cartItemRemoved });
            })
          );
      })
    )
  );

  effectivelyGetCartItems$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getCartItems),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              "client/api/operationstaff" +
              `/cartitems?personTenantRoleId=${action.personTenantRoleId}`
          )
          .pipe(
            map((cartItems) =>
              gotCartItems({
                cartItems: cartItems,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  public submissionForm$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getvoucherCartDetails),
      switchMap((action) => {
        return this.httpClient
          .get(
            `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${action.personTentantRoleId}`
          )
          .pipe(
            map((data: any) => {
              return vouchercartDetails({ voucherCartDeatils: data });
            })
          );
      })
    );
  });

  public deleteCartItem$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getcartId),
      switchMap((action) => {
        return this.httpClient
          .delete(
            `${environment.baseUrl}candidate/api/Exam/cart-item?personTenantRoleId=${action.personTentantRoleId}&cartItemId=${action.cartId}`
          )
          .pipe(
            map((data: any) => {
              this.services.callSnackbaronSuccess("Deleted successfully");
              return deletedCartId({ deletedCartStatus: data });
            })
          );
      })
    );
  });

  public makePayment$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(paymentDetails),
      switchMap((action) => {
        try {
          return this.httpClient
            .post(
              `${environment.baseUrl}candidate/api/Exam/makepayment/voucher?personTenantRoleId=${action.personTentantRoleId}`,
              action.details
            )
            .pipe(
              map((data: any) => {
                if (data != null && data.success == true) {
                  // this.services.callSnackbaronSuccess(
                  //   "Payment successfully"
                  // );
                  return successPayment({ paidCartStatus: data });
                } else {
                  this.services.callSnackbaronWarning("Payment Unsuccessfully");
                }
              })
            );
        } catch (e) {
          this.services.callSnackbaronWarning("Payment Unsuccessfully");
        }
      })
    );
  });

  public generate$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(createVoucher),
      switchMap((action) => {
        return this.httpClient
          .post(
            `${environment.baseUrl}sponsor/api/Voucher/generate`,
            action.newvoucherDetails
          )
          .pipe(
            map((data: any) => {
              this.services.callSnackbaronSuccess(
                " Voucher Generated successfully"
              );
              return gotNewVoucher({ successNewVoucher: data });
            })
          );
      })
    );
  });

  public cartDetails$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(reAddData),
      switchMap((action) => {
        try {
          return this.httpClient
            .put(
              `${environment.baseUrl}paymentmsvc/api/cart/quantity/update?cartItemId=${action.itemId}&quantity=${action.quantity}&personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`,
              {}
            )
            .pipe(
              map((data: any) => {
                this.services.callSnackbaronSuccess("Edited successfully");
                return reAddedSuccess({ sucessCartmessage: data });
              })
            );
        } catch (e) {}
      })
    );
  });

  public makepaymentnull$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(makenull),
      map(() => {
        return madenull({ paramater: null });
      })
    );
  });

  public getTIStateID$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getStateId$),
      switchMap((action) => {
        return this.httpClient
          .get(
            `${environment.baseUrl}client/api/traininginstitute/list?trainingInstituteId=${action.trainingInstituteID}&pageNumber=1&pageSize=1000`
          )
          .pipe(
            map((data: any) => {
              return trainingStateId$({ gotTiId: data });
            })
          );
      })
    );
  });

  effectivelyGetPaymentMethods$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPaymentMethod),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/getPaymentMethods?customerId=${action.customerId}`
          )
          .pipe(
            map((paymentMethod) =>
              gotPaymentMethod({
                paymentmethods: paymentMethod,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyMakeCharge$ = createEffect(() =>
    this.actions$.pipe(
      ofType(makeCharge),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.CANDIDATE_URL +
              `Exam/makepayment?personTenantRoleId=${action.personTenantRole}`,
            action.chargeBodu
          )
          .pipe(
            map((res) =>
              madeCharge({
                chargeResponse: res,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyGetCustomerId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(getPaymentCustomerId),
      switchMap((action) => {
        return this.httpClient
          .get<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/getPaymentCustomerId?PersonTenantRoleId=${action.PersonTenantRoleId}`
          )
          .pipe(
            map((res) =>
              gotPaymentCustomerId({
                customerIdObj: res,
              })
            ),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyCreatePaymentMethod$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createPaymentMethod),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER +
              `paymentmsvc/api/fattmerchant/createpaymentmethod`,
            action.CreatePaymentMethod
          )
          .pipe(
            map((res) => {
              return paymentMethodCreated({
                response: res,
              });
            }),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyCreatePaymentCustomerId$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createPaymentCustomerId),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SHORTER + `paymentmsvc/api/createPaymentCustomerId`,
            action.body
          )
          .pipe(
            map((res) => {
              return paymentCustomerIdCreated({
                response: res,
              });
            }),
            catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  effectivelyVocherUpdate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(voucherUpdate),
      switchMap((action) => {
        return this.httpClient
          .post<any>(
            URL.BASE_URL_SPONSOR + `Candidate/update-used-vouchers`,
            action.VocherUpdateDetails
          )
          .pipe(
            map((rescheduled) => {
              return voucherUpdated({
                response: rescheduled,
              });
            }),
            take(1)
            // catchError((err) => of(setErrorMessage({ message: err })))
          );
      })
    )
  );

  public VoucherDetails$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(VoucherDetails),
      switchMap((action) => {
        if (action.personDetail.roleId == 15) {
          return this.httpClient
            .get(
              `${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`
            )
            .pipe(
              map((data: any) => {
                return getVoucherDetails({ voucherDetails: data });
              })
            );
        } else {
          return this.httpClient
            .get(
              `${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`
            )
            .pipe(
              map((data: any) => {
                return getVoucherDetails({ voucherDetails: data });
              })
            );
        }
      })
    );
  });

  effectivelyDeleteCard$ = createEffect(() =>
    this.actions$.pipe(
      ofType(deleteCard),
      switchMap((action) => {
        return this.httpClient
          .delete<any>(
            URL.BASE_URL_SHORTER + `paymentmsvc/api/delete-card/${action.id}`
          )
          .pipe(
            map(
              (data) => {
                if (data)
                  this.services.callSnackbaronSuccess(
                    "card sucessfully deleted"
                  );
                return cardDeleted();
              },
              catchError((err) => {
                this.services.callSnackbaronError("Error in deleting Card");
                return of(setErrorMessage({ message: err }));
              })
            )
          );
      })
    )
  );

  deleteItem(roleID: number, itemID: any): Observable<any> {
    return this.httpClient.delete<any>(
      URL.BASE_URL_SHORTER +
        "client/api/operationstaff" +
        `/cart-item?personTenantRoleId=${roleID}&cartItemId=${itemID}`
    );
  }

  deleteVoucherItem(roleID: number, itemID: any): Observable<any> {
    return this.httpClient.delete(
      `${environment.baseUrl}candidate/api/Exam/cart-item?personTenantRoleId=${roleID}&cartItemId=${itemID}`
    );
  }
}
