import { HttpClient } from "@angular/common/http";
import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { button, customPopup, submitEvent } from "../pop-up.types";
import { BehaviorSubject, forkJoin } from "rxjs";
import { URL } from "src/app/core/url";
import { GlobalUserService } from "src/app/core/global-user.service";
import { environment } from "src/environments/environment";
import { MatDialogRef } from "@angular/material/dialog";
import { NavigationEnd, Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { recentLinkClickEventSelector } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import { linkClickEvent } from "../../table/dynamic-table-types";
import { FormTypes } from "src/app/client/application/application.types";
import { Roles } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { HttpService } from "src/app/core/http.service";
import { getUserDetails } from "src/app/client/state/shared/shared.actions";
import { SnackbarService } from "src/app/core/snackbar.service";
import { LanguageService } from "src/app/core/language.service";
import { ThemePalette } from "@angular/material/core";
import { Registry, RegistryExpiryDetails } from "src/@exai/layout/toolbar/toolbar-notifications/interfaces/notification.interface";

@Component({
  selector: "exai-add-application-for-candidate",
  templateUrl: "./add-application-for-candidate.component.html",
  styleUrls: ["./add-application-for-candidate.component.scss"],
})
export class AddApplicationForCandidateComponent
  implements OnInit, customPopup
{
  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> =
    new EventEmitter<submitEvent>();
  

  FormTypes = FormTypes;
  state:boolean = false
  IsEmployer:boolean=false
  form: FormGroup;
  showExamDropdown: boolean = false;
  applicationCategories: Array<any> = [];
  states: Array<any> = [];
  ticket:Array<any>=[];
  eligibilityRoutes: Array<any> = [];
  FormfilledCompleted: Array<any> = [];
  tickets:Array<any>=[];
  registeredExams;
  isApplicationFilled: boolean;
  recentLinkClickEvent: linkClickEvent = null;
  isGrievance: boolean = false;
  isShow:boolean=false;
  color: 'primary';
  tenantStateId
  isChecked = true;
  disabled = false;
  Interval: Array<any> = [];

  text
  text1
  element:Array<any>=[];


  
  // examTypeModels=[{id: 1, name: 'Active', checked: false, disabled: false},{id: 2, name: 'Registry', checked: false, disabled: false}]
  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    public globalUserService: GlobalUserService,
    private router: Router,
    private store: Store,
    private services: HttpService,
    private snackbar:SnackbarService,
    public global: LanguageService,
   
  ) {}
  ngOnInit(): void {
  
  
    if(this.globalUserService.userDetails.value.roleId !=13){
      this.form = this.formBuilder.group({
        state: ["", [Validators.required]],
        applicationCategory: ["", Validators.required],
        eligibilityRoutes: ["", Validators.required],
        enable : false
      });
    }else{
      
      this.form = this.formBuilder.group({
        state: [this.preResponse ? this.preResponse.stateName : "", [Validators.required]],
        stateCode: [this.preResponse?this.preResponse.stateCode:"", Validators.required],
        enable : [this.preResponse?this.preResponse.active:true],
        isRegistry:[this.preResponse && this.preResponse.isRegistry?this.preResponse.isRegistry:false],
      isEmployer:[this.preResponse && this.preResponse.isEmployer?this.preResponse.isEmployer:false,],
      registryexpire:[this.preResponse && this.preResponse.registryExpiryDetails ?this.preResponse.registryExpiryDetails.registryExpiryInYears:"",[]],
      renewalexpire:[this.preResponse &&  this.preResponse.registryExpiryDetails ?this.preResponse.registryExpiryDetails.renewalExpiryInYears:"",[]],
      startingNumber:[this.preResponse && this.preResponse.registrationNumber?this.preResponse.registrationNumber.startingNumber:"",[]],
      startsWith:[this.preResponse && this.preResponse.registrationNumber?this.preResponse.registrationNumber.startsWith:"",[]],
      reciporating:[this.preResponse && this.preResponse.registryExpiryDetails ?this.preResponse.registryExpiryDetails.reciprocityExpiryInYears:"",[]],
      // eligibilityRoutes:["",this.isRegistry?[Validators.required]:[]],
      reminders:[this.preResponse && "",[]],
      dropdown:[this.preResponse?this.preResponse.ticketCategories:"",[Validators.required]],
      fullssn:[this.preResponse && this.preResponse.isShowFullSSN?this.preResponse.isShowFullSSN:false,],
      renewal:[this.preResponse && this.preResponse.sendRenewalReminder?this.preResponse.sendRenewalReminder:false]
     // dropdown:["",[Validators.required]]
     
      });
    }
    this.filter()
    this.update()
    
  
    if(this.globalUserService.userDetails.value.roleId == Roles.OperationStaff || this.globalUserService.userDetails.value.roleId == Roles.SupportingStaff ){
      this.store
      .select(recentLinkClickEventSelector)
      .subscribe((x: linkClickEvent) => (this.recentLinkClickEvent = x));

    forkJoin([
      this.http.get<any>(URL.BASE_URL + `form/formtypesbyclient`),

      this.http.get<any>(
        URL.BASE_URL +
          `state/${this.globalUserService.userDetails.value.clientORtenantId}`
      ),
      this.http.get(
        URL.BASE_URL +
          `operationstaff/registeredexams?candidateId=${this.recentLinkClickEvent.element.personTenantRoleId}`
      ),
      this.http.get(
        URL.BASE_URL +
          `Candidate/any-activeform?personTenantRoleId=${this.recentLinkClickEvent.element.personTenantRoleId}&formTypeId=3`
      ),

    ]).subscribe((responses: Array<any>) => {
      this.applicationCategories = responses[0];
      this.states = responses[1];
      this.states = this.states.filter(x=>this.recentLinkClickEvent.element.stateName === x.stateName)

      if (responses[2]) this.registeredExams = responses[2];
      this.isApplicationFilled = responses[3];
      if (this.isApplicationFilled == false) {
        this.applicationCategories = this.applicationCategories.filter(
            (x: any) => {
                return x.id == 3 || x.id == 4;
             
            }
          );

      } else if (this.isApplicationFilled == true) {
        this.registeredExams?.forEach((x) => {
          if (x.isGrievanceFormSubmitted == false) {
            this.isGrievance = true;
          }
        });
        if (!this.isGrievance)
          this.applicationCategories = this.applicationCategories.filter(
            (x: any) => x.id != FormTypes.Grievance
          );
        this.applicationCategories = this.applicationCategories.filter(
          (x: any) => {
            return x.id == 4 || x.id == 2 || x.id == 3;
          }
        );
        if (this.registeredExams) {
          this.registeredExams = this.registeredExams.filter((x: any) => {
            return x.isGrievanceFilled == false
          });
        }
      }
      this.subscribeToValueChanges();
    });
    }else {
      forkJoin([
        this.http.get<any>(URL.BASE_URL + `form/formtypesbyclient`),
        this.http.get<any>(
          URL.BASE_URL +
            `state/${this.globalUserService.userDetails.value.clientORtenantId}`
        ),
        // this.http.get(
        //   URL.BASE_URL +
        //     `operationstaff/registeredexams?candidateId=${this.recentLinkClickEvent.element.personTenantRoleId}`
        // ),
        // this.http.get(
        //   URL.BASE_URL +
        //     `Candidate/any-activeform?personTenantRoleId=${this.recentLinkClickEvent.element.personTenantRoleId}&formTypeId=3`
        // ),
      ]).subscribe((responses: Array<any>) => {
        this.applicationCategories = responses[0];
        this.states = responses[1];
        if (responses[2]) this.registeredExams = responses[2];
        this.isApplicationFilled = responses[3];
        if (this.isApplicationFilled == false) {
          this.applicationCategories = this.applicationCategories.filter(
            (x: any) => {
              return x.id == 3 || x.id == 4;
            }
          );
        } else if (this.isApplicationFilled == true) {
          this.registeredExams?.forEach((x) => {
            if (x.isGrievanceFormSubmitted == false) {
              this.isGrievance = true;
            }
          });
          if (!this.isGrievance)
            this.applicationCategories = this.applicationCategories.filter(
              (x: any) => x.id != FormTypes.Grievance
            );
          this.applicationCategories = this.applicationCategories.filter(
            (x: any) => {
              return x.id == 4 || x.id == 2;
            }
          );
          if (this.registeredExams) {
            this.registeredExams = this.registeredExams.filter((x: any) => {
              return x.isGrievanceFilled == false;
            });
          }
        }
        this.subscribeToValueChanges();
      });
    }
    this.preResponse.renewalReminders.renewalRemindersInterval.forEach(x=>this.Interval.push(x))
  }
  subscribeToValueChanges() {
    this.form.controls.state.valueChanges.subscribe((x: number) => {
      this.http
        .get(
          environment.baseUrl +
            `client/api/eligibility/eligibilityroutesbystate?stateId=${x} &candidateId=${this.recentLinkClickEvent.element.personId}`
        )
        .subscribe((data: any) => {
          this.eligibilityRoutes = data;
        });
    });
    this.form.controls.applicationCategory.valueChanges.subscribe(
      (value: any) => {
        if (value) {
          var tempValue = this.form.value;
          if (value == FormTypes.Grievance) {
            this.form = this.formBuilder.group({
              state: [tempValue.state, [Validators.required]],
              applicationCategory: [value, Validators.required],
              eligibilityRoutes: [
                tempValue.eligibilityRoutes,
                Validators.required,
              ],
              selectExam: ["", Validators.required],
            });
          } else {
            this.form = this.formBuilder.group({
              state: [tempValue.state, [Validators.required]],
              applicationCategory: [value, Validators.required],
              eligibilityRoutes: [
                tempValue.eligibilityRoutes,
                Validators.required,
              ],
            });
          }
          this.subscribeToValueChanges();
        }
      }
    );
  }

 

  update(){
    this.text = (this.form.value.enable || this.form.value.isRegistry || this.form.value.fullssn ||this.form.value.sendRenewalReminder) ? "true" : "false";
  }
  isActive(active:boolean){
  }
  isRegistry(isRegistry:boolean){
      
     this.state = isRegistry ? true:false 
  }
  isEmployer(isEmployer:boolean){
    this.IsEmployer=isEmployer?true:false
  }
  isFullssn(active:boolean){
   }
   sendRenewalReminder(active:boolean){
   }
  Add() {
  
   
     let a = this.Interval.every(x=>
     (Number(this.form.controls.reminders.value ) !=x ) 
    )
    
     if(a && (this.form.value.reminders != "" &&this.form.value.reminders != null  && this.form.value.reminders != '0' ) && this.form.controls.reminders.value <='90'){
      
      this.Interval.push(Number(this.form.controls.reminders.value))
     
      this.form.patchValue({reminders:""})
     }
      else {
       
        this.form.patchValue({reminders:""})
      }
     
    
  }
  close(event){
    this.Interval = this.Interval.filter(x=>event != x)
  }

  
  onselect(event){
   
    this.ticket.push(event.id)
  //  let a = this.element.filter(x=>event.id === x.id)
  //  if(a.length > 0 && this.element.length > 0 ){
  //     let index = this.element.findIndex(e => e.id === event.id);
  //     if (index !== -1) {
  //       this.element.splice(index, 1);
  //     }
    
  //  }else{
  //   this.element.push(event)
  //   this.preResponse.renewalReminders.renewalRemindersInterval.forEach(x=>this.Interval.push(x))
  //  }
    
  
  }

  filter(){
 
    this.services.ticketcategories(this.globalUserService.tenantStateId).subscribe(
      (data:any)=>{
      if(data){
      
        this.tickets=data;
      
       
   
      
      }
    })
  }
  submit() {
    if(this.globalUserService.userDetails.value.roleId !=13){
        this.router.navigate([
          "application",
          this.form.value.applicationCategory,
          this.recentLinkClickEvent.element.personTenantRoleId,
          this.form.value.eligibilityRoutes,
          this.form.value.state,
          this.form.value.selectExam ? this.form.value.selectExam : 0,
          this.form.value.applicationCategory == 4?this.states[0].stateCode :""
        ]);
        this.dialogRef.close({});
    }
    else{
      let userDetails:Registry = {
        id:this.preResponse?this.preResponse.id:null,
        stateName:this.form.value.state,
        stateCode:this.form.value.stateCode,
        active:this.form.value.enable,
        isRegistry:this.form.value.isRegistry?this.form.value.isRegistry:this.state ,
        isEmployer:this.form.value.isEmployer?this.form.value.isEmployer:this.IsEmployer ,
        sendRenewalReminder:this.form.value.renewal,
        registryExpiryDetails:{
          registryExpiryInYears:this.state?this.form.value.registryexpire:null|| this.form.value.isRegistry?this.form.value.registryexpire:null,
          renewalExpiryInYears:this.state?this.form.value.renewalexpire:null|| this.form.value.isRegistry?this.form.value.renewalexpire:null,
          reciprocityExpiryInYears:this.state?this.form.value.reciporating:null|| this.form.value.isRegistry?this.form.value.reciporating:null,
        },
        registrationNumber:{
          startingNumber:this.form.value.startingNumber,
          startsWith:this.form.value.startsWith,
        },
        renewalReminders:{
          renewalRemindersInterval:this.form.value.reminders?this.form.value.reminders:this.Interval ,
          maxDays:this.state?90:null||this.form.value.isRegistry?90:null
        },
        isShowFullSSN :this.form.value.fullssn,
        TicketCategories:this.form.value.dropdown?this.form.value.dropdown:this.ticket,
      }
      
  this.preResponse? this.services.UpdateState(userDetails).subscribe(
       (data:any)=>{
      
        if (data) {
        
            this.snackbar.callSnackbaronSuccess(
              this.global.Updated_Successfully 
            )
            
          this.router.navigateByUrl("/dashboard");
       
        } else {
          this.snackbar.callSnackbaronError(this.global.Registration_failed);
        }
       
        this.dialogRef.close({});
        
     
      },(error:any)=>{
    
         this.snackbar.callSnackbaronError(`${error.message.message.error}`)
      })

:this.services.Addstate(userDetails).subscribe(
  (data:any)=>{
   
    if (data) {
    
        
        this.snackbar.callSnackbaronSuccess(
          this.global.Created_Successfully
        )
    
        this.router.navigateByUrl("/dashboard");
    } else {
      this.snackbar.callSnackbaronError(this.global.Registration_failed);
    }
    this.dialogRef.close({});

  },
  (error:any)=>{
         this.snackbar.callSnackbaronError(`${error.message.message.error}`)
        
      }
)
    }
    }
  
  }



