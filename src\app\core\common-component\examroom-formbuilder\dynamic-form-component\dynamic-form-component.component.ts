import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, DebugElement, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewChildren } from '@angular/core';
import { DynamicCheckboxModel, DynamicDatePickerModel, DynamicFormArrayGroupModel, DynamicFormArrayModel, DynamicFormControl, DynamicFormControlEvent, DynamicFormControlModel, DynamicFormControlRelation, DynamicFormGroupModel, DynamicFormLayout, DynamicFormService, DynamicFormValueControlModel, MATCH_REQUIRED, MATCH_VISIBLE, } from '@ng-dynamic-forms/core';
import { DomSanitizer } from "@angular/platform-browser";
import { FormArray, FormBuilder, FormControl, FormGroup, } from '@angular/forms';
import { form, validatedSubmission, sectionCompleteEvent, fetchOptionsFromApi, requestParam, requestDetails, paramTypes, observableApiCallsMetaData, section, CertStatus, sectionName, feildName, PARenewal, StateLists, Healthcare_Co_s3, HealthCare_CO_S3_E7, HealthCare_CO_S3_E8, Role, PARenewalCheckbox, SpanishRemovingExam, MACESC, FormTypeID, GAdemographic, StateAllow, FormTypesAllow, SCRECIPORATING, SCRECIVALUES, VAFORMS, VAFormsId, VAEligilibity, StateClientMaceAL_Allow } from '../form-builder.types'
import { HttpClient } from '@angular/common/http';
import { Observable, Subscription } from 'rxjs';
import { environment } from 'src/environments/environment';
import { forkJoin } from 'rxjs';
import { DynamicMaterialFormComponent } from '@ng-dynamic-forms/ui-material';
import lodash from 'lodash';
import { customCheckboxRequiredValidator } from '../formBuilder.validators';
import moment from 'moment';
import { DatePipe } from '@angular/common';
import { getDate } from 'date-fns';
import { SnackbarService } from '../../../snackbar.service';
import { Store } from '@ngrx/store';
import { FormBuilderService } from '../form-builder.service';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { FormTypes } from '../form-builder.service';
import { ActivatedRoute, Params } from '@angular/router';






// import { FormTypes } from 'src/app/client/application/application.types';
@Component({
  selector: 'exai-dynamic-form-component',
  templateUrl: './dynamic-form-component.component.html',
  styleUrls: ['./dynamic-form-component.component.scss'],
})
export class DynamicFormComponentComponent implements OnInit, OnDestroy, AfterViewInit {
  paramTypes = paramTypes;
  values: boolean
  apiCallsSub: Subscription;
  submitButton: HTMLElement;
  formGroupArray: FormGroup[] = [];
  formLayout: DynamicFormLayout;
  sectionsValidationsObservables: Array<Observable<any>> = [];
  sectionsValidationSubscribers: Array<Subscription> = [];
  SelectedSection: number = null;
  getfromdate: any;
  lazyListOfObservableApiCallsMetaData: observableApiCallsMetaData[] = [];
  dates: any;
  formID: number;
  fnUpdateFields: any[] = [];
  examDate: any;
  ExamDetails = { answerSheetSerialNumber: '', facilityName: '', trainingProgramName: '', evaluatorName: '' }
  paramsdata: any;
  formTypeId: number
  dateSelection: Date;
  ClientStateCode: string
  res: Date;
  AgeLimits: number
  constructor(
    private sanitizer: DomSanitizer,
    public formService: DynamicFormService,
    private http: HttpClient,
    private reactiveFormBuilder: FormBuilder,
    private snackbar: SnackbarService,
    private service: FormBuilderService,
    private cdr: ChangeDetectorRef,
    private globalUserService: GlobalUserService,
    private route: ActivatedRoute
  ) { }

  @Input() genesisForm?: form;
  @Input() data?: Array<any>;
  @Input() elementId?: string = null;
  @Input() submitButtonRef?: Array<HTMLElement> = null;
  @Input() disabled?: Array<boolean> | boolean = false;
  @Input() showStatic?: boolean = false;
  // element needed form 'elementPropertyToBeExtracted'
  @Input() element?: any;
  @Input() openAll?: boolean = false;

  @Output('validatedSubmission') submissionValidation: EventEmitter<validatedSubmission> = new EventEmitter<validatedSubmission>();
  @Output('onBlur') exaiOnBlur: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onChange') exaiOnChange: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onFocus') exaiOnFocus: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  @Output('onMatEvent') exaiOnMatEvent: EventEmitter<DynamicFormControlEvent> = new EventEmitter<DynamicFormControlEvent>();
  // this event is emitted on completion of individial sections
  @Output('sectionCompleted') sectionCompletionEvent: EventEmitter<sectionCompleteEvent> = new EventEmitter<sectionCompleteEvent>();
  // this event is emitted on the initial load of the form and gives out the validity of all complete sections
  @Output('initalSectionValidationEvent') initalSectionValidationEvent: EventEmitter<Array<sectionCompleteEvent>>
    = new EventEmitter<Array<sectionCompleteEvent>>();

  @ViewChildren('DynamicFormComponent') sectionsRef;


  ngOnInit(): void {

    if (!this.element) this.element = { "eligibilityRouteId": "21", "personEventId": "1108" };
    this.service.cellclickedDetails = this.element

    this.route.paramMap.subscribe((params: Params) => {
      this.paramsdata = params.params
      this.getAgeRestrictedDate()
      this.loadEntireForm()
    })




  }
  ngOnChanges() {
    this.formID = this.element.formTypeId
  }

  ngAfterViewInit() {

    this.afterViewInitLogic();
    this.formService.detectChanges();
  }
  detectChangesForIndividualFeilds() {
    this.sectionsRef.forEach((x: DynamicMaterialFormComponent) => {
      this.formService.detectChanges(x);
    })
  }
  afterViewInitLogic() {

    let submitButtonTrigger: any = this.submitButtonRef;
    if (this.submitButtonRef) {
      try {
        (this.submitButtonRef as any).forEach((x: any) => {
          x._elementRef?.nativeElement.addEventListener('click', () => {
            this.validateSubmission();
          });
        });
      }
      catch (e) {
        submitButtonTrigger._elementRef?.nativeElement.addEventListener('click', () => {
          this.validateSubmission();
        });
      }
    }
    for (let i = 0; i < this.sectionsValidationsObservables.length; i++) {
      let x = this.sectionsValidationsObservables[i];
      this.sectionsValidationSubscribers.push(x.subscribe((status: string) => {
        this.sectionCompletionEvent.emit(<sectionCompleteEvent>{
          sectionIndex: i,
          sectionName: this.genesisForm?.sections[i].name,
          sectionDesc: this.genesisForm?.sections[i].description,
          status: this.formGroupArray[i].status
        })
      }))
    }
    this.saveFormProgressOnState()
  }

  private initializeSubmitButtons() {

  }

  saveFormProgressOnState() {
    let tempSectionsProgress: Array<sectionCompleteEvent> = [];
    for (let i = 0; i < this.formGroupArray.length; i++) {
      tempSectionsProgress.push(<sectionCompleteEvent>{
        sectionIndex: i,
        sectionName: this.genesisForm?.sections[i].name,
        sectionDesc: this.genesisForm?.sections[i].description,
        status: this.formGroupArray[i].status
      })
    }
  }

  onBlur($event: DynamicFormControlEvent) {
    this.exaiOnBlur.emit($event);
  }
  onChange($event: DynamicFormControlEvent | any, sectionIndex: number) {
    // $event.model.value !='' && $event.model.value !=null && $event.model.value!=undefined? this.globalUserService.GrievamceEvaluator.next($event.model.value):this.globalUserService.GrievamceEvaluator.next('tester')
    //totally custom based on client requirement the below 2 lines
    // $event.model.id == sectionName.PARenewal ? this.HideField($event) : null
    $event.model.id == sectionName.Employer ? this.EmployerSelectedValues($event) : null
    $event.model.id == "e75e0b3101a04273a2bd0f4a374f93a6" ? this.hideField($event) : null
    // $event.model.id == "c8e558db99684e9ebb6d061b6ad28b10" ? this.globalUserService.GrievamceEvaluator.next($event) : null
    PARenewalCheckbox.includes($event.model.id) ? this.DisableDate($event) : null,
      $event.model.id == sectionName.AccomodationExams ? this.getdisableExamsType($event) : null
    $event.model?.parent?.id == sectionName.problemReport && ($event.model.value != "open" || $event.model.value != "closed") ? this.applyClosedDate_toProblemReportLogic($event, sectionIndex) : null
    $event.model?.parent?.id == sectionName.problemReport ? this.enableFeildOnMutliSelect($event) : null
    $event.model.parent.id == sectionName.FromAccomadation && ($event.model.label != "To") ? this.TodayDisable($event) : null
    Healthcare_Co_s3.includes($event.model.parent.id) || (HealthCare_CO_S3_E8.includes($event.model.parent.id)) || (HealthCare_CO_S3_E7.includes($event.model.parent.id)) && this.element.stateId == StateLists.CO ? this.HideAddButton($event) : null
    this.exaiOnChange.emit($event);
    this.checkAndFetchOptionsForDependentFields($event.model.id, $event.control.value);

    this.saveFormProgressOnState()
  }




  EmployerSelectedValues($event) {

    let EmployerInformation = ["Address", "City", "State", "ZipCode", "Employer Telephone Number", "Other Employer Name"]
    let EmployerData = this.genesisForm.sections[2].QuestionGroups[0].group.filter((x) => {
      return EmployerInformation.includes(x.label)
    })
    this.service.HideEmployerValues.next($event)
    this.service.HideNOEmployerValue.next(null)
    this.service.HideYesEmployerValue.next(null)



    $event.model.value == "no" ? this.service.HideEmployerValue.next(EmployerData) : null
    this.formService.detectChanges();
  }



  hideField($event) {
    let EmployerInformation = ["Address", "City", "State", "ZipCode", "Employer Telephone Number", "Other Employer Name", "Employer Phone Number"]
    let EmployerData = this.genesisForm.sections[2].QuestionGroups[0].group.filter((x) => {
      return EmployerInformation.includes(x.label)
    })
    this.service.HideEmployerValue.next(null)
    this.service.HideNOEmployerValue.next(null)
    this.service.HideEmployerValues.next($event)
    this.service.HideYesEmployerValue.next($event)
    $event.model.value == "no" ? this.service.HideNOEmployerValue.next(EmployerData) : null
  }



  DisableDate($event) {
    if (this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes) {
      for (let j = 0; j < this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes.length; j++) {
        var dateAttr = this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes[j];
        let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
          this.genesisForm.sections[2].QuestionGroups);

        if (formControlModel.label == "Date Last Worked" && $event.$event.checked == true) {
          formControlModel.disabled = true;
          formControlModel.value = null;

        }
        else {
          formControlModel.disabled = false;
        }
      }
    }
  }
  HideAddButton($event) {
    let HidebuttonId = Healthcare_Co_s3.find(y => y == $event.model.parent.id) ? Healthcare_Co_s3.find(y => y == $event.model.parent.id) : HealthCare_CO_S3_E8.find(y => y == $event.model.parent.id) ? (HealthCare_CO_S3_E8.find(y => y == $event.model.parent.id)) : (HealthCare_CO_S3_E7.find(y => y == $event.model.parent.id))
    let HideIndex = Healthcare_Co_s3.includes($event.model.parent.id) ? Healthcare_Co_s3.findIndex(y => y == $event.model.parent.id) : HealthCare_CO_S3_E8.includes($event.model.parent.id) ? (HealthCare_CO_S3_E8.findIndex(y => y == $event.model.parent.id)) : (HealthCare_CO_S3_E7.findIndex(y => y == $event.model.parent.id))
    const a: any = document.getElementsByClassName("hover:bg-gray-100")
    const AddButton: any = document.getElementsByClassName("hover:bg-gray-100")
    let AddNewButtons = []
    for (let i = 0; i < AddButton.length; i++) {
      if (AddButton[i].attributeStyleMap.size != 0 && AddButton[i].innerText != "close Remove" && (Healthcare_Co_s3.includes(AddButton[i].__ngContext__[8].id) || HealthCare_CO_S3_E7.includes(AddButton[i].__ngContext__[8].id) || HealthCare_CO_S3_E8.includes(AddButton[i].__ngContext__[8].id))
      ) {
        AddNewButtons.push(AddButton[i])
      }
    }
    if ($event.model.value != 'yes') {
      Healthcare_Co_s3.includes(HidebuttonId) || (HealthCare_CO_S3_E8.includes(HidebuttonId)) || (HealthCare_CO_S3_E7.includes(HidebuttonId)) ? AddNewButtons[HideIndex].style.display = "none" : null
      let genesisForm = this.genesisForm.sections[3]
      if (genesisForm.QuestionGroupsMetaData.formArrayModelIds) {
        genesisForm.QuestionGroupsMetaData.formArrayModelIds.forEach((ele: any, i) => {
          if (ele.parentModelID == $event.model.parent.id) {
            let formControlModel = this.formService.findModelById<any>(ele.modelID,
              genesisForm.QuestionGroups);
            if (formControlModel) {
              // Finding a modelIdIndex for each of Questions Group //
              let modelIdIndex = genesisForm.QuestionGroupsMetaData.formArrayModelIds.findIndex(x => x.modelID === formControlModel.id)
              if (Healthcare_Co_s3.includes($event.model.parent.id) || (HealthCare_CO_S3_E8.includes(HidebuttonId)) || (HealthCare_CO_S3_E7.includes(HidebuttonId))) {
                for (let i = 0; i < AddButton.length; i++) {
                  if (AddButton[i]) {
                    // Calling a removeItem function for removing a fields which are added when  clicking a event is no //
                    this.removeItem(3, modelIdIndex, formControlModel, AddButton[i])
                  }
                }
              }
            }
          }
        })
      }
    }
    else if ($event.model.value == 'yes') {
      (Healthcare_Co_s3.includes(HidebuttonId) || (HealthCare_CO_S3_E8.includes(HidebuttonId)) || (HealthCare_CO_S3_E7.includes(HidebuttonId))) ? (AddNewButtons[HideIndex]).style.display = "block" : null
      let genesisForm = this.genesisForm.sections[3]
      if (genesisForm.QuestionGroupsMetaData.formArrayModelIds) {
        genesisForm.QuestionGroupsMetaData.formArrayModelIds.forEach((ele: any, i) => {
          if (ele.parentModelID == $event.model.parent.id) {
            let formControlModel = this.formService.findModelById<any>(ele.modelID,
              genesisForm.QuestionGroups);
            if (formControlModel) {
              // Finding a modelIdIndex for each of Questions Group //
              let modelIdIndex = genesisForm.QuestionGroupsMetaData.formArrayModelIds.findIndex(x => x.modelID === formControlModel.id)
              if (Healthcare_Co_s3.includes($event.model.parent.id) || (HealthCare_CO_S3_E8.includes(HidebuttonId)) || (HealthCare_CO_S3_E7.includes(HidebuttonId))) {
                if (AddButton[i]) {
                  // Calling a removeItem function for removing a fields which are added when  clicking a event is no //
                  this.insertItem(3, modelIdIndex, formControlModel, AddButton[i])
                  // AddNewButtons[HideIndex].style.display ="none"
                }
              }
            }
          }
        })
      }
    }
  }

  // Accomodation Examtype disable the online exams when one online exam is selected //
  getdisableExamsType($event) {
    let geneformsExamType = this.genesisForm.sections[4] || this.genesisForm.sections[3] || this.genesisForm.sections[2] || this.genesisForm.sections[5]
    geneformsExamType.QuestionGroups[0].group.forEach((data, index) => {
      if (data.label == "ExamType") {
        let ExamTypesValue = data.options.filter(x => $event.model.value.find(y => y == x.value))
        let OnlyOnlineExams = ExamTypesValue.filter(x => (x.label != (ExamTypesForHardcoding.NA_SES || ExamTypesForHardcoding.NA_SE)))
        if (OnlyOnlineExams.length > 0) {
          switch (OnlyOnlineExams[0].label) {
            case ExamTypesForHardcoding.NA_WE:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_OEE || element.label == ExamTypesForHardcoding.NA_OSE) {
                  return element.disabled = true
                }
              });
              break;
            case ExamTypesForHardcoding.NA_OEE:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_WE || element.label == ExamTypesForHardcoding.NA_OSE) {
                  return element.disabled = true
                }
              });
              break;
            case ExamTypesForHardcoding.NA_OSE:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_WE || element.label == ExamTypesForHardcoding.NA_OEE) {
                  return element.disabled = true
                }
              });
              break
            case ExamTypesForHardcoding.NA_WEP:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_OEEA || element.label == ExamTypesForHardcoding.NA_OSEA || element.label == ExamTypesForHardcoding.NA_WEP_MAE) {
                  return element.disabled = true
                }
              });
              break

            case ExamTypesForHardcoding.NA_WEP_MA:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_OEEA || element.label == ExamTypesForHardcoding.NA_OSEA || element.label == ExamTypesForHardcoding.NA_WEP_MAE) {
                  return element.disabled = true
                }
              });
              break
            case ExamTypesForHardcoding.NA_OEEA:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_WEP || element.label == ExamTypesForHardcoding.NA_OSEA || element.label == ExamTypesForHardcoding.NA_WEP_MA) {
                  return element.disabled = true
                }
              });
              break
            case ExamTypesForHardcoding.NA_OSEA:
              data.options.forEach(element => {
                if (element.label == ExamTypesForHardcoding.NA_WEP || element.label == ExamTypesForHardcoding.NA_OEEA) {
                  return element.disabled = true
                }
              });
              break
          }
        } else {
          data.options.forEach(element => {
            return element.disabled = false
          });
        }
      }
    })
  }


  applyClosedDate_toProblemReportLogic($event, secInd) {
    if (this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes) {
      for (let j = 0; j < this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes.length; j++) {
        var dateAttr = this.genesisForm.sections[2].QuestionGroupsMetaData.dateAttributes[j];
        let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
          this.genesisForm.sections[2].QuestionGroups);
        var curDate = new Date();
        if (formControlModel) {
          if (dateAttr.displayClosedDate && $event.model.value == "closed") {
            formControlModel.disabled = true;
            formControlModel.value = curDate
          }
          else if (dateAttr.displayClosedDate && $event.model.value == "open") {
            formControlModel.value = null
          }
        }
      }
    }

    //the below logic is to disable all feilds in problem report in only summary section when closed feild  is selecetd...
    this.genesisForm.sections[secInd].QuestionGroups[0].group.forEach((data, index) => {
      this.SelectedSection = secInd;
      if ($event.model.value == "closed") {
        data.disabled = (data.id != feildName.Problemreportsection3ClosedDropDown && data.id != feildName.Problemreportsection3ClosedDate) ? true : false
      }
      if ($event.model.value == "open") {
        data.disabled = false
      }
      if (data.value?.length > 0) {
        if (data.id == feildName.Problemreportsection3Description && data.value?.find(x => x == "Abuse")) {
          for (let i = (this.genesisForm.sections[secInd].QuestionGroups[0].group.length - 1); i < this.genesisForm.sections[secInd].QuestionGroups[0].group.length; i++) {
            this.genesisForm.sections[secInd].QuestionGroups[0].group[i].hidden = false
          }
        }
      }
    })
  }

  TodayDisable($event) {
    var maxDate = new Date();
    let genesisForm = this.genesisForm.sections[4] || this.genesisForm.sections[3] || this.genesisForm.sections[2] || this.genesisForm.sections[5]
    let b = genesisForm.QuestionGroupsMetaData.dateAttributes
    if (genesisForm.QuestionGroupsMetaData.dateAttributes) {
      for (let j = 0; j < genesisForm.QuestionGroupsMetaData.dateAttributes.length; j++) {
        var dateAttr = genesisForm.QuestionGroupsMetaData.dateAttributes[j];
        let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
          genesisForm.QuestionGroups);
        // var curDate = new Date();
        if (formControlModel) {
          let maxDate = new Date($event.control.value)
          let minDate = new Date($event.control.value)
          if (dateAttr.feildID == "6a7136e212b84160af9139eac5380005" && $event.model.label == "From") {
            minDate.setDate(($event.control.value).getDate() - 0)
            maxDate.setDate(($event.control.value).getDate() + 364)
            formControlModel.max = maxDate
            formControlModel.min = minDate
            formControlModel.value = null

          } else if (dateAttr.todateDisabled && $event.model.label == "TO") {
            formControlModel.value = new Date($event.control.value)

          }



          // if (dateAttr.todateDisabled != true){
          //   formControlModel.value =new Date($event.control.value)

          // }else{
          //   formControlModel.value =$event.control.value.setDate(($event.control.value).getDate() + 365);
          //   formControlModel.max = new Date($event.control.value)
          // }
          //  else if (dateAttr.displayClosedDate && $event.model.value=="open") {
          //   formControlModel.value = null
          // }
        }
        else {
          return false
        }
      }
    }
  }

  checkStatus() {
    if (this.SelectedSection != null) {
      this.genesisForm.sections[this.SelectedSection].QuestionGroups[0].group.forEach((data, index) => {
        let formcontrol =
          this.formService.findModelById<DynamicFormArrayModel>(
            data.id,
            this.genesisForm.sections[this.SelectedSection].QuestionGroups);
        formcontrol.disabled = false
      })
    }
  }

  checkAndFetchOptionsForDependentFields(modelId: string, controlValue: any) {

    var index = this.lazyListOfObservableApiCallsMetaData.findIndex((x: observableApiCallsMetaData) => { return x.optionsToFetch.relatedFieldId == modelId });
    if (index > -1) {
      // make api call to fetch data here for the dependent field .....
      // only question that remains is esentially which param from the 
      // requestParamArray has to be replaced by $event.control.value
      // paramindex is the index of the param that needs a value
      var paramIndex = this.lazyListOfObservableApiCallsMetaData[index]
        .optionsToFetch
        .requestDetails
        .requestParams
        .findIndex((x: requestParam) => {
          return !x.paramValue
        });
      var requestDetailsClone = lodash.cloneDeep(this.lazyListOfObservableApiCallsMetaData[index].optionsToFetch.requestDetails);
      if (paramIndex > -1) {
        requestDetailsClone.requestParams[paramIndex].paramValue = controlValue;
      }
      this.preprocessAction(requestDetailsClone).subscribe((response: any) => {
        if (response) {
          // JSON.stringify(requestDetailsClone.fetchUrl) != JSON.stringify(['candidate', 'api', 'Form', 'grievance-form', 'skill', 'steps']) ? (response as Array<any>).sort(this.comparator('label')) : response
          let formControlModel = this.formService.findModelById<any>(
            this.lazyListOfObservableApiCallsMetaData[index].optionsToFetch.feildID,
            this.genesisForm.sections[this.lazyListOfObservableApiCallsMetaData[index].sectionIndex].QuestionGroups);
          if (formControlModel)
            formControlModel.options = response;
          this.detectChangesForIndividualFeilds();
          if (this.fnUpdateFields) {
            setTimeout(() => {
              this.fnUpdateFields.forEach(x => x());
            }, 750);
          }
        }
      })
    }
  }
  onFocus($event: DynamicFormControlEvent) {
    this.exaiOnFocus.emit($event);
  }
  onMatEvent($event: DynamicFormControlEvent) {
    this.exaiOnMatEvent.emit($event);
  }
  transformHTML(msg: string) {
    // return this.sanitizer.bypassSecurityTrustHtml(msg);
    return msg;
  }
  removeItem(sectionIndex: number, modelIdIndex: number, context: DynamicFormArrayModel, index: number) {
    let formArrayModel =
      this.formService.findModelById<DynamicFormArrayModel>(
        this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds[modelIdIndex].modelID,
        this.genesisForm.sections[sectionIndex].QuestionGroups);
    let formArrayControl = this.formService.findControlByModel<FormArray>(formArrayModel, this.formGroupArray[sectionIndex]);
    this.formService.removeFormArrayGroup(index, formArrayControl, context);
    this.formService.detectChanges();
  }

  insertItem(sectionIndex: number, modelIdIndex: number, context: DynamicFormArrayModel, index: number) {
    let formArrayModel =
      this.formService.findModelById<DynamicFormArrayModel>(
        this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds[modelIdIndex].modelID,
        this.genesisForm.sections[sectionIndex].QuestionGroups);
    //the below logic is auto prefill the date for selected date when a formgroup is added from formarray this is done dynamically
    //to make same for other form check the checkboox of current date when creating forms
    if (this.genesisForm.sections[sectionIndex].id == sectionName.ProblemreportSction6) {
      if ((this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds[0].totalCount > formArrayModel.groups.length)) {
        let formArrayControl = this.formService.findControlByModel<FormArray>(formArrayModel, this.formGroupArray[sectionIndex]);
        this.formService.insertFormArrayGroup(index, formArrayControl, context ? context : formArrayModel);
        setTimeout(() => {
          for (let j = 0; j < this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes.length; j++) {
            let dateAttr = this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes[j];
            if (formArrayModel.groups.length > 0) {
              formArrayModel.groups.forEach(groups => {
                let input = this.formService.findModelById<any>(dateAttr.feildID, groups.group);
                if (dateAttr.displayCurrentDate && input.value == null) {
                  input.value = new Date();
                }
              })
            }
          }
          this.formService.detectChanges();

        }, 200)
        this.formService.detectChanges();
      }
      else {
        this.snackbar.callSnackbaronWarning("Add Limit Exceeded!")
      }
    }
    else {
      let formArrayControl = this.formService.findControlByModel<FormArray>(formArrayModel, this.formGroupArray[sectionIndex]);
      this.formService.insertFormArrayGroup(index, formArrayControl, context ? context : formArrayModel);
      setTimeout(() => {
        for (let j = 0; j < this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes.length; j++) {
          let dateAttr = this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.dateAttributes[j];
          if (formArrayModel.groups.length > 0) {

            formArrayModel.groups.forEach(groups => {
              let input = this.formService.findModelById<any>(dateAttr.feildID, groups.group);

              if (dateAttr.displayCurrentDate && input.value == null) {
                input.value = new Date();
              }
            })
          }
        }
        this.formService.detectChanges();
      }, 500)
      this.formService.detectChanges();
    }

  }
  // can later be used while adding drag and drop functionality 
  moveItem(sectionIndex: number, modelIdIndex: number, context: DynamicFormArrayModel, index: number) {
    let formArrayModel =
      this.formService.findModelById<DynamicFormArrayModel>(
        this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.formArrayModelIds[modelIdIndex].modelID,
        this.genesisForm.sections[sectionIndex].QuestionGroups);
    let formArrayControl = this.formService.findControlByModel<FormArray>(formArrayModel, this.formGroupArray[sectionIndex]);
    this.formService.moveFormArrayGroup(index, -1, formArrayControl, context);
    this.formService.detectChanges();
  }
  validateSubmission() {
    this.checkStatus()
    let errorMessages = [];
    for (let i = 0; i < this.formGroupArray.length; i++) {
      let ABrelatedFeilds = this.genesisForm.sections[i].QuestionGroupsMetaData.ABrelatedFeilds;
      for (let feildPairs of ABrelatedFeilds) {
        let feildsToBeFilled1 = [];
        let feildsToBeFilled2 = [];
        let formModel1 = this.formService.findModelById<DynamicFormArrayModel | DynamicFormGroupModel>(feildPairs.feild1ID, this.genesisForm.sections[i].QuestionGroups);
        let formControl1 = this.formService.findControlByModel<FormArray | FormGroup>(formModel1, this.formGroupArray[i]);
        for (let value of Object.entries(formControl1.value)) {
          if (value[1] == null || value[1] == "") feildsToBeFilled1.push(value[0]);
        }
        let formModel2 = this.formService.findModelById<DynamicFormArrayModel | DynamicFormGroupModel>(feildPairs.feild2ID, this.genesisForm.sections[i].QuestionGroups);
        let formControl2 = this.formService.findControlByModel<FormArray | FormGroup>(formModel2, this.formGroupArray[i]);

        for (let value of Object.entries(formControl2.value)) {
          if (value[1] == null || value[1] == "") feildsToBeFilled2.push(value[0]);
        }
        if (feildsToBeFilled2.length > 0 && feildsToBeFilled1.length > 0) {
          errorMessages.push("Please Fill the following feilds of " + formModel1.label + " : " + feildsToBeFilled1.join(", ") + " OR " + "fill the following feilds of " + formModel2.label + " : " + feildsToBeFilled2.join(", "));
        }
      }
    }

    this.submissionValidation.emit({
      valid: errorMessages.length == 0 && !(this.formGroupArray.find((x: any) => { return x.valid == false })),
      errorMessages,
      formValue: this.formGroupArray.map((x: any, sectionIndex: number) => {
        //the above three line login is for the duration of given date for SC E6 and E7 Eligibility route the date is hardcoded
        if (x.value.hasOwnProperty(sectionName.SCE6SectionName)) {
          x.value.south_carolina_nurse_aide_testing_application_section_1_q1[feildName.SCE6Eligibility] = '06/06/2024'
        }
        if (x.value.hasOwnProperty(sectionName.SCE7SectionName)) {
          x.value.south_carolina_nurse_aide_testing_application_section_1_s1q1[feildName.SCE7Eligibility] = '06/06/2024'
        }
        if (x.value.hasOwnProperty(sectionName.MSTempE9Eligibility)) {

          x.value.mississippi_nurse_aide_testing_application_section_1_q1s1[feildName.E9Eligibility] = '10/07/2022'
        }

        if (x.value.hasOwnProperty(feildName.E2EligibilityNV)) {

          x.value.nevada_nurse_aide_testing_application_section_1_q1s1[feildName.E2EligibilityNV] = '9992';
        }
        if (x.value.hasOwnProperty(feildName.E3eligibilityNV)) {


          x.value.nevada_nurse_aide_testing_application_section_1_q1s1[feildName.E3eligibilityNV] = '9990';
        }
        if (x.value.hasOwnProperty(sectionName.NVE4)) {

          x.value.nevada_nurse_aide_testing_application_section_1_s1q1[feildName.E4eligibilityNV] = '9993';
        }


        var sectionValueClone = lodash.cloneDeep(x.value);
        this.genesisForm.sections[sectionIndex].QuestionGroups.forEach((g: DynamicFormGroupModel) => {
          g.group?.forEach((f: DynamicFormControlModel) => {
            if (f.relations && f.relations.length > 0) {
              let idOfDependentField =
                f.relations
                  .filter((x: DynamicFormControlRelation) => { return x.match == MATCH_VISIBLE })
                  .map((x: DynamicFormControlRelation) => { return x.when[0].id })[0];
              let valueOfDependentField =
                f.relations
                  .filter((x: DynamicFormControlRelation) => { return x.match == MATCH_VISIBLE })
                  .map((x: DynamicFormControlRelation) => { return x.when[0].value })[0];

              let actualValue = getObject(sectionValueClone, idOfDependentField, false);
              if (actualValue != valueOfDependentField)
                getObject(sectionValueClone, f.id, true);
            }
          })
        })
        for (let quesGroupKeyValue of Object.entries(sectionValueClone)) {
          if (Array.isArray(quesGroupKeyValue[1])) {
            var sampleValueOfSet = lodash.cloneDeep(quesGroupKeyValue[1][0]);
            for (let groupValue of quesGroupKeyValue[1]) {
              for (let feildValues of Object.entries(groupValue)) {
                if (!Array.isArray(sampleValueOfSet[feildValues[0]]))
                  sampleValueOfSet[feildValues[0]] = [feildValues[1]];
                else sampleValueOfSet[feildValues[0]].push(feildValues[1]);
              }
            }
            sectionValueClone[quesGroupKeyValue[0]] = sampleValueOfSet;
          }
        }
        return sectionValueClone;
      })
    })
  }
  populateData(data: Array<any>) {
    if (data.length > 0) {
      let filledData = data
      for (let i = 0; i < data.length; i++) {
        for (let key of Object.keys(this.data[i])) {
          var formArrayIdsPopulated = [];
          if (this.data[i][key]) {
            for (let object of Object.entries(this.data[i][key])) {
              object.forEach((x: any, i) => {
                if (moment(x).isValid() && typeof (x = Date) == "string") {
                  try {
                    var datepipe = new DatePipe("en-us")
                    let a = datepipe.transform((x), "MM/dd/YYYY", "+0000",)
                    object[i] = new Date(a)
                  }
                  catch (e) {
                  }
                }
              });
              for (let secInd = 0; secInd < this.genesisForm.sections.length; secInd++) {
                let questionGroupModel = this.formService.findById(key, this.genesisForm.sections[secInd].QuestionGroups);
                if (questionGroupModel && questionGroupModel.type == 'ARRAY' && !formArrayIdsPopulated.includes(key)) {

                  //the below conditiion id for viewing the disabled formarray for problem report which does not applys for other form
                  if (this.genesisForm.id == sectionName.ProblemReportId) {
                    this.genesisForm.sections[secInd].QuestionGroupsMetaData.formArrayModelIds.forEach((data, index) => {
                      let formArrayModel =
                        this.formService.findModelById<DynamicFormArrayModel>(
                          this.genesisForm.sections[secInd].QuestionGroupsMetaData.formArrayModelIds[index].modelID,
                          this.genesisForm.sections[secInd].QuestionGroups);
                      setTimeout(() => {
                        for (let j = 0; j < this.genesisForm.sections[secInd].QuestionGroupsMetaData.parsingMetaData.length; j++) {
                          let dateAttr = this.genesisForm.sections[secInd].QuestionGroupsMetaData.parsingMetaData[j];
                          if (formArrayModel.groups.length > 0) {
                            formArrayModel.groups.forEach(groups => {
                              let input = this.formService.findModelById<any>(dateAttr.feildID, groups.group);
                              input != null ? input.disabled = this.getSectionDisabled(secInd) : '';
                            })
                          }
                        }
                      }, 200)
                    })
                  }
                  questionGroupModel.disabled = this.getSectionDisabled(secInd);
                  let formArrayControl = this.formService.findControlByModel<FormArray>(questionGroupModel, this.formGroupArray[secInd]);
                  let sizeOfFormArray = (Object.values(this.data[i][key])[0] as Array<any>).length;
                  let toInsert = Math.abs(formArrayControl.length - sizeOfFormArray);
                  for (let formArrInd = 0; formArrInd < toInsert; formArrInd++)
                    this.formService.insertFormArrayGroup(formArrInd, formArrayControl, questionGroupModel as DynamicFormArrayModel);
                  var formArrayValues = [];
                  var ModdedFormArrayKeys = Object.keys(this.data[i][key]);
                  var ModdedformArrayValues = Object.values(this.data[i][key]);
                  for (let formArrInd = 0; formArrInd < sizeOfFormArray; formArrInd++) {
                    var tempObj = {};
                    for (let keyIndex = 0; keyIndex < ModdedFormArrayKeys.length; keyIndex++)
                      tempObj[ModdedFormArrayKeys[keyIndex]] = ModdedformArrayValues[keyIndex][formArrInd];
                    formArrayValues.push(tempObj);
                  }
                  for (let formArrInd = 0; formArrInd < sizeOfFormArray; formArrInd++)
                    formArrayControl.controls[formArrInd].setValue(formArrayValues[formArrInd]);
                  formArrayIdsPopulated.push(key);
                  this.formService.detectChanges();
                }
                else {
                  let formControlModel = this.formService.findById(object[0], this.genesisForm.sections[secInd].QuestionGroups);
                  if (formControlModel) {
                    formControlModel.disabled = this.getSectionDisabled(secInd);
                    this.checkAndFetchOptionsForDependentFields(object[0], object[1]);
                    if (formControlModel['required'] && formControlModel['hidden']) {
                      formControlModel['required'] = false;
                    }
                    let formControl = this.formService.findControlByModel(formControlModel, this.formGroupArray[secInd]);
                    if (formControl) {
                      formControl.setValue(object[1]);
                      this.fnUpdateFields.push(() => {
                        formControl.setValue(formControl.value);
                      });
                      if (!object[1] && (formControlModel as DynamicFormValueControlModel<any>).required && ((formControlModel as any).relations.findIndex((x: DynamicFormControlRelation) => { return (x.match === MATCH_REQUIRED || x.match === MATCH_VISIBLE) }) == -1)) {
                        formControl.setErrors({ required: true });
                      }
                      this.formService.detectChanges();
                    }
                  }
                  if (secInd == 2) {
                    //the below logic is to disable all feilds in problem report in only summary section when closed feild  is selecetd...
                    this.genesisForm.sections[secInd].QuestionGroups[0].group.forEach((data, index) => {
                      if (!this.disabled) {
                        this.SelectedSection = secInd
                        let formcontrol =
                          this.formService.findModelById<DynamicFormArrayModel>(
                            data.id,
                            this.genesisForm.sections[secInd].QuestionGroups);
                        formcontrol.disabled = (formcontrol.id != feildName.Problemreportsection3ClosedDropDown && formcontrol.id != feildName.Problemreportsection3ClosedDate) && filledData[secInd].problem_report_section_1_q1s1[feildName.Problemreportsection3ClosedDropDown] == "closed" ? true : false
                      }
                      if (data.id == feildName.Problemreportsection3Description) {
                        for (let i = (this.genesisForm.sections[secInd].QuestionGroups[0].group.length - 1); i < this.genesisForm.sections[secInd].QuestionGroups[0].group.length; i++) {
                          this.genesisForm.sections[secInd].QuestionGroups[0].group[i].hidden = true;
                        }
                        this.enableFeildOnMutliSelect({ model: data, $event: { value: filledData[secInd].problem_report_section_1_q1s1[feildName.Problemreportsection3Description] } })
                      }
                    })

                  }
                  this.formGroupArray[secInd].markAsTouched();
                }

                setTimeout(() => {
                  Object.keys(this.data[2]).forEach((value, i) => {
                    this.genesisForm.sections[2].QuestionGroupsMetaData.formArrayModelIds.forEach((data) => {
                      if (data.parentModelID == value && Object.values(this.data[2][value])[0] == 'yes') {
                        const AddButton: any = document.getElementsByClassName("hover:bg-gray-100")
                        if (this.element.stateId == StateLists.CO) {
                          AddButton[i].style.display = "block"
                        }
                      }
                    })
                  })
                }, 4000)
              }
            }
          }
        }
      }
    }
  }

  prefillData() {
    for (let sectionIndex = 0; sectionIndex < this.genesisForm.sections.length; sectionIndex++) {
      for (let parsingMetaDataIndex = 0; parsingMetaDataIndex < this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData.length; parsingMetaDataIndex++) {
        var parsingMetaDataObj = this.genesisForm.sections[sectionIndex].QuestionGroupsMetaData.parsingMetaData[parsingMetaDataIndex];
        if (parsingMetaDataObj.prefillValue) {

          let formControlModel = this.formService.findById(parsingMetaDataObj.feildID,
            this.genesisForm.sections[sectionIndex].QuestionGroups);
          if (formControlModel) {

            let formControl = this.formService.findControlByModel(formControlModel, this.formGroupArray[sectionIndex]);
            if (formControl) {
              let statusId = this.globalUserService.personEventId != undefined && this.globalUserService.personEventId != null ? this.globalUserService.personEventId.formTypeId in CertStatus : ""
              if (parsingMetaDataObj.elementPropertyToBeExtracted && this.element[parsingMetaDataObj.elementPropertyToBeExtracted] && !(this.element.certStatusId in CertStatus || statusId) && (this.formID != FormTypes.Demographic || this.globalUserService.personEventId.formTypeId != FormTypes.Demographic) || (this.element.formTypeId === FormTypes.Grievance)) {
                // commenting out disabling on directions on Phani and Pankaj, as conductor api is not sending proper data
                // formControlModel.disabled = true;

                formControl.setValue(this.element[parsingMetaDataObj.elementPropertyToBeExtracted]);


              }
              if (!this.element[parsingMetaDataObj.elementPropertyToBeExtracted] && (formControlModel as DynamicFormValueControlModel<any>).required) formControl.setErrors({ required: true });

              this.formService.detectChanges();
            }
          }
        }
      }
    }
  }
  emitInitialSectionsCompletionEvent() {

    const fn = () => {
      let tempSectionsProgress: Array<sectionCompleteEvent> = [];
      for (let i = 0; i < this.formGroupArray.length; i++) {
        tempSectionsProgress.push(<sectionCompleteEvent>{
          sectionIndex: i,
          sectionName: this.genesisForm?.sections[i].name,
          sectionDesc: this.genesisForm?.sections[i].description,
          status: this.data
            ? this.formGroupArray[i].status
            : (this.formGroupArray[i].touched ? this.formGroupArray[i].status : 'UNTOUCHED')
        })
      }
      this.initalSectionValidationEvent.emit(tempSectionsProgress)
    }
    const rg = new RegExp(/Grievance/, 'gmi')
    if (this.genesisForm.name.match(rg)) setTimeout(() => fn(), 100);
    else fn();
  }
  expandInstructions($event: Event, id: string) {
    $event.stopPropagation();
    let element = document.getElementById(id);
    element.style.display === "none" ? element.style.display = "block" : element.style.display = "none";
  }

  onKey(event, id) {
    if (event != '' && event != null && event != undefined) {

      if (((id.target.name === '0d13928eef624576af3208d204e71200') && id.target.value)) {
        this.globalUserService.GrievamceEvaluator.next({ id: '0d13928eef624576af3208d204e71200', value: id.target.value })

      } else if ((id.target.name === '809687c1e8b149d78001b97891d0099a') && id.target.value) {
        this.globalUserService.GrievamceEvaluator.next({ id: '809687c1e8b149d78001b97891d0099a', value: id.target.value })


      } else if (id.target.name === 'db463d23413b480882541e45aefe2382' && id.target.value) {
        this.globalUserService.GrievamceEvaluator.next({ id: 'db463d23413b480882541e45aefe2382', value: id.target.value })

      } else if (id.target.name === 'e5ad83e8fd4f4486bba529a8d523b5e1' && id.target.value) {
        this.globalUserService.GrievamceEvaluator.next({ id: 'e5ad83e8fd4f4486bba529a8d523b5e1', value: id.target.value })

      }

      else if (id.target.value == 'online' || id.target.value == 'ada' || id.target.value == 'turned' || id.target.value == 'skills') {
        this.globalUserService.GrievamceEvaluator.next({ id: 'e5ad83e8fd4f4486bba529a8d523b5e1', value: 'tester' })

      }


    } else {
      this.globalUserService.GrievamceEvaluator.next({ id: 'e5ad83e8fd4f4486bba529a8d523b5e1', value: 'tester' })
    }
  }
  onKeydownMain(event) {

    if (event !== 'N' && event !== 'C' && event != '') {
      event = ''; // Clear the input field
    } else {
      let genesisFormfordate = (this.paramsdata.eligibilityRouteId == '42' || this.paramsdata.eligibilityRouteId == '47' || this.paramsdata.eligibilityRouteId == '22535') && Number(this.paramsdata.stateId) == StateLists.WA ? this.genesisForm.sections[2] : (this.paramsdata.eligibilityRouteId == '43' || this.paramsdata.eligibilityRouteId == '44' || this.paramsdata.eligibilityRouteId == '45' || this.paramsdata.eligibilityRouteId == '46' || this.paramsdata.eligibilityRouteId == '48' || this.paramsdata.eligibilityRouteId == '97' || this.paramsdata.eligibilityRouteId == '98') && Number(this.paramsdata.stateId) == StateLists.WA ? this.genesisForm.sections[1] : null
      if (genesisFormfordate != undefined && genesisFormfordate != null) {
        if (Number(this.paramsdata.stateId) == StateLists.WA) {
          genesisFormfordate.QuestionGroups[0].group.forEach((x) => {
            if (x.label === 'NAC Credential Number') {
              // Initially set the value of the input field and x.value
              x.value = "NC";
            }
          });
        }

      }// Keep the value as it is
    }
  }

  loadEntireForm() {

    try {
      this.formGroupArray = [];
      this.sectionsValidationsObservables = [];
      let listOfObservableApiCalls: Array<Observable<any>> = [];
      let listOfObservableApiCallsMetaData: observableApiCallsMetaData[] = [];
      if (this.genesisForm.fetchFromApi) this.fetchGenesisFormData();
      let Values = []
      this.genesisForm.sections.forEach((x) => {
        if (x.description.includes('TRAINING') || x.description.includes('Training')) {
          Values.push(x.description)
        }
      })
      this.genesisForm.sections = this.globalUserService.userDetails.value.roleId === 15 ? this.genesisForm.sections.filter((x) => Values.includes(x.description)) : this.genesisForm.sections
      for (let i = 0; i < this.genesisForm.sections.length; i++) {
        for (let j = 0; j < this.genesisForm.sections[i].QuestionGroupsMetaData.optionsFromApi.length; j++) {
          if (this.genesisForm.sections[i].QuestionGroupsMetaData.optionsFromApi.length > 0) {
            for (let optionsToFetch of this.genesisForm.sections[i].QuestionGroupsMetaData.optionsFromApi[j]) {
              var optionsToFetchClone: fetchOptionsFromApi = lodash.cloneDeep(optionsToFetch);
              this.setParamValues(optionsToFetchClone.requestDetails, this.element);
              if (optionsToFetch.fetchOptionsBasedOnResponseAnotherField) {
                this.lazyListOfObservableApiCallsMetaData.push({
                  sectionIndex: i,
                  questionGrpIndex: j,
                  optionsToFetch: optionsToFetchClone
                })
              }
              else {
                listOfObservableApiCalls.push(this.preprocessAction(optionsToFetchClone.requestDetails));
                listOfObservableApiCallsMetaData.push({
                  sectionIndex: i,
                  questionGrpIndex: j,
                  optionsToFetch: optionsToFetchClone
                });
              }
            }
          }
        }
        if (this.genesisForm.sections[i].QuestionGroupsMetaData.dateAttributes) {

          for (let j = 0; j < this.genesisForm.sections[i].QuestionGroupsMetaData.dateAttributes.length; j++) {
            // disable past and future dates here, disabling dates based on and api response is yet to be implemented
            var dateAttr = this.genesisForm.sections[i].QuestionGroupsMetaData.dateAttributes[j];
            let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
              this.genesisForm.sections[i].QuestionGroups);
            if (formControlModel) {

              let inputField = document.getElementById(dateAttr.feildID);
              formControlModel.disabled = this.getSectionDisabled(i);
              var minDate = new Date();
              var maxDate = new Date();
              var curDate = new Date();
              if (dateAttr.pastDisabled) {
                minDate.setDate((new Date).getDate() - dateAttr.pastOffset);
                formControlModel.min = minDate;
              }

              if (dateAttr.futureDisabled) {
                maxDate.setDate((new Date).getDate() + dateAttr.futureOffset);
                formControlModel.max = maxDate;
              }
              if (dateAttr.displayCurrentDate) {
                formControlModel.value = curDate;
              }
            }
            if (formControlModel == null) {
              let formArrayModel =
                this.formService.findModelById<DynamicFormArrayModel>(
                  this.genesisForm.sections[i].QuestionGroupsMetaData.formArrayModelIds[0]?.modelID,
                  this.genesisForm.sections[i].QuestionGroups);

              if (formArrayModel.groups.length > 0) {
                formArrayModel.groups.forEach(groups => {
                  let input = this.formService.findModelById<any>(dateAttr.feildID, groups.group);
                  if (dateAttr.displayCurrentDate) {
                    input.value = curDate;
                  }
                })
              }


            }

          }
        }


        // save draft when selected date is less than current date //
        let genesisFormfordate = this.genesisForm.sections[3] || this.genesisForm.sections[4] || this.genesisForm.sections[5] || this.genesisForm.sections[1] || this.genesisForm.sections[2]
        if (genesisFormfordate != undefined && genesisFormfordate != null) {

          // let b = genesisFormfordate.QuestionGroupsMetaData.dateAttributes
          if (genesisFormfordate.QuestionGroupsMetaData.dateAttributes) {

            for (let j = 0; j < genesisFormfordate.QuestionGroupsMetaData.dateAttributes.length; j++) {
              var dateAttr = genesisFormfordate.QuestionGroupsMetaData.dateAttributes[j];
              let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
                genesisFormfordate.QuestionGroups);
              if (formControlModel) {
                setTimeout(() => {
                  this.globalUserService.userstatus.subscribe(data => {
                    if (data) {
                      const Today = new Date()
                      if (formControlModel.type === "DATEPICKER" && formControlModel.value != '' && formControlModel.value != null && formControlModel.disabled != true && formControlModel.value != undefined && (data.statusId == 1 || data.statusId == 4 || data.status == "Drafted" || data.status == "Change Request")) {
                        formControlModel.value = Today

                      }
                    }
                  })
                }, 2000)


              }

            }
          }
          if (Number(this.paramsdata.stateId) == StateLists.WA && Number(this.paramsdata.formTypeId) == FormTypes.Application) {
            let genesisFormfordate = (this.paramsdata.eligibilityRouteId == '42' || this.paramsdata.eligibilityRouteId == '47' || this.paramsdata.eligibilityRouteId == '22535') && Number(this.paramsdata.stateId) == StateLists.WA ? this.genesisForm.sections[2] : (this.paramsdata.eligibilityRouteId == '43' || this.paramsdata.eligibilityRouteId == '44' || this.paramsdata.eligibilityRouteId == '45' || this.paramsdata.eligibilityRouteId == '46' || this.paramsdata.eligibilityRouteId == '48' || this.paramsdata.eligibilityRouteId == '97' || this.paramsdata.eligibilityRouteId == '98') && Number(this.paramsdata.stateId) == StateLists.WA ? this.genesisForm.sections[1] : null
            genesisFormfordate.QuestionGroups[0].group.forEach((x) => {
              if (x.label === 'NAC Credential Number') {
                let inputField = document.getElementsByTagName('input')[6];
                x.value = "NC";
                inputField.value = x.value;
                function formatInputValue() {
                  let inputValue = inputField.value;

                  // Ensure the input starts with "NC" and the rest are digits
                  if (inputValue.length > 2) {
                    let digits = inputValue.slice(2); // Slice the part after "NC"

                    // Remove any non-digit characters
                    digits = digits.replace(/[^0-9]/g, '');

                    // Limit the digits to a maximum of 8
                    inputValue = "NC" + digits.slice(0, 8);
                  } else {
                    // If the input is empty or less than 2 chars, show "NC" (it can't be deleted)
                    inputValue = "NC";
                  }
                  // Set the formatted value back to both the input field and x.value
                  inputField.value = inputValue;
                  x.value = inputValue; // Ensure x.value is updated as well
                }

                // Add input event listener to automatically format input
                inputField.addEventListener('input', formatInputValue);

                // Set the initial value of the input field
                inputField.value = "NC";
              }
            });
          }

        }

        if (StateAllow.includes(Number(this.paramsdata.stateId)) && FormTypesAllow.includes(Number(this.paramsdata.formTypeId))) {
          let genesisFormforReciRene = this.genesisForm.sections[2] || this.genesisForm.sections[1] || this.genesisForm.sections[3]

          if (genesisFormforReciRene != undefined && genesisFormforReciRene != null) {

            // let b = genesisFormfordate.QuestionGroupsMetaData.dateAttributes
            if (genesisFormforReciRene.QuestionGroupsMetaData.dateAttributes) {

              for (let j = 0; j < genesisFormforReciRene.QuestionGroupsMetaData.dateAttributes.length; j++) {
                var dateAttr = genesisFormforReciRene.QuestionGroupsMetaData.dateAttributes[j];
                let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
                  genesisFormforReciRene.QuestionGroups);
                if (formControlModel) {

                  setTimeout(() => {
                    this.globalUserService.userstatus.subscribe(data => {
                      if (data) {
                        if ((formControlModel.id == '4822ceacfa0341809b35d013a1b3061a' || formControlModel.id == "89043c6e944e424cbbef3008de136f16") && (formControlModel.value != '' && formControlModel.value != null && formControlModel.value != undefined) && (data.statusId == 1 || data.statusId == 4 || data.status == "Drafted" || data.status == "Change Request")) {
                          var datepipe = new DatePipe("en-us")
                          let SelectedDate = datepipe.transform((formControlModel.value), "MM/dd/YYYY", "+0000",)
                          let TodayDate = datepipe.transform((new Date()), "MM/dd/YYYY",)

                          if (SelectedDate !== TodayDate) {
                            formControlModel.value = null;
                          }
                          else {

                          }

                        }
                      }
                    })
                  }, 2000)


                }

              }
            }
          }
        }
        if (this.paramsdata.stateId == StateLists.WA || this.globalUserService.personEventId?.stateId == StateLists.WA || this.globalUserService.stateName == StateLists.WAState) {
          this.genesisForm.sections.forEach((x) => {
            if (x.description.replace(/\s+/g, '') == 'ACCOMMODATIONS') {
              x.description = "ONLINE WRITTEN/ORAL ACCOMMODATION"
            }
          })
        }

        setTimeout(() => {

          let genesisFormm = this.genesisForm.sections[1]
          if (genesisFormm != undefined && genesisFormm != null) {
            if (genesisFormm.QuestionGroupsMetaData.dateAttributes) {
              for (let j = 0; j < genesisFormm.QuestionGroupsMetaData.dateAttributes.length; j++) {
                var dateAttr = genesisFormm.QuestionGroupsMetaData.dateAttributes[j];
                let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
                  genesisFormm.QuestionGroups);
                if (formControlModel) {
                  if (GAdemographic.includes(formControlModel.id)) {
                    const today = new Date();
                    const years = Math.floor(this.AgeLimits); // Extract the whole years
                    const monthsDecimal = this.AgeLimits - years; // Extract the decimal part representing months
                    // Calculate months and days
                    const months = Math.floor(monthsDecimal * 12);
                    const days = Math.floor((monthsDecimal * 12 - months) * 30); // Assuming an average of 30 days per month
                    const maxDate = new Date(today.getFullYear() - years, today.getMonth() - months, today.getDate() - days);
                    formControlModel.max = maxDate
                  }
                }
                else {
                  return false
                }
              }
            }
          }
        }, 2000)






        // showing saved data which  we selected from From Date in accomodation /
        let genesisForm = this.genesisForm.sections[4] || this.genesisForm.sections[3] || this.genesisForm.sections[2] || this.genesisForm.sections[5]
        if (genesisForm != undefined && genesisForm != null) {

          let b = genesisForm.QuestionGroupsMetaData.dateAttributes
          if (genesisForm.QuestionGroupsMetaData.dateAttributes) {
            for (let j = 0; j < genesisForm.QuestionGroupsMetaData.dateAttributes.length; j++) {
              var dateAttr = genesisForm.QuestionGroupsMetaData.dateAttributes[j];
              let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
                genesisForm.QuestionGroups);
              if (formControlModel) {
                setTimeout(() => {
                  if (formControlModel.id === "62bda1d2e47e4aae86ede21ad31279d5") {
                    this.dates = formControlModel.value
                  }
                  if (this.dates != null) {
                    if (formControlModel.id == "6a7136e212b84160af9139eac5380005") {
                      var maxDate = new Date(this.dates)
                      var minDate = new Date(this.dates)
                      minDate.setDate((new Date(this.dates)).getDate() - 0)
                      maxDate.setDate((new Date(this.dates)).getDate() + 364)
                      formControlModel.max = maxDate
                      formControlModel.min = minDate
                    }
                  }

                }, 3500)
              }
              else {
                return false
              }
            }
          }
        }



        // disable date filed for online grievance or excused absence and not allowing the candidate to change date
        let genesisForms = this.genesisForm.sections[0]
        if (genesisForms != undefined && genesisForms != null) {
          if (genesisForms.QuestionGroupsMetaData.dateAttributes) {
            for (let j = 0; j < genesisForms.QuestionGroupsMetaData.dateAttributes.length; j++) {
              var dateAttr = genesisForms.QuestionGroupsMetaData.dateAttributes[j];
              let formControlModel = this.formService.findModelById<any>(dateAttr.feildID,
                genesisForms.QuestionGroups);
              if (formControlModel) {
                setTimeout(() => {
                  if (formControlModel && formControlModel.parent.id == "grievance__grievance_form_q2") {
                    if (formControlModel.id == "6f035c3f83544144b13f823eea2493f2" || formControlModel.id == 'fb99089e5ed74d95ad1748e0a4a14ace' || formControlModel.id == 'e434da614b854636916c296540468a23' || formControlModel.id == '0b8f8e0e823d45e6a0ed78f5883364e4' || formControlModel.id === '4129f089e5bf4b079b20398cfc690839') {
                      formControlModel.value = this.examDate
                      formControlModel.disabled = true

                    }

                  }
                  else if (formControlModel && formControlModel.parent.id == "excused_absence_request_form___q1s1") {
                    if (formControlModel.id == "86cc33e3e42f4e97b7a638eed7d15cc5") {
                      formControlModel.value = this.examDate
                      formControlModel.disabled = true
                    }
                  }


                }, 2000)



              }

            }
          }
          setTimeout(() => {
            genesisForms.QuestionGroups[1].group.forEach((x) => {
              if (x.id === '289e9a27b0b14a55894c84a8cd44d780' || x.id === 'e784b182428844bb84fefdbca57df21c' || x.id === 'd252c8418f414fed96e236fe9779c529' || x.id == '86cf190f61e849b4bfa3bc20a1fd969b' || x.id === '20e39b0e77594ca5af92c9268ec5ab77' || x.id === 'f481a11ac6fd4cf692c0c60fa3861412' || x.id == 'e784b182428844bb84fefdbca57df21c' || x.id == '250274aeca544ef4ac4f1ee407d3e3c7' || x.id === '3e085b17768741e89540f70ac931df5a' || x.id == '0b444401167948018d99677b500df1cb' || x.id === '670c4a16a404487c92da347058dee68e' || x.id === "0bf8a4790573447b9c046863975503a2" || x.id === '696d8b0c796e43078d79467e7ac98cab' || x.id === '4419513d668b4a76ac8f13221e58c696' || x.id === '7d8b16e179394cd9aee5e5e1a1f58f8f') {
                x.value = (x.id === '289e9a27b0b14a55894c84a8cd44d780' || x.id === '3e085b17768741e89540f70ac931df5a') ? this.ExamDetails.answerSheetSerialNumber : (x.id === '250274aeca544ef4ac4f1ee407d3e3c7' || x.id === '20e39b0e77594ca5af92c9268ec5ab77' || x.id == 'e784b182428844bb84fefdbca57df21c' || x.id === '0b444401167948018d99677b500df1cb') ? this.ExamDetails.trainingProgramName : (x.id === 'd252c8418f414fed96e236fe9779c529' || x.id === '86cf190f61e849b4bfa3bc20a1fd969b' || x.id == 'f481a11ac6fd4cf692c0c60fa3861412' || x.id === '670c4a16a404487c92da347058dee68e') ? this.ExamDetails.facilityName : (x.id === '0bf8a4790573447b9c046863975503a2' || x.id === '4419513d668b4a76ac8f13221e58c696' || x.id === '696d8b0c796e43078d79467e7ac98cab' || x.id === '7d8b16e179394cd9aee5e5e1a1f58f8f') ? this.ExamDetails.evaluatorName : ''
                x.disabled = true
                let text = document.querySelectorAll('label')
                let input = document.querySelectorAll('mat-form-field')
                text.forEach((x) => {
                  if (x.innerText === 'Name of the Training Program you attended' && (this.ExamDetails.trainingProgramName == '' || this.ExamDetails.trainingProgramName == null || this.ExamDetails.trainingProgramName == undefined)) {
                    return x.style.display = 'none'
                  }
                })
                if ((this.ExamDetails.trainingProgramName == '' || this.ExamDetails.trainingProgramName == null || this.ExamDetails.trainingProgramName == undefined)) {
                  input[7].classList.add('trainingValueHide')
                  input[13].classList.add('trainingValueHide')
                  input[19].classList.add('trainingValueHide')
                  input[28].classList.add('trainingValueHide')
                }




              }


            })
          }, 2000)


        }

        setTimeout(() => {
          this.genesisForm.sections[0].QuestionGroups[1].group.forEach((x) => {
            if (x.id === '0d13928eef624576af3208d204e71200' && x.label === 'Answer') {
              x.validators.pattern = '^[a-zA-Z0-9!\"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~\\w\\s-]+$';
              this.formService.detectChanges();
            }
            // if (x.value == "Evaluator" || x.value == "online" || x.value == "Skills") {
            //   this.globalUserService.GrievamceEvaluator.next({ model: { value: x.value } })
            //   this.globalUserService.Grievanceroute = x.value
            // }
            // if (x.id === "05a32eba96634d51815d2babc8053014" && x.value != null) {
            //   this.globalUserService.GrievamceEvaluatorresponse.next({ value: x.value })
            // }

          })
        }, 2000)

        setTimeout(() => {
          const AddButton: any = document.getElementsByClassName("hover:bg-gray-100")
          for (let i = 0; i < AddButton.length; i++) {
            AddButton[i].style.display = "none"

          }
        }, 2400)
        setTimeout(() => {
          const AddButton: any = document.getElementsByClassName("hover:bg-gray-100")
          for (let i = 0; i < AddButton.length; i++) {
            if (!Healthcare_Co_s3.includes(AddButton[i].__ngContext__[8].id) && this.element.eligibilityRouteId != StateLists.CO_ER7 && this.element.eligibilityRouteId != StateLists.CO_ER8) {
              AddButton[i].style.display = "block"
            } else if (!HealthCare_CO_S3_E7.includes(AddButton[i].__ngContext__[8].id) && this.element.eligibilityRouteId == StateLists.CO_ER7) {
              AddButton[i].style.display = "block"
            } else if (!HealthCare_CO_S3_E8.includes(AddButton[i].__ngContext__[8].id) && this.element.eligibilityRouteId == StateLists.CO_ER8) {
              AddButton[i].style.display = "block"
            }
          }
        }, 4000)


        setTimeout(() => {
          if (this.element?.StateId == 4 || this.element?.stateId == 4)
            this.genesisForm.sections[1].QuestionGroups[0].group.forEach((data) => {
              if (PARenewal.includes(data.id)) {
                data.disabled = true
                if (data.value == null && (data.id == "985a249fec204cf197fd75f71a01818c" || data.id == "9b9f04ade1014e0aa7f972177bfc94e1")) {
                  data.disabled = true
                  data.value = this.element?.CertNumber ? this.element.CertNumber : this.element?.registryDetail?.certificateNumber


                } else if (data.value == null && (data.id != "985a249fec204cf197fd75f71a01818c" || data.id != "9b9f04ade1014e0aa7f972177bfc94e1")) {
                  data.disabled = true
                  data.value = this.element?.ExpirationDate ? this.element.ExpirationDate : this.element?.registryDetail?.expirationDate

                }

              }
            })
          this.genesisForm.sections[0].QuestionGroups[0].group.forEach((data) => {
            if (data.value == null && (data.id == "a7042189c88549ff911bef9e59decd9a")) {
              data.value = "DHS 99"
              data.disabled = true
            }
          })

        }, 1500)
        /////// DC RENEWAL changes made only label in section 4 ///////
        setTimeout(() => {
          if ((Number(this.paramsdata.stateId) === StateLists.DC || this.globalUserService.personEventId?.stateId == StateLists.DC) && (this.paramsdata.formTypeId == FormTypes.CertificateRenewal || this.formID == FormTypes.CertificateRenewal)) {
            this.genesisForm.sections[3].QuestionGroups[0].group.forEach((data) => {
              if (data.id === "44242c7f470c46d088103671a08052f8") {

                data.label = '<div>2.Once you make a payment: You will receive an email receipt with a Fieldprint Code (Fieldprint Code will be <span><b>FPDCCNA</b></span>). The Fieldprint Code will also appear on your payment confirmation page. You will be redirected to the Fieldprint scheduling website</div>'
                this.formService.detectChanges();
              } else if (data.id === "f1777bd1d3f44d028de5c6fae36614c9") {
                data.label = '<div>7. When your fingerprint scan is completed notify <a href="mailto:<EMAIL>" target="_blank"> <b><EMAIL></b></div>'
                this.formService.detectChanges();
              }
            })
          }
        }, 1000)
        ///////////////DC RENEWAL changes made end //////////

        /////// VA Application E1 and E4  changes  ///////
        setTimeout(() => {
          if ((Number(this.paramsdata?.stateId) === StateLists.VA || this.globalUserService?.personEventId?.stateId == StateLists.VA) && (this.paramsdata?.formTypeId == FormTypes.Application || this.formID == FormTypes.Application)) {
            let Values: any = document.querySelectorAll('a')
            Values.forEach((x) => {
              if (x.host === "www.dhp.virginia.gov") {
                x.classList.add('Guidance')
              }
            })


          }
        })
        /////// VA Application E1 and E4  changes  end ///////

        /////// SC Reciporating changes made only label in section 4 ///////
        setTimeout(() => {
          if ((Number(this.paramsdata.stateId) === StateLists.SC || this.globalUserService.personEventId?.stateId == StateLists.SC) && (this.paramsdata.formTypeId == FormTypes.CertificateReciprocity || this.formID == FormTypes.CertificateReciprocity)) {
            this.genesisForm.sections[1].QuestionGroups[0].group.forEach((data) => {
              switch (data.id) {
                case SCRECIPORATING.SCRECSect1:
                  data.hidden = true
                  break;
                case SCRECIPORATING.SCRECSect2:
                  data.label = SCRECIVALUES.SCRECVALUESSect1
                  break;
                case SCRECIPORATING.SCRECSect3:
                  data.label = SCRECIVALUES.SCRECVALUESSect2
                  break;
                case SCRECIPORATING.SCRECSect4:
                  data.label = SCRECIVALUES.SCRECVALUESSect3
                  break;
                case SCRECIPORATING.SCRECSect5:
                  let Values: any = document.querySelectorAll('.mat-checkbox-layout')
                  data.label = SCRECIVALUES.SCRECVALUESSect4
                  Values[4].style.display = 'flow'
                  Values[4].children[1].children[1].style.display = "table-cell"
                  Values[4].children[1].children[1].children[0].classList.add('SCRECI')

              }
              this.formService.detectChanges();
            })
          }
        }, 1000)
        ///////////////SC Reciporating changes made end //////////
        setTimeout(() => {
          let role = this.globalUserService.userDetails.getValue().roleId ? this.globalUserService.userDetails.getValue().roleId : this.globalUserService.userDetails.getValue().roles
          if (this.element.stateId == 4 && role[0].roleId != Role.OperationStaff && (this.paramsdata.certStatusId == "1" || this.paramsdata.certStatusId == "4") && (this.paramsdata.formTypeId == FormTypes.CertificateRenewal || this.formID == FormTypes.CertificateRenewal)) {
            this.genesisForm.sections[2].QuestionGroups[0].group.forEach((data) => {
              var a: any = document.getElementsByClassName('mat-checkbox-input')
              if (a[1].ariaChecked) {
                data.disabled = data.label == 'Date Last Worked' && a[1].ariaChecked == 'true' ? true : false
              }
            })
          }

          else if (this.paramsdata.stateId == "4" && this.paramsdata.formTypeId == (FormTypes.CertificateRenewal).toString() && this.globalUserService.personEventId.status == "Drafted") {
            setTimeout(() => {
              this.genesisForm.sections[2].QuestionGroups[0].group.forEach((data) => {
                var a: any = document.getElementsByClassName('mat-checkbox-input')
                if (a[1].ariaChecked) {
                  data.disabled = data.label == 'Date Last Worked' && a[1].ariaChecked == 'true' ? true : false
                }
              })

            }, 1500)
          }

        }, 2500)

        // disabled remaining online exams when u select one Online exams not consideration of  Skills Exams from accomodation form Examtype///
        setTimeout(() => {
          let geneformsExamType = this.genesisForm.sections[4] || this.genesisForm.sections[3] || this.genesisForm.sections[2] || this.genesisForm.sections[5]
          geneformsExamType.QuestionGroups[0].group.forEach((data: any) => {
            if (data.label == "ExamType") {
              let ExamsName = [
                "English Oral (audio recording)", "Written (paper or computer)"]
              data.options = (Number(this.paramsdata?.stateId) === StateLists.IA || this.globalUserService?.personEventId?.stateId == StateLists.IA) ? data.options.filter((x) => ExamsName.includes(x.label)) : data.options
              let ExamTypesValue = data.options.filter(x => data.value.find(y => y == x.value))
              let OnlyOnlineExams = ExamTypesValue.filter(x => (x.label != (ExamTypesForHardcoding.NA_SES || ExamTypesForHardcoding.NA_SE)))
              if (OnlyOnlineExams.length > 0) {
                switch (OnlyOnlineExams[0].label) {
                  case ExamTypesForHardcoding.NA_WE:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_OEE || element.label == ExamTypesForHardcoding.NA_OSE) {
                        return element.disabled = true
                      }
                    });
                    break;
                  case ExamTypesForHardcoding.NA_OEE:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_WE || element.label == ExamTypesForHardcoding.NA_OSE) {
                        return element.disabled = true
                      }
                    });
                    break;
                  case ExamTypesForHardcoding.NA_OSE:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_WE || element.label == ExamTypesForHardcoding.NA_OEE) {
                        return element.disabled = true
                      }
                    });
                    break
                  case ExamTypesForHardcoding.NA_WEP:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_OEEA || element.label == ExamTypesForHardcoding.NA_OSEA) {
                        return element.disabled = true
                      }
                    });
                    break
                  case ExamTypesForHardcoding.NA_WEP_MA:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_OEEA || element.label == ExamTypesForHardcoding.NA_OSEA || element.label == ExamTypesForHardcoding.NA_WEP_MAE) {
                        return element.disabled = true
                      }
                    });
                    break
                  case ExamTypesForHardcoding.NA_OEEA:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_WEP || element.label == ExamTypesForHardcoding.NA_OSEA || element.label == ExamTypesForHardcoding.NA_WEP_MA) {
                        return element.disabled = true
                      }
                    });
                    break
                  case ExamTypesForHardcoding.NA_OSEA:
                    data.options.forEach(element => {
                      if (element.label == ExamTypesForHardcoding.NA_WEP || element.label == ExamTypesForHardcoding.NA_OEEA) {
                        return element.disabled = true
                      }
                    });
                    break
                }
              } else {
                data.options.forEach(element => {
                  return element.disabled = false
                });
              }
            }
          })
        }, 3500)





        setTimeout(() => {
          if (this.paramsdata.formTypeId == "5" && this.paramsdata.stateId == "4") {

            let genes = this.genesisForm.sections[2]



            genes.QuestionGroups[0].group.filter((x) => {

              if (x.id == 'e75e0b3101a04273a2bd0f4a374f93a6' && x.value == 'yes') {
                this.service.HideEmployerValues.next({ model: { value: x.value } })
                if (this.paramsdata.certStatusId == "4") {
                  this.service.SearchValue = x.value
                }


              } else if (x.id == '4133c51e390240fb90c3a6da72df27fb' && x.label == 'Other Employer Name' && (x.value != null && x.value != '' && x.value != undefined)) {
                this.service.SearchValue = x.value
              }

            })

          }

        }, 1500)


        setTimeout(() => {

          let geneformsExamType = this.genesisForm.sections[4] || this.genesisForm.sections[3] || this.genesisForm.sections[2] || this.genesisForm.sections[5] || this.genesisForm.sections[0]

          let genesForm = geneformsExamType.QuestionGroups[0]?.group != undefined ? geneformsExamType.QuestionGroups[0].group : geneformsExamType.QuestionGroups

          genesForm.filter((x) => {

            if (x.id == "f6f95c9626874226b1814854d384a3d3" || x.id == "8a51a583788041279a00c4fa6bd1e72f" || x.id == "015c6d5bc1674688b0aa8ad99479d185" || x.id == "d0c2ab0c47c34161a6c9ebbd25ef699c") {
              const nodeList: any = document.querySelectorAll(".mat-select-value");
              for (let i = 0; i < nodeList.length; i++) {
                nodeList[i].classList.add('Overflow-x')
              }



            }

          })

        }, 2000)

        setTimeout(() => {
          let Matradiogroupbuttonhideandshow: any = document.getElementsByClassName('mat-radio-button');

          for (let i = 0; i < Matradiogroupbuttonhideandshow.length; i++) {
            let x = Matradiogroupbuttonhideandshow[i];
            if (x.innerText !== "Re-evaluate") {
              x.style.opacity = '1';  // Keep the "Re-evalute" visible
              x.disabled = false;     // Enable "Re-evalute"
            } else {
              x.style.opacity = '0.5'; // Optionally, dim the other buttons
              x.disabled = true;
              x.style.pointerEvents = 'none'
              // Disable other buttons
            }
          }

          // Matradiogroupbuttonhideandshow[2].children[4].disabled = true;
          // this.paramsdata.mode === 'Online' ? Matradiogroupbuttonhideandshow[2].children[4].classList.add('hideradiobutton') : Matradiogroupbuttonhideandshow[2].children[4].classList.add('showradiobutton')
        }, 500)




        this.genesisForm.sections[i].QuestionGroups.forEach((value: DynamicFormGroupModel & DynamicFormArrayModel, qgpIndex: number) => {
          value.type == "ARRAY"
            ?
            value.groupPrototype.forEach((control: DynamicFormControlModel, index: number) => {
              if (control.type == 'CHECKBOX' && (control as DynamicCheckboxModel).required) {
                (this.genesisForm.sections[i].QuestionGroups[qgpIndex] as DynamicFormArrayModel).groupPrototype[index] = new DynamicCheckboxModel({
                  ...control,
                  validators: {
                    ...control.validators,
                    customCheckboxRequiredValidator
                  }
                })
              }
            })
            :
            value.group.forEach((control: DynamicFormControlModel, index: number) => {
              if (control.type == 'CHECKBOX' && (control as DynamicCheckboxModel).required) {
                (this.genesisForm.sections[i].QuestionGroups[qgpIndex] as DynamicFormGroupModel).group[index] = new DynamicCheckboxModel({
                  ...control,
                  validators: {
                    ...control.validators,
                    customCheckboxRequiredValidator
                  }
                })
              }
            })
        })
      }
      if (listOfObservableApiCalls.length > 0) {
        this.apiCallsSub = forkJoin(listOfObservableApiCalls).subscribe((responses: Array<any>) => {

          for (let i = 0; i < responses.length; i++) {
            // (responses[i] as Array<any>).sort(this.comparator('label'));
            let formControlModel = this.formService.findModelById<any>(
              listOfObservableApiCallsMetaData[i].optionsToFetch.feildID,
              this.genesisForm.sections[listOfObservableApiCallsMetaData[i].sectionIndex].QuestionGroups);
            if (formControlModel)
              formControlModel.options = responses[i];

            if (formControlModel.label == "ExamType" && SpanishRemovingExam.includes(Number(this.paramsdata.stateId))) {
              setTimeout(() => {
                let Values = formControlModel.options.filter((x) => x.label != ExamTypesForHardcoding.NA_OSEA)
                formControlModel.options = Values
              }, 2000)
            }
            else if (formControlModel.label == "Please select state" && MACESC.includes(Number(this.paramsdata.stateId)) && FormTypeID.includes(Number(this.paramsdata.formTypeId))) {
              let StateAllowtocertificate = ["AL", "NY", "UT", "VT", "WA", "WV", "WY", "NC", 'GA']
              setTimeout(() => {
                let Values = responses[i].filter((x => StateAllowtocertificate.includes(x.code)))
                let StateAllowedForMASCEReciporate = formControlModel.options.filter((x) => Values.find((y) => x.label == y.label))
                formControlModel.options = StateAllowedForMASCEReciporate
              }, 500)

            }

          }
          this.detectChangesForIndividualFeilds();
        })
      }


      for (let section of this.genesisForm.sections) {
        this.formGroupArray.push(this.formService.createFormGroup(section.QuestionGroups));
        this.sectionsValidationsObservables.push(this.formGroupArray[this.formGroupArray.length - 1].statusChanges);
      }


      this.formLayout = this.genesisForm?.formLayout;
      if (this.data) this.populateData(this.data);
      this.emitInitialSectionsCompletionEvent();
      //this.initializeSubmitButtons();
    }
    catch (error) {
      console.error(error);
    }
  }
  fetchGenesisFormData() {

    if (this.genesisForm.fetchFromApi && this.genesisForm.requestDetails) {
      var requestDetailsClone = lodash.cloneDeep(this.genesisForm.requestDetails);
      this.setParamValues(requestDetailsClone, this.element);
      this.preprocessAction(requestDetailsClone).subscribe((data: any) => {
        this.examDate = data.examDate
        this.ExamDetails = data
        this.ClientStateCode = data.clientStateCode
        if (data) this.element = { ...this.element, ...data };
        this.prefillData();
      }, (error: any) => {
        // this sample data has been put for demo purposes
        this.element = {
          ...this.element,
          answerSheetSerialNumber: '177676871',
          examDate: new Date('Thu Jan 27 2022 00:00:00 GMT+0530'),
          facilityName: 'Cal Tech',
          trainingProgramName: 'Training Center',
        }
        this.prefillData();
      })
    }
  }
  public preprocessAction(requestDetails: requestDetails): Observable<any> {

    if (requestDetails) {
      var fetchUrlArray = lodash.cloneDeep(requestDetails.fetchUrl);
      requestDetails?.requestParams?.forEach((x: requestParam, index: number) => {
        if (x.paramType == paramTypes.Required)
          fetchUrlArray.splice(x.position, 0, x.paramValue.toString());
      })
      var fetchUrl: string = environment.baseUrl + fetchUrlArray.join('/');
      var filteredOptionalParams = requestDetails?.requestParams?.filter((x: requestParam) => { return x.paramType == paramTypes.Optional });
      if (JSON.stringify(requestDetails.fetchUrl) == JSON.stringify(['candidate', 'api', 'TrainingInstitute', 'traininginstitute'])) {
        var filteredOptionalParam = requestDetails?.requestParams?.filter((x: requestParam) => { return x.paramType == paramTypes.Optional });
        filteredOptionalParam.forEach((x) => {
          let PersonTenantRoleId = this.globalUserService.userDetails.getValue().roleId == Role.Candidate ? Number(this.element.candidateId) : (Number(this.paramsdata.candidateId) || this.globalUserService.personEventId.personTenantRoleId)
          fetchUrl = filteredOptionalParam.length > 0 && this.globalUserService.userDetails.getValue().roleId == Role.OperationStaff ? `${environment.baseUrl}candidate/api/TrainingInstitute/traininginstitute/operationstaff?eligibilityId=${x.paramValue != '' ? x.paramValue : this.element.eligibilityRouteId ? this.element.eligibilityRouteId : this.globalUserService.personEventId.eligibilityRouteId}&personTenantroleId=${PersonTenantRoleId} ` : `${environment.baseUrl}candidate/api/TrainingInstitute/traininginstitute?eligibilityId=${x.paramValue != '' ? x.paramValue : this.element.eligibilityRouteId}&personTenantroleId=${PersonTenantRoleId} `
        })
      }
      else if (JSON.stringify(requestDetails.fetchUrl) == JSON.stringify(['formmsvc', 'api', 'form', 'accommodationitems', 'examTypeIds'])) {
        var filteredOptionalParams1 = requestDetails?.requestParams?.filter((x: requestParam) => { return x.paramType == paramTypes.Required });
        filteredOptionalParams1?.forEach((x: requestParam, index: number) => {
          let DetailsValues = []
          DetailsValues.push(x.paramValue)
          DetailsValues.forEach(x => {
            fetchUrl = x.length == 2 && x != null && x != undefined ? `${environment.baseUrl}formmsvc/api/form/accommodationitems/examTypeIds?examTypeIds=${x[0]}&examTypeIds=${x[1] != null && x[1] != undefined ? x[1] : null}` : x.length != 0 ? `${environment.baseUrl}formmsvc/api/form/accommodationitems/examTypeIds?examTypeIds=${x[0]}` : null
          })
        })
      }
      else if (JSON.stringify(requestDetails.fetchUrl) == JSON.stringify(['candidate', 'api', 'account', 'get']) && (this.service.RemoveGetuserDataafterpopupopen != null && this.service.RemoveGetuserDataafterpopupopen != '' && this.service.RemoveGetuserDataafterpopupopen != undefined && this.globalUserService.roleId === Role.Candidate)) {
        var filteredOptionalParam = requestDetails?.requestParams?.filter((x: requestParam) => { return x.paramType == paramTypes.Optional });
        filteredOptionalParam?.forEach((x: requestParam, index: number) => {
          fetchUrl = `${environment.baseUrl}candidate/api/exam/cartitems?personTenantRoleId=${x.paramValue}`
          this.service.RemoveGetuserDataafterpopupopen = ''
        })
      }
      else {
        filteredOptionalParams?.forEach((x: requestParam, index: number) => {
          fetchUrl += (index == 0 ? '?' : '&') + x.paramName + "=" + x.paramValue?.toString();
        })

      }
      var returnObs: Observable<any>;
      switch (requestDetails.method) {

        case 'GET': returnObs = this.http.get(fetchUrl);
          break;
        case 'POST': returnObs = this.http.post(fetchUrl, requestDetails.requestBody);
          break;
        case 'PUT': returnObs = this.http.put(fetchUrl, requestDetails.requestBody);
          break;
        case 'DELETE': returnObs = this.http.delete(fetchUrl);
          break;
        default: returnObs = this.http.get(fetchUrl);
      }
      return returnObs;
    }
  }

  comparator(prop) {
    function getFirstLetter(str: string) {
      let temp = str.split('-');
      if (temp.length == 1) return temp[0].toLowerCase();
      else if (temp.length > 1) return temp[1].toLowerCase();
      else return str.toLowerCase();
    }
    return function (a, b) {
      if (getFirstLetter(a[prop]) > getFirstLetter(b[prop])) {
        return 1;
      } else if (getFirstLetter(a[prop]) < getFirstLetter(b[prop])) {
        return -1;
      }
      return 0;
    }
  }
  getSectionDisabled(sectionIndex: number) {
    if (typeof this.disabled == 'boolean')
      return this.disabled;
    return this.disabled[sectionIndex];
  }

  public setParamValues(xArr: requestDetails, element: any) {

    xArr?.requestParams?.forEach((x: requestParam) => {
      if (x.extractedFromElement) {
        let role = this.globalUserService.userDetails.getValue().roleId ? this.globalUserService.userDetails.getValue().roleId : this.globalUserService.userDetails.getValue().role
        if (element[x.elementPropertyToBeExtracted] == undefined && (role == Role.OperationStaff || role == Role.QAC) && (this.formID != 2) && (this.service.editFormValue != 2)) {
          x.paramValue = this.globalUserService.personEventId[x.elementPropertyToBeExtracted] != undefined && this.globalUserService.personEventId.status != "Drafted" ? this.globalUserService.personEventId[x.elementPropertyToBeExtracted] : this.globalUserService.personEventId.personTenantRoleId

        } else if (element[x.elementPropertyToBeExtracted] == undefined && (role == Role.OperationStaff || role == Role.QAC) && (this.formID == 2)) {
          x.paramValue = this.globalUserService.personEventId[x.elementPropertyToBeExtracted]
        }
        else {
          x.paramValue = element[x.elementPropertyToBeExtracted]

        }



      }

    })

  }

  private enableFeildOnMutliSelect($event) {
    this.cdr.markForCheck();
    this.cdr.detectChanges();
    for (let i = 0; i <= this.genesisForm.sections.length; i++) {
      for (let j = 0; j <= this.genesisForm.sections[i]?.QuestionGroups.length; j++) {
        for (let z = 0; z <= this.genesisForm.sections[i]?.QuestionGroups[j]?.group?.length; z++) {
          for (let value = 0; value <= this.genesisForm.sections[i]?.QuestionGroups[j]?.group[z]?.relations.length; value++) {
            if (this.genesisForm.sections[i]?.QuestionGroups[j]?.group[z]?.relations[value]?.when[0]?.id == $event.model.id && $event.$event?.value?.find(x => x == this.genesisForm.sections[i]?.QuestionGroups[j]?.group[z]?.relations[value]?.when[0]?.value)) {
              this.genesisForm.sections[i].QuestionGroups[j].group[z].hidden = false;
              setTimeout(() => {
                this.cdr.detectChanges();
              }, 2000)
            }
          }
        }
      }
    }
    this.formService.detectChanges();
  }

  getAgeRestrictedDate() {

    let ClientStateCode = this.globalUserService.userDetails.getValue().roleId == Role.OperationStaff ? this.paramsdata.stateCode : this.globalUserService.userDetails.getValue().stateCode
    Number(this.paramsdata.formTypeId) === FormTypes.Demographic ? this.service.getAgeLimitsrestriction(ClientStateCode).subscribe((data: Array<{ ageLimit: number }>) => {
      if (data.length > 0) {
        this.AgeLimits = data[0].ageLimit
      } else {

      }
    }) : null




  }

  ngOnDestroy() {
    this.apiCallsSub?.unsubscribe();
    this.sectionsValidationSubscribers.forEach((sub: Subscription) => { sub?.unsubscribe });

  }
}

export function getObject(theObject: any, key: string, setValueToNull: boolean) {
  var result = null;
  if (theObject instanceof Array) {
    for (var i = 0; i < theObject.length; i++) {
      result = getObject(theObject[i], key, setValueToNull);
      if (result)
        break;
    }
  }
  else {
    for (var prop in theObject) {
      if (prop == key) {
        result = Array.isArray(theObject[prop]) ? theObject[prop][0] : theObject[prop];
        if (setValueToNull)
          theObject[prop] = null;
        break;
      }
      else if (theObject[prop] instanceof Object || theObject[prop] instanceof Array) {
        result = getObject(theObject[prop], key, setValueToNull);
        if (result) break;
      }
    }
  }
  return result;
}
enum ExamTypesForHardcoding {
  NA_WE = "Written",
  NA_SE = "Skill",
  NA_OSE = "Spanish Oral",
  NA_OEE = "English Oral",
  NA_WEP = 'Written (paper or computer)',
  NA_WEP_MA = 'Written (paper or computer',
  NA_WEP_MAE = 'English Oral (audio recording',
  NA_OEEA = "English Oral (audio recording)",
  NA_OSEA = 'Spanish Oral (audio recording)',
  NA_SES = 'Skills'
}