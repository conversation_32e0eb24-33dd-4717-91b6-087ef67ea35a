.bg-pattern {
    background: var(--page_background);
    height: 100%;
    overflow: auto;
  }
   ::ng-deep .mat-input-element:disabled, .mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
    color: #1a202e !important;
  }
  ::ng-deep .mat-checkbox-checkmark {
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    /* position: absolute; */;
  }
  
  // ::ng-deep svg {
  //   width: 13px !important;
  // }
.text-center{
  color: #0076c1;
}
 ::ng-deep .mat-input-element:disabled, .mat-form-field-type-mat-native-select.mat-form-field-disabled .mat-form-field-infix::after {
  color: #1a202e !important;
}
::ng-deep .mat-checkbox-checkmark {
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  /* position: absolute; */;
}

::ng-deep svg {
  width: 13px !important;
}
.text-center{
color: #0076c1;
}
.addNewNote {
  height:39vh;
  overflow: auto;

}

::ng-deep .buttonFixes{
place-content: space-around
}

// ::ng-deep .error-snackbar{
//   height: 74px !important;
//   margin-top: 33em !important;
//   margin-left: 8em !important;
//   animation: fadeOut 5s forwards;
// }

.custom-class{
display: block !important ;
}

::ng-deep .cdk-overlay-pane{
  width: auto;
}

::ng-deep svg {
  width: 48px !important;
}