import { HttpBackend, HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { MatSnackBar, MatSnackBarHorizontalPosition } from '@angular/material/snack-bar';
import { SnackbarComponent } from './common-component/snackbar/snackbar.component';
import { GlobalUserService } from './global-user.service';
import { VocherUpdate } from '../client/schedule/state/models/VocherList';
import { AnyFn } from '@ngrx/store/src/selector';
import { BehaviorSubject } from 'rxjs';

interface refundPaymentPayload {
  transAmount: number;
  transId?: string;
  personTenantRoleId: number;
  description: string;
  capturedId?:string;
  orderId?:string
}


@Injectable({
  providedIn: 'root'
})

export class HttpService {
  emailIdforFrgtPwd: any;
  RoleId?: number;
  TenantCode?: any;
  StateId?: number;
  horizontalPosition: MatSnackBarHorizontalPosition = 'end';
  roleID: number = 1;
  id: number = 0;
  createdBy: number = 6;
  tenantId: number = 3;
  personEventIDSubject$: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  private candidateIdSystemFileNameSubject = new BehaviorSubject<string>('');
  private candidateFaceSystemFileNameSubject = new BehaviorSubject<string>('');

  candidateIdSystemFileName$ = this.candidateIdSystemFileNameSubject.asObservable();
  candidateFaceSystemFileName$ = this.candidateFaceSystemFileNameSubject.asObservable();
  constructor(private http: HttpClient, private https: HttpBackend, private _snackBar: MatSnackBar, private global:GlobalUserService) { }

  updateCandidateIdSystemFileName(fileName: string): void {
    this.candidateIdSystemFileNameSubject.next(fileName);
  }

  updateCandidateFaceSystemFileName(fileName: string): void {
    this.candidateFaceSystemFileNameSubject.next(fileName);
  }
  register(userDetails: any) {
    var url = `${environment.baseUrl}client/api/Account/adduser`;
    return this.http.post(url, userDetails);
  }
  Addstate(userDetails:any){
    var url = `${environment.baseUrl}client/api/state`;
    return this.http.post(url, userDetails);
  }
  UpdateState(userDetails:any){
    var url=`${environment.baseUrl}client/api/state`;
    return this.http.put(url, userDetails)
  }
  checkStatus(userDetails: any,personId:any) {
    var url = `${environment.baseUrl}client/api/Account/check-user-status?personId=${personId}`;
    return this.http.post(url, userDetails);
  }
  ticketcategories(tenantStateId){
    return this.http.get(`${environment.baseUrl}customerservice/api/tickets/categories?tenantStateId=${tenantStateId}`)
  }
 
  confirmuser(userDetails: any) {
    var url = `${environment.baseUrl}client/api/Account/confirm-account?personId=${this.global.personId}`;
    return this.http.post(url, userDetails);
  }

 
  update(userDetails: any) {
    var url = `${environment.baseUrl}client/api/Account/updateuser`;
    return this.http.put(url, userDetails);
  }
  manageUserUpdate(personrole:any ,userDetails:any){
    var url = `${environment.baseUrl}client/api/Account/updateuser?personTenantRoleId=${personrole}`;
    return this.http.put(url, userDetails);
  }

  getStates() {
    var url = `${environment.baseUrl}client/api/state/3`;
    return this.http.get(url);
  }

  openSnackBar(message: string, panelClass: string) {
    this._snackBar.openFromComponent(SnackbarComponent, {
      data: message,
      panelClass: panelClass,
      // horizontalPosition: this.horizontalPosition,
      duration: 4000
    });
  }
 
  loginReport(details:any){
    return this.http.post(`${environment.reportUrlapi}api/pinboardlist`,details)
  }

  manageUserRoleUpdate(emailId: string, requestBody: any) {
    var url = `${environment.baseUrl}client/api/Account/add-manage-role/${emailId}`;
    return this.http.patch(url, requestBody);
  }

  searchTrainingInstitutes(stateId: number, searchText: string) {
    var url = `${environment.baseUrl}client/api/traininginstitute/search-trainingprogram?stateId=${stateId}&searchText=${searchText}`;
    return this.http.get<any[]>(url);
  }

  loginReportTrustedToken(details:any){
    return this.http.post(`${environment.reportUrlapi}api/trutsedtoken`,details)
  }
  
  getUser(personTenantRoleId){
    return this.http.get(`${environment.baseUrl}client/api/Account/getusers?personTenantRoleId=${personTenantRoleId}`)
  }

  getclear(body){
    return this.http.post(`${environment.baseUrl}client/api/operationstaff/cart/clear?personId=${this.global.personId}`,body)
    
  }

  get_retry_schedule(ScheduleDetails){
    return this.http.post(`${environment.baseUrl}candidate/api/Exam/retry-schedule`,ScheduleDetails)
  }

  mergeRequest(reqBody:any){
    return this.http.post(`${environment.baseUrl}client/api/Account/Merge-user`, reqBody);
  }
  
  conformuser(userDetails: any, personId:number) {
    var url = `${environment.baseUrl}client/api/Account/check-user-status?personId=${personId}`;
    return this.http.post(url, userDetails);
  }
  getSchedule(personTentantRole:number,cartId:any,ScheduleDetails){
    var url = `${environment.baseUrl}candidate/api/Exam/makepaymentwithzero?personTenantRoleId=${personTentantRole}&cartId=${cartId}`;
    return this.http.post(url, ScheduleDetails);
  }
  getVoucherUpdate(VoucherUpdate:VocherUpdate){
    var url = `${environment.baseUrl}sponsor/api/Candidate/update-used-vouchers`;
    return this.http.post(url, VoucherUpdate);
  }
  createform(formdetails){
  var url=`${environment.baseUrl}client/api/form`;
  return this.http.post(url,formdetails)
}

updateForm(formdetails){
  var url =`${environment.baseUrl}client/api/form`;
  return this.http.put(url,formdetails)
}



getFormDetails(formId:number,clientId:number,stateId:number,eligibilityId:number){
var url = `${environment.baseUrl}client/api/form/formsbyformtypeid?formTypeId=${formId}&clientId=${clientId}&stateId=${stateId}&eligibilityId=${eligibilityId}`;
return this.http.get(url)
}

  getVoucherGenerate(body){
    var url = `${environment.baseUrl}sponsor/api/Voucher/generate`;
    return this.http.post(url, body);
  }

  getAddnotes(body){
    var url = `${environment.baseUrl}client/api/form/AddNote`;
    return this.http.post(url, body);
  }
  getCancelled(id: number, candidateId: number, modifiedBy: any) {
    var url = `${environment.baseUrl}client/api/operationstaff/exam/cancel?examScheduleId=${id}&candidateId=${candidateId}&modifiedBy=${modifiedBy}`;
    return this.http.delete(url);
  }
  Unassigned(VoucherId:number){
    var url =`${environment.baseUrl}sponsor/api/Voucher/unassign-to-person?voucherId=${VoucherId}`;
    return this.http.put(url,{});
  }
  getPerson(){
    var url =`${environment.baseUrl}client/api/account/getusers-page?personRoleId=1&pageNo=1&pageSize=8`
    return this.http.get(url);
  }
  isSubmit(candidateId:number,eRId:number){
    var url =`${environment.baseUrl}client/api/form/issubmissionallowed?candidateId=${candidateId}&eRId=${eRId}`
    return this.http.get(url);
  }
getshowAbbusebuttonDetails(PersonTenantRoleId){
  var url =`${environment.baseUrl}tenantmsvc/api/api/AbuseAllegation/show-abuse-allegations?PersonTenantRoleId=${PersonTenantRoleId}`
  return this.http.get(url);
}

getUserstatus(tenantId:number){
  var url =`${environment.baseUrl}client/api/traininginstitute/${tenantId}/admins`
  return this.http.get(url); 
}

getContactUpdate(contactDetails){
  var url = `${environment.baseUrl}client/api/Account/update-contactInfo`;
  return this.http.post(url, contactDetails);
}

getExamTypes(eligibilityRouteId:number,personTenantRoleId:number){
   var url = `${environment.baseUrl}client/api/operationstaff/exams?eligibilityRouteId=${eligibilityRouteId}&personTenantRoleId=${personTenantRoleId}`
  return this.http.get(url)
}

getExamId(candidateId){
  var url=`${environment.baseUrl}client/api/operationstaff/active-eligibility-route?candidateId=${candidateId}`
  return this.http.get(url)
}
getPayPalVoucherPayment(body) {
  const url = `${environment.baseUrl}candidate/api/Exam/paypal/initiate-voucher-payment`;
  return this.http.post(url, body);
}

confirmPayPalVoucherPayment(orderId, payerId, body) {
  const url = `${environment.baseUrl}candidate/api/Exam/paypal/confirm-voucher-payment?orderId=${orderId}&payerId=${payerId}`;
  return this.http.post(url, body);
}

getPayPalPayment(body){
  const url =`${environment.baseUrl}candidate/api/Exam/paypal/initiate-payment`
  return this.http.post(url,body)
}

confirmPayPalPayment(orderId, payerId, body){
 const url =`${environment.baseUrl}candidate/api/Exam/paypal/confirm-payment?orderId=${orderId}&payerId=${payerId}`;
 return this.http.post(url,body)
}

cancelPayPalPayment(orderId, body){
const url =`${environment.baseUrl}candidate/api/Exam/paypal/cancel-payment?orderId=${orderId}`;

return this.http.post(url,body)
}

refundPayment(data: refundPaymentPayload) {
  const body = {
    transId: data.transId,
    transAmount: data.transAmount,
    personTenantRoleId: data.personTenantRoleId,
    description: data.description
  }
  const apiUrl = `${environment.baseUrl}client/api/payment/authorizenet/transactions/refund`;
  return this.http.post<any>(apiUrl, body);
}

refundPayPayment(data: refundPaymentPayload) {
  const body = {
    personTenantRoleId: data.personTenantRoleId,
    description: data.description,
    refundAmount: data.transAmount,
    captureId: data.capturedId,
    orderId: data.orderId,
  }

  {
   
  }
  const apiUrl = `${environment.baseUrl}client/api/payment/paypal/orders/refund`;
  return this.http.post<any>(apiUrl, body);
}

getScheduleGistNAE(legacy:string,headers) {
  var url = legacy == "false"?`${environment.GisApiUrl}EventOffer/GetEventOfferByLoggedUser`:`${environment.GisApiUrl}EventOffer/HistoricalEventOfferByLoggedUser`
  return legacy == "false"? this.http.get(url,{headers:headers}): this.http.get(url,{headers:headers});
}

  getScheduleGistProctor(headers) {
    var url = `${environment.GisApiUrl}Event/GetEventsByGISLoggedUser`;
    return this.http.get(url,{headers:headers});
  }

  getUploadORM(
    clientEventId: number,
    candidateEmailId: string,
    formData: object,
    bookletNumber: string
  ) {
    var url = `${environment.baseUrl}formmsvc/api/form/UploadCandidatesOMRSheet?clientEventId=${clientEventId}&candidateEmailId=${candidateEmailId}&bookletNumber=${bookletNumber}`;
    return this.http.post(url, formData);
  }

postUploadImage(formData: object) {
  var url = `${environment.baseUrl}formmsvc/api/File/upload`;
  return this.http.post(url, formData);
}
getConfirm(body){
var url = `${environment.baseUrl}client/api/exam/ConfirmToArckiv`
 return this.http.post(url,body)
}


getCandidateDetailsfromGis(eventType,userId:number) {
  var url = `${environment.baseUrl}client/api/exam/CandidateDetails?clientEventId=${eventType}&userId=${userId} `;
    return this.http.post(url, [1, 10, 8,80,81,82,83,84,85,86,87,88,89,79,92,93,94,97]);
}

getCandidateTPfromGis(emailId: string, eventType: number) {
  var url = `${environment.baseUrl}client/api/exam/GetPersonDetailsforTPuser?emailid=${emailId}&clientEventId=${eventType}`;
  return this.http.get(url);
}

getTimer(personTenantRoleId) {
  var url = `${environment.baseUrl}client/api/exam/GetTimer?personTenantRoleId=${personTenantRoleId}`;
  return this.http.get(url);
}

  AddTimer(
    personTenantRoleId: number,
    activityTime: string,
    activityTypeId: number,
    clientEventId:string,
    body: object
  ) {
    var url = `${environment.baseUrl}client/api/exam/AddTimer?personTenantRoleId=${personTenantRoleId}&activityTime=${activityTime}&activityTypeId=${activityTypeId}&clientEventId=${clientEventId}`;
    return this.http.post(url, body);
  }

UpdateBookletNumber(body: object) {
  var url = `${environment.baseUrl}client/api/arkiv/booklet-assign`;
  return this.http.post(url, body);
}
ErruptCodeNAE(body) {
  var url = `${environment.NAERiredectorErrupt}Api/SendNAERedirector`;
  return this.http.post(url, body);
}

formSaveFile(body) {
  var url = `${environment.baseUrl}schedulemsvc/api/scheduler/SaveFileName`;
  return this.http.post(url, body);
}

CancelBookletId(body){
  var url = `${environment.baseUrl}client/api/exam/UpdateBooklet`
  return this.http.post(url,body)
}

getEventTP(email){
  var url = `${environment.baseUrl}schedulemsvc/api/scheduler/GetClientEventId?emailId=${email}`
  return this.http.post(url,{})
}

getTraninigId(personTenanatroleId:number,tenantId:number){
   var url =`${environment.baseUrl}client/api/traininginstitute/persontenantroleid?persontenantRoleId=${personTenanatroleId}&tenantId=${tenantId}&showAll=false`
   return this.http.get(url)
}



  getEventForProctor(emailId){
    var url =`${environment.GisApiUrl}Event/GetEventsWithOfferToEmail`
    return this.http.post(url,emailId)
  }

  getElapsedDateTime(candidate:number){
    var url =`${environment.baseUrl}client/api/Candidate/${candidate}/forms-filled?actionBy=${this.global.userDetails.getValue().personId}`
    return this.http.get(url)
  }

  ErruptRescore(body){
    var url = `${environment.baseUrl}client/api/exam/SendNAERedirector`
    return this.http.post(url, body,{ responseType: 'text' })
  }
  
  getEligibilityRoute(id:number){
    var url = `${environment.baseUrl}candidate/api/EligibilityRoute/acknowledgement?id=${id}`;
    return this.http.get(url);
  }
  UpdateElapsedDate(body){
    var url = `${environment.baseUrl}client/api/form/application/elapsedtime`
    return this.http.post(url,body)
  }

  getOMrVideos(personEventId){
     var url =`${environment.baseUrl}client/api/form/GetArkivDocument/${personEventId}`
     return this.http.get(url)
  }

  TransferVoucher(body){
    var url =`${environment.baseUrl}sponsor/api/Voucher/voucher-transfer`
    return this.http.post(url,body);
  }

  getNotes(personFormId ,candidateId,noteTypeId){
    var url =`${environment.baseUrl}Formmsvc/api/form/getnotes?personFormId=${personFormId}&candidateId=${candidateId}&noteTypeId=${noteTypeId}&showAll=false`
    return this.http.get(url);
  }

  submitAuditForm(body){
    var url =`${environment.GisApiUrl}FormBuilder/SaveAuditFormResponse`
    return this.http.post(url,body);
  }

  getBookletUrl(clientEventId:number,userId:number,personId:number, personEmailId:string){
    var url = `${environment.baseUrl}client/api/exam/DownloadBooklet?personId=${personId}&clientEventId=${clientEventId}&userId=${userId}&personEmailId=${personEmailId}`;
    return this.http.post(url ,[1, 10, 8, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 79]);
  }
  getEventDetail(eventSKU){
    var url =`${environment.GisApiUrl}Event/CredentiaEventDetails?eventSKU=${eventSKU}`
    return this.http.get(url);
  }
}
