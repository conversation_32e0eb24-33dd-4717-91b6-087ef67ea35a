<div class="p-4" *ngIf="isSelected">

    <div class="">
        <div class="flex justify-between pb-2">
            <div class="text-sm font-semibold">{{Heading}}</div>
            <div class="flex justify-end cursor-pointer" (click)="closeSideBar()">
                <mat-icon class="text-sm flex justify-end font-semibold">close</mat-icon>
            </div>
        </div>
        <hr>
    </div>

    <div fxLayout="row-reverse" class="pt-4 pb-2">
        <button *ngIf="isChangeRequest && isSubmit" mat-button class="btn-2 text-xs" (click)="submitChangeRequest()"
            (click)="sidenavRef.toggle()">
            Submit
        </button>
        <!-- <button *ngIf="isRejectedGrievance && recentLinkClickEvent?.element?.statusId !== 12 && noteTypeId !=9 &&noteTypeId !=12  && globalUserService.userDetails.value.roleId != 22 " mat-button class="btn1 text-xs" (click)="switch()">
            <mat-icon>add</mat-icon>Add New
        </button> -->
        <button *ngIf="isRejectedGrievance" mat-button class="btn1 text-xs" (click)="switch()">
            <mat-icon>add</mat-icon>Add New
        </button>
        <!-- <button *ngIf="(globalUserService.userDetails.value.roleId == 22  ) && noteTypeId !=12" mat-button class="btn1 text-xs" (click)="switch()">
            <mat-icon>add</mat-icon>Add New
        </button> -->
        
       
    </div>
    <ng-container  *ngIf="noteTypeId == 1">
        <ng-container *ngFor="let note of notes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>
   
    <ng-container  *ngIf="noteTypeId == 2">
        <ng-container *ngFor="let note of notes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>
    <ng-container  *ngIf="noteTypeId == 3">
        <ng-container *ngFor="let note of notes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>
    <ng-container  *ngIf="noteTypeId == 5">
        <ng-container *ngFor="let note of notes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>

    <ng-container *ngIf="noteTypeId == 14">
        <ng-container *ngFor="let note of dataSource ;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            <strong>{{note.label}}</strong>
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="ml-2 pt-2 text-xs word">
                        <strong class="scoreValue">Candidate Final Value</strong> : <span class="ml-2">{{note.candidateFinalValue}}</span>
                    </div>

                    <div class="ml-2 pt-2 pb-2 text-xs word">
                        <strong class="scoreValue">Proctor Final Value</strong> : <span class="ml-2">{{note.proctorFinalValue}}</span>
                    </div>
                </div>
            </div>
        </ng-container>

    </ng-container>
    <ng-container  *ngIf="noteTypeId == 6">
        <ng-container *ngFor="let note of notes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>
    <ng-container  *ngIf="noteTypeId == 7">
        <ng-container *ngFor="let note of NAEnotes;let i = index">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
        </ng-container>
    </ng-container>

    <ng-container *ngIf="noteTypeId == 10">
        <div class="card cardBorder shadow-none mb-4" fxLayout="column" *ngFor="let note of NAEnotes;let i = index">
            <div id="fullDiv{{i}}">
                <div class=" flex justify-between bg-color p-2" fxLayout="row">
                    <div class="text-xs flex">
                        {{note.title}}
                    </div>
                    <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                        <mat-menu #actions>
                            <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                <mat-icon class="text-normal">edit</mat-icon> Edit
                            </span>
                            <hr>
                            <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                            </span>
                        </mat-menu>
                        <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                        </mat-icon>
                    </div> -->
                </div>
                <hr>
                <div class="py-4 p-2 text-xs word">
                    {{note.body}}
                    <br>
                    <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                    <br>
                    <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                </div>
                <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                    <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                </div> -->
                <div *ngIf="note.files.length>0">
                    <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                        <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                        </dynamic-material-form-control>
                    </form>
                </div>
            </div>
            <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                Download Note
            </button>
        </div>
    </ng-container>


    <ng-container *ngIf="noteTypeId == 12">
        <div class="card cardBorder shadow-none mb-4" fxLayout="column"
            *ngFor="let note of ExamNaeComments;let i = index">
            <div id="fullDiv{{i}}">
                <div class="bg-color px-4 py-3">
                    <div class="flex justify-between title-hed " fxLayout="row">
                        <!-- <div class="t-xs title-hed"> -->
                            <strong>{{ note.incidentDescription }}</strong>
                        <!-- </div> -->
                    </div>
                    <div fxLayout="row" class="header state-elig pt-1">
                        {{ note.value }}
                    </div>
                </div>
                
                    <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                <hr>
                <div class="py-4 p-2 text-xs word">
                    {{note.incidentComments}}
                    <br>
                    <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                    <br>
                    <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                </div>
                <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <!-- <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div> -->
                </div>
                <!-- <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button> -->
            </div>
           </ng-container>

           <ng-container *ngIf="noteTypeId == 9">
            <div class="card cardBorder shadow-none mb-4" fxLayout="column" *ngFor="let note of AppealNotes;let i = index">
                <div id="fullDiv{{i}}">
                    <div class=" flex justify-between bg-color p-2" fxLayout="row">
                        <div class="text-xs flex">
                            {{note.title}}
                        </div>
                        <!-- <div *ngIf="isRejectedGrievance"  class="flex cursor-pointer">
                            <mat-menu #actions>
                                <span class="dropDown-view1" mat-menu-item (click)="edit(note)">
                                    <mat-icon class="text-normal">edit</mat-icon> Edit
                                </span>
                                <hr>
                                <span class="dropDown-view2" mat-menu-item (click)="delete(note)">
                                    <mat-icon class="text-normal color-del">delete_outline</mat-icon> Delete
                                </span>
                            </mat-menu>
                            <mat-icon class="text-base flex justify-end font-semibold moreIcon" [matMenuTriggerFor]="actions">more_vert
                            </mat-icon>
                        </div> -->
                    </div>
                    <hr>
                    <div class="py-4 p-2 text-xs word">
                        {{note.body}}
                        <br>
                        <span class="py-2 t-xs">{{note.lastUpdatedOn|date:'medium'}}</span>
                        <br>
                        <span class="py-2 r-0 t-xs">{{note.userName}}</span>
                    </div>
                    <!-- <div *ngIf="globalUserService.userDetails.value.roleId==8 && note.files.length>0">
                        <mat-icon (click)="viewBtn($event,note,i)">visibility</mat-icon>
                    </div> -->
                    <div *ngIf="note.files.length>0">
                        <form class="ml-4" *ngIf="fileUploadFormGroup[i]" [formGroup]="fileUploadFormGroup[i]">
                            <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="fileUploadFormGroup[i]" [model]="fileUploadModel[i]">
                            </dynamic-material-form-control>
                        </form>
                    </div>
                </div>
                <button  mat-button class="btn1 text-xs" (click)="toPdf(i)">
                    Download Note
                </button>
            </div>
         </ng-container>
          



    
</div>


<div class="p-4" *ngIf="!isSelected">
    <div>

        <div class="">
            <div class="flex pb-2">
                <div class="flex justify-end cursor-pointer">
                    <button (click)="switch()">
                        <mat-icon class="text-base flex justify-start items-center">arrow_back</mat-icon>
                    </button>
                </div>
                <div class="text-sm font-semibold">{{Heading}}</div>
            </div>
            <hr>
        </div>

        <div class="card cardBorder shadow-none p-2 addNewNote" fxLayout="column">
            <form [formGroup]="addNewNote">
                <!-- <mat-form-field *ngIf=""  class=" eligibility-list" appearance="outline" autocomplete="off">
                    <mat-label>Enter</mat-label>
                    <input matInput placeholder="title" formControlName="title">
                </mat-form-field> -->

                <mat-form-field appearance="outline" class="editor textEditor">
                    <!-- <mat-label>Description</mat-label> -->
                    <textarea class="editor flex-wrap" matInput formControlName="body" placeholder="Description"
                        cols="80" rows="10"></textarea>
                    <!-- <input matInput [style.display]="'none'">
                    <ngx-editor-menu class="editor flex-wrap" [editor]="editor" [toolbar]="toolbar">
                    </ngx-editor-menu>
                    <ngx-editor class="editor" [editor]="editor" formControlName="body">
                    </ngx-editor> -->
                </mat-form-field>
            </form>
            <div class="custom" *ngIf="this.noteTypeId!=2 && this.noteTypeId!=7 ">
                <form [formGroup]="formGroupUpload" class="ml-2">
                    <dynamic-material-form-control  style="margin-top: 12px;"class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="formGroupUpload" [model]="formGroupModel[0]">
                    </dynamic-material-form-control>
                </form>
            </div>
        </div>
    </div>
    <div class="flex justify-end switch-group">
        <button mat-button class="btn-7 t-xs mr-2" (click)="switch()">
            Cancel
        </button>
        <button mat-button class="btn-1 t-xs" (click)="saveNote()">
            Save
        </button>
    </div>
</div>