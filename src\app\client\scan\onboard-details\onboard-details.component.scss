// .bold {
//   font-weight: 700;
// }
// .mrg {
//   margin: 0.75rem;
// }

// .mrg-right {
//   margin-right: 10px;
// }
// .shadow-none{
//   padding-left: 7px;
// }

// .onBoardingHeader{
//   flex-direction: row;
//   box-sizing: border-box;
//   display: flex;
//   justify-content: space-between;
// }

// .PopUpbuttons{
//   cursor: pointer;
//   color: #0076C1;
//   float: right;
//   // padding-left: 8%;
//  }

//  ::ng-deep .cdk-overlay-pane{
//   min-width: 60vw !important;
//   min-height: 42vh !important
//  }

//  ::ng-deep .mat-dialog-container{
// // overflow: clip !important;
//  }

//   @media only screen and (max-width: 770px) {
//     ::ng-deep .cdk-overlay-pane {
//     width: 86% !important;
//     height: 71% !important;
//     // max-width: 86vw !important;
//     position: static;
//   }
// }

// .onboardingContent{
//   display: flex;
//   flex-direction: column;
// }

// .spanMargin{
//   margin-left: 10%;
// }
.mandatory {
  color: red;
}
.btn-1:disabled {
  /* Styles for disabled button */
  background-color: #cccccc !important;
  color: black !important;
  cursor: not-allowed;
}

@media only screen and (max-width: 770px) {
  ::ng-deep .cdk-overlay-pane {
    min-height: 62vh !important;
    width: 90% !important;
    height: 62% !important;
    max-width: 90vw !important;
    position: static;
  }
}

.signaturebox {
  border: 1px solid;
  border-style: dashed;
}

.signaturePlaceholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  font-size: 16px;
}

.signatureTitle {
  color: #767678;
  margin-top: 31%;
  margin-right: 5px;
}

.signatureDone{
  margin-left: 10px;
  color: green;
}

.signatureConsentText {
  font-size: 14px;
  width: 500px;
}
@media (max-width: 576px) {
  .signaturebox {
    max-width: 100%; /* Full width on smaller screens */
  }
}
