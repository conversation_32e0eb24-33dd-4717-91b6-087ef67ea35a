
import {
    consoleLoaderJSON,
    moduleTypes,
    nestingTypes,
    paramTypes,
    Roles,
    widgetTypes,
  } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
  import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
  
  export const moduleJSON: consoleLoaderJSON = {
    modules: [
      {
        type: moduleTypes.Dynamic,
        name: "Manage Booklet Inventory",
        moduleID: "booklet_inventory",
        nestingType: nestingTypes.ColumnsInRows,
        iconRef: "demographicManageIcon",
        routeRef: "/loader",
        rows: [
          {
            class: "",
            gdColumns: "1fr",
            gdColumnsltlg: "1fr",
            gdColumnsltmd: "1fr",
            gdColumnsltsm: "1fr",
            gdGap: "12px",
            columns: [
              {
                class: "w-full",
                gdColumn: "1 / 1",
                gdColumnltlg: "1 / 1",
                gdColumnltmd: "1 / 1",
                gdColumnltsm: "1",
                fxLayout: "",
                fxLayoutAlign: "start center",
                fxFlex: "none",
                fxFlexltmd: "auto",
                fxHidexs: "false",
                fxHidesm: "false",
                widget: {
                  type: widgetTypes.DynamicTable,
                  tableOptions: {
                    headerRowCssClasses: ["tab-col"],
                    showPaginator: true,
                    pageSizeOptions: [8, 20, 100],
                    pageSize: 8,
                    showColumnFilter: true,
                    showAddplaceorder:true,
                    showColumnWiseFilter: true,
                    showMatTabs: false,
                    showAddButton: [false],
                    addButtonTexts: ["Place Order"],
                    showSearch: true,
                    verticalScroll: true,
                    horizontalScroll: true,
                    isPaginationServerSide: [true],
                    isSearchServerSide: [true],
                    showStateSelectionDropdown:true,
                    showOrderStatusDropdown: true
                  
                  },
                  displayedColumns: [
                    [
                      
                      <tableTypes.column>{
                        visible: true,
                        id: "orderNumber",
                        name: "Order Id",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                    
                      <tableTypes.column>{
                        visible: true,
                        id: "waybillNumber",
                        name: "Way Bill Number",
                        cellType: "link",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-8"],
                        cellCssClasses: ["link-animation"],
                        filterable: true,
                        isFilter: false,
                        linkMetaData:{
                          navigateToType:moduleTypes.Way_Bill_Url
                        }
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "contactEmail",
                        name: "Email",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-5"],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                    
                      <tableTypes.column>{
                        visible: true,
                        id: "contactPhoneNumber",
                        name: "Phone Number ",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-8"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "stateName",
                        name: "State Name ",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-8"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "testCenterId",
                        name: "Test Center Id",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-8"],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                
                      <tableTypes.column>{
                        id: "requiredDate",
                        name: "Order Date",
                        cellType: "onlydate",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-5"],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "preferredCarrier",
                        name: "Carrier Partner",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-5"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "shippingMethod",
                        name: "Shipping Method",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-5"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "createdBy",
                        name: "Created By",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-5"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "modifiedOn",
                        name: "Created Date",
                        cellType: "date",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "orderNotes",
                        name: "Order Notes",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                     
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "orderStatus",
                        name: "Status",
                        statuses: tableTypes.tableStatuses,
                        cellType: "status",
                        headerType: "sort",
                          headerAlign: "center",
                          cellAlign: "center",
                          headerCssClasses: ["width-5"],
                          cellCssClasses: [],
                          filterable: false,
                          isFilter: false
                  
                      },
                     
                     
                 
                  
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        actions: [
                        
                          <tableTypes.action>{
                            icon: "visibility",
                            type: tableTypes.actionTypes.Bookletview,
                            tooltip: "View",
                            color: "#7d7d7d",
                            visibilityRule:[
                              {
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'orderStatus',
                                compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                                shouldHaveValue: "Error"
                              }
                            ]
                          },
                        ],
                       
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                      },    
                      
                    
                    ],
                  ],
                  requestDetails: [
                    {
                      fetchUrl: ["client", "api","credentia", "order-inventory"],
                      requestBody: null,
                      requestParams: [
                        {
                          paramType: paramTypes.Optional,
                          paramName: "pageNo",
                          paramValue :1,
                        },
                        {
                          paramType: paramTypes.Optional,
                          paramName: "pageSize",
                          paramValue: 8,
                        },
                      ],
                      method: "GET",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
    ],
  };
  