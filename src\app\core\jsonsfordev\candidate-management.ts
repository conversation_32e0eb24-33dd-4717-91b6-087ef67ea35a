import { state } from "@angular/animations";
import { FormTypes } from "src/app/client/application/application.types";
import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  State,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Manage Candidates",
      moduleID: "candidate-management",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "demographicManageIcon",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  showMatTabs: false,
                  showAddButton: [true],
                  addButtonTexts: ["Add Candidate"],
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  showStateSelectionDropdown:true,
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "addCandidate",
                      title: ["Add Candidate"],
                      id: "addCandidate",
                      buttons: [
                        {
                          buttonText: "Submit",
                          requestDetails: {
                            fetchUrl: ["client", "api", "user"],
                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "checkbox",
                      name: "Checkbox",
                      cellType: "text",
                      headerType: "checkbox",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-4"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "personId",
                      name: "Candidate ID",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: ["fullName"],
                      name: "Candidate Name",
                      cellType: "link",
                      linkMetaData: {
                        navigateToType: moduleTypes.Offspring,
                        navigateTo: "candidate-details",
                        eventDataUseful: false,
                        navigateToLabel: "Candidate Details",
                      },
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: ["link-animation"],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "pastFullName",
                      name: "Previous Name",
                      cellType: "link",
                      headerDisable:true,
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      visibilityRule: [{
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.Pennsylvania,
                      },
                      {
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'roleId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:Roles.StateClient,
                      }
                     
                    ],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    
                    <tableTypes.column>{
                      visible: true,
                      id: "address",
                      name: "Address",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "pastAddress",
                      name: "Previous Address",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      headerDisable:true,
                      cellAlign: "center",
                      headerCssClasses: [],
                      visibilityRule: [{
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.Pennsylvania,
                      },
                      {
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'roleId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:Roles.StateClient,
                      }
                     
                    ],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "emailId",
                      name: "Email Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "contactNumber",
                      name: "Contact Number",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "dateofBirth",
                      name: "Date of Birth",
                      cellType: "dobtype",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "stateName",
                      name: "State",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                     <tableTypes.column>{
                      visible: true,
                      id: "ssn",
                      name: "Last 4 SSN",
                      cellType: "text",
                      headerType: "sort",
                      headerDisable:true,
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      visibilityRule: [{
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                        shouldHaveValue:State.Virginia,
                      }],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "edit",
                          type: tableTypes.actionTypes.Edit,
                          tooltip: "Edit",
                          color: "#7d7d7d",
                          visibilityRule: [{
                            operator: tableTypes.Operators.AND,
                            elementProperty: 'emailId',
                            compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                            shouldHaveValue:null,
                          }],
                          requestDetails: [
                            {
                              fetchUrl: ["client", "api", `user`],
                              requestBody: null,
                              requestParams: [],
                              method: "PUT",
                            },
                          ],
                        },
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Offspring,
                        navigateTo: "",
                        eventDataUseful: false,
                        navigateToLabel: "",
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },    
                    
                    <tableTypes.column>{
                      visible: true,
                      id: "fullSSN",
                      name: "SSN",
                      cellType: "text",
                      headerType: "sort",
                      headerDisable:true,
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      visibilityRule: [{
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.Virginia,
                      }],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "externalId",
                      name: "Registered/ Migrated",
                      cellType: "text",
                      cellDataMapping: {
                        Migrated: "Migrated",
                        default:'Registered'
                      },
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "account", "getusers-page"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personRoleId",
                        paramValue: Roles.Candidate,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNo",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "stateId",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: 'stateId'
                      },
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
