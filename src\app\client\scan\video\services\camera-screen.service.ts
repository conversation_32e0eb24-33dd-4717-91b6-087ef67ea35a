import { Inject, Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject } from 'rxjs';
import { StreamService } from './stream.service';
import { DOCUMENT } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { GlobalUserService } from 'src/app/core/global-user.service';

declare var OT;

@Injectable({
  providedIn: 'root'
})
export class CameraScreenService {

  campublisher;
  screenpublisher;

  cameraConfiguration = {
    audioSource: localStorage.getItem('audioDeviceId'),
    videoSource: localStorage.getItem('videoDeviceId'),
    insertMode: "append",
    // name: "360",
    name: "Shredding",
    facingMode: "user",
    // style: { buttonDisplayMode: "off" }, //hide mute button from candidate
    publishAudio: true,
    publishVideo: true,
    width: '100%',
    height: '100%',
    echoCancellation: true
  }
  
  public recordVideo$ = new BehaviorSubject(true);  
  public stopRecordVideo$ = new BehaviorSubject(false);  

  public microphoneOn$ = new BehaviorSubject(true);

  disableStartCam = false;

  constructor(@Inject(DOCUMENT) private document: Document, private http: HttpClient, 
  private global: GlobalUserService,
    private snackbar: MatSnackBar, private streamService:StreamService) { }


  toggleMicrophone(value) {
    if(this.campublisher)
      this.campublisher.publishAudio(value);
  }

  async askForPermission() {
    await navigator.mediaDevices.getUserMedia({ audio: true, video: true });

    // mediaTracks.getVideoTracks().forEach(function(track) {
    //   track.stop();
    // });

    // mediaTracks.getAudioTracks().forEach(function(track) {
    //   track.stop();
    // });
  }

  async setCamera() {

    const devices = await navigator.mediaDevices.enumerateDevices();

    const videoCameras = devices.filter(device => device.kind === 'videoinput');
    const audioInputs = devices.filter(device => device.kind === 'audioinput');

    const audioDeviceId = audioInputs.length ? audioInputs[0]?.deviceId : null;
    const videoDeviceId = videoCameras.length ? videoCameras[0]?.deviceId : null;

    localStorage.setItem('videoDeviceId', videoDeviceId);
    localStorage.setItem('audioDeviceId', audioDeviceId);

    this.cameraConfiguration.audioSource = audioDeviceId;
    this.cameraConfiguration.videoSource = videoDeviceId;

  }

  async startCam() {
    
    const audioDeviceId = localStorage.getItem('audioDeviceId');
    const videoDeviceId = localStorage.getItem('videoDeviceId');
    
    if(!audioDeviceId || !videoDeviceId) {

    try {

      await this.askForPermission();
      
      await this.setCamera();


    } catch(err) {
      this.snackbar.open('You must allow camera and microphone permissions', "Ok", {
        duration: 3000
      })

      location.href = "/";
    }

  }

    if(this.disableStartCam) return;

    const session = this.streamService.session;

    this.disableStartCam = true;

    this.campublisher = OT.initPublisher(this.document.getElementById('camera'), this.cameraConfiguration, error => {
      
      this.disableStartCam = false;

      if(error != undefined) return;
      if(!(session && this.campublisher)) return;
      
      this.recordVideo$.next(true);

      session.publish(this.campublisher, err =>{
        if(err) return;

        this.campublisher.on('streamCreated', event =>{

        })
      })

    });
  }

  stopRecording(obj){
    this.http.post(
      `${environment.apiUrl}api/ArchiveApi/StopArchive`, obj
    ).subscribe(data => {
      
      });
  }
  stopSession() {

    const session = this.streamService.session;
  
    if(this.campublisher) {
      this.campublisher.destroy();

      if(session)
        session.unpublish(this.campublisher)

      this.campublisher = undefined;

    }
  }

  changeCameraAndAudio() {
    this.campublisher.setAudioSource(localStorage.getItem('audioDeviceId')).then(() => {});
    this.campublisher.setVideoSource(localStorage.getItem('videoDeviceId')).then(() => {});;
  }

  switchCamera() {
    if(this.campublisher)
      this.campublisher.cycleVideo().then(() => {});
  }

}
