import { setPageSize } from "./pageSize.actions";
import { createReducer, on } from "@ngrx/store";
import { PageSizeState, initialState } from "./pageSize.state";

const _pageSizeReducer = createReducer<PageSizeState>(initialState,
    on(setPageSize, (state, { pageSize, pageNumber }) => {
        return {
            ...state,
            pageSize: pageSize,
            pageNumber: pageNumber
        };
    }),
)

export function pageSizeReducer(state, action) {
    return _pageSizeReducer(state, action)
}
