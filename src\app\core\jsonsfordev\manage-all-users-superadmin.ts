import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [{
    type: moduleTypes.Dynamic,
    name: "Manage All Users",
    moduleID: "manage-all-users-superadmin",
    nestingType: nestingTypes.ColumnsInRows,
    iconRef: "managaeUserRoleIcon",
    routeRef: "/loader",
    rows: [
      {
        class: "",
        gdColumns: "1fr",
        gdColumnsltlg: "1fr",
        gdColumnsltmd: "1fr",
        gdColumnsltsm: "1fr",
        gdGap: "12px",
        columns: [
          {
            class: "w-full",
            gdColumn: "1 / 1",
            gdColumnltlg: "1 / 1",
            gdColumnltmd: "1 / 1",
            gdColumnltsm: "1",
            fxLayout: "",
            fxLayoutAlign: "start center",
            fxFlex: "none",
            fxFlexltmd: "auto",
            fxHidexs: "false",
            fxHidesm: "false",
            widget: {
              type: widgetTypes.DynamicTable,
              tableOptions: {
                headerRowCssClasses: ["tab-col"],
                showPaginator: true,
                pageSizeOptions: [8, 20, 100],
                pageSize: 8,
                showColumnFilter: true,
                showAddButton: [true],
                addButtonTexts:['Add User'],
                addPopupData: [{
                  isCustom: true,
                  customTemplateRef: 'customAddPopupFormManageAllUsers',
                  title: ['Add User', 'Edit User Details'],
                  id: 'addUsersPopup',
                  buttons: [
                    {
                      buttonText: "Submit",
                      requestDetails: {
                        fetchUrl: ["Login", "register"],
                        requestBody: null,
                        requestParams: null,
                        method: "POST",
                      },
                    },
                  ],
                }],
                
                showMatTabs: false,
                showSearch: true,
                verticalScroll: true,
                horizontalScroll: true,
              },
              displayedColumns: [
                [
                  <tableTypes.column>{
                    visible: true,
                    id: "checkbox",
                    name: "Checkbox",
                    cellType: "text",
                    headerType: "checkbox",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [ "w-4"],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: ["firstName", "lastName"],
                    name: "Name",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "contactNumber",
                    name: "Contact Number",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "emailId",
                    name: "Email",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "createdDate",
                    name: "Created Date",
                    cellType: "date",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "roleName",
                    name: "Role Name",
                    cellType: "text",
                    headerType: "sort",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "active",
                    name: "Status",
                    cellType: "toggle",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: ['w-12'],
                    cellCssClasses: [],
                    statusUpdateDetails: {
                      fetchUrl: ["client", "api", "account", "updateuserstatus"],
                      requestBody: null,
                      requestParams: null,
                      method: "PUT",
                    },
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "action",
                    name: "Actions",
                    cellType: "action",
                    actions: [
                      <tableTypes.action>{
                        icon: "edit",
                        type: tableTypes.actionTypes.Edit,
                        tooltip: "Edit",
                        color: '#7d7d7d',
                        requestDetails: [
                          {
                            fetchUrl: ["client","api","account", "UpdateUser"],
                            requestBody: null,
                            requestParams: [],
                            method: "PUT",
                          },
                        ],
                      },
                    ],
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: ["w-12", ],
                    cellCssClasses: ["w-12","cursor-pointer"],
                  },
                ],
              ],
              requestDetails: [
                {
                  fetchUrl: [`client`,`api`, `Account`, `getusers`],
                  requestBody: null,
                  requestParams: [
                  { 
                    paramType: paramTypes.Required,
                    paramName: null,
                    paramValue: null,
                    extractedFromGlobal: true,
                    position:3,
                    elementPropertyToBeExtracted: "personTenantRoleId",
                  },
                  { 
                    paramType: paramTypes.Optional,
                    paramName: 'showAll',
                    paramValue: 'true',
                  },
                ],
                  method: "GET",
                },
              ],
            },
          },
        ],
      },
    ],
  }
  ]
}