export const environment = {
  production: true,
  name: "prod",
  baseUrl: "https://credentiauatapi.examroom.ai/",
  reportUrl: "https://reports.examroom.ai/",
  reportUrlapi: "https://reportapiv2.examroom.ai/",
  redirectUrl: "https://credentiauat.examroom.ai/",
  redirectUrls: "https://credentiauat.examroom.ai/",
  candidate: "candidate",
  client: "client",
  training: "/training",
  sponsor: "/voucher",
  state:"/loader/manage-applications",
  superset:'https://workbenchuat.examroom.ai/', 
  Employees:"/loader/manage-all-users",
  Evalutor:"https://credentiauat.examroom.ai/gis/validate-gisaccess",
  Finance:"/training",
  gistUrl: "https://credentiauat.examroom.ai/gis/validate-gisaccess", 
  URLNotAllowedToken:'https://credentiauatapi.examroom.ai/gisapis/api/EventOffer/GetEventOfferByEmail',
  uplaodApi: "https://aiapi.examroom.ai/api/",
  ScanUrl: "https://erlensuat.examroom.ai/",
  NAERiredectorErrupt: "https://testapi.examroom.ai/",
  RedirecttoSkillsExams:'https://skillsreviewqa.examroom.ai/',
  SkillsDownload:"https://skillsqaapi.examroom.ai/api/",
  /// Secret and Api key for AI scanner upload////
  AISecretKet: "D50F173D-0F28-410C-ACCB-186360190491",
  AIAPIKey: "2020439",
  SkillsAccessKey :'CNA.QATester',
  SkillsSecrectKey:"8cu75",
  GisApiUrl:'https://credentiauatapi.examroom.ai/gisapis/api/',
  TSMredirect:'https://testsitemanagementdev.examroom.ai/#/',

  apiUrl: "https://webrtcuatapi.examroom.ai/",
  streamApiKey: "47095304",

  PayPalConfigurationId:
    "AURSiJyrCRnfnU6pVeuu4IQhELaQVOTGC5JALwdCYx2AllFyOB1inK36PY8QY7rxfAiDUuKXtbJHxi3v",
  config: {
    iceServers: [
      {
        urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"],
      },
      {
        username: "brucewayne",
        credential: "1234567890",
        urls: [
          "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
          "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
        ],
      },
    ],
  },
};
