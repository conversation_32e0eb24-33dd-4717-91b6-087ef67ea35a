import { state } from "@angular/animations";
import { createFeatureSelector, createSelector } from "@ngrx/store";
import { Cart } from "./models/cart";
import { ExamType } from "./models/examType";
import { MonthlySlot } from "./models/monthlySlot";
import { slot, Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import { ScheduledState } from "./scheduled.state";

export const SCHEDULED_STATE_NAME = "ScheduleModuleState";

const getScheduledState =
  createFeatureSelector<ScheduledState>(SCHEDULED_STATE_NAME);

export const selectorGetTimeSlotsTestCenterStatus = createSelector(
  getScheduledState,
  (state): "idle" | "loading" | "success" | "error" =>
    state.timeSlotsTestCenterStatus
);

export const get_timezones = createSelector(
  getScheduledState,
  (state): Timezone[] => {
    return state.timezones;
  }
);
export const selectorGetTimeSlotsTestCenter = createSelector(
  getScheduledState,
  (state): slot[] => {
    return state.timeslotsTestCenter;
  }
);

export const get_timeslots = createSelector(
  getScheduledState,
  (state): Slot[] => {
    return state.timeslots;
  }
);

export const get_cart = createSelector(getScheduledState, (state): Cart => {
  return state.cart;
});

export const selectorGetVoucher = createSelector(getScheduledState, (state) => {
  return state.VocherResponse;
});

export const get_monthlyslots = createSelector(
  getScheduledState,
  (state): MonthlySlot[] => {
    return state.monthlySlots;
  }
);
export const get_examId = createSelector(
  getScheduledState,
  (state): ExamType[] => {
    return state.examId;
  }
);
export const get_eligibilityroute = createSelector(
  getScheduledState,
  (state) => {
    return state.route;
  }
);

export const get_registeredexam = createSelector(getScheduledState, (state) => {
  return state.registeredExams;
});

export const get_makepayment = createSelector(getScheduledState, (state) => {
  return state.makepaymentresponse;
});

export const get_rescheduledResponse = createSelector(
  getScheduledState,
  (state) => {
    return state.rescheduleResponse;
  }
);

export const selectorGetScheduledExam = createSelector(
  getScheduledState,
  (state) => {
    return state.scheduleResponse;
  }
);

export const get_schedule = createSelector(getScheduledState, (state) => {
  return state.schedule;
});

export const get_isCancelled = createSelector(getScheduledState, (state) => {
  return state.isCancelled;
});
export const get_isPayment = createSelector(getScheduledState, (state) => {
  return state.isPayment;
});

export const get_cartItems = createSelector(getScheduledState, (state) => {
  return state.cartItems;
});

export const get_candidateId = createSelector(getScheduledState, (state) => {
  return state.candidateId;
});

export const get_timezone = createSelector(getScheduledState, (state) => {
  return state.timezone;
});
export const selectorGetVouchersApply = createSelector(
  getScheduledState,
  (state) => {
    return state.VochersApply;
  }
);

export const gotVoucherList = createSelector(getScheduledState, (state) => {
  return state.voucherCart;
});

export const stausofVoucherDetail = createSelector(
  getScheduledState,
  (state) => {
    return state.deleteVoucherCart;
  }
);

export const transcationdetails = createSelector(getScheduledState, (state) => {
  return state.paymentDetails;
});

export const generatedSucessfully = createSelector(
  getScheduledState,
  (state) => {
    return state.generateVoucher;
  }
);

export const editedCartDetails = createSelector(getScheduledState, (state) => {
  return state.editCartItems;
});

export const gotTrainingInstStateID$ = createSelector(
  getScheduledState,
  (state) => {
    return state.trainingStateID;
  }
);

export const getPaymentMethods$ = createSelector(getScheduledState, (state) => {
  return state.paymentMethods;
});

export const getChargeResources$ = createSelector(getScheduledState, (state) => {
  return state.chargeResponse
})
export const getCustomerId$ = createSelector(getScheduledState, (state) => {
  return state.customerIdObj;
});

export const listOFVoucher = createSelector(getScheduledState, (state) => {
  return state.voucherDetails;
});

export const selectorGetUpdateVocher = createSelector(
  getScheduledState,
  (state) => {
    return state.VocherUpdateResponse;
  }
);

export const createPaymentMethodResponse$ = createSelector(
  getScheduledState,
  (state) => {
    return state.createPaymentMethodResponse;
  }
);

export const createPaymentCustomerId$ = createSelector(
  getScheduledState,
  (state) => {
    return state.createPaymnetCustomerIdResponse;
  }
);

export const getSavedVoucher$ = createSelector(getScheduledState, (state) => {
  return state.voucherCode;
});
