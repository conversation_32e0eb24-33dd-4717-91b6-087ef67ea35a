



import { HttpClient } from "@angular/common/http";
import { Component, Input, OnInit, ViewChild, OnChanges, SimpleChanges, AfterViewInit, ChangeDetectionStrategy } from '@angular/core';
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatSidenav } from "@angular/material/sidenav";
import { Store } from "@ngrx/store";
import { Editor, Toolbar } from "ngx-editor";
import { BehaviorSubject } from "rxjs";
import { recentLinkClickEventSelector } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import { cellClickEvent } from "../table/dynamic-table-types";
import { ThrowStmt } from "@angular/compiler";
import { GlobalUserService } from "../../global-user.service";
import { SnackbarService } from "../../snackbar.service";
import { Router } from "@angular/router";
import { URL } from 'src/app/core/url';
import { PersonFormNoteTypeEnum } from "../form-view/form-view.component";
import { environment } from "src/environments/environment";
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormModel, DynamicFormService } from "@ng-dynamic-forms/core";
import { FormTypes } from "src/app/client/application/application.types";
import { Roles, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import domtoimage from 'dom-to-image';
import jsPDF from "jspdf";
import { AppComponent } from "src/app/app.component";
import { fetchData } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";



@Component({
  selector: "exai-notes-sidebar",
  templateUrl: "./notes-sidebar.component.html",
  styleUrls: ["./notes-sidebar.component.scss"],
  changeDetection: ChangeDetectionStrategy.Default 
})
export class NotesSidebarComponent implements OnInit, OnChanges  {
  recentLinkClickEvent: cellClickEvent;
  notes: Note[];
  NAEnotes: Note[]=[];
  AppealNotes:Note[]=[]
  addNewNote: FormGroup;
  formGroupUpload 
  formGroupModel
  ExamNaeComments:Array<object> =[]
  constructor(private store: Store, private http: HttpClient,
    public globalUserService: GlobalUserService,
    private snackbar: SnackbarService,
    private httpClient: HttpClient,
    private dynamicFormService: DynamicFormService,
    public router: Router,) {
    }

  @Input() sidenavRef: MatSidenav;
  @Input() viewId: PersonFormNoteTypeEnum = 1;
   personId: any;
  isSelected = true;
  isChangeRequest:boolean = false;
  Heading:string;
  personFormId: number;
  isSubmit:boolean = false;
  noteTypeId:number ;
  splitBeforeSlash:string
  splitAfterSlash:string
  isRejectedGrievance:boolean = true;
  curNote: BehaviorSubject<Note> = new BehaviorSubject<Note>(null);
  dataSource :Array<{candidateFinalValue:string,proctorFinalValue:string,label?:string}> = [];
  disableTitle:boolean=true;
  fileUploadModel: DynamicFormModel=[];
  fileUploadFormGroup: FormGroup[] =[];
  supportDocs:any;
  showAllsupportDocs:boolean=false
  dataUpload = new DynamicFileUploadModel({
    required: false,
    id:'1',
    name:'2',
    label: 'Upload Supporting Documents',
    accept: ['.pdf', '.doc', '.docx','.jpg','.png'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList:true,
    additional: {
      appearance: 'outline'
    }
  })

  ngOnChanges(changes: SimpleChanges | any) {
    if(changes['viewId']["currentValue"]==1 ){
      this.Heading="Notes (Internal Only)"
      this.noteTypeId=1;
      this.isChangeRequest=false;
      this.isRejectedGrievance=true;
       this.getNote()      // this.getNAENotes()
      // this.getAppealNote()
      // this.getNAENotes()
      // this.getAppealNote()
      this.isSubmit=false;
    }
    else if(changes['viewId']["currentValue"]==2){
      this.Heading="Change Request (Viewable by Candidate)"
      this.noteTypeId=2;
      this.isChangeRequest=true;
      this.isRejectedGrievance=true;
      const personFormId = this.recentLinkClickEvent?.element?.personFormId;
      if (personFormId) {
        this.httpClient
          .get(`${environment.baseUrl}client/api/form/getnotes?personFormId=${personFormId}&noteTypeId=2`)
          .subscribe((data: any) => {
            this.isSubmit = data && data.length > 0;
          });
      }
      this.getNote();
      // this.getAppealNote()
    }
    else if(changes.viewId.currentValue ==3){
      this.Heading="Grievance Response"
      this.noteTypeId=3;
      if(this.recentLinkClickEvent?.element.statusName =="Rejected" || this.recentLinkClickEvent?.element.statusId ==12 ){
        this.isRejectedGrievance=false; 
      }else{
        this.isRejectedGrievance=true;  
      }
      this.isChangeRequest=false;
      this.isSubmit=false;
     this.getNote()
    }
    else if(changes['viewId']["currentValue"]==5){
      this.isSubmit=false;
      this.Heading="Notes"
      this.noteTypeId=5;
this.getNote()   }
    else if(changes['viewId']["currentValue"]==6){
      this.Heading="Notes"
      this.noteTypeId=6;
this.getNote()
          this.isSubmit=false;
    }

    else if(changes['viewId']["currentValue"]==10){
      this.Heading="Misconduct Reasons"
      this.noteTypeId=10;
      this.isRejectedGrievance=true;
      this.recentLinkClickEvent?.element?.personEventId !=null && this.recentLinkClickEvent?.element?.personEventId !='' && this.recentLinkClickEvent?.element?.personEventId !=undefined ?this.getNAENotes():null
      this.isSubmit=false;
    }
    else if(changes['viewId']["currentValue"]==12){
      this.Heading="NAE Notes"
      this.isRejectedGrievance=false;
      this.noteTypeId=12;
      this.recentLinkClickEvent?.element?.personEventId !=null && this.recentLinkClickEvent?.element?.personEventId !='' && this.recentLinkClickEvent?.element?.personEventId !=undefined ?this.getnaeExamcomments():null
    }
    else if(changes['viewId']["currentValue"]==9){
      this.Heading="Notes"
      this.noteTypeId=9;
      this.isRejectedGrievance=true;  
      this.recentLinkClickEvent?.element?.personEventId !=null && this.recentLinkClickEvent?.element?.personEventId !='' && this.recentLinkClickEvent?.element?.personEventId !=undefined ?this.getAppealNote():null 
      this.isSubmit=false;
    }
    else if(changes['viewId']["currentValue"]==14){
      this.Heading="Measurement Details"
      this.noteTypeId=14;
      this.isRejectedGrievance=false;  
      this.recentLinkClickEvent?.element?.personEventId !=null && this.recentLinkClickEvent?.element?.personEventId !='' && this.recentLinkClickEvent?.element?.personEventId !=undefined ?this.getMeasurResults():null
    }
    else if(changes['viewId']["currentValue"]['viewId']==7){
      this.Heading="Notes"
      this.noteTypeId=7;
      this.personId = changes['viewId']["currentValue"]['personId']
    this.getNote()
        this.isSubmit=false;
    }
  }
  ngOnInit(): void {
    this.formGroupModel = [this.dataUpload] 
    
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel)
    this.editor = new Editor();
    this.addNewNote = new FormGroup({
      // title: new FormControl("", [Validators.required]),
      body: new FormControl("", [Validators.required]),
    });
    // use this.recentLinkClickEvent.element to access all properties
    this.store.select(recentLinkClickEventSelector).subscribe((x: any) => {
      if (x) {
        this.recentLinkClickEvent = x;
        this.personFormId = this.recentLinkClickEvent.element.personFormId;
        // this.getNote();
      }
    });
   setTimeout(()=>{
    this.ngOnChanges({viewId:{currentValue:this.viewId}})
   },1000)
   
   
  }

  switch(clear: boolean = true) {
    this.isSelected = !this.isSelected;
    this.formGroupModel[0].value=[];
    if (clear) this.addNewNote.reset();
  }
  submitChangeRequest(){

    var requestBody = {
      personFormId: this.recentLinkClickEvent.element.personFormId,
      personFormReviewId: this.recentLinkClickEvent.element.personFormReviewId,
      comment: this.globalUserService.userDetails.value.roleId === 15?this.notes[0].body:'',
      statusId:this.globalUserService.userDetails.value.roleId == Roles.TrainingInstitue?14: 4,
      personTenantRoleId: this.globalUserService.userDetails.value.personTenantRoleId,
    }
    
   this.globalUserService.userDetails.value.roleId !== Roles.TrainingInstitue? this.httpClient.post(URL.BASE_URL_SHORTER + `client/api/form/personform/action`, requestBody).subscribe((x: any) => {
           
        this.snackbar.callSnackbaronSuccess( 'Change Request'+ ' Application Submited Successfully');
        
        // this.store.dispatch(fetchData({
        //   widgetType: widgetTypes.DynamicProcessLogs,
        //   moduleIndex: AppComponent.consoleLoaderJson.modules.findIndex((x) => { return x.moduleID == 'certificate-renewal-details' }),
        //   requestDetails: {
        //     fetchUrl: ["client", "api", "form", "personformlogs"],
        //     requestBody: null,
        //     requestParams: [
        //       {
        //         paramType: 'optional',
        //         paramName: 'personFormId',
        //         paramValue: requestBody.personFormId,
        //         extractedFromElement: true,
        //         elementPropertyToBeExtracted: 'personFormId'
        //       }
        //     ],
        //     method: 'GET'
        //   },
        // }));
        
        if(this.recentLinkClickEvent.column.linkMetaData.navigateTo=='reciprocity-request-details')
        this.router.navigate(['loader', 'reciprocity-request']);
        else{
          if (this.recentLinkClickEvent && this.recentLinkClickEvent.element.formTypeId){
            switch (this.recentLinkClickEvent.element.formTypeId) {
              case FormTypes.Accomodation:
                
                this.router.navigate(['loader' ,'manage-accommodations']);
                break;
              case FormTypes.Application:
                  this.router.navigate(['loader',
                    (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff) ? 'manage-applications-ops' : 'manage-applications']);
                break;
              case FormTypes.Grievance:
                this.router.navigate(['loader', 'manage-grievence']);
                break;
              case FormTypes.Demographic:
                this.router.navigate(['loader', 'manage-profile-request']);
                break;
               case FormTypes.Excused_Absence:
                this.router.navigate(['loader', 'manage-excuses']);
                break;
                case FormTypes.Certificate_Renewal:
                  break;
              default: this.router.navigate(['loader',
                (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff)
                ?
                'manage-applications-ops'
                :
                'manage-applications']);
            }
          }
          else this.router.navigate(['loader',
            (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff)
              ?
              'manage-applications-ops'
              :
              'manage-applications']);
        }
    
    }, (err: any) => {
      this.snackbar.callSnackbaronError(err.message.message.error.slice(err.message.message.error.search("Content"),-1));
    }):
    this.httpClient.post(URL.BASE_URL_SHORTER + `formmsvc/api/form/Withdraw-Approval`, requestBody).subscribe((x: any) => {
           
      this.snackbar.callSnackbaronSuccess( 'Change Request'+ ' Application Submited Successfully');
      
      // this.store.dispatch(fetchData({
      //   widgetType: widgetTypes.DynamicProcessLogs,
      //   moduleIndex: AppComponent.consoleLoaderJson.modules.findIndex((x) => { return x.moduleID == 'certificate-renewal-details' }),
      //   requestDetails: {
      //     fetchUrl: ["client", "api", "form", "personformlogs"],
      //     requestBody: null,
      //     requestParams: [
      //       {
      //         paramType: 'optional',
      //         paramName: 'personFormId',
      //         paramValue: requestBody.personFormId,
      //         extractedFromElement: true,
      //         elementPropertyToBeExtracted: 'personFormId'
      //       }
      //     ],
      //     method: 'GET'
      //   },
      // }));
      
      if(this.recentLinkClickEvent.column.linkMetaData.navigateTo=='reciprocity-request-details')
      this.router.navigate(['loader', 'reciprocity-request']);
      else{
        if (this.recentLinkClickEvent && this.recentLinkClickEvent.element.formTypeId){
          switch (this.recentLinkClickEvent.element.formTypeId) {
            case FormTypes.Accomodation:
              
              this.router.navigate(['loader' ,'manage-accommodations']);
              break;
            case FormTypes.Application:
                this.router.navigate(['loader',
                  (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff) ? 'manage-applications-ops' : 'manage-applications']);
              break;
            case FormTypes.Grievance:
              this.router.navigate(['loader', 'manage-grievence']);
              break;
            case FormTypes.Demographic:
              this.router.navigate(['loader', 'manage-profile-request']);
              break;
             case FormTypes.Excused_Absence:
              this.router.navigate(['loader', 'manage-excuses']);
              break;
              case FormTypes.Certificate_Renewal:
                break;
            default: this.router.navigate(['loader',
              (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff)
              ?
              'manage-applications-ops'
              :
              'manage-applications']);
          }
        }
        else this.router.navigate(['loader',
          (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff)
            ?
            'manage-applications-ops'
            :
            'manage-applications']);
      }
  
  }, (err: any) => {
    this.snackbar.callSnackbaronError(err.message.message.error.slice(err.message.message.error.search("Content"),-1));
  })
    
  }
  saveNote() {
   if(this.noteTypeId ==2){
      if (this.addNewNote) {        
        var isEdit: boolean = this.curNote.value != null;
        this.curNote.next({
          id:
            this.curNote.value && this.curNote.value.id
              ? this.curNote.value.id
              : 0,
          title: "",
          body: this.addNewNote.value.body,
          // files: [
          //   {
          //     id: 0,
          //     name: this.formGroupModel[0]._value.split('|',2)[0],
          //     filePath: this.formGroupModel[0]._value.split('|',2)[1],
          //     mime: "",
          //     personFormNoteId: 0,
          //     deleted: false
          //   }
          // ],
          noteTypeid:this.noteTypeId,
          userId: this.globalUserService.userDetails.value.personTenantRoleId,
          userName:this.globalUserService.userDetails.value.userName
        });
  
        if(this.personFormId){
          this.curNote.next({...this.curNote.value,candidateId:this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.personEventId.personTenantRoleId,personFormId:this.personFormId});          
        }else{
          this.curNote.next({...this.curNote.value,candidateId:this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.personEventId.personTenantRoleId})
        }

        var httpObservable = isEdit
          ? this.http.put(
              environment.baseUrl+`client/api/form/updatenote`,
              this.curNote.value
            )
          : this.http.post<any>(
            environment.baseUrl+"client/api/form/AddNote",
              this.curNote.value
            );
        httpObservable.subscribe((res) => {
          if (res) {
            this.getNote();
            this.switch();
            this.curNote.next(null);
          }
        });
        this.isSubmit=true
      }  
    }
    
    else{
      if (this.addNewNote.valid && this.noteTypeId !=7 && this.noteTypeId !=10 && this.noteTypeId !=9) {
      
      var isEdit: boolean = this.curNote.value != null;       

      if(this.formGroupModel.length && typeof this.formGroupModel[0]._value == 'string'){
       this.formGroupModel[0]._value = [this.formGroupModel[0]._value]
      }

        this.curNote.next({
          id:
            this.curNote.value && this.curNote.value.id
              ? this.curNote.value.id
              : 0,
          title: "",
          body: this.addNewNote.value.body,
          files:  this.formGroupModel[0]._value && this.formGroupModel[0]._value.length ?                     
          this.formGroupModel[0]._value.map((file:string)=>{                         
            return {
              id: 0,
              name: file.split('|')[0],
              filePath: file.split('|')[1],
              mime: "",
              personFormNoteId: 0,
              deleted: false
            }
          }):[],          
          noteTypeid:this.noteTypeId,
          userId: this.globalUserService.userDetails.value.personTenantRoleId,
          userName:this.globalUserService.userDetails.value.userName
        });

        if(this.personFormId){
          this.curNote.next({...this.curNote.value,candidateId:this.globalUserService.personEventId.personTenantRoleId ? this.globalUserService.personEventId.personTenantRoleId : this.globalUserService.personEventId.PersonTenantRoleId?this.globalUserService.personEventId.PersonTenantRoleId:this.recentLinkClickEvent.element.personTenantRoleId,personFormId:this.personFormId});  
                 
        }else{
          this.curNote.next({...this.curNote.value,candidateId:this.globalUserService.personEventId.personTenantRoleId ? this.globalUserService.personEventId.personTenantRoleId : this.globalUserService.personEventId.PersonTenantRoleId})
        }

        var httpObservable = isEdit
          ? this.http.put(
              `${environment.baseUrl}client/api/form/updatenote`,
              this.curNote.value
            )
          : this.http.post<any>(      
            `${environment.baseUrl}client/api/form/AddNote`,
              this.curNote.value
            );
        httpObservable.subscribe((res) => {
          if (res) {
            this.getNote();
            this.switch();
            this.curNote.next(null);
          }
        });
        this.isSubmit=true
      }
      
    }


    if(this.noteTypeId==6){
      var isEdit: boolean = this.curNote.value != null;
      this.curNote.next({
        id:
          this.curNote.value && this.curNote.value.id
            ? this.curNote.value.id
            : 0,
        title: "",
        body: this.addNewNote.value.body,
        files: this.curNote.value ? this.curNote.value.files : [],
        noteTypeid:this.noteTypeId,
        userId: this.globalUserService.userDetails.value.personTenantRoleId,
        userName:this.globalUserService.userDetails.value.userName
      });

      if(this.personFormId)
        this.curNote.next({...this.curNote.value,personFormId:this.personFormId});
    if(this.recentLinkClickEvent.element.personId)
        this.curNote.next({...this.curNote.value,candidateId:this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.personEventId.personTenantRoleId})
      
      var httpObservable = isEdit
        ? this.http.put(
            environment.baseUrl+`client/api/form/updatenote`,
            this.curNote.value
          )
        : this.http.post<any>(
          environment.baseUrl+"client/api/form/AddNote",
            this.curNote.value
          );
      httpObservable.subscribe((res) => {
        if (res) {
          // this.getNote();
          this.switch();
          this.curNote.next(null);
        }
      });
      this.isSubmit=true
    }
    if(this.noteTypeId==9){
      var isEdit: boolean = this.curNote.value != null;
      this.curNote.next({
        id:
          this.curNote.value && this.curNote.value.id
            ? this.curNote.value.id
            : 0,
        title: "",
        body: this.addNewNote.value.body,
        files: this.curNote.value ? this.curNote.value.files : [],
        noteTypeid:this.noteTypeId,
        userId: this.globalUserService.userDetails.value.personTenantRoleId,
        userName:this.globalUserService.userDetails.value.userName
      });

      if(this.personFormId)
        this.curNote.next({...this.curNote.value,personFormId:this.personFormId});
    if(this.recentLinkClickEvent.element.personId)
        this.curNote.next({...this.curNote.value,candidateId:this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.personEventId.personTenantRoleId})
      
      var httpObservable = isEdit
        ? this.http.put(
            environment.baseUrl+`client/api/form/updatenote`,
            this.curNote.value
          )
        : this.http.post<any>(
          environment.baseUrl+"client/api/form/AddNote",
            this.curNote.value
          );
      httpObservable.subscribe((res) => {
        if (res) {
         this.getAppealNote()
          this.switch();
          this.curNote.next(null);
        }
      });
      this.isSubmit=true
    }


    if(this.noteTypeId == 7){
      var isEdit: boolean = this.curNote.value != null;
      this.curNote.next({
        id:
          this.curNote.value && this.curNote.value.id
            ? this.curNote.value.id
            : 0,
        title: "",
        body: this.addNewNote.value.body,
        files: this.curNote.value ? this.curNote.value.files : [],
        noteTypeid:this.noteTypeId,
        userId: this.globalUserService.userDetails.value.personTenantRoleId,
        userName:this.globalUserService.userDetails.value.userName
      });
      if(this.personFormId)
        this.curNote.next({...this.curNote.value,personFormId:this.personFormId});
    if(this.personId)
        this.curNote.next({...this.curNote.value,candidateId:Number(this.personId.personTenantRoleID),personEventId:Number(this.personId.personEventID),personFormId:Number(this.personId.personFormID)})
      
      var httpObservable = isEdit
        ? this.http.put(
            environment.baseUrl+`client/api/form/updatenote`,
            this.curNote.value
          )
        : this.http.post<any>(
          environment.baseUrl+"client/api/form/AddNote",
            this.curNote.value
          );
      httpObservable.subscribe((res) => {
        if (res) {
          this.getNote();
          this.switch();
          this.curNote.next(null);
        }
      });
      this.isSubmit=true
    }
    // if(this.noteTypeId == 9){
    //   var isEdit: boolean = this.curNote.value != null;
    //   this.curNote.next({
    //     id:
    //       this.curNote.value && this.curNote.value.id
    //         ? this.curNote.value.id
    //         : 0,
    //     title: "",
    //     body: this.addNewNote.value.body,
    //     files: this.curNote.value ? this.curNote.value.files : [],
    //     noteTypeid:this.noteTypeId,
    //     userId: this.globalUserService.userDetails.value.personTenantRoleId,
    //     userName:this.globalUserService.userDetails.value.userName
    //   });
    //   if(this.personFormId)
    //     this.curNote.next({...this.curNote.value,personFormId:this.personFormId});
    // if(this.personId)
    //     this.curNote.next({...this.curNote.value,candidateId:Number(this.personId.personTenantRoleID),personEventId:Number(this.personId.personEventID),personFormId:Number(this.personId.personFormID)})
      
    //   var httpObservable = isEdit
    //     ? this.http.put(
    //         environment.baseUrl+`client/api/form/updatenote`,
    //         this.curNote.value
    //       )
    //     : this.http.post<any>(
    //       environment.baseUrl+"client/api/form/AddNote",
    //         this.curNote.value
    //       );
    //   httpObservable.subscribe((res) => {
    //     if (res) {
    //       // this.getAppealNote();
    //       this.switch();
    //       this.curNote.next(null);
    //     }
    //   });
    //   this.isSubmit=true
    // }

    if(this.noteTypeId == 10){
      var isEdit: boolean = this.curNote.value != null;
      this.curNote.next({
        id:
          this.curNote.value && this.curNote.value.id
            ? this.curNote.value.id
            : 0,
        title: "",
        body: this.addNewNote.value.body,
        files:this.formGroupModel[0]._value && this.formGroupModel[0]._value.length ?                     
        this.formGroupModel.map((file:{value:string})=>{                         
          return {
            id: 0,
            name: file.value.split('|')[0],
            filePath: file.value.split('|')[1],
            mime: "",
            personFormNoteId: 0,
            deleted: false
          }
        }):[],
        noteTypeid:this.noteTypeId,
        userId: this.globalUserService.userDetails.value.personTenantRoleId,
        userName:this.globalUserService.userDetails.value.userName
      });
    if(this.personFormId){
      this.curNote.next({...this.curNote.value,personFormId:this.personFormId});    }
      else{
        this.curNote.next({...this.curNote.value,candidateId:Number(this.globalUserService.personEventId.personTenantRoleId),personEventId:Number(this.globalUserService.personEventId.personEventId),personFormId:0})
    }
      
      var httpObservable = isEdit
        ? this.http.put(
            environment.baseUrl+`client/api/form/updatenote`,
            this.curNote.value
          )
        : 
        this.http.post<any>(
          environment.baseUrl+"client/api/form/AddNote",
            this.curNote.value
          );
      httpObservable.subscribe((res) => {
        if (res) {
          this.getNAENotes();
          this.switch();
          this.curNote.next(null);
        }
      });
      this.isSubmit=true
    }
  }

 
  editor: Editor;
  toolbar: Toolbar = [
    ["bold", "italic"],
    ["underline", "strike"],
    [{ heading: ["h1", "h2", "h3", "h4", "h5", "h6"] }],
    ["align_left", "align_center", "align_right", "align_justify"],
  ];
  getNote() {
    if(this.globalUserService.userDetails.getValue().roleId == Roles.NAE){
      var fetchUrl = environment.baseUrl+`client/api/form/getnaenotes?noteTypeId=${this.noteTypeId}&personeventId=${this.personId.personEventID}`
      if(this.noteTypeId){
        this.http
        .get(fetchUrl)
        .subscribe((data: Note[]) => {
          this.NAEnotes = data;
          this.fileUploadModel = []
          this.fileUploadFormGroup = [];
          this.notes.forEach(note=>{
            this.fileUploadModel .push (
              new DynamicFileUploadModel({
                required: false,
                id: 'attachments',
                name: 'attachments',
                label: 'Supporting Documents',
                disabled: true,
                value: (Object.values(note.files)as any).map((x: any) => {
                  if (x.name && x.filePath)
                    return x.name + '|' + x.filePath
                  return ''
                }),
                accept: ['.pdf', '.doc', '.docx'],
                multiple: true,
                updateOn: DynamicFormHook.Change,
                showFileList: true,
                additional: {
                  appearance: 'outline'
                }
              })
            )
            this.fileUploadFormGroup.push(this.dynamicFormService.createFormGroup(this.fileUploadModel)) ;  
          })
         
          
        });
      }
      if(this.noteTypeId==6 ){
        this.disableTitle=false;
      }
      else{
        this.disableTitle=true;
      }
    }else{

      var fetchUrl = environment.baseUrl+`client/api/form/getnotes?noteTypeId=${this.noteTypeId}&candidateId=${this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.PersonTentantRoleId }`
      if(this.personFormId)
        fetchUrl += `&personFormId=${this.personFormId}`; 
      if(this.recentLinkClickEvent.element.personId || this.globalUserService.PersonTentantRoleId)
        fetchUrl += `&candidateId=${this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.PersonTentantRoleId}`
      if(this.noteTypeId){
      this.noteTypeId == 1 && (this.personFormId ==null && this.personFormId ==undefined)?null:  this.http
        .get(fetchUrl)
        .subscribe((data: Note[]) => {
          this.notes = data;
          this.fileUploadModel = [];
          this.fileUploadFormGroup = [];
          this.notes.forEach(note=>{
            this.fileUploadModel .push (
              new DynamicFileUploadModel({
                required: false,
                id: 'attachments',
                name: 'attachments',
                label: 'Supporting Documents',
                disabled: true,
                value: (Object.values(note.files)as any).map((x: any) => {
                  if (x.name && x.filePath)
                    return x.name + '|' + x.filePath
                  return ''
                }),
                accept: ['.pdf', '.doc', '.docx'],
                multiple: true,
                updateOn: DynamicFormHook.Change,
                showFileList: true,
                additional: {
                  appearance: 'outline'
                }
              })
            )
            this.fileUploadFormGroup.push(this.dynamicFormService.createFormGroup(this.fileUploadModel)) ;  
          })
         
          
        });
      }
      if(this.noteTypeId==6 ){
        this.disableTitle=false;
      }
      else{
        this.disableTitle=true;
      }
    }
 
   
  }

  getAppealNote() {
   this.noteTypeId = 9
      var fetchUrl = environment.baseUrl+`client/api/form/getnotes?noteTypeId=${this.noteTypeId}&candidateId=${this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.PersonTentantRoleId }`
      if(this.personFormId)
        fetchUrl += `&personFormId=${this.personFormId}`; 
      if(this.recentLinkClickEvent.element.personId || this.globalUserService.PersonTentantRoleId)
        fetchUrl += `&candidateId=${this.recentLinkClickEvent.element.personTenantRoleId !=undefined?this.recentLinkClickEvent.element.personTenantRoleId:this.globalUserService.PersonTentantRoleId}`
      if(this.noteTypeId){
        this.http
        .get(fetchUrl)
        .subscribe((data: Note[]) => {
          this.AppealNotes = data;
          this.fileUploadModel = [];
          this.fileUploadFormGroup = [];
          this.AppealNotes.forEach(note=>{
            this.fileUploadModel .push (
              new DynamicFileUploadModel({
                required: false,
                id: 'attachments',
                name: 'attachments',
                label: 'Supporting Documents',
                disabled: true,
                value: (Object.values(note.files)as any).map((x: any) => {
                  if (x.name && x.filePath)
                    return x.name + '|' + x.filePath
                  return ''
                }),
                accept: ['.pdf', '.doc', '.docx'],
                multiple: true,
                updateOn: DynamicFormHook.Change,
                showFileList: true,
                additional: {
                  appearance: 'outline'
                }
              })
            )
            this.fileUploadFormGroup.push(this.dynamicFormService.createFormGroup(this.fileUploadModel)) ;  
          })
         
          
        });
      }
      if(this.noteTypeId==6 ){
        this.disableTitle=false;
      }
      else{
        this.disableTitle=true;
      }
    
 
   
  }

  getNAENotes(){
    let noteTypeId = this.noteTypeId == undefined ?10:this.noteTypeId
    var fetchUrl = environment.baseUrl+`client/api/form/getnaenotes?noteTypeId=${noteTypeId}&personeventId=${this.recentLinkClickEvent.element.personEventId}`
    if(this.noteTypeId){
      this.http
      .get(fetchUrl)
      .subscribe((data: Note[]) => {
        this.NAEnotes = data;
        this.fileUploadModel = [];
        this.fileUploadFormGroup = [];
        this.NAEnotes.forEach(note=>{
          this.fileUploadModel .push (
            new DynamicFileUploadModel({
              required: false,
              id: 'attachments',
              name: 'attachments',
              label: 'Supporting Documents',
              disabled: true,
              value: (Object.values(note.files)as any).map((x: any) => {
                if (x.name && x.filePath)
                  return x.name + '|' + x.filePath
                return ''
              }),
              accept: ['.pdf', '.doc', '.docx'],
              multiple: true,
              updateOn: DynamicFormHook.Change,
              showFileList: true,
              additional: {
                appearance: 'outline'
              }
            })
          )
          this.fileUploadFormGroup.push(this.dynamicFormService.createFormGroup(this.fileUploadModel)) ;  
        })
       
        
      });
    }
    if(this.noteTypeId==6 ){
      this.disableTitle=false;
    }
    else{
      this.disableTitle=true;
    }
  }


  delete(note: Note) {
    this.http
      .delete(
        environment.baseUrl+`/client/api/form/deletenote?personFormNoteId=${note.id}`
      )
      .subscribe((res) => {
        if (res) {
          this.getNote();

          this.httpClient
          .get(environment.baseUrl +`client/api/form/getnotes?personFormId=${this.recentLinkClickEvent.element.personFormId}&noteTypeId=${2}`)
          .subscribe((data:any) => {
            if(data && data.length>0){
              
              this.isSubmit=true;
            }
            else{
              this.isSubmit=false;
            }
          });

        }
      });    
  }

  getnaeExamcomments(){
    this.http.get(`${environment.baseUrl}client/api/exam/GetPersonEventMetaInfo?examType=1&personEventId=${this.recentLinkClickEvent?.element?.personEventId}&incidentTypeId=0`).subscribe((data:Array<{incidentDescription:string,incidentComments:string,createdOn:string,userName:string}>)=>{
       if(data){
        this.ExamNaeComments =[]
        data.forEach((x)=>{
            if (x.incidentDescription.includes('/')) {
              const parts = x.incidentDescription.split('/');
              this.splitBeforeSlash = parts[0]?.trim() || '';
              this.splitAfterSlash = this.transformString(`Steps ${parts[1]?.trim() || ''}`);
              this.ExamNaeComments.push({incidentDescription:this.splitBeforeSlash,value:this.splitAfterSlash,incidentComments:x.incidentComments,lastUpdatedOn:x.createdOn,userName:x.userName})
            } else {
              this.splitBeforeSlash = x.incidentDescription;
              this.splitAfterSlash = '';  
              this.ExamNaeComments.push({incidentDescription:this.splitBeforeSlash,value:this.splitAfterSlash,incidentComments:x.incidentComments,lastUpdatedOn:x.createdOn,userName:x.userName})
            }

        })
       }
    })
  }

  getMeasurResults(){
    this.http.get(`${environment.baseUrl}client/api/form/grievance-form/measurement-skills?personEventId=${this.recentLinkClickEvent?.element?.personEventId}`).subscribe((data:Array<{candidateFinalValue:string,proctorFinalValue:string,label:string}>)=>{
       if(data.length > 0){
        this.dataSource =[]
          data.forEach((x)=>{
            x.candidateFinalValue !='' && x.proctorFinalValue !='' &&  x.candidateFinalValue !=null && x.proctorFinalValue !=null &&  x.candidateFinalValue !=undefined && x.proctorFinalValue !=undefined?this.dataSource.push({candidateFinalValue:x.candidateFinalValue,proctorFinalValue:x.proctorFinalValue,label:x.label}):null
          })
          this.dataSource = [...this.dataSource];
           
       }
    })

  }
 
  transformString(input: string): string {
    // Define the pattern to look for
    const pattern = /^(Steps\s\d+)/;

    // Replace the pattern with itself followed by a colon
    return input.replace(pattern, '$1:');
  }

  edit(note: Note) {
    this.addNewNote.patchValue({
      title: "",
      body: note.body,
    });
    this.curNote.next(note);
    this.switch(false);
  }
  closeSideBar(){
  this.sidenavRef.toggle();
  }

  toPdf(i:any) {
    const dashboard = document.getElementById(`fullDiv${i}`);
    const dashboardHeight = dashboard.clientHeight;
    const dashboardWidth = dashboard.clientWidth;
    const options = { background: 'white', width: dashboardWidth, height: dashboardHeight, allowTaint : true,
    useCORS: true
     };

    domtoimage.toPng(dashboard, options).then((imgData) => {
         const doc = new jsPDF(dashboardWidth > dashboardHeight ? 'l' : 'p', 'pc', [dashboardWidth, dashboardHeight],false);
         doc.internal.scaleFactor = 1;
         const imgProps = doc.getImageProperties(imgData);
         const pdfWidth = doc.internal.pageSize.getWidth()*0.3;
         const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

         doc.addImage(imgData, 'jpeg', 0, 0, pdfWidth, pdfHeight,"NONE",);
         doc.save('Notes Summary.pdf');
    });
}

}
export interface Note {
  id?: number;
  title: string;
  body: string;
  personFormId?: number;
  files?: File[] | any;
  noteTypeid: number;
  candidateId?:number;
  userId:number,
  userName:string
  personEventId?:number
}
export class File {
  id: number;
  name: string;
  filePath: string;
  mime: string;
  personFormNoteId: number;
  deleted?: any;
}


