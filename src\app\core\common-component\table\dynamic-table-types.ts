import {
  moduleTypes,
  requestDetails,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { popupData } from "../pop-up/pop-up.types";
import { PageEvent } from "@angular/material/paginator";
export interface column {
  //  id can only be array in case of celltype being link,text,number
  id: string | Array<string>;
  name: string;
  cellType?:
    | "action"
    | "status"
    | "text"
    | "number"
    | "toggle"
    | "link"
    | "date"
    | "array"
    | "onlydate"
    | "dobtype"
    | "json"
    | "Date"
    | "regDate";
  cellDataMapping?: any;
  dateCellProps?:
    | "short"
    | "medium"
    | "long"
    | "full"
    | "shortDate"
    | "mediumDate"
    | "fullDate"
    | "shortTime"
    | "mediumTime"
    | "longTime"
    | "fullTime";
  cellArrayProps?: Array<string>;
  statusUpdateDetails: requestDetails;
  linkMetaData?: linkMetaData;
  actions?: Array<action>;
  statuses?: any;
  statusesboolean?: any;
  headerType?: "normal" | "dropdown" | "sort" | "checkbox" | "visible";
  dropDownActions?: Array<string>;
  // these string identifiers will be present in the dropdowns and the event emitter will contain a key with this value to process it
  headerAlign?: "center" | "right" | "left";
  cellAlign?: "center" | "right" | "left";
  headerCssClasses?: Array<string>;
  cellCssClasses?: Array<string>;
  visible?: boolean;
  visibilityRule?: Array<VisibilityRule>;
  headerDisable?: boolean;
  filterable?: boolean;
  isFilter?: boolean;
  showOnlyData?: boolean;
}

export interface tableOptions {
  tableName?: string;
  showPaginator: boolean;
  pageSizeOptions?: Array<number>;
  pageSize?: number;
  showSearch: boolean;
  showMatTabs?: boolean;
  showColumnFilter?: boolean;
  showColumnWiseFilter?: boolean;
  matTabs?: Array<matTab>;
  showAddButton?: Array<boolean>;
  addButtonTexts?: Array<string>;
  showHeaderRow?: boolean;
  horizontalScroll?: boolean;
  verticalScroll?: boolean;
  addPopupData?: Array<popupData>;
  headerRowCssClasses?: Array<string>;
  cellRowCssClasses?: Array<string>;
  isPaginationServerSide?: Array<boolean>;
  isSearchServerSide?: Array<boolean>;
  isStateSearchServerSide?: boolean;
  showStateSelectionDropdown?: boolean;
  ShowDownload?: boolean;
  ShowDownloadPayment?: boolean;
  showSlectdropstatedataattributes?: string;
  ShowBulkImport?: boolean;
  ShowFilenameFilter?: boolean;
  showAddplaceorder?: boolean;
  showOrderStatusDropdown?: boolean;
  ShowGreivanceStatus?: boolean;
  showExamModeDropdown?: boolean;
  showTestCenterPreferenceDropdown?: boolean;
}

export interface linkMetaData {
  navigateToType: moduleTypes;
  navigateToLabel?: string;
  navigateTo: string;
  eventDataUseful: boolean;
  requestDetails?: requestDetails;
}

export interface matTab {
  id: number;
  name: string;
  visibilityRule?: Array<VisibilityRule>;
}
export interface status {
  icon: string;
  color: string;
  text: string;
  background: string;
}
export interface action {
  type: actionTypes | string;
  icon?: string;
  color?: string;
  tooltip?: string;
  requestDetails?: requestDetails[];
  visibilityRule?: Array<VisibilityRule>;
}

export interface VisibilityRule {
  notApplied?: boolean;
  operator?: Operators;
  // row json object key to be used
  extractedFromGlobal?: boolean;
  extractedFromElement?: boolean;
  elementProperty?: string;
  shouldHaveValue?: any;
  compareWithOperator?: comparisionOperators;
}

export enum Operators {
  AND = "&&",
  OR = "||",
}
export enum comparisionOperators {
  EQUAL = "==",
  NOTEQUAL = "!=",
  GREATERTHAN = ">",
  GREATERTHANEQUAL = ">=",
  LESSTHAN = "<",
  LESSTHANEQUAL = "<=",
}
export enum actionTypes {
  Edit = "edit",
  Delete = "delete",
  View = "view",
  Link = "link",
  Custom = "custom",
  RichTextView = "richtextview",
  ComponentView = "componentview",
  email = "email",
  Person = "person",
  StateemulatePerson = "StateemulatePerson",
  DeleteApp = "deleteApp",
  FormView = "view",
  AddFormView = "addFormView",
  FormView1 = "view",
  Voucher = "Voucher",
  Employee_delete = "delete_employee",
  Refresh_Noshow_to_cancel = "Noshow_to_cancel",
  Roast = "Skills",
  Refund = "Refund_Payment",
  Elapsed = "Update_Elapsed",
  Transfer_Candidate = "Tansfer_exam",
  Import = "Candidate_bulk_import",
  omrsheetview = "overview",
  Bookletview = "bookletview",
  Refund_payment = "Refund_payment",
  Certificdown = "Certificdown",
  Preregistration_view = "Preregistration_view",
  suspension_List_remove = "suspension_List_remove",
}
export class performedAction {
  action: action;
  column: column;
  element: any;
  rowIndex: number;
}

export class cellClickEvent {
  column?: column;
  element: any;
  rowIndex: number;
}
export class linkClickEvent {
  column?: column;
  element: any;
  rowIndex: number;
  action?: any;
}
export class headerClickEvent {
  column: column;
}
export class headerDropdownSelectionEvent {
  column: column;
  dropdownKey: string;
}
export class columnsEditEvent {
  column: column;
  colIndex: number;
  removed: boolean;
}

export class checkboxClickEvent {
  selection: any[];
}

export class accMerge {
  selected: any[];
}

export interface tabSelectionEvent {
  tabIndex: number;
}

export const tableStatuses: status[] = [
  {
    icon: "cancel",
    color: "red",
    text: "Rejected",
    background: "rgba(255,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "green",
    text: "Approved",
    background: "rgba(0,128,0, 0.2)",
  },
  {
    icon: "cancel",
    color: "orange",
    text: "Pending",
    background: "rgba(255,239,213, 0.8)",
  },
  {
    icon: "check_circle",
    color: "green",
    text: "Submitted",
    background: "rgba(0,128,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "grey",
    text: "Drafted",
    background: "rgba(0,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "grey",
    text: "Yet to Recieve",
    background: "rgba(0,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "orange",
    text: "Change Request",
    background: "rgba(255,239,213, 0.8)",
  },
  {
    icon: "check_circle",
    color: "grey",
    text: "Saved",
    background: "rgba(0,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "red",
    text: "InActive",
    background: "rgba(0,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "green",
    text: "Active",
    background: "rgba(0,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "green",
    text: "Completed",
    background: "rgba(0,128,0, 0.2)",
  },
  {
    icon: "cancel",
    color: "red",
    text: "Error",
    background: "rgba(255,0,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "green",
    text: "Ordered",
    background: "rgba(0,128,0, 0.2)",
  },
  {
    icon: "check_circle",
    color: "orange",
    text: "Shipped",
    background: "rgba(255,239,213, 0.8)",
  },
  {
    icon: "check_circle",
    color: "orange",
    text: "Uploaded",
    background: "rgba(255,239,213, 0.8)",
  },
];

export interface tablePageEvent {
  searchText: string | null;
  searchColumn: string | null;
  pageEvent: PageEvent;
}

export enum examstatus {
  scheduling_error = 68,
  Exam_Cancelled = 9,
  Exam_Cancelled_by_Proctor = 64,
  Noshow = 10,
  ExamCompleted = 8,
  ExamSchedule = 1,
  Event_Cancelled = 77,
}
export enum state {
  Mississippi = 6,
  South_Carolina = 7,
  District_of_Columbia = 5,
  Colorado = 9,
}

export const ModuleID = {
  Suuport_Ticket: "support-ticket",
  Problem_Report: "problem-report",
  CandidateDetails: "candidate-details",
  TrainingInstitueDetails: "training-institute-details",
  EmployerDetails: "employer-details",
};

export const ColumnID = {
  TicketId: "TicketId",
  ReportId: "ReportId",
};
export const tabs = [
  "Application Details",
  "Exam Details",
  "Registration Details",
  "Problem Report",
  "Employer History",
  "Candidate History",
];
