import { ComponentFixture, TestBed } from '@angular/core/testing';

import { YesNoValidationComponent } from './yes-no-validation.component';

describe('YesNoValidationComponent', () => {
  let component: YesNoValidationComponent;
  let fixture: ComponentFixture<YesNoValidationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ YesNoValidationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(YesNoValidationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
