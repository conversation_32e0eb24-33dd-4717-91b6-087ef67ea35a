
import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Manage Employer",
      moduleID: "manage-employer",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "demographicManageIcon",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  showMatTabs: false,
                  showAddButton: [true],
                  addButtonTexts: ["Add Employer Account"],
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  showStateSelectionDropdown:false,
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "add-employer",
                      title: ["Add Employer"],
                      id: "add-employer",
                      buttons: [
                        {
                          buttonText: "Submit",
                          requestDetails: {
                            fetchUrl: ["client", "api", "user"],
                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "checkbox",
                      name: "Checkbox",
                      cellType: "text",
                      headerType: "checkbox",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-4"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "employerName",
                      name: "Employer Name",
                      cellType: "text",
                 
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                  
                    <tableTypes.column>{
                      visible: true,
                      id: "employerCode",
                      name: "Employer Code",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                   
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "state",
                      name: "State",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "createdOn",
                      name: "Created on ",
                      cellType: "date",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "isEnable",
                      name: "Enable/Disable",
                      showOnlyData:true,
                      cellType: "toggle",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [""],
                      
                      statusUpdateDetails: {
                        fetchUrl: ["client", "api", "state", "status"],
                        requestBody: null,
                        requestParams: null,
                        method: "PUT",
                      },
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "edit",
                          type: tableTypes.actionTypes.Edit,
                          tooltip: "Edit",
                          color: "#7d7d7d",
                          // visibilityRule: [{
                          //   operator: tableTypes.Operators.AND,
                          //   elementProperty: 'emailId',
                          //   compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                          //   shouldHaveValue:null,
                          // }],
                          requestDetails: [
                            {
                              fetchUrl: ["client", "api", `user`],
                              requestBody: null,
                              requestParams: [],
                              method: "PUT",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "View",
                          color: "#7d7d7d",
                        },
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Offspring,
                        navigateTo: "employer-details",
                        eventDataUseful: false,
                        navigateToLabel: "employer-details",
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },    
                    
                  
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api","api", "Employer", "list"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNo",
                        paramValue :1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
