import { AfterViewInit, Component, HostListener, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Observable } from "rxjs";
import { map, startWith } from "rxjs/operators";
import { GlobalUserService } from "src/app/core/global-user.service";
import { HttpService } from "src/app/core/http.service";
import {
  init,
  Action,
  PinboardEmbed,
  EmbedEvent,
  AuthType,
  SearchEmbed,
  AppEmbed,
  logout
} from "@thoughtspot/visual-embed-sdk";
import { Action as NgrxAction, Store } from "@ngrx/store";
import {
  get_decodeInfo,
  get_reportJSON,
  get_userDetails,
} from "../state/shared/shared.selectors";
import { decodedIdentityToken } from "../state/shared/shared.state";
import {
  reportSpinBoardList,
  setReportToken,
} from "../state/shared/shared.actions";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { SnackbarService } from "src/app/core/snackbar.service";

@Component({
  selector: "app-report",
  templateUrl: "./report.component.html",
  styleUrls: ["./report.component.scss"],
})
export class ReportComponent implements OnInit, AfterViewInit {
  gridColumns = 4;
  selectId: number
  selectedName: string
  duplicate_tableau_data: any;
  mainUrl: any = null;
  tableau_data: Array<any> = [];
  selectedUser: any;
  emailId = "";
  token = "";
  personTenantRoleId: number;
  embed: any;
  idName: any = [];
  headerArray: Array<any>
  headerArrays:Array<any>
  viewIsOn: boolean = false;
  viewSearch: boolean = false;
  inputData: FormGroup;
  isOperationStaff: boolean = false;

  constructor(
    private global: GlobalUserService,
    private http: HttpService,
    private store: Store,
    private https: HttpClient,
    private snackbar:SnackbarService
    // private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    // this.store.select(getCurrentRoute).subscribe((url) => {
    //   if (url.url == "/self-service") {
    //     this.viewSearch = true;
    //     this.searchBoard();
    //   }
    // });

    this.store.select(get_userDetails).subscribe((data) => {
      if (data && data.roles[0].roleId == 17) {
        this.isOperationStaff = true;
      }
    });

    this.inputData = new FormGroup({
      data: new FormControl(""),
    });
    this.store
      .select(get_decodeInfo)
      .subscribe((data: decodedIdentityToken) => {
        if (data) {
          this.emailId = this.global.TypeValue!='' && this.global.TypeValue!=undefined ?data.email:this.snackbar.EmailVerified.email;         
          this.store.dispatch<NgrxAction>(
            reportSpinBoardList({ username: this.global.TypeValue!='' && this.global.TypeValue!=undefined ?data.email:this.snackbar.EmailVerified.email,org_id:0,dependent_object_version:"V2",include_auto_created_objects:false,include_dependent_objects:false,include_details:false,include_headers:true,include_hidden_objects:false,include_incomplete_objects:false,include_visualization_headers:false,record_offset:0,record_size:100.,include_stats:false })

          );
          if (!sessionStorage.getItem("reportJSON")) {
            this.store.dispatch<NgrxAction>(
              reportSpinBoardList({ username: this.global.TypeValue!='' && this.global.TypeValue!=undefined?data.email:this.snackbar.EmailVerified.email,org_id:0,dependent_object_version:"V2",include_auto_created_objects:false,include_dependent_objects:false,include_details:false,include_headers:true,include_hidden_objects:false,include_incomplete_objects:false,include_visualization_headers:false,record_offset:0,record_size:100.,include_stats:false })              );
          } else {
            this.store.dispatch<NgrxAction>(
              setReportToken({
                json: JSON.parse(sessionStorage.getItem("reportJSON")),
              })
            );
          }
        }
      });

  }

  searchborad = true;

  // ngAfterViewInit() {
  //   this.store.select(get_reportJSON).subscribe((report) => {
  //     if(report){
  //       let a=[];
  //       this.token = report["Message"]["trutedtoken"];
  //       //this.loadpinboard(report["Message"]["headers"][0]["id"]);
  //       report["Message"]["headers"].forEach(element=>{
  //         a.push(element)
  //       })
  //       this.loadpinboard(a);
  //     }
  //   });
  //   // this.loadpinboard();
  // }

  public ngAfterViewInit(): void {
    this.store.select(get_reportJSON).subscribe((report:any) => {
      if (report) {
        // this.token = report["Message"]["trutedtoken"];
        this.headerArray =[...report];


        this.headerArray = this.headerArray.sort((a: any, b: any) => {
          if (a.metadata_name && b.metadata_name) {
            let _a = a.metadata_name.toUpperCase();
            let _b = b.metadata_name.toUpperCase();
            return _a < _b ? -1 : _a > _b ? 1 : 0;
          }
        });



      }
    });
    init({
      thoughtSpotHost: /*param-start-hosturl*/ `${environment.reportUrl}` /*param-end-hosturl*/,
      authType: AuthType.TrustedAuthTokenCookieless,
      username: this.snackbar.EmailVerified.email,
      callPrefetch: true,
      autoLogin: true,
      disableLoginRedirect: true,
      getAuthToken: () => this.getToken(),
    });
  }

  back() {
    this.viewIsOn = false;
  }



  getToken(): any {
    let token: any;
    return new Promise((resolve, reject) => {
      this.https
        .post("https://reportapiv2.examroom.ai/auth/trustedtoken", {
          username: this.emailId,
          org_id:0
        })
        .subscribe((data: any) => {
          token = data;
          resolve(token);
        });
    });
  }

  searchBoard() {
    // Initialize embed configuration

    setTimeout(() => {
      const embed = new SearchEmbed("#searchembed", {
        frameParams: {
          width: "100%",
          height: "100%",
        } /*param-start-pinboardId*/ /*param-end-modifyActions*/,
        /*param-start-modifyActions*/
        collapseDataSources: true,
        

        /*param-end-runtimeFilters*/
        hiddenActions: [
          Action.Save,
          Action.MakeACopy,
          Action.AddFilter,
          Action.PinboardInfo,
          // Action.CustomAction,
          Action.CopyLink,
        ],

    
        /*param-end-vizId*/
        /*param-end-pinboardId*/
        /*param-start-vizId*/
        /*param-start-runtimeFilters*/
      });
      // Functions to show/hide a loader while the iframe loads
      const showLoader = () => {
        document.getElementById("loader").style.display = "block";
      };

      const hideLoader = () => {
        document.getElementById("loader").style.display = "none";
      };

      const showAuthExpired = () => {
        document.getElementById("authExpiredBanner").style.display = "flex";
      };

      // Functions to show or hide No data images

      const showNoDataImage = () => {
        document.getElementById("no-data").style.display = "block";
      };

      const hideNoDataImage = () => {
        document.getElementById("no-data").style.display = "none";
      };

      hideNoDataImage();

      // this.embed.forEach((embedlist) => {
      embed
        // Register event listeners
        .on(EmbedEvent.Init, showLoader)
        .on(EmbedEvent.Load, hideLoader)
        .on(
          EmbedEvent.AuthExpire,
          showAuthExpired
        ) /*param-end-customActionHandle*/
        /*param-start-customActionHandle*/.on(EmbedEvent.Error, () => {
          showNoDataImage();
          hideLoader();
        })
        // // Render a viz within a pinboard
        .render();
    }, 500);
  }

  loadpinboard(view) {
    this.selectId = view
    this.viewIsOn = true;
    // Initialize embed configuration
    // init({
    //   thoughtSpotHost: /*param-start-hosturl*/ `${environment.reportUrl}` /*param-end-hosturl*/,
    //   authType: AuthType.AuthServer,
    //   username: this.emailId,
    //   getAuthToken: () => Promise.resolve(this.token),
    // });

    // Instantiate class to embed a visualization
    // let listEmbed: any = [];
    // this.idName = [];
    // view.forEach((element, index) => {
    //   let id: string = "embed";
    //   this.idName.push(element.name);
    //   listEmbed.push(
    //     new PinboardEmbed("#" + id + index, {
    //       frameParams: {
    //         width: "100%",
    //         height: "100%",
    //       } /*param-start-pinboardId*/ /*param-end-modifyActions*/,
    //       /*param-start-modifyActions*/

    //       pinboardId: element.id /*param-end-runtimeFilters*/,
    //       hiddenActions: [
    //         Action.Save,
    //         Action.MakeACopy,
    //         Action.AddFilter,
    //         Action.PinboardInfo,
    //         Action.CustomAction,
    //         Action.CopyLink,
    //       ],

    //       /*param-end-vizId*/
    //       /*param-end-pinboardId*/
    //       /*param-start-vizId*/
    //       /*param-start-runtimeFilters*/
    //     })
    //   );
    // });
    // this.embed = listEmbed;
    setTimeout(() => {
      const embed = new PinboardEmbed("#embed", {
        frameParams: {
          width: "100%",
          height: "100%",
        } /*param-start-pinboardId*/ /*param-end-modifyActions*/,
        /*param-start-modifyActions*/

        pinboardId: view /*param-end-runtimeFilters*/,
        hiddenActions: [
          Action.Save,
          Action.MakeACopy,
          Action.AddFilter,
          Action.PinboardInfo,
          // Action.CustomAction,
          Action.CopyLink,
        ],

      //   visibleActions:[
      //     Action.Edit,
      //     Action.DownloadAsCsv,
      //     Action.DownloadAsXlsx,
      //  ]
        /*param-end-vizId*/
        /*param-end-pinboardId*/
        /*param-start-vizId*/
        /*param-start-runtimeFilters*/
      });
      // Functions to show/hide a loader while the iframe loads
      const showLoader = () => {
        document.getElementById("loader").style.display = "block";
      };

      const hideLoader = () => {
        document.getElementById("loader").style.display = "none";
      };

      const showEdit =()=>{
        let element =document.getElementById('edit')
        element.removeAttribute("disabled");
      }

      const showAuthExpired = () => {
        document.getElementById("authExpiredBanner").style.display = "flex";
      };

      // Functions to show or hide No data images

      const showNoDataImage = () => {
        document.getElementById("no-data").style.display = "block";
      };

      const hideNoDataImage = () => {
        document.getElementById("no-data").style.display = "none";
      };

      hideNoDataImage();

      // this.embed.forEach((embedlist) => {
      embed
        // Register event listeners
        .on(EmbedEvent.Init, showLoader)
        .on(EmbedEvent.Load, hideLoader)
        .on(
          EmbedEvent.AuthExpire,
          showAuthExpired
        ) /*param-end-customActionHandle*/
        /*param-start-customActionHandle*/.on(EmbedEvent.Error, () => {
          showNoDataImage();
          hideLoader();
        })
        // // Render a viz within a pinboard
        .render();
    }, 100);

    // });
  }

  selectedItems(view) {
    this.selectedName = view.metadata_name  }

  appembed() {
    // init({
    //   thoughtSpotHost: /*param-start-hosturl*/ `${environment.reportUrl}` /*param-end-hosturl*/,
    //   authType: AuthType.AuthServer,
    //   username: this.emailId,
    //   callPrefetch: true,
    //   getAuthToken: () => Promise.resolve(this.token),
    // });
    setTimeout(() => {
      const embed1 = new AppEmbed("#appembed", {
        frameParams: {
          width: "100%",
          height: "100%",
        } /*param-start-pinboardId*/ /*param-end-modifyActions*/,
        /*param-start-modifyActions*/
        //collapseDataSources: true,
        /*param-end-runtimeFilters*/
        hiddenActions: [
          Action.Save,
          Action.MakeACopy,
          Action.AddFilter,
          Action.PinboardInfo,
          // Action.CustomAction,
          Action.CopyLink,
        ],

      
        /*param-end-vizId*/
        /*param-end-pinboardId*/
        /*param-start-vizId*/
        /*param-start-runtimeFilters*/
      });
      // Functions to show/hide a loader while the iframe loads
      const showLoader = () => {
        document.getElementById("loader").style.display = "block";
      };
      const hideLoader = () => {
        document.getElementById("loader").style.display = "none";
      };
      const showAuthExpired = () => {
        document.getElementById("authExpiredBanner").style.display = "flex";
      };
      // Functions to show or hide No data images
      const showNoDataImage = () => {
        document.getElementById("no-data").style.display = "block";
      };
      const hideNoDataImage = () => {
        document.getElementById("no-data").style.display = "none";
      };
      hideNoDataImage();
      // this.embed.forEach((embedlist) => {
      embed1
        // Register event listeners
        .on(EmbedEvent.Init, showLoader)
        .on(EmbedEvent.Load, hideLoader)
        .on(
          EmbedEvent.AuthExpire,
          showAuthExpired
        ) /*param-end-customActionHandle*/
        /*param-start-customActionHandle*/.on(EmbedEvent.Error, () => {
          showNoDataImage();
          hideLoader();
        })
        // // Render a viz within a pinboard
        .render();
    }, 500);
  }

  // viewReport(view) {
  //   (document.getElementById("selction") as HTMLInputElement).blur();
  //   this.initViz(view.workbookname, view.viewName);
  // }

  // initViz(contentUrl, viewUrlName) {}

  // filterUsers() {
  //   if (this.selectedUser == "") {
  //     this.duplicate_tableau_data = this.tableau_data;
  //   }
  //   this.duplicate_tableau_data = this.tableau_data.filter((item) =>
  //     item.viewName.toLowerCase().includes(this.selectedUser.toLowerCase())
  //   );
  // }

  enable() {
    ;
    if (this.viewSearch == false) {
      this.viewSearch = true;
      this.searchBoard();
    }
  }

  enable1() {
    if (this.viewSearch == false) {
      this.viewSearch = true;
      this.appembed();
    }
  }
  ngOnDestroy() {
    logout()
  }
}
