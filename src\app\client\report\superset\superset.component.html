
<div class="w-full">
    <form [formGroup]="inputData">
    <div  class="bg-card rounded border search cardBorder" fxFlex="500px"
  fxFlex.lt-md="500px" fxLayout="row" fxLayoutAlign="end center">
  <input matInput formControlName="data" (keyup)="reportsearch($event)"
  class="px-4 py-2 border-0 outline-none w-full bg-transparent cursor-pointer" 
  placeholder="Global Search">
  <mat-icon class="cursor-pointer mr-4 mb-2" (click)="search()" >search</mat-icon>
  </div>
  </form>
  </div>
  
  <div fxLayout="row wrap" fxLayoutGap="10px grid">
    <div class="justify-start mt-2" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
      *ngFor="let view of headerArrays">
  
      <div class="card p-2 h-full shadow-none cardBorder flex justify-between cursor-pointer"
        (click)="loadpinboard(view);selectedItems(view)" [ngClass]="
        selectId == view.id
          ? 'selectedView'
          : 'NonselectedView' ">
          {{view.dashboard_title }} <mat-icon class="cursor-pointer ">keyboard_arrow_right</mat-icon>
      </div>
      
    </div>
  
  </div>
  
  <div class="flex justify-around mt-2 pb-4  SelectedName" *ngIf="viewSearch " >
    {{selectedName}}
  
  </div>
  
  <!-- <div *ngIf="viewSearch" >
      <iframe [src]="embedUrl"   width="100%" height="800"
      frameborder="0"
      allow="camera; microphone;" sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"></iframe>
  
  </div> -->
  