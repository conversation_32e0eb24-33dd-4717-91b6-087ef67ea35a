import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";
import { FormTypes } from "src/app/client/application/application.types";


export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Offspring,
      name: "View Details",
      moduleID: "duplicate-certificates-details",
      nestingType: nestingTypes.RowsInColumns,
      iconRef: "",
      routeRef: "/loader",
      class: "",
      gdColumns: "1fr",
      gdColumnsltlg: "1fr",
      gdColumnsltmd: "1fr",
      gdColumnsltsm: "1fr",
      gdGap: "12px",
      columns: [
        {
          class: "flex-col-important",
          gdColumn: "1/1",
          gdColumnltlg: "1/1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "space-between stretch",
          fxFlex: "auto",
          rows: [
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicSingleEntryTables,
                displayedColumns: [
                  [
                    <singleEntryTableTypes.column>{
                      id: ["firstName", "middleName", "lastName"],
                      name: "Full Name",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "ssn",
                      name: "Security Number",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "id",
                      name: "Candidate ID",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "address",
                      name: "Address",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "emailId",
                      name: "Email",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "phoneNumber",
                      name: "Contact Number",
                      editable: false,
                    },
                  ],
                ],
                requestDetails: [
                  {
                    // fetchUrl: ["account", "client", "user"],
                    fetchUrl: ["client", "api", "account", "getuser"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personTenantRoleId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: "duplicate-certificates"
              },
            },
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: [],
                  cellRowCssClasses: ["my-3 shadow"],
                  showPaginator: false,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: false,
                  showAddButton: [false, false, false],
                  showMatTabs: false,
                  showSearch: false,
                  verticalScroll: true,
                  horizontalScroll: true,
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "RegistryName",
                      name: "Registration Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "CertNumber",
                      name: "Registration Number",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "EffectiveDate",
                      name: "Issue Date",
                      cellType: "date",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "ExpirationDate",
                      name: "Expiry Date",
                      cellType: "date",
                      statuses: tableTypes.tableStatuses,
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: ["text-xs"],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "RegistryStatus",
                      name: "status",
                      cellType: "status",
                      statuses: tableTypes.tableStatuses,
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent", 'text-center'],
                      cellCssClasses: ["text-xs", 'text-center'],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "View",
                          color: '#7d7d7d',
                          requestDetails: [
                            {
                              fetchUrl: [
                                "registry",
                                "api",
                                `certificate`,
                                `get-url`,
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: 'certPath',
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "Certpath"
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: 'certNo',
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "CertNumber"
                                }
                              ],
                              method: "GET",
                            },
                          ],
                          backwardsDataDependency: true,
                          grandParentReference: "manage-registry",
                        },
                      ],
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-12",],
                      cellCssClasses: ["cursor-pointer"],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: 'certificate',
                        eventDataUseful: false,
                        navigateToLabel: 'certification',
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [],
                          method: "GET",
                        },
                      },
                    },
                  ],
                ],
                requestDetails: [{
                  fetchUrl: ['registry', 'api', 'certificate', 'get-details'],
                  requestBody: null,
                  requestParams: [
                    {
                      paramType: paramTypes.Optional,
                      paramName: 'registryId',
                      paramValue: null,
                      extractedFromElement: true,
                      elementPropertyToBeExtracted: "personFormRegistryId",
                    },
                  ],
                  method: 'GET'
                }],
                backwardsDataDependency: true,
                grandParentReference: "duplicate-certificates"
              }
            },
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicForms,
                formOptions: {
                  formDisabled: true,
                  approveRejectAllowed: true,
                  changeRequestAllowed: false,
                  addORviewNotesAllowed: true,
                },
                requestDetails: [
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      `form`,
                      `formsbyformtypeid`,
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "formTypeId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "formTypeId",
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "clientId",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: 'clientORtenantId'
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "eligibilityId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted:
                          "eligibilityRouteId",
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "form",
                      "personform",
                      "list"
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personFormId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted:
                          "personFormId"
                      }
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "form",
                      `assignpersonform`,
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personFormId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personFormId",
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personTenantRoleId",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      }
                    ],
                    method: "POST",
                  }
                ],
                backwardsDataDependency: true,
                grandParentReference: "duplicate-certificates"
              },
            }
          ],
        },
        {
          class: "grid",
          gdColumn: "1 / 1",
          gdColumnltlg: "1 / 1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "start center",
          fxFlex: "flex-grow",
          rows: [
            {
              class: "appProcessLog w-full h-full items-initial",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start",
              fxFlex: "none",
              fxFlexltmd: "auto",

              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicProcessLogs,
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "form", "personformlogs"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personFormId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personFormId",
                      },
                    ],
                    method: "GET",
                  },
                ],
                widgetHeader: "Duplicate Process",
                backwardsDataDependency: true,
                grandParentReference: "duplicate-certificates"
              },
            }
          ],
        },

      ],
    }
  ]
}
