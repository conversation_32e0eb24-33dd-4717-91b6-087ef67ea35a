<div class="cardBrd p-6 overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="card shadow-none w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-end cursor-pointer" fxLayout="row" mat-dialog-close>
                <mat-icon class="text-sm flex -mr-3 -mt-3">close</mat-icon>
            </div>
        </div>
    </div>
    <div class="card shadow-none w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-center submit" fxLayout="row">
                <h3> <b>{{data.title}}</b></h3>
            </div>
            
        </div>
    </div>
    <div class="card shadow-none w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div class="pb-4 flex justify-center t-xs confirm" fxLayout="row">
            <h6>{{data.message}}</h6>
        </div>
    </div>

    <div class=" p-2 " *ngIf="data.title == 'Re-Evaluate'" fxLayout="column">        <form [formGroup]="addNewNote">
            <!-- <mat-form-field *ngIf=""  class=" eligibility-list" appearance="outline" autocomplete="off">
                <mat-label>Enter</mat-label>
                <input matInput placeholder="title" formControlName="title">
            </mat-form-field> -->

            <mat-form-field appearance="outline" class="block">
                <!-- <mat-label>Description</mat-label> -->
                    <textarea  class="editor flex-wrap" matInput formControlName="body" placeholder="Description"
                    cols="80" rows="10"></textarea>
                <!-- <input matInput [style.display]="'none'">
                <ngx-editor-menu class="editor flex-wrap" [editor]="editor" [toolbar]="toolbar">
                </ngx-editor-menu>
                <ngx-editor class="editor" [editor]="editor" formControlName="body">
                </ngx-editor> -->
            </mat-form-field>
        </form>
    </div>
    <div class="px-24 pb-4" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px">
        <div class="card shadow-none w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
            <div class="" fxLayout="column" >
                <button *ngIf='!NoConfirm' mat-dialog-close class="btn-3 justify-center" mat-button>{{data.cancelButton}}</button>

            </div>
        </div>
        
        <div class="card shadow-none w-full" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="2 / -1">
            <div class="" fxLayout="column" *ngIf="!NoConfirm">
                <button *ngIf="data.title != 'Re-Evaluate'" (click)="submitApp()" class="btn-1" mat-button color=primary>{{data.OkButton}}</button>
                <button *ngIf="data.title == 'Re-Evaluate'" (click)="submitApp()" class="btn-1" mat-button color=primary [disabled]="!addNewNote.valid">{{data.OkButton}}</button>
            </div>
            <!-- <div class="" fxLayout="column"  *ngIf="Noconfirm !='UNCONFIRMED'">
                <button mat-dialog-close class="btn-1" mat-button color=primary>{{data.OKButton}}</button>
            </div> -->
           
        </div>
        <div class="card shadow-none w-full -mt-5" gdColumn="1/ -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1/ -1">
            <div class="" fxLayout="column" *ngIf="NoConfirm">
                <button *ngIf='NoConfirm' mat-dialog-close class="btn-1 justify-center" mat-button>{{data.OKButton}}</button>
            </div>
            <!-- <div class="" fxLayout="column"  *ngIf="Noconfirm !='UNCONFIRMED'">
                <button mat-dialog-close class="btn-1" mat-button color=primary>{{data.OKButton}}</button>
            </div> -->
           
        </div>
    </div>
 
</div>