import { Component, ElementRef, HostBinding, Inject, Input, OnInit } from '@angular/core';
import { LayoutService } from '../../services/layout.service';
import icBookmarks from '@iconify/icons-ic/twotone-bookmarks';
import emojioneUS from '@iconify/icons-emojione/flag-for-flag-united-states';
import emojioneDE from '@iconify/icons-emojione/flag-for-flag-germany';
import icMenu from '@iconify/icons-ic/twotone-menu';
import { ConfigService } from '../../services/config.service';
import { map } from 'rxjs/operators';
import icPersonAdd from '@iconify/icons-ic/twotone-person-add';
import icAssignmentTurnedIn from '@iconify/icons-ic/twotone-assignment-turned-in';
import icBallot from '@iconify/icons-ic/twotone-ballot';
import icDescription from '@iconify/icons-ic/twotone-description';
import icAssignment from '@iconify/icons-ic/twotone-assignment';
import icReceipt from '@iconify/icons-ic/twotone-receipt';
import icDoneAll from '@iconify/icons-ic/twotone-done-all';
import { NavigationService } from '../../services/navigation.service';
import icArrowDropDown from '@iconify/icons-ic/twotone-arrow-drop-down';
import { PopoverService } from '../../components/popover/popover.service';
import { MegaMenuComponent } from '../../components/mega-menu/mega-menu.component';
import icSearch from '@iconify/icons-ic/twotone-search';
import { DOCUMENT } from '@angular/common';
import { Action, Store } from '@ngrx/store';
import { GlobalUserService, userData } from 'src/app/core/global-user.service';
import { Router } from '@angular/router';
import { getCartItems, getvoucherCartDetails } from 'src/app/client/schedule/state/scheduled.actions';
import { get_cartItems, gotVoucherList } from 'src/app/client/schedule/state/scheduled.selectors';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { LayoutState } from '../state/layout.state';
import { getLayoutVoucherlist } from '../state/layout.action';
import { cartDetails } from '../state/layout.selector';
import { Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { Role, RolesList } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { decodedIdentityToken } from 'src/app/client/state/shared/shared.state';
import jwt_decode from 'jwt-decode';

@Component({
  selector: 'exai-toolbar',
  templateUrl: './toolbar.component.html',
  styleUrls: ['./toolbar.component.scss']
})
export class ToolbarComponent implements OnInit {

  @Input() mobileQuery: boolean;
  Roles:Array<{roleId:number,roleName:string}>=[]
  @Input()
  token:string
  @HostBinding('class.shadow-b')
  hasShadow: boolean;
  isButtonOpen = false;
  navigationItems = this.navigationService.items;

  isHorizontalLayout$ = this.configService.config$.pipe(map(config => config.layout === 'horizontal'));
  isVerticalLayout$ = this.configService.config$.pipe(map(config => config.layout === 'vertical'));
  isNavbarInToolbar$ = this.configService.config$.pipe(map(config => config.navbar.position === 'in-toolbar'));
  isNavbarBelowToolbar$ = this.configService.config$.pipe(map(config => config.navbar.position === 'below-toolbar'));
  showneedhelpbutton:boolean = false
  icSearch = icSearch;
  icBookmarks = icBookmarks;
  emojioneUS = emojioneUS;
  emojioneDE = emojioneDE;
  icMenu = icMenu;
  icPersonAdd = icPersonAdd;
  icAssignmentTurnedIn = icAssignmentTurnedIn;
  icBallot = icBallot;
  icDescription = icDescription;
  icAssignment = icAssignment;
  icReceipt = icReceipt;
  icDoneAll = icDoneAll;
  icArrowDropDown = icArrowDropDown;
  elem: any;
  disableCartIcon:boolean = true;
  // cart: Array<any> = [];
  personTenantRoleId: number;
  roleId: number;
  screenShow: boolean = false;
  roles=RolesList

  constructor(private layoutService: LayoutService,
    private configService: ConfigService,
    private navigationService: NavigationService,
    private popoverService: PopoverService,
    public global: GlobalUserService,
    private router: Router,
    private http: HttpClient,

    @Inject(DOCUMENT) private document: any) { }

  ngOnInit() {
    this.token = sessionStorage.getItem('token')

    this.global.userDetails.subscribe((user) => {
      if (user) {
        this.getRoles()
        this.personTenantRoleId = user.personTenantRoleId
        if(user.roleId==14 || user.roleId==Roles.OperationStaff || user.roleId === Roles.SupportingStaff || user.roleId === Roles.TrainingInstitue || user.roleId === Roles.Sponsor){
          this.disableCartIcon=false;
          this.showneedhelpbutton = true
        }else if( user.roleId == Roles.Employer || user.roleId == Roles.SupportingStaff){
          this.disableCartIcon =false
          this.showneedhelpbutton = false
        }
      }
    })
    // setTimeout(() => {
    //   this.store.dispatch<Action>(
    //     getCartItems({ personTenantRoleId: this.personTenantRoleId })
    //   );
    //   this.store.select(get_cartItems).subscribe(cart => {
    //     this.cart = cart
    //   })
    // }, 2000);
    // this.global.userDetails.subscribe((x:userData)=>{
    //   if(x){
    //     this.store.dispatch<Action>(
    //       getLayoutVoucherlist({ tooldetails: x })
    //         );
    //       }
    // })

    


    // this.store.select(cartDetails).subscribe(cart => {
    //   this.cart = cart
    // })
    this.global.userDetails.subscribe((x:userData)=>{
      if(x){
        let subs:Subscription = this.http.get(`${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${x.personTenantRoleId}`).subscribe((cart: any) => {
          this.global.cart = cart;
          subs?.unsubscribe();
      })
      }
    })

    this.elem = document.documentElement;
    this.navigationService.items$.subscribe(data => {
      this.navigationItems = data;
    });
  }
  openQuickpanel() {
    this.layoutService.openQuickpanel();
  }

  openSidenav() {
    this.layoutService.openSidenav()
  }

  toggleButton(){
    if (window.productFruits.api.inAppCenter.open()) {
      window.productFruits.api.inAppCenter.close();
    } else {
      window.productFruits.api.inAppCenter.open();
    }
  }

  openMegaMenu(origin: ElementRef | HTMLElement) {
    this.popoverService.open({
      content: MegaMenuComponent,
      origin,
      position: [
        {
          originX: 'start',
          originY: 'bottom',
          overlayX: 'start',
          overlayY: 'top'
        },
        {
          originX: 'end',
          originY: 'bottom',
          overlayX: 'end',
          overlayY: 'top',
        },
      ]
    });
  }

  openSearch() {
    this.layoutService.openSearch();
  }

  getRoles(){
    try {
      this.global.emulateEmailId = sessionStorage.getItem('emulateEmailId')
      if(this.global.emulateEmailId){
        this.http.get(`${environment.baseUrl}login/get-role-assign?emailId=${this.global.emulateEmailId?this.global.emulateEmailId:this.global.email}`).subscribe({
          next: (data:Array<{roleId:number,roleName:string,tenantId:number,personTenantRoleId:number}>) => {
            if(data.length > 0){
              data.forEach((x)=>{
                this.global.TrainingInstitue.push({tenantId:x.tenantId,persontenantrole:x.personTenantRoleId})
              })
              
              const currentRole = this.global.userDetails.getValue()?.roleName;
  
              // Filter out current role and deduplicate by roleName
              const filteredRoles = data.filter((x)=>x.roleName != currentRole);
  
              // Remove duplicate role names (e.g., multiple "Training Institute" entries)
              const uniqueRoles = filteredRoles.filter((role, index, self) =>
                index === self.findIndex(r => r.roleName === role.roleName)
              );
  
              this.Roles = uniqueRoles;
            }
          },
          error: (error) => {
            console.error('Error in getRoles API call:', error);
          }
        });
      }else{
        const userDatadetails:decodedIdentityToken= jwt_decode(sessionStorage.getItem('token'))

        this.http.get(`${environment.baseUrl}login/get-role-assign?emailId=${userDatadetails.email}`).subscribe({
          next: (data:Array<{roleId:number,roleName:string,tenantId:number,personTenantRoleId:number}>) => {
            if(data.length > 0){
              data.forEach((x)=>{
                this.global.TrainingInstitue.push({tenantId:x.tenantId,persontenantrole:x.personTenantRoleId})
              })
              
              const currentRole = this.global.userDetails.getValue()?.roleName;
  
              // Filter out current role and deduplicate by roleName
              const filteredRoles = data.filter((x)=>x.roleName != currentRole);
  
              // Remove duplicate role names (e.g., multiple "Training Institute" entries)
              const uniqueRoles = filteredRoles.filter((role, index, self) =>
                index === self.findIndex(r => r.roleName === role.roleName)
              );
  
              this.Roles = uniqueRoles;
            }
          },
          error: (error) => {
            console.error('Error in getRoles API call:', error);
          }
        });
      }
    
    } catch (error) {
      console.error('Error in getRoles method:', error);
    }
 }

  openFullscreen() {
    this.screenShow = true;
    if (this.elem.requestFullscreen) {
      this.elem.requestFullscreen();
    } else if (this.elem.mozRequestFullScreen) {
      /* Firefox */
      this.elem.mozRequestFullScreen();
    } else if (this.elem.webkitRequestFullscreen) {
      /* Chrome, Safari and Opera */
      this.elem.webkitRequestFullscreen();
    } else if (this.elem.msRequestFullscreen) {
      /* IE/Edge */
      this.elem.msRequestFullscreen();
    }
  }

  closeFullscreen() {
    this.screenShow = false;
    if (document.exitFullscreen) {
  
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      /* Firefox */
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      /* Chrome, Safari and Opera */
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      /* IE/Edge */
      this.document.msExitFullscreen();
    }
  }
  cartItems() {

    if (this.global.cart.length > 0) {
      this.router.navigateByUrl(`scheduleExam/${this.global.userDetails.value.personTenantRoleId}/payment/page`)
    } else if (this.global.cart.length == 0) {
      this.router.navigateByUrl(`/`)
    }

  }

  SelectRole(role, idToken, stateId): void {
    this.global.SelectRole(role,idToken,stateId)
  }
}


