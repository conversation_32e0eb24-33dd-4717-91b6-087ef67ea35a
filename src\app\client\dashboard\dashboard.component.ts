import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ApexOptions } from 'src/@exai/components/chart/chart.component';
import { createDateArray } from 'src/@exai/utils/create-date-array';
import { defaultChartOptions } from 'src/@exai/utils/default-chart-options';
import icSearch from '@iconify/icons-ic/twotone-search';
import {
  getExamSurveyReport,
  getStateER,
  getUserAccountDetail,
  gotUserAccountDetail,
} from './state/dashboard.actions';
import { Action, Store } from '@ngrx/store';
import { DashboardState } from './state/dashboard.state';
import {
  selectExamSurveyReport,
  selectStateER,
  selectUserAccount,
} from './state/dashboard.selectors';
import data from '@iconify/icons-ic/twotone-more-horiz';
import { dbState } from './dashboard.types';
import moment from 'moment';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { ActivatedRoute } from '@angular/router';
import { setUserDetailsFromToken } from '../state/shared/shared.actions';
import { MatSelect } from '@angular/material/select';
import { GlobalUserService, userData } from 'src/app/core/global-user.service';
import { Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { URL } from 'src/app/core/url';
@Component({
  selector: 'exai-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  icSearch = icSearch;
  // data: dbState[];
  today: Date = new Date();
  fromDate: string;
  toDate: string;
  selectedState: string;
  selectedEr: string;
  maxDate = new Date();
  graphHeading:string="Exam Survey";
  graph_x_axis_first:string="Online";
  graph_x_axis_second:string="Test Center";
  // fromDate;
  // toDate;
  // value: Date;
  @Input() series: ApexNonAxisChartSeries | ApexAxisChartSeries;
  @Input() options: ApexOptions = defaultChartOptions({
    grid: {
      show: true,
      strokeDashArray: 3,
      padding: {
        left: 16,
      },
    },
    chart: {
      type: 'area',
      height: 235,
      sparkline: {
        enabled: false,
      },
      zoom: {
        enabled: false,
      },
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 0.9,
        opacityFrom: 0.7,
        opacityTo: 0.5,
        stops: [0, 90, 100],
      },
    },
    colors: ['#0076C1', '#7D7D7D'],
    labels: createDateArray(7),
    xaxis: {
      max: new Date().getTime(),
      type: 'datetime',
      labels: {
        show: true,
      },
    },
    yaxis: {
      labels: {
        show: true,
      },
    },
    legend: {
      show: false,
      itemMargin: {
        horizontal: 4,
        vertical: 4,
      },
    },
  });

  userSessionsSeries: ApexAxisChartSeries = [];
  userDetailsSub: Subscription;

  searchControl: FormControl;
  range: FormGroup;
  CandidateCount: number;
  OperationStaffCount: number;
  SupportingStaffCount: number;
  StatesCount: number;
  EligibilityRoutesCount: number;
  trainingProgramCount: number;
  applicationPendingCount: number;
  accommocationPendingCount: number;
  grievanceCount: number;
  profileRequestCount: number;
  completedExams;
  apiData;
  searchText = '';
  stateEr;
  stateFilter;
  eligibilityFilter;
  isSuperadmin: boolean = false;
  isOperationStaff: boolean = false;
  isStateClient: boolean = false;
  isSupportingStaff:boolean = false;
  personTenantRoleId;
  totalTickets:number;
  ticketsSolved:number;
  ticketsPending:number;
  constructor(
    private store: Store<DashboardState>,
    private activatedRoute: ActivatedRoute,
    private globalUserService: GlobalUserService,
    private http: HttpClient,
    private elementRef:ElementRef
  ) {
    this.searchControl = new FormControl('');
  }
  ngOnInit(): void {
    this.today.setDate(this.today.getDate());
    this.searchControl.valueChanges.subscribe((value: string) => {
      // this.dataSource.filter = value.trim().toLocaleLowerCase();
    });
    // getting count of different types of users
    this.store.dispatch<Action>(getUserAccountDetail());
    var dt = new Date();
    dt.setDate(dt.getDate() - 7);
    this.range = new FormGroup({
      end: new FormControl(new Date(), Validators.required),
      start: new FormControl(dt, Validators.required),
    });
    this.fromDate = moment(this.range.value.start).format('YYYY-MM-DD');
    this.toDate = moment(this.range.value.end).format('YYYY-MM-DD');
    this.userDetailsSub = this.globalUserService.userDetails.subscribe(
      (x: userData) => {
        if (x) {
          this.userDetailsSub?.unsubscribe();
          if (x.roleName == 'SuperAdmin') {
            this.store.dispatch<Action>(
              getStateER({
                clientId:
                  this.globalUserService.userDetails.value.clientORtenantId,
              })
            );
            this.isSuperadmin = true;
            this.store
              .select(selectUserAccount)
              .subscribe((data: dbState[]) => {
                if (data.length > 0) {
                  this.CandidateCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Candidate')
                    ]?.personCount;
                  this.OperationStaffCount =
                    data[
                      data.findIndex(
                        (x) => x.personRoleName == 'OperationStaff'
                      )
                    ]?.personCount;
                  this.SupportingStaffCount =
                    data[
                      data.findIndex(
                        (x) => x.personRoleName == 'SupportingStaff'
                      )
                    ]?.personCount;
                  this.StatesCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'States')
                    ]?.personCount;
                  this.EligibilityRoutesCount =
                    data[
                      data.findIndex(
                        (x) => x.personRoleName == 'Eligibility Routes'
                      )
                    ]?.personCount;
                  this.trainingProgramCount =
                    data[
                      data.findIndex(
                        (x) => x.personRoleName == 'TrainingInstitue'
                      )
                    ]?.personCount;
                }
              });
          } else if (x.roleName == 'OperationStaff') {
            this.isOperationStaff = true;
            this.http
              .get(
                URL.BASE_URL +
                  `dashboard/operationstaff-dashboard?personTenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}`
              )
              .subscribe(
                (data: any) => {
                  this.trainingProgramCount =
                    data[
                      data.findIndex(
                        (x) => x.personRoleName == 'TrainingInstitue'
                      )
                    ]?.personCount;
                  this.applicationPendingCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Application')
                    ]?.personCount;
                  this.accommocationPendingCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Accommodation')
                    ]?.personCount;
                  this.grievanceCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Grievance')
                    ]?.personCount;
                  this.profileRequestCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Demographic')
                    ]?.personCount;
                  this.CandidateCount =
                    data[
                      data.findIndex((x) => x.personRoleName == 'Candidate')
                    ]?.personCount;
                },
                (err: any) => {}
              );
          } else if (x.roleName == 'StateClient') {
            this.isStateClient = true;
          }
          else if (x.roleName =='SupportingStaff'){
            this.isSupportingStaff=true;
            this.graphHeading="Tickets"
            this.graph_x_axis_first="Tickets Raised";
            this.graph_x_axis_second="Tickets Solved";
            this.getGraphTickets();
            this.http
              .get(
                URL.BASE_URL_SHORTER +
                  `customerservice/api/dashboard/counts`
              )
              .subscribe(
                (data: any) => {
                    if(data){
                      this.totalTickets=data.totalTickets;
                      this.ticketsPending=data.totalPendingTickets;
                      this.ticketsSolved=data.totalSolvedTickets;
                      this.CandidateCount=data.candidates;
                    }
                },
                (err: any) => {}
              );
          }
        }
      }
    );
    this.getFilterdData();
    
    this.store.select(selectStateER).subscribe((res) => {
      if (res) {
        this.stateEr = res.map(({ name }) => name);
        this.eligibilityFilter = res
          .filter((x: any) => {
            return x.typeId == 3;
          })
          .map(({ name }) => name);
        this.stateFilter = res
          .filter((x: any) => {
            return x.typeId == 2;
          })
          .map(({ name }) => name);
      }
    });

    // this.test()
    // this.store.dispatch<Action>(getExamSurveyReport({ examFromDateTime: startDate, examToDateTime: endDate }));
    //operation staff dashboard dashboard
  }

  private test(): void{
    var s = document.createElement("script");
    s.id = "zsiqchat";
    s.type = "text/javascript";
    s.src = `${environment.redirectUrl}/client/assets/static data/zoho.js`;
    this.elementRef.nativeElement.appendChild(s);
  }

  stateErselected(event: Event) {
    var selected = event['value'];
    if (this.eligibilityFilter.includes(selected)) {
      this.selectedEr = selected;
    } else if (this.stateFilter.includes(selected)) {
      this.selectedState = selected;
    }
    this.getFilterdData();
  }
  updateFromDateRange(event: MatDatepickerInputEvent<Date>) {
    this.fromDate = moment(event.value).format('YYYY-MM-DD');
  }
  updateToDateRange(event: MatDatepickerInputEvent<Date>) {
    this.toDate = moment(event.value).format('YYYY-MM-DD');
    if (this.toDate != 'Invalid date') {
      this.getFilterdData();
      if(this.isSupportingStaff==true){
        this.getGraphTickets();
      }
    }
  }
  getFilterdData() {
    this.store.dispatch<Action>(
      getExamSurveyReport({
        examFromDateTime: this.fromDate,
        examToDateTime: this.toDate,
        stateName: this.selectedState,
        eligibilityName: this.selectedEr,
      })
    );
    this.store.select(selectExamSurveyReport).subscribe((data: any) => {
      if (data) {
        this.apiData = data;
        this.userSessionsSeries = [
          {
            name: 'Online',
            data: data
              .filter((x: any) => {
                return x.examMode == 'Online';
              })
              .map((x: any) => {
                return {
                  x: moment(x.examDateTime).format('YYYY-MM-DD'),
                  y: x.examCount,
                };
              }),
          },
          {
            name: 'Test Center',
            data: data
              .filter((x: any) => {
                return x.examMode == 'Test Center';
              })
              .map((x: any) => {
                return {
                  x: moment(x.examDateTime).format('YYYY-MM-DD'),
                  y: x.examCount,
                };
              }),
          },
        ];
      }
    });
  }

  getGraphTickets(){
    this.http
    .get(
      URL.BASE_URL_SHORTER +
        `customerservice/api/dashboard/ticket-graph?startDate=${this.fromDate}&endDate=${this.toDate}`
    )
    .subscribe(
      (data: any) => {
          if(data){
            this.userSessionsSeries = [
              {
                name: 'Open',
                data: data
                  .filter((x: any) => {
                    return x.ticketMode == 1;
                  })
                  .map((x: any) => {
                    return {
                      x: moment(x.ticketModifiedDate).format('YYYY-MM-DD'),
                      y: x.ticketCount,
                    };
                  }),
              },
              {
                name: 'Closed',
                data: data
                  .filter((x: any) => {
                    return x.ticketMode == 2;
                  })
                  .map((x: any) => {
                    return {
                      x: moment(x.ticketModifiedDate).format('YYYY-MM-DD'),
                      y: x.ticketCount,
                    };
                  }),
              },
            ];
          }
      },
      (err: any) => {}
    );  
  }
}
