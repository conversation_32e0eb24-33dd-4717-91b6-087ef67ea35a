import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [{
        type: moduleTypes.Dynamic,
        name: "Support Tickets",
        moduleID: "support-ticket",
        nestingType: nestingTypes.ColumnsInRows,
        iconRef: "ticket",
        routeRef: "/loader",
        rows: [
            {
                class: "",
                gdColumns: "1fr",
                gdColumnsltlg: "1fr",
                gdColumnsltmd: "1fr",
                gdColumnsltsm: "1fr",
                gdGap: "12px",
                columns: [
                    {
                        class: "w-full",
                        gdColumn: "1 / 1",
                        gdColumnltlg: "1 / 1",
                        gdColumnltmd: "1 / 1",
                        gdColumnltsm: "1",
                        fxLayout: "",
                        fxLayoutAlign: "start center",
                        fxFlex: "none",
                        fxFlexltmd: "auto",
                        fxHidexs: "false",
                        fxHidesm: "false",
                        widget: {
                            type: widgetTypes.DynamicTable,
                            tableOptions: {
                                headerRowCssClasses: ['tab-col'],
                                showPaginator: true,
                                pageSizeOptions: [20, 50, 200,300],
                                pageSize: 20,
                                showColumnFilter:true,
                                showAddButton: [false],
                                showStateSelectionDropdown:true,
                                showMatTabs: true,
                                    matTabs: [
                                        { id: 1, name: "Assigned" },
                                        { id: 2, name: "Open" },
                                        { id: 3, name: "Closed" },
                                        {
                                            id: 4, name: 'Transferred', visibilityRule: [{
                                                operator: tableTypes.Operators.AND,
                                                elementProperty: 'roleId',
                                                compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                shouldHaveValue: Roles.SupportingStaff 
                                          }]},
                                    ],
                                showSearch: true,
                                verticalScroll: true,
                                horizontalScroll: true,
                                isPaginationServerSide: [true, true, true, true],
                                showColumnWiseFilter: true,
                                isSearchServerSide: [true, true, true, true]
                            },
                            displayedColumns: [
                                [
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "id",
                                        name: "Ticket Id",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "personId",
                                        name: "Candidate ID",
                                        cellType: "link",
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Offspring,
                                            navigateTo: "candidate-details",
                                            eventDataUseful: false,
                                            navigateToLabel: "Candidate Details",
                                        },
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["link-animation"],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: ["firstName","middleName","lastName"],
                                        name: "Candidate Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "city",
                                    //     name: "State",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },



                                    <tableTypes.column>{
                                        visible: true,
                                        id: "state",
                                        name: "State Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "categoryName",
                                        name: "Category",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "mode",
                                    //     name: "Mode",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "createdDate",
                                        name: "Created Date",
                                        cellType: "Date",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "statusName",
                                        name: "Status",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "assignedTo",
                                        name: "Assigned To",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "action",
                                        name: "Action",
                                        cellType: "action",
                                        actions: [
                                            <tableTypes.action>{
                                                icon: "visibility",
                                                type: tableTypes.actionTypes.Link,
                                                tooltip: "View",
                                                color: '#7d7d7d',
                                            },
                                        ],
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Custom,
                                            navigateTo: 'support-ticket/support-ticket-details',
                                            eventDataUseful: false,
                                            navigateToLabel: 'Support Ticket',
                                        },
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["cursor-pointer"],
                                    },
                                ],
                                [
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "id",
                                        name: "Ticket Id",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "personId",
                                        name: "Candidate ID",
                                        cellType: "link",
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Offspring,
                                            navigateTo: "candidate-details",
                                            eventDataUseful: false,
                                            navigateToLabel: "Candidate Details",
                                        },
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["link-animation"],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: ["firstName","middleName","lastName"],
                                        name: "Candidate Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "city",
                                    //     name: "State",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },




                                    <tableTypes.column>{
                                        visible: true,
                                        id: "state",
                                        name: "State Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "categoryName",
                                        name: "Category",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "mode",
                                    //     name: "Mode",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "createdDate",
                                        name: "Created Date",
                                        cellType: "Date",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "statusName",
                                        name: "Status",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "action",
                                        name: "Action",
                                        cellType: "action",
                                        actions: [
                                            <tableTypes.action>{
                                                icon: "visibility",
                                                type: tableTypes.actionTypes.Link,
                                                tooltip: "View",
                                                color: '#7d7d7d',
                                            },
                                        ],
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Custom,
                                            navigateTo: 'support-ticket/support-ticket-details',
                                            eventDataUseful: false,
                                            navigateToLabel: 'Ticket Details',
                                        },
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["cursor-pointer"],
                                    },
                                ],
                                [
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "id",
                                        name: "Ticket Id",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "personId",
                                        name: "Candidate ID",
                                        cellType: "link",
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Offspring,
                                            navigateTo: "candidate-details",
                                            eventDataUseful: false,
                                            navigateToLabel: "Candidate Details",
                                        },
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["link-animation"],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: ["firstName","middleName","lastName"],
                                        name: "Candidate Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "city",
                                    //     name: "State",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },




                                    <tableTypes.column>{
                                        visible: true,
                                        id: "state",
                                        name: "State Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "categoryName",
                                        name: "Category",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "mode",
                                    //     name: "Mode",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "createdDate",
                                        name: "Created Date",
                                        cellType: "Date",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "statusName",
                                        name: "Status",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "assignedTo",
                                        name: "Assigned To",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "action",
                                        name: "Action",
                                        cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Custom,
                                            navigateTo: 'support-ticket/support-ticket-details',
                                            eventDataUseful: false,
                                            navigateToLabel: 'Ticket Details',
                                        },
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["cursor-pointer"],
                                    },
                                ],
                                [
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "id",
                                        name: "Ticket Id",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "personId",
                                        name: "Candidate ID",
                                        cellType: "link",
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Offspring,
                                            navigateTo: "candidate-details",
                                            eventDataUseful: false,
                                            navigateToLabel: "Candidate Details",
                                        },
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["link-animation"],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "candidateName",
                                        name: "Candidate Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "city",
                                    //     name: "State",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },




                                    <tableTypes.column>{
                                        visible: true,
                                        id: "state",
                                        name: "State Name",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "categoryName",
                                        name: "Category",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    // <tableTypes.column>{
                                    //     visible: true,
                                    //     id: "mode",
                                    //     name: "Mode",
                                    //     cellType: "text",
                                    //     headerType: "normal",
                                    //     headerAlign: "center",
                                    //     cellAlign: "center",
                                    //     headerCssClasses: [],
                                    //     cellCssClasses: [],
                                    // },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "createdDate",
                                        name: "Created Date",
                                        cellType: "Date",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "statusName",
                                        name: "Status",
                                        cellType: "text",
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "action",
                                        name: "Action",
                                        cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                        linkMetaData: {
                                            navigateToType: moduleTypes.Custom,
                                            navigateTo: 'support-ticket/support-ticket-details',
                                            eventDataUseful: false,
                                            navigateToLabel: 'Ticket Details',
                                        },
                                        headerType: "normal",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: [],
                                        cellCssClasses: ["cursor-pointer"],
                                    },
                                ],
                            ],
                            requestDetails: [
        
                            {
                                fetchUrl: ['customerservice', 'api', 'tickets'],
                                requestBody: null,
                                method: 'GET',
                                requestParams: [
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "statusId",
                                        paramValue: 3,
                                        extractedFromElement: false,
                                    },
                                     {
                                        paramType: paramTypes.Optional,
                                        paramName: 'roleId',
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'roleId'
                                    },
                                ]
                            },
                            {
                                fetchUrl: ['customerservice', 'api', 'tickets'],
                                requestBody: null,
                                method: 'GET',
                                requestParams: [
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "statusId",
                                        paramValue: 1,
                                        extractedFromElement: false,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: 'roleId',
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'roleId'
                                    },
                                ]
                            },
                            {
                                fetchUrl: ['customerservice', 'api', 'tickets'],
                                requestBody: null,
                                method: 'GET',
                                requestParams: [
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "statusId",
                                        paramValue: 2,
                                        extractedFromElement: false,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: 'roleId',
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'roleId'
                                    },
                                ]
                            },
                            {
                                fetchUrl: ['customerservice', 'api', 'tickets'],
                                requestBody: null,
                                method: 'GET',
                                requestParams: [
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: 'roleId',
                                        paramValue: Roles.OperationStaff,
                                        extractedFromGlobal: false,
                                    },
                                    {
                                        paramType: paramTypes.Optional,
                                        paramName: 'transfer',
                                        paramValue: 'true',
                                        extractedFromElement: false,
                                    },
                                    
                                ]
                            }
                            ]
                        }
                    },
                ],
            },
        ],
    }]
}