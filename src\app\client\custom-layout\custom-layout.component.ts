import { Component, OnInit, ViewChild } from '@angular/core';
import { LayoutService } from '../../../@exai/services/layout.service';
import { filter, map, startWith } from 'rxjs/operators';
import { NavigationEnd, Router } from '@angular/router';
import { checkRouterChildsData } from '../../../@exai/utils/check-router-childs-data';
import { BreakpointObserver } from '@angular/cdk/layout';
import { ConfigService } from '../../../@exai/services/config.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SidebarComponent } from '../../../@exai/components/sidebar/sidebar.component';
import { Action, Store } from '@ngrx/store';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { CartItem } from '../schedule/state/models/cartItem';
import { Roles } from '../dynamic-component-loader/dynamic-component-loader.types';
import { getErrorMessage, getLoading } from '../state/shared/shared.selectors';
import { Observable } from 'rxjs';
import { SnackbarService } from 'src/app/core/snackbar.service';


@UntilDestroy()
@Component({
  selector: 'exai-custom-layout',
  templateUrl: './custom-layout.component.html',
  styleUrls: ['./custom-layout.component.scss']
})
export class CustomLayoutComponent implements OnInit {
  loadingObs: Observable<boolean>;
  Roles = Roles;
  sidenavCollapsed$ = this.layoutService.sidenavCollapsed$;
  isFooterVisible$ = this.configService.config$.pipe(map(config => config.footer.visible));
  isDesktop$ = this.layoutService.isDesktop$;
  currentlyConnectedChats: number = 0;

  toolbarShadowEnabled$ = this.router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    startWith(null),
    map(() => checkRouterChildsData(this.router.routerState.root.snapshot, data => data.toolbarShadowEnabled))
  );

  @ViewChild('configpanel', { static: true }) configpanel: SidebarComponent;
  cart: CartItem[];

  constructor(private layoutService: LayoutService,
    private configService: ConfigService,
    private router: Router, private store: Store,
    public global: GlobalUserService,private snackbar:SnackbarService) { }

  ngOnInit() {
    this.loadingObs = this.store.select(getLoading);
    this.layoutService.configpanelOpen$.pipe(
      untilDestroyed(this)
    ).subscribe(open => open ? this.configpanel.open() : this.configpanel.close());
  }
}
