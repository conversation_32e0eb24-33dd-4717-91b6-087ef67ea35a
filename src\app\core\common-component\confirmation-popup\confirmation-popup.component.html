<div class="card shadow-none p-2 overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div class="flex justify-between" fxLayout="row">
            <div class="flex justify-start submit text-sm" fxLayout="row">
               <b *ngIf="data.type"> {{(data.type == formActionTypes.ChangeRequest) ? 'Raise '+formActionTypes[data.type] :
                formActionTypes[data.type]+' Application'}} </b>
            </div>
            <div class="flex justify-end cursor-pointer" mat-dialog-close fxLayout="row">
                <mat-icon class="text-base flex justify-end">close</mat-icon>
            </div>
        </div>
    </div>
    <hr>
    <div class="w-full text-sm" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1" >
        <h6 ><strong>{{data.message}}</strong></h6>
    </div>
    <div  class="w-full mb-2" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <mat-form-field appearance="outline" class="editor textEditor w-full" >
            <textarea class="editor flex-wrap h-full" matInput [formControl]="commentControl" placeholder="Comments" cols="70"></textarea>
        </mat-form-field>
        <form [formGroup]="qacForm">
            <div class="grid-container" *ngIf="recentLinkClickEvent.column.linkMetaData.navigateToLabel == 'View Grievance Details'">
                <div>
                    <mat-form-field appearance="outline" class="wt">
                        <mat-select formControlName="resultCategory" placeholder="Result Category">
                        <mat-option
                            *ngFor="let option of resultCategoryList"
                            [value]="option.id"
                        >
                            {{ option.name }}
                        </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div>
                    <mat-form-field appearance="outline" class="wt">
                        <mat-select formControlName="remedition" placeholder="Remedition">
                        <mat-option
                            *ngFor="let option of remeditionList"
                            [value]="option.id"
                        >
                            {{ option.name }}
                        </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="ml-1">
                    <input type="checkbox" formControlName="systemIssue">&nbsp;&nbsp; System issue
                </div>
                <div class="ml-1">
                    <input type="checkbox" formControlName="evaluatorResponsible">&nbsp;&nbsp; Evaluator Responsible
                </div>
            </div>
        </form>

        <div *ngIf="data.id==1">
            <form [formGroup]="formGroupUpload">
                <dynamic-material-form-control class="w-full flex flex-col flex-wrap relative popUp-input text-xs" [group]="formGroupUpload" [model]="formGroupModel[0]">
                </dynamic-material-form-control>
            </form>
            <mat-form-field *ngIf="data?.selectticket =='Reply and Close Ticket' " class="pb-1 mr-4  ml-3" appearance="outline" fxFlex="50%">
                <mat-label class="text-xs fontColor2">Select categories </mat-label>
                <mat-select  placeholder="Select Event ID" attr.data-cre-type="select_event" (selectionChange)="onCategoryChange($event)" [formControl]="categoriesControl"
                  class="border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                  <mat-option *ngFor="let categories of categories$"  [value]="categories">
                    {{  categories.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
           
            </div>
           
    </div>

  
    <div class="px-24" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px">
        <div class="w-full" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
            <div class="" fxLayout="column">
                <button mat-dialog-close class="btn-3" mat-button>No</button>
            </div>
        </div>
        <div class="w-full" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="2 / -1">
            <div class="" fxLayout="column">
                <button class="btn-1" mat-button color=primary (click)="confirm()" [disabled]="rejectDisable">Yes</button>
            </div>
        </div>
    </div>
</div>