<ng-container *ngIf="data && data.length">
    <div class="bg-card  cardBorder p-4 my-2 overflow-auto" *ngFor="let d of data; let dIndex = index;">
        <div fxLayout="row nowrap" fxLayoutGap="">
            <div *ngFor="let column of displayedColumns;let colIndex = index " class="shadow-none justify-start w-full" [fxFlex]="(100/4) + '%'" fxFlex.xs="100%" fxFlex.sm="25%">
                <div fxLayout="column"  >
                    <div class="" fxFlexFill>
                        <ng-container *ngIf="(isEditing$ | async) == column.id && (editingIndex | async) == dIndex ; else editableFormFeild">
                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                <div class="status1 text-xs eligibility-list ">
                                    <mat-form-field appearance="outline">
                                        <mat-label>{{column.name}}</mat-label>
                                        <input matInput [formControl]="editFormControl" />
                                    </mat-form-field>
                                    <span *ngIf="disableSave" class="link cursor-pointer" (click)="save(column.id,dIndex)">&nbsp;{{"Save"}}
                                    </span>
                                    <span *ngIf="disableSave" class="link cursor-pointer" (click)="cancel(column.id,dIndex)">&nbsp;{{"Cancel"}}
                                    </span>
                                </div>
                            </div>
                        </ng-container>
                        <ng-template #editableFormFeild>
                            <div class="status t-xs ">{{column.name}}</div>
                            <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                <div *ngIf="tabdetails !='verification'" class="status1 text-xs  ">
                                    <span matTooltip="{{columnText(d,column.id,column.cellArrayProps)}}">{{columnText(d,column.id,column.cellArrayProps)}}</span><br/>
                                    <span *ngIf="column.editable && (isEditing$ | async) == null " class="link cursor-pointer" (click)="edit(column.id,dIndex)">
                                    <mat-icon  class="text-base editIcon" matTooltip="Edit" *ngIf="(global.userDetails.value.roleId !=14 )" >edit</mat-icon>
                                    
                                    
                                    <!-- <button  mat-button class="btn-2 t-xs mr-2 absolute custom1" matTooltip="Edit" (click)="edit(column.id,dIndex)">
                                        Edit
                                    </button> -->
                                    </span>
                                    </div>
                                    <div *ngIf="tabdetails =='verification'" class="status1 text-xs width-8  ">
                                        <span matTooltip="{{columnText(d,column.id,column.cellArrayProps)}}">{{columnText(d,column.id,column.cellArrayProps)}}</span><br/>
                                        <span *ngIf="column.editable && (isEditing$ | async) == null " class="link cursor-pointer" (click)="edit(column.id,dIndex)">
                                        <mat-icon  class="text-base editIcon" matTooltip="Edit" *ngIf="(global.userDetails.value.roleId !=14 )" >edit</mat-icon>
                                        </span>
                                        </div>
                                    <div class="status1 text-xs -mt-3 -ml-1 " *ngIf="column.name =='Action' && Omr.length > 0 ">
                                    <span >
                                        <mat-icon *ngIf="ExamName !='Nurse Aide Skills Exam'" (click)="OMRVIEW('ocr')" matTooltip="Answer Sheet">question_answer</mat-icon>
                                        <!-- <mat-icon *ngIf="ExamName !='Nurse Aide Skills Exam'" (click)="OMRVIEW('measure')" matTooltip="Measurement Sheet">question_answer</mat-icon> -->
                                        <mat-icon *ngIf="ExamName =='Nurse Aide Skills Exam'" (click)="OMRVIEW('video')" matTooltip="Video">ondemand_video</mat-icon>
                                    </span>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>


<ng-container *ngIf="description && (this.global.userDetails.value.roleId == 17 || this.global.userDetails.value.roleId == 28)">

    <div class="py-2">
        <div class="bg-white cardBorder p-4">
            <div>
                <h6 class="mb-2"><strong>Appeal Form</strong></h6>
                <p class="text-xs text-gray">
                    This appeal addresses the rejection of a grievance concerning exam procedures. Significant irregularities, such as the exam starting late and technical difficulties, adversely impacted performance. Evidence, including screenshots and emails, supports these claims. These issues deviated from standard procedures. A re-evaluation, retake opportunity, for reconsideration of grading is requested.
                </p>
            </div>
            <div class="wwidth">
                <h6 class="mb-2" ><strong> Please be specific regarding your appeal for rejected grievance</strong> </h6>
                <form>
                    <mat-form-field appearance="outline" class="block">
                      <mat-label>Answer</mat-label>
                      <textarea readonly class="flex-wrap" matInput formControlName="description" placeholder="Answer" rows="3" [value]="description">
                      </textarea>
                    </mat-form-field>
                  </form>
            </div>
        </div>
    </div>
        
    </ng-container>

<div *ngIf="tabdetails ==='View Misconduct Details'" class="py-2">
    <div class="bg-white cardBorder p-4">
        <div>
            <h6 class="mb-2"><strong>Misconduct Reasons</strong></h6>
            <p class="text-xs text-gray">
                This section details the specific reasons for a candidate being flagged for misconduct. Each entry provides clear and concise information on  the nature of the infraction, helping to ensure transparency and consistency in addressing examination integrity issues.
            </p>
        </div>
        <div class="wwidth">
            <form>
                <mat-form-field appearance="outline" class="block">
                  <mat-label>Answer</mat-label>
                  <textarea readonly class="flex-wrap" matInput formControlName="NAE_Description" placeholder="Misconduct reasons from NAE to student" rows="1" [value]="NAE_Description">
                  <textarea readonly class="flex-wrap" matInput formControlName="description" placeholder="Answer" rows="1" [value]="description">
                  </textarea>
                </mat-form-field>
              </form>
        </div>
    </div>
</div>
    


<div  *ngIf='tabdetails ==="View Application Detailss"'class="card cardBorder p-4">
    <h4> {{ eligibilityRoute.title }} </h4>
    <p class="status text-sm" [innerHTML]="eligibilityRoute.description"></p>
</div>

<ng-container *ngIf="(this.global.userDetails.value.roleId == 28 || this.global.userDetails.value.roleId == 17  ) && tabdetails === 'View Grievance Details'">
   <app-grievance-view-edit>
    
   </app-grievance-view-edit>
</ng-container>



