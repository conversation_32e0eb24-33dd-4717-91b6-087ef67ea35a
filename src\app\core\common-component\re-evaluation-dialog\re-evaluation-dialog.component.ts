import { Component, OnIni<PERSON>, Inject, <PERSON><PERSON>hil<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
 
// RXJS
import { BehaviorSubject, Observable, pipe } from 'rxjs';
import { tap, take } from 'rxjs/operators';
 
//Angular Material
import { MatDialog, MatDialogConfig, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
 
// Services
import { SnackbarService } from '../../snackbar.service';
import { GlobalUserService } from '../../global-user.service';
import { ReEvaluationService } from '../re-evaluation.service';
 
// Const
import { Action, Store } from "@ngrx/store";
import { LanguageService } from '../../language.service';
 
// Shared Components
import { getLoading } from 'src/app/client/state/shared/shared.selectors';
import { PopUpComponent } from "../../../client/application/pop-up/pop-up.component";
import { environment } from 'src/environments/environment';
import { HttpService } from '../../http.service';
 
 
 
@Component({
  selector: 'app-re-evaluation-dialog',
  templateUrl: './re-evaluation-dialog.component.html',
  styleUrls: ['./re-evaluation-dialog.component.scss']
})
export class ReEvaluationDialogComponent implements OnInit, OnDestroy {
  isLoading: boolean = true
  selected = 'option2';
  responses: any[] = [[]];
  responseForm: FormGroup;
  header: string = '';
  submitBtn: string = '';
 
  questionsColumn1;
  questionsColumn2;
  questionsColumn3;
  questionsColumn4;
  options: string[] = ['A', 'B', 'C', 'D'];
  answerSheetPDF: any = {};
  answerSheetIMG: any = {};
  disablePaybutton: boolean = false;
  event: any = null;
  details: any;
 
  Gisturl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  $Gisturl: Observable<string> = this.Gisturl.asObservable();
  @ViewChild('candidateResponseForm') candidateResponseForm: any;
 
  constructor(
    private store: Store,
    private http: HttpClient,
    private dialog: MatDialog,
    private lngSrvc: LanguageService,
    private formBuilder: FormBuilder,
    public global: GlobalUserService,
    private snackbar: SnackbarService,
    private reEvaluationService: ReEvaluationService,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<ReEvaluationDialogComponent>,
    private https:HttpService
  ) {
    if (this.global?.personEventId != null && this.global?.personEventId != undefined) {
 
      this.global?.personEventId?.examCode =='NA-PR'?this.getSkillsExams():this.getVideoImage();
    }else{
      data?.details?.examName !='Nurse Aide Written Exam'?this.getSkillsExams():this.getVideoImage();
 
    }
 
    this.event = this.store.select(getLoading).subscribe(data => {
      this.disablePaybutton = data;
      //  this.getVideoImage();
    })
 
 
  }
 
  ngOnInit(): void {
    this.details = this.data?.details;
    this.responseForm = this.formBuilder.group({
 
    })
    window.addEventListener('message', (event) => {
      if (event.data === true) {
        this.dialogRef.close();
      }
    });
  }
 
 
 
  getSkillsExams(){
    let body = {
      personFormId: this.global.personEventId.personFormId,
      personTenantRoleId: this.global.personEventId.personTenantRoleId,
      examRoom_CandidateId: this.global.personEventId.personId,
      examRoom_ExamId: this.global.personEventId.personId,
      vMasterId: this.global.personEventId.vMasterId,
      naE_Id: Number(this.global.personEventId.naePersonId),
      eventId: this.global.userDetails.value.personId,
      skillsAccessKey: environment.SkillsAccessKey,
      skillsSecrectKey: environment.SkillsSecrectKey,
      isRescore: !this.global.personEventId.isViewResultOnly,
      grievanceId: this.global.userDetails.value.personId
    };

    this.https.ErruptRescore(body).subscribe((data) => {
      if (data) {
        let url = `${environment.RedirecttoSkillsExams}#/pages?id=${data}`;
        this.Gisturl.next(url)
      }
    }, (err: HttpErrorResponse | any) => {
      this.snackbar.callSnackbaronError(`${err.message.message.error}`)
    });
  }
 
  onNoClick(): void {
    this.dialogRef.close();
  }
 
  getCredentiaCandidateResponses(bookletNumber) {
    this.reEvaluationService.getCredentiaCandidateResponse(bookletNumber).pipe(
      tap((res: any) => {
        this.questionsColumn1 = res.slice(0, 18);
        this.questionsColumn2 = res.slice(18, 36);
        this.questionsColumn3 = res.slice(36, 54);
        this.questionsColumn4 = res.slice(54, 70);

      }, (err: HttpErrorResponse | any) => {
        this.snackbar.callSnackbaronError(`${err.message}`);
        this.dialogRef.close();
      })
    ).subscribe();
  }
 
  getVideoImage() {
    this.header = this.data?.header? this.data?.header : 'Rescore';
    this.submitBtn = this.data?.header? this.data?.header : 'Rescore';
 
    const personEventId = this.data?.details?this.data.details.id:this.global.personEventId.personEventId;
    this.reEvaluationService.getVideoImage(personEventId).pipe(
      tap((data: any) => {
        if (data.arkivOMRSheets.length > 0) {
          this.answerSheetPDF = (data.arkivOMRSheets.filter((x) => x.extension == 'pdf'))[0];
          this.answerSheetIMG = (data.arkivOMRSheets.filter((x) => x.extension == 'image'))[0];
          const bookletNumber = this.answerSheetPDF?.bookletNumber? this.answerSheetPDF.bookletNumber : this.answerSheetIMG.bookletNumber;
          const appointmentId = this.answerSheetPDF?.appointmentId? this.answerSheetPDF.appointmentId : this.answerSheetIMG.appointmentId;
          this.details = { ...this.details, bookletNumber: bookletNumber, appointmentId: appointmentId };
          this.getCredentiaCandidateResponses(bookletNumber);
          // this.event?.unsubscribe();
        }
      }),
      take(1)
    ).subscribe();
 
  }
 
  onPdfLoadComplete() {
    this.isLoading = false;
  }
 
  postResponse(updatedResponse) {
    this.reEvaluationService.postCredentiaCandidateResponse(updatedResponse).pipe(
      tap((data: { message: string }) => {
        if (data) {
          this.http.post(`${environment.baseUrl}client/api/form/AddNote`,{body:updatedResponse.notes,candidateId:updatedResponse.personTenantRoleId,files:[],id:0,noteTypeid:5,title:'',userId:this.global.userDetails.getValue().personId,userName:this.global.userDetails.value.userName}).subscribe(resp=>{
             if(resp){
              this.dialogRef.close();
              this.snackbar.callSnackbaronSuccess(`${data.message}`);
            
             }
          })
        
        }
      }, (err: HttpErrorResponse | any) => {
        this.snackbar.callSnackbaronError(`${err.message}`);
        this.dialogRef.close();
      })
    ).subscribe();
  }
 
  submitUpdatedResponse() {
    const dialogNoshow = this.dialog.open(PopUpComponent, {
      data: {
        title: "Re-Evaluate",
        message: this.lngSrvc.curLangObj.value.Re_evaluate,
        cancelButton: this.lngSrvc.curLangObj.value.cancel,
        OKButton: this.lngSrvc.curLangObj.value.cancel,
        OkButton: this.lngSrvc.curLangObj.value.save,
        id:120
      },
    });
 
    dialogNoshow.afterClosed().subscribe((data: any) => {
      if ((data.confirmed == true || data.confirmed.confirmed == true)) {
        const updatedResponse = {
          "answer": this.candidateResponseForm.value,
          "appointmentId": this.answerSheetPDF?.appointmentId? this.answerSheetPDF.appointmentId : this.answerSheetIMG.appointmentId,
          "bookletNumber":  this.answerSheetPDF?.bookletNumber? this.answerSheetPDF.bookletNumber : this.answerSheetIMG.bookletNumber,
          "personTenantRoleId":  this.answerSheetPDF?.personTenantRoleId? this.answerSheetPDF.personTenantRoleId : this.answerSheetIMG.personTenantRoleId,
          "notes":data.notes
        };
        this.postResponse(updatedResponse);
      }
    })
  }
 
  showLoader(item) {
    if (item == 0) {
      if (this.disablePaybutton) {
        return false;
      }
      else {
        return true;
      }
 
    }
    else if (item == 1) {
      if (this.disablePaybutton) {
        return this.disablePaybutton;
      }
      else {
        return false;
      }
    }
  }
 
  ngOnDestroy(): void {
    this.event?.unsubscribe();
  }
}