import { Component, Inject, Input, OnInit } from '@angular/core';
import { ReplaySessionService } from 'src/app/core/openreplay.services';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CameraScreenService } from './services/camera-screen.service';
import { switchMap } from 'rxjs/operators';
import { MonitoringService } from './services/monitoring.service';
import { environment } from 'src/environments/environment';
import { StreamService } from './services/stream.service';
import { StreamSignalsService } from './services/stream-signals.service';
import { BehaviorSubject, from } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { HttpService } from 'src/app/core/http.service';
import { VideoStreamComponent } from './video-stream/video-stream.component';
import { ShredingService } from 'src/shared/video-provider/lib/service/shredding.service';
import { PaymentRefundPopupComponent } from '../payment-refund-popup/payment-refund-popup.component';


export interface SaveSessionArchive{
  sessionId:string,
  archiveId:string,
  personEventId:string
}

@Component({
  selector: 'app-video',
  templateUrl: './video.component.html',
  styleUrls: ['./video.component.scss']
})
export class VideoComponent implements OnInit {
  generatedLink: string;
  @Input() PersonEventID:string
  recordVideo$;
  stopRecordVideo$;
  recordVideoDisable$: any;
  microphoneOn$: any;
  sessionConnected$: any;
  userId: string;
  tenantId: string;
  webRTCSessionId:string
  saveSesion:string
  private sessionIDSubject = new BehaviorSubject<string>(null);
  // saveSessionArchive:SaveSessionArchive;


  sessionId:string;
  archiveId:string;
  personEventId:string
 blackBoxID:any;
 respId:string;

  @Input() StartStopRecording: boolean
  DisableShreddingButton: boolean = false;
  StopShreddingButton: boolean = true;
  isSessionStarted:boolean = false;
  constructor(
    private monitoringService: MonitoringService,
    public snackBar: SnackbarService,
    private streamService: StreamService,
    private cameraScreenService: CameraScreenService,
    private streamSignalsService: StreamSignalsService,
    private http: HttpClient,
    private global: GlobalUserService,
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<VideoStreamComponent>,
    private diallogRef: MatDialogRef<PaymentRefundPopupComponent>,
    private httpService: HttpService,
    private shredingService : ShredingService,
  ) {
    this.recordVideo$ = this.cameraScreenService.recordVideo$.asObservable();
    this.stopRecordVideo$ = this.cameraScreenService.stopRecordVideo$.asObservable();
    this.microphoneOn$ = this.cameraScreenService.microphoneOn$.asObservable();
    this.sessionConnected$ = this.streamService.sessionConnected$;

    setTimeout(() => {
      // alert('personEventId');
      this.personEventId = this.PersonEventID;
    }, 1000);

  }

  ngOnInit(): void {
// alert('ngOnInit startRecording() ');
    this.global.userDetails.subscribe(data => {
      if (data != null) {
        // alert('userDetails ');
        this.userId = data.personId.toString();
        this.tenantId = data.personTenantRoleId.toString();
        // this.webRTCSessionId = data.
        // this.startRecording();
      }
    })

    // this.monitoringService.getArchiveID().subscribe((archiveID =>{
    // }))
    const EmulateGisheader = new HttpHeaders({
      Authorization: `Bearer ${sessionStorage.getItem("Gis")}`, // Replace with your actual authorization token
      "Content-Type": "application/json", // Adjust content type if needed
    });

       this.httpService.getScheduleGistNAE("false",EmulateGisheader).subscribe((data: Array<any>)=>{
        if(data){
          this.shredingService.setLoading(true);
        }
        this.blackBoxID= data.find(e => e?.person?.email)?.person?.email ?? 'unknown_'+new Date().getTime();
        },
        err => {
          this.diallogRef.close();
        });
  }




  // personEventID(){
  //   this.personEventID$
  // }

  startRecording() {
    alert('startRecording()  ');
    this.switchCamera();
    this.http.post(
      `${environment.apiUrl}api/ProviderSessionApi/CreateSession`,
      {
        userId: this.userId,
        tenantId: '548F49A4-AC84-4AD9-BF02-06F045B2D401',
        "userType": 0,
      }
     ).subscribe(data => {
      this.saveSesion = data["sessionId"];
      this.global.Apikey = data["apiKey"];
      this.global.SessionID = data["sessionId"];
      this.monitoringService.SessionIDSubject.next(data["sessionId"]);
      // this.sessionIDSubject.next(data["sessionId"]);
      this.global.Token = data["token"];
      this.global.webRTCSessionId = data["webRTCSessionId"];
      this.streamService.startStream(this.global.Apikey, this.global.SessionID, this.global.Token);

      this.cameraScreenService.microphoneOn$.subscribe(value => {

        this.cameraScreenService.toggleMicrophone(value);

      });

      this.monitoringService.saveSessionAndArchive(this.respId, this.saveSesion , this.PersonEventID)


      let obj = {
        userId: this.userId,
          userType: 0,
          applicationId: 0,
          tenantId: '548F49A4-AC84-4AD9-BF02-06F045B2D401',
          webRTCSessionId: data["webRTCSessionId"],
          currentUserId: this.userId
      }
      //tokbox session connected

      this.sessionConnected$.pipe(
        switchMap((sessionID) => this.monitoringService.recordStream(obj))
      ).subscribe(() => {
        this.startCamera();
        setTimeout(()=>{
          this.archiveID();
        },2500)
       
        this.streamSignalsService.userConnectedSignal();
      });

    },(err:HttpErrorResponse | any)=>{
       this.shredingService.setLoading(false);
       this.snackBar.callSnackbaronError(`${err.message.message.error}`)
       
    });
    this.sessionID();
    this.DisableShreddingButton = true;
    this.StopShreddingButton = false;
    this.isSessionStarted = true;
  }


  sessionID(){
    this.monitoringService.SessionIDSubject.subscribe((data)=>{
    setTimeout(() => {
      this.sessionId = data;
    }, 1000);
     
    })
   }
 
   archiveID(){
      this.monitoringService.getArchiveID().subscribe(data =>{
      this.archiveId = data.RespId;
      this.respId = data.respId;

        // this.monitoringService.saveSessionAndArchive(data.RespId , this.sessionId , this.PersonEventID);
  })}



  generateLink(CandidateID, ExamID) {
    // http://localhost:4200/#/monitoring/started-session?candidateId=3232&examId=3232
    return window.location.origin + `/#/monitoring/started-session?candidateId=${CandidateID}&examId=${ExamID}`;
  }

  startCamera() {
    this.cameraScreenService.startCam();
  }


  // recordVideo() {
  //   this.monitoringService.recordStream(this.monitoringService.getSessionID()).subscribe();
  // }

  toggleMicrophone() {
    this.cameraScreenService.microphoneOn$.next(!this.cameraScreenService.microphoneOn$.value);
  }



  stopRecordingVideo() {
    let obj = {
      userId: this.userId,
        userType: 0,
        applicationId: 0,
        tenantId: '548F49A4-AC84-4AD9-BF02-06F045B2D401',
        webRTCSessionId: this.global.webRTCSessionId,
        currentUserId: this.userId
    }
    this.cameraScreenService.stopRecording(obj);
    // this.streamSignalsService.candidateExitApplication();
    this.cameraScreenService.stopSession();
    this.DisableShreddingButton = false;
    this.dialogRef.close();
    this.StopShreddingButton = false;
  }

  // disconnectUser() {
  //   this.stopRecordingVideo();
  //   location.href = "/";
  // }

  deviceConfigurationSaved() {
    this.cameraScreenService.changeCameraAndAudio();
    this.showOptions = false;
  }

  showOptions: boolean = false;

  switchCamera() {
    this.checkDevicesNumber();
  }


  checkDevicesNumber() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
      return;
    }

    // List cameras and microphones.
    from(navigator.mediaDevices.enumerateDevices()).subscribe(devices => {

      const videoCameras = devices.filter(device => device.kind === 'videoinput');

      if (videoCameras.length <= 2) {
        this.cameraScreenService.switchCamera();

        return;
      }

      this.showOptions = true;

    }, err => {


    })
  }

 // close and submit funtionality working same 

  close(): void {
    if (this.isSessionStarted) {
      this.stopRecordingVideo();
      this.dialogRef.close();
    } else {
      this.dialogRef.close();
    }
  }

  Submit(): void {
    if (this.isSessionStarted) {
      this.stopRecordingVideo();
      this.dialogRef.close();
    } else {
      this.dialogRef.close();
    }

    this.snackBar.callSnackbaronSuccess('Shred video submitted successfully');
  }

  // ngOnDestroy(): void {
  //   this.stopRecordingVideo();
  // }


}
