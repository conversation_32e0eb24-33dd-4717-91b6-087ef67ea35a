
.bg-color {
    background-color: var(--background-base2);
}

.editor {
    height: 11rem !important;
}

.switch-group {
    padding-top: 0.5rem;
}
.title-hed {
    color: var(--text-dropdown);
    font-size: 0.75rem;
  }
  .header{
    font-size: 0.75rem;
  }

.addNewNote {
    height: 58vh;
    overflow: auto;
 
}

.btn1 {
    color: var(--text-color2);
}

.color-del {
    font-size: 1rem;
    color: var(--sing-out);
}

.scoreValue{
    color: #7d7d7d !important;
}

.mat-icon {
    font-size: 1rem !important;
    text-align: center;
    vertical-align: sub !important;
}

.moreIcon {
    color: var(--text-color1);
}

.mat-menu-item .mat-icon {
    margin-right: 2px !important;
}

.word{
    word-wrap: break-word;
}
.custom{
    margin-top: 4rem;
}