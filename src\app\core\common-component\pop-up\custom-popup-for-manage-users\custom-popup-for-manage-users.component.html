<div exaiContainer class="w-full pt-2 customPopUp">
    <form [formGroup]="form" class="w-full">
        <ng-container>
            <div fxLayout="row" class="flex justify-between pt-2">
                <mat-form-field appearance="outline" class="manageUsers mr-2">
                    <mat-label>First Name</mat-label>
                    <input type="text" formControlName="firstName" matInput>
                    <mat-error *ngIf="form.controls['firstName'].hasError('required')">
                        First Name is required
                    </mat-error>
                </mat-form-field>
                <mat-form-field appearance="outline" class="manageUsers mr-2">
                    <mat-label>Middle Name</mat-label>
                    <input type="text" formControlName="middleName" matInput>
                </mat-form-field>
                <mat-form-field appearance="outline" class="manageUsers">
                    <mat-label>Last Name</mat-label>
                    <input type="text" formControlName="lastName" matInput>
                    <mat-error *ngIf="form.controls['lastName'].hasError('required')">
                        Last Name is required
                    </mat-error>
                </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="w-full">
                <mat-label>Email</mat-label>
                <input type="text" formControlName="email" matInput>
                <mat-error *ngIf="form.controls['email'].hasError('required')">
                    Email is required
                </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline" class="w-full">
                <mat-label>Phone Number</mat-label>
                <ngx-mat-intl-tel-input formControlName="phoneNumber" [preferredCountries]="['us','in']" [enablePlaceholder]="true" [enableSearch]="true" name="phone" #phone placeholder="Enter phone">
                </ngx-mat-intl-tel-input>
                <mat-error *ngIf="form.controls['phoneNumber'].hasError('required')">
                    Phone Number is required
                </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline" class="w-full">
                <mat-label>User Role</mat-label>
                <mat-select (selectionChange)="selectedRole($event)" formControlName="role" multiple>
                    <mat-option *ngFor="let role of userRoles" [value]="role.id">{{role.roleName}}</mat-option>
                </mat-select>
                <mat-error *ngIf="form.controls['role'].hasError('required')">
                    User Role is required
                </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="w-full">
                <mat-label>
                    {{form.value.role == Roles.TrainingInstitue||(Array.isArray(form.value.role) && form.value.role.includes(Roles.TrainingInstitue)) ? 'Training Institute' : 'State' }}
                </mat-label>
                <mat-select formControlName="tenantId"  (selectionChange)="state($event)">
                    <mat-option *ngFor="let state of statesOrTrainingIns" [value]="state.id"  >{{state.stateName ? state.stateName : state.name}}</mat-option>
                </mat-select>
            </mat-form-field>
            
            <div *ngIf="showAddress" fxLayout="row" class="flex justify-between pt-2">
                <div class="form-row" [ngClass]="{ 'three-fields': rolesId, 'two-fields': !rolesId }">
                    <mat-form-field *ngIf="rolesId" appearance="outline" class="manageUsers">
                        <mat-label>TestCentres</mat-label>
                        <mat-select formControlName="testCenterId">
                            <mat-option *ngFor="let testsite of testCenterId" (click)="testcenterCode(testsite)"
                                [value]="testsite.testCenterId">
                                {{ testsite.testcenterName }} - {{ testsite.testCenterId }} - {{ testsite.tenantCode }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="manageUsers">
                        <mat-label>City</mat-label>
                        <input type="text" formControlName="city" matInput>
                        <mat-error *ngIf="form.controls['city'].hasError('required')">
                            City is required
                        </mat-error>
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="manageUsers">
                        <mat-label>Zipcode</mat-label>
                        <input type="text" formControlName="zipcode" matInput>
                        <mat-error *ngIf="form.controls['zipcode'].hasError('required')">
                            Zipcode is required
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
            <mat-form-field *ngIf="showAddress " appearance="outline" class="w-full">
                <mat-label>Address</mat-label>
                <input type="text" formControlName="address" matInput>
                <mat-error *ngIf="form.controls['address'].hasError('required')">
                    Address is required
                </mat-error>
            </mat-form-field>
            <mat-form-field *ngIf="showAddress && rolesId" appearance="outline" class="w-full">
                <mat-label>License</mat-label>
                <input type="text" formControlName="License" matInput>
                <mat-error *ngIf="form.controls['License'].hasError('required')">
                    License is required
                </mat-error>
            </mat-form-field>
        </ng-container>
        <div>
            <mat-checkbox  formControlName="permissionDetail">Allow Manage Application</mat-checkbox>
            <mat-checkbox *ngIf="userData.roleId == 13 && showAddress && !rolesId" formControlName="cantest">Can Test</mat-checkbox>

        </div>
        <div #ButtonDiv class="flex justify-end pb-2">
            <button class="btn-2 t-xs" type="submit" [disabled]="form.invalid" primary mat-button *ngFor="let button of buttons; let btnIndex = index" (click)="submit(button.requestDetails,btnIndex)">
                <span>{{button.buttonText}}</span>
            </button>
        </div>
    </form>
</div>