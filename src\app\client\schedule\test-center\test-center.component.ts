import { DatePipe } from '@angular/common';
import { Component, ElementRef, Input, OnInit, Renderer2, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatMenuTrigger } from '@angular/material/menu';
import { ActivatedRoute, Router } from '@angular/router';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { interval, Observable, Subject, Subscription } from 'rxjs';
import { crumb } from 'src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types';
import { DynamicPopupMesageComponent } from 'src/app/core/common-component/pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component';
import { cellClickEvent } from 'src/app/core/common-component/table/dynamic-table-types';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { HttpService } from 'src/app/core/http.service';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { moduleTypes } from '../../dynamic-component-loader/dynamic-component-loader.types';
import { setRecentLinkClickEvent } from '../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { breadCrumbsSelector, recentLinkClickEventSelector } from '../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { RescheduledComponent } from '../rescheduled/rescheduled.component';
import { ExamName } from '../state/models/examName';
import { Reschedule } from '../state/models/makePayment';
import { Slot, slot } from '../state/models/slot';
import { addCartTC, getTimeSlotsTestCenter, getTimezones, getCartItems, clearTimeSlotsTestCenter, reschedule, getRegisteredExam, removeAllCartItem, getEligibilityroute, getExamId } from '../state/scheduled.actions';
import { get_timezones, selectorGetTimeSlotsTestCenter, get_cartItems, get_cart, get_rescheduledResponse, get_eligibilityroute, selectorGetTimeSlotsTestCenterStatus } from '../state/scheduled.selectors';
import { ScheduledState } from '../state/scheduled.state';
import { parseISO } from 'date-fns';
import { StateAllowTestCenterforOnline } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { filter, finalize, take } from 'rxjs/operators';

@Component({
  selector: 'exai-test-center',
  templateUrl: './test-center.component.html',
  styleUrls: ['./test-center.component.scss'],
})
export class TestCenterComponent implements OnInit {
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;
  @Input() events:Observable<any>;
  @Input() EplasedDateTime:any;
  isSearching:boolean = false
  @Input() drawer:any;
  radioselect = new FormControl('Search a Test Center by Mileage Radius');
  // paymentModeStatus = new FormControl('MI');
  expanded = -1;
  time
  dates
  minDate = new Date();
  TSMSLOT:boolean
  breadCrumbsArray: crumb[] = null;
  recentLinkClickEvent: cellClickEvent = null;
  AllowSchedule=allowSchedule
  cartId: any;
  isClicked: boolean = false
  timeslots: any[] = [];
  value:number=0;
  rescheduleloading:boolean=false;
  scheduleLoading:boolean=false;
  isChecked:boolean = false
  Inf:string="";
  Validators: FormGroup;
  checkbox:boolean = false
  testCenterLocation: FormGroup;
  range: FormGroup;
  examstatusId:number
  timezones: any[] = [];
  testCenters: any[] = [];
  selectedExamTime: Date;
  selectedTimeSlot: any;
  selectedTestCenter: any;
  allUniqueDates: any[] = [];
  selectedDateTestCenters: any[] = [];
  @Input() selectedExam: any;
  @Input() candidateId: any;
  @Input() Examtype :Array<object>=[];
  step: number = 0
  daySlots: any[] = [];
  selectedTestCenterSoltDate:string
  timeSlots: any[] = [];
  cannotSearch: boolean = true;
  navigate : string
  displayPaymentMethods: boolean = false;
  examDateTime: any;
  maxDate=new Date()
  selectedSlots:any;
  avaiableslots: string;
  testsiteId: any;
  availableDates: Slot[] = [];
  isSelectDate:boolean = false
  GetUserDetails
  GetUserDetailschedule
  examTypeModels=[{id:1,name:"Search a Test Center by Mileage Radius",checked: false,},{id:2,name:"Search a Test Center by Code",checked: false,}]
  @Input('Reschedule') Reschedule: number
  private unsubscribe: Subject<void> = new Subject<void>();
  validation_messages = {"testId" :[
    { type: 'pattern', message: this.global.INF_Validation_message},
  ]}
  examName: ExamName;
  private spinnerTimeout: any;
  constructor(
    private fb: FormBuilder,
    private store: Store<ScheduledState>,
    private renderer: Renderer2,
    public global: GlobalUserService,
    private elem: ElementRef,
    private router: Router,
    private services: SnackbarService,
    private lngSrvc: LanguageService,
    private dialog:MatDialog,
    private http:HttpService,
    private dialogRef: MatDialogRef<RescheduledComponent>,
  ) { 
  }

  /**
   * Setting initial values for time zones and validators.
   */
 
  ngOnInit(): void {

    this.store
    .select(selectorGetTimeSlotsTestCenterStatus)
    .pipe(filter((status) => status === "success" || status === "error"))
    .subscribe(() => {
      // Stop searching spinner
      this.isSearching = false;
    });
      this.store.select(recentLinkClickEventSelector).subscribe(data=>{
        this.examstatusId = data.element.examStatusId
        this.recentLinkClickEvent = data
      })
        this.http.getUser(this.candidateId).subscribe((data:any)=>{
          if(data !=null && data){
            this.GetUserDetailschedule = data;
            this.setValidators();
          }
        })
 
      
    // this.recentLinkClickEvent.element.stateName =="Alaska" || this.recentLinkClickEvent.element.stateId ==19 || this.global.personEventId.stateId == 19 ?this.minDate.setDate(this.minDate.getDate() + 10): this.minDate.setDate(this.minDate.getDate() + 1);
    this.minDate.setDate(this.minDate.getDate() + 1)

    
    this.getCart();
    this.events.subscribe(data=>{
      if(data){
        this.Validators.reset();
        this.testCenterLocation.reset();
        this.availableDates=[]
        this.selectedDateTestCenters=[];
        this.displayPaymentMethods=false;
        this.testCenterLocation.patchValue({testRadius:"100"});
        
      }
    })
   
    this.setValidators();
    this.getTimeZones();
    this.store.select(selectorGetTimeSlotsTestCenter).subscribe((data:any) => {
      if (data) {
        this.TSMSLOT = data.isTSM
         if(this.TSMSLOT ==true){
          let tempDatesSlots: Slot[] = [];
          
       
           /// filter manually for date range slots ///
           const sortedArray = [...data.tsm].sort((a, b) => {
            const dateA = new Date(a.day).getTime();
            const dateB = new Date(b.day).getTime();
            
            if (isNaN(dateA)) return 1; // Handle invalid dates
            if (isNaN(dateB)) return -1; // Handle invalid dates
    
            return dateA - dateB;
        });
        sortedArray.forEach(item => item.testCenters.forEach((x:any) => {
          tempDatesSlots.push(x);
        }));
        sortedArray.forEach((x)=>{

              this.availableDates.push(x)
            
             })
          
          const key = 'day';
          this.availableDates = [...new Map(this.availableDates.map(item => [item[key], item])).values()];
          
          this.testCenters  = data.tsm.length > 0? tempDatesSlots :[];
  
        
          if(this.testCenters.length > 0){
            this.isSelectDate = true
            this.allUniqueDates = this.setAllUniqueDates(this.createDateArray(this.testCenters));
            const time:any = tempDatesSlots.filter((ele: any) => ele.siteSlot.length > 0 );
            time.length > 0 ?  
            (( this.setTestCentersForSelectedDate(time[0].siteSlot[0].examdate)), this.selectedDate(time[0].siteSlot[0]),this.selectedDateTime(time[0].siteSlot[0]),this.setStep(0)) : null;
          }
          else {
            this.services.callSnackbaronWarning("No slots available for the selected date");
            this.global.No_slots_Avaiable.next('No slots available for the selected date')
            this.selectedDateTestCenters=[]
            this.selectedDate(data)
            this.displayPaymentMethods = false
            this.isSelectDate = false
    
          }
         }else{
          let tempDatesSlots: Slot[] = [];
          let avaiableSlotes=[]
          avaiableSlotes.push(data.conductor)
          avaiableSlotes[0].slots.forEach(item => item.slots.forEach(x => {
          tempDatesSlots.push(x);
        }));
        this.availableDates = tempDatesSlots.sort((a, b) => new Date(a.strSlotDate).getTime() - new Date(b.strSlotDate).getTime());
        const key = 'strSlotDate';
        this.availableDates = [...new Map(this.availableDates.map(item => [item[key], item])).values()];
        this.testCenters = avaiableSlotes[0].slots;
        if(this.testCenters.length > 0){
          this.isSelectDate = true
          this.allUniqueDates = this.setAllUniqueDates(this.createDateArray(data.conductor.slots));
          const time:any = tempDatesSlots.filter((ele: any) => ele);
          time.length > 0 ?  
          (( this.setTestCentersForSelectedDate(time[0].slotDate)), this.selectedDate(time[0]),this.selectedDateTime(time[0]),this.setStep(0)) : null;
        }
        else {
          this.services.callSnackbaronWarning("No slots available for the selected date");
        
          this.selectedDateTestCenters=[]
          this.selectedDate(data)
          this.displayPaymentMethods = false
          this.isSelectDate = false
  
        }
     
         }
  
      }      
    },(err)=>{
      this.services.callSnackbaronWarning(`${err.message.message.error}`)
    }
    )

  setTimeout(()=>{
    if(this.EplasedDateTime !='' && this.EplasedDateTime !=null && this.EplasedDateTime !=undefined){
      this.maxDate = parseISO(this.EplasedDateTime)
      this.maxDate.setDate(this.maxDate.getDate() - 1)
    }else{
      this.maxDate = null
    }
    
  })

   

     this.store.select(breadCrumbsSelector)
     .subscribe((x: any) => {
       this.breadCrumbsArray = x
     }
     )
     this.store.dispatch<Action>(
      getEligibilityroute({ candidateId: this.candidateId })
    );
    this.store.select(get_eligibilityroute).subscribe((route: ExamName) => {
      if (route) {
        this.examName = route;
      }
    });
  }

  /**
   * Getting time zones from API
   */
  getTimeZones() {
    this.store.dispatch<Action>(getTimezones());
    this.store.select(get_timezones).subscribe((timezones) => {
      this.timezones = timezones;
    })
  }
  /**
   * API call for getting timeSlots
   */

  
  showOptions($event){
    if($event.checked === true){
      this.isChecked = true
      this.displayPaymentMethods=true
      
    }else{
      this.isChecked = false 
      this.displayPaymentMethods=false
    }
  }

  getTimeSlots() {
    if (this.isSearching) return; // prevent multiple clicks

    let userDetail = `${this.GetUserDetailschedule[0].address} ${this.GetUserDetailschedule[0].city} ${this.GetUserDetailschedule[0].zipCode}`;

    if (
      (this.testCenterLocation.value.testAddress != "" ||
        this.testCenterLocation.value.testRadius != "") &&
      (this.testCenterLocation.value.testId != "" ||
        this.testCenterLocation.value.testName != "")
    ) {
      if (this.Validators.valid && this.range.valid) {
        this.isSearching = true; // Start searching spinner

        if (this.spinnerTimeout) {
          clearTimeout(this.spinnerTimeout);
        }

        this.spinnerTimeout = setTimeout(() => {
          this.isSearching = false;
        }, 10000);

        // Clear previous results to ensure UI updates
        this.store.dispatch<Action>(clearTimeSlotsTestCenter());

        this.store.dispatch<Action>(
          getTimeSlotsTestCenter({
            timezone: "Eastern Standard Time",
            examId: this.selectedExam.id,
            startDate: moment(this.range.value.start).format("MM/DD/YYYY"),
            endDate: moment(this.range.value.end).format("MM/DD/YYYY"),
            testCenterName: this.testCenterLocation.value.testName,
            testCenterId: `${this.testCenterLocation.value.testId}`,
            testCenterAddress:
              userDetail == this.testCenterLocation.value.testAddress
                ? this.GetUserDetailschedule[0].zipCode
                : this.testCenterLocation.value.testAddress,
            radius: this.testCenterLocation.value.testRadius,
            candidateId: this.global.PersonId,
          })
        );

        this.store
          .select(selectorGetTimeSlotsTestCenterStatus)
          .pipe(
            // Wait for a non-null status that indicates completion
            filter((status) => status === "success" || status === "error"),
            // Take only one result to avoid multiple subscriptions
            take(1),
            // Always run this code when the observable completes or errors
            finalize(() => {
              this.isSearching = false;
              if (this.spinnerTimeout) {
                clearTimeout(this.spinnerTimeout);
              }
            })
          )
          .subscribe();
      } else {
        this.range.reset();
        this.isSelectDate = false;
        this.services.callSnackbaronWarning("Please Select Date Range");
      }
    } else {
      this.isSelectDate = false;
      this.services.callSnackbaronWarning("Please enter any one of the fields");
    }

    // this.testCenterLocation.reset();
  }

  getSelectedRoute(event){
    if(event==1){
      this.Validators.reset()
      this.testCenterLocation.patchValue({testId:null,testName:null,testAddress:`${this.GetUserDetailschedule[0]?.address} ${this.GetUserDetailschedule[0]?.city} ${this.GetUserDetailschedule[0]?.zipCode}`,testRadius:100});
      this.isSelectDate = false
      this.displayPaymentMethods =false
      
    }else if(event==2) {
      this.Validators.reset()
      this.testCenterLocation.patchValue({testId:'INF',testName:'',testAddress:null,testRadius:null});
      this.displayPaymentMethods =false
      this.isSelectDate = false
    }
  }

  /**
   * 
   * @param data - from API about all test-centers
   * @returns all available dates for exam
   */
  createDateArray(data) {
    let array = [];
    data.forEach(elem1 => {
      let element = elem1.slots?elem1.slots:elem1.siteSlot
      element.forEach(elem2 => {
        let examDate = elem2.slotDate?elem2.slotDate:elem2.examdate
        let date = new Date(Date.parse(examDate));
        array.push(date)
      });
    });
    return array;
  }

  /**
   * 
   * @param data - all available dates for exam
   * @returns - unique dates
   */
  
  setAllUniqueDates(data: any[]) {
    data.forEach(element => {
      element.setHours(0, 0, 0, 0)
    });
    return Array.from(new Set(data))
  }
  /**
   * 
   * @param slot - slected date for exam
   * fills selectedDateTestCenters for filtering
   */
  setTestCentersForSelectedDate(slotDate: string) {
    var datePipe = new DatePipe("en-US");
    let slotDates = this.TSMSLOT == true? datePipe.transform(slotDate, 'MM/dd/yyyy', '+0000'):slotDate
    let date = new Date(Date.parse(slotDates));
    const slot = this.allUniqueDates.find(x => x.toDateString() === date.toDateString());
    if (slot) {

      this.selectedDateTestCenters = [];

      for (let i = 0; i < this.testCenters.length; i++) {
        let testcenter = this.testCenters[i].slots !=null?this.testCenters[i].slots:this.testCenters[i]?.siteSlot

        for (let j = 0; j < testcenter.length; j++) {
          let slotdate = testcenter[j].slotDate !=null?testcenter[j].slotDate:testcenter[j].examdate


          let comparedDate = new Date(Date.parse(slotdate)).setHours(0, 0, 0, 0);

          if (comparedDate == slot.getTime()) {
            // let Values = this.testCenters[i].testCenter ==this.selectedDayTestcenter
          this.selectedDateTestCenters.push(this.testCenters[i])
          

            break;

          }

        }

      }

    }

  }
  /**
   * 
   * @param slot - selected time slot
   * @param item - selected testCenter
   */
  selectedDateTime(slot) {
    this.examDateTime = slot.slotDateUtc?slot.slotDateUtc: slot.examdate?slot.examdate:slot.day
    // this.avaiableslots = slot.;
    this.selectedTestCenterSoltDate=slot.strSlotDate?slot.strSlotDate: slot.examdate?slot.examdate:slot.day
  }
  selectedDate(event) {
    this.step =event.slotDate?event.slotDate: event.day?event.day:event.examdate
    
  }

  /**
   * For a specific test center create an array of all the time slots available for exam
   * @param testCenter 
   */


  /**
   * Function for checking and adding exam into cart.
   */
   checkalreadyinCart(){
    this.store.dispatch(getCartItems({ personTenantRoleId: this.candidateId }))
    if(this.cartId.find(item=>item.personTenantRoleid!=this.candidateId)){
      this.dialog
      .open(DynamicPopupMesageComponent, {
        data: {
          title: this.lngSrvc.curLangObj.value.deleteMsg,
          message: this.lngSrvc.curLangObj.value.deletemsg,
          cancelButton: this.lngSrvc.curLangObj.value.no,
          OkButton: this.lngSrvc.curLangObj.value.yes,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        if (confirmed==true||confirmed.confirmed==true) {
          //delete cart and add to cart
          this.deleteAllItems()
          this.addToCart()
        }
      });
    }
    else if(this.cartId.find(item=>item.personTenantRoleid==this.candidateId)||this.cartId.length==0){
      this.addToCart()
    }
    this.drawer.toggle();
    if (this.drawer._opened) {
      this.store.dispatch<Action>(
        getCartItems({ personTenantRoleId: this.candidateId })
      );
      this.store.dispatch<Action>(
        getRegisteredExam({ candidateId: this.candidateId })
      );
    }
   }


   deleteAllItems(){
    if (this.cartId) {
      this.store.dispatch<Action>(
        removeAllCartItem({
          cartId:this.cartId[0].cartId,
          personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        })
      );
      setTimeout(() => {
        this.store.dispatch<Action>(
          getCartItems({ personTenantRoleId: this.candidateId })
        );
        this.store.dispatch<Action>(
          getRegisteredExam({ candidateId: this.candidateId})
        );
      }, 2000);
    }
   }

  /**
   * Function for adding exam into cart.
   */
  addToCart() {
    if(this.selectedExam.page=="PopPage"){
      if(this.TSMSLOT == false){
        this.store.dispatch<Action>(
          addCartTC(
            {
              body: {
                personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                amount: this.selectedExam.price,
                cartItemTypeId: 1,
                currencyId: 1,
                examDetail: {
                  candidateId: this.candidateId,
                  examId: this.selectedExam.id,
                  timeZone: this.selectedSlots.timezoneId,
                  // offset: this.Validators.value.timezone.offset,
                  examModeId:this.selectedExam.title ==="Nurse Aide Skills Exam"&& this.Examtype.length > 0 && StateAllowTestCenterforOnline.includes(this.global.userDetails.getValue().stateId)?1: 2, // 1 = online, 2 = testCenter
                  personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                  testCenterId: this.testsiteId.testSiteId,
                  ExamDateTime: this.examDateTime,
                  testCenterName: this.testsiteId.testSiteName,
                  testCenterAddress: this.testsiteId.testSiteAddress,
                  testCenterCity: this.testsiteId.city,
                  testCenterState: this.testsiteId.state,
                  testCenterPostalCode: this.testsiteId.postalCode,
                  testSiteName: this.testsiteId.testSiteName,
                  directions:this.testsiteId.directions
                  
                },
                personEventId: this.selectedExam.personEventId?this.selectedExam.personEventId:null,
                voucherCode: this.selectedExam.voucherCode?this.selectedExam.voucherCode:null
              }
            }));
            setTimeout(() => {
              this.store.dispatch<Action>(
                getEligibilityroute({ candidateId: this.candidateId })
              );
            }, 2000);
      }else{
        this.store.dispatch<Action>(
          addCartTC(
            {
              body: {
                personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                amount: this.selectedExam.price,
                cartItemTypeId: 1,
                currencyId: 1,
                examDetail: {
                  candidateId: this.candidateId,
                  examId: this.selectedExam.id,
                  timeZone: this.testsiteId.timezone,
                  Slotime:this.selectedSlots.slotime,
                  SlotId:this.selectedSlots.siteslotid,
                  // offset: this.Validators.value.timezone.offset,
                  examModeId:this.selectedSlots.examCode =='VS-PR'?1:2,
                  personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                  testCenterId: (this.testsiteId.testCenterId).toString(),
                  ExamDateTime: this.examDateTime,
                  testCenterName: this.testsiteId.testCenter,
                  testCenterAddress: this.testsiteId.address,
                  testCenterCity: this.testsiteId.city,
                  testCenterState:this.global.StateName,
                  testCenterPostalCode:this.testsiteId.zipCode ,
                  // testSiteName: this.testsiteId.testSiteName,
                  directions:this.testsiteId.getMap,
                  testSiteId:this.testsiteId.testSiteId,
                  testCenterCode:this.testsiteId.testCenterCode
                  
                  
                },
                personEventId: this.selectedExam.personEventId?this.selectedExam.personEventId:null,
                voucherCode: this.selectedExam.voucherCode?this.selectedExam.voucherCode:null,
                examClientEventId:this.selectedSlots.clientEventCode
              }
  
              
            }));
            setTimeout(() => {
              this.store.dispatch<Action>(
                getEligibilityroute({ candidateId: this.candidateId })
              );
            }, 2000);
      }
     
    }else{
        if(this.TSMSLOT == false){
          this.store.dispatch<Action>(
            addCartTC(
              {
                body: {
                  personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                  amount: this.selectedExam.price,
                  cartItemTypeId: 1,
                  currencyId: 1,
                  examDetail: {
                    candidateId: this.candidateId,
                    examId: this.selectedExam.id,
                    timeZone: this.selectedSlots.timezoneId,
                    // offset: this.Validators.value.timezone.offset,
                    examModeId:this.selectedExam.title ==="Nurse Aide Skills Exam"&& this.Examtype.length > 0 && StateAllowTestCenterforOnline.includes(this.global.userDetails.getValue().stateId)?1: 2, // 1 = online, 2 = testCenter
                    personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                    testCenterId: this.testsiteId.testSiteId,
                    ExamDateTime: this.examDateTime,
                    testCenterName: this.testsiteId.testSiteName,
                    testCenterAddress: this.testsiteId.testSiteAddress,
                    testCenterCity: this.testsiteId.city,
                    testCenterState: this.testsiteId.state,
                    testCenterPostalCode: this.testsiteId.postalCode,
                    testSiteName: this.testsiteId.testSiteName,
                    directions:this.testsiteId.directions
                  },
                }
              }));
              setTimeout(() => {
                this.store.dispatch<Action>(
                  getExamId({ eligibilityRouteId: this.examName["id"],candidateId:this.candidateId })
                );
              }, 2000);
        }else{
          this.store.dispatch<Action>(
            addCartTC(
              {
                body: {
                  personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                  amount: this.selectedExam.price,
                  cartItemTypeId: 1,
                  currencyId: 1,
                  examDetail: {
                    candidateId: this.candidateId,
                    examId: this.selectedExam.id,
                    timeZone: this.testsiteId.timezone,
                    Slotime:this.selectedSlots.slotime,
                    SlotId:this.selectedSlots.siteslotid,
                    // offset: this.Validators.value.timezone.offset,
                    examModeId:this.selectedSlots.examCode =='VS-PR'?1:2,
                    personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
                    testCenterId: (this.testsiteId.testCenterId).toString(),
                    ExamDateTime: this.examDateTime,
                    testCenterName: this.testsiteId.testCenter,
                    testCenterAddress: this.testsiteId.address,
                    testCenterCity: this.testsiteId.city,
                    testCenterState:this.global.StateName,
                    testCenterPostalCode:this.testsiteId.zipCode ,
                    // testSiteName: this.testsiteId.testSiteName,
                    directions:this.testsiteId.getMap,
                    testSiteId:this.testsiteId.testSiteId,
                    testCenterCode:this.testsiteId.testCenterCode
                  },
                  examClientEventId:this.selectedSlots.clientEventCode
                }
              }));
              setTimeout(() => {
                this.store.dispatch<Action>(
                  getExamId({ eligibilityRouteId: this.examName["id"],candidateId:this.candidateId })
                );
              }, 2000);
        }
    
    }
    this.store.select(get_cart).subscribe(data=>{
      if(data){
        this.getCart();
      }
    })
  }


  paste(e: any) {
    let clipboardData = e.clipboardData || window.Clipboard;
    let pastedText = clipboardData.getData('text');
    e.target.value = pastedText.replace(/[^0-9]/g, '').slice(0,5)
  }

  navigateToParentUsingBreadCrumb() {
    // extracting the second last 
    let crumb = this.breadCrumbsArray[this.breadCrumbsArray.length - 1];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.store.dispatch(setRecentLinkClickEvent({ event: this.recentLinkClickEvent }));
      this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
    }
    // this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
  }
  selectedId(i: any,item:any) {
    this.testsiteId = i;
    this.selectedSlots=item;
    this.examDateTime=item.slotDateUtc !=null?item.slotDateUtc:item.examdateUTC;
    this.checkbox = item.examCode =='VS-PR'?true:false
    this.isChecked = item.examCode !='VS-PR'?false:false
    this.displayPaymentMethods =item.examCode ==='VS-PR' && this.isChecked? true:item.examCode ==='VS-PR' && this.isChecked ==false? false:true;


  }
  eventHandler(event){
    if(event.target.value.length == 3 && (event.code == "Backspace" || event.code == "Delete") || (event.target.value.length >3 && (event.code == "Delete")) || (event.code ==="KeyX") ||((event.target.value == 3) && (event.code ==="KeyV"))||(event.code== "KeyA")){
      return false;
    }
  }

  isActive(item: any) {
    return this.selectedSlots === item;
  };

  reschedule() {
    this.rescheduleloading = true;
    const subs$: Subscription = interval(500).subscribe(res => {
      this.value = this.value + 10;
      if(this.value === 150) {
        subs$.unsubscribe();
        this.rescheduleloading = false;
        this.value = 0;
        
      }
    });

    if(this.TSMSLOT == false){
      var v: Reschedule = {
        candidateId: this.candidateId,
        examId: this.selectedExam.id,
        slotId: 0,
        timeZone: this.selectedSlots.timezoneId,
        offSet: this.Validators.value.timezone?this.Validators.value.timezone.offset:"",
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        scheduleId: Number(this.Reschedule),
        examModeId:this.selectedExam.title ==="Nurse Aide Skills Exam"&& this.Examtype.length > 0 && StateAllowTestCenterforOnline.includes(this.global.userDetails.getValue().stateId)?1: 2, // 1 = online, 2 = testCenter
        examDateTime: this.examDateTime,
        testCenterId: this.testsiteId.testSiteId,
        testCenterName:this.testsiteId.testSiteName,
        testCenterAddress:this.testsiteId.testSiteAddress ,
       testCenterCity:this.testsiteId.city,
       testCenterState:this.testsiteId.state ,
        testCenterPostalCode:this.testsiteId.postalCode,
      
      };
      this.store.dispatch<Action>(reschedule({ rescheduleBody: v }));
      this.store.select(get_rescheduledResponse).subscribe((data:number)=>{
        var datePipe = new DatePipe("en-US");
        this.time =datePipe.transform(this.recentLinkClickEvent.element.examDateTime, 'shortTime','+0000')
        if(data !=null && data){
          let body={
            body: `${this.recentLinkClickEvent.element.mode} ${this.recentLinkClickEvent.element.examName} scheduled ${this.recentLinkClickEvent.element.testCenterDetails.testCenterName?"for" + this.recentLinkClickEvent.element.testCenterDetails.testCenterName:""} on ${moment(this.recentLinkClickEvent.element.examDateTime).format('MM/DD/YYYY')} at ${this.time} ${this.recentLinkClickEvent.element.timeZoneAbbreviation}  was reschedule by ${this.global.userDetails.value.roleName}`,
            candidateId:this.candidateId,
            files: [],
            id: 0,
            noteTypeid: 5,
            title: ` ${this.recentLinkClickEvent.element.mode} ${this.selectedExam.title} Rescheduled`,
            userId:  this.global.userDetails.value.personTenantRoleId,
            userName: `${this.recentLinkClickEvent.element.candidateName}`,
  
          }
          this.http.getAddnotes(body).subscribe(data=>{
                
          })
          this.navigateToParentUsingBreadCrumb()
        }
      })
          // this.navigateToParentUsingBreadCrumb()
          // this.store.dispatch<Action>(
          //   getRegisteredExam({ candidateId: this.candidateId })
          // );
    }else{
      var v: Reschedule = {
        candidateId: this.candidateId,
        examId: this.selectedExam.id,
        slotId: this.selectedSlots.siteslotid,
        timeZone: this.testsiteId.timezone,
        Slotime:this.selectedSlots.slotime,
        offSet: this.Validators.value.timezone?this.Validators.value.timezone.offset:"",
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        scheduleId: Number(this.Reschedule),
        examModeId:this.selectedSlots.examCode =='VS-PR'?1:2,
        examDateTime: this.examDateTime,
        testCenterId: (this.testsiteId.testCenterId).toString(),
        testCenterName: this.testsiteId.testCenter,
        testCenterAddress: this.testsiteId.address,
       testCenterCity:this.testsiteId.city,
       testCenterState:this.global.StateName,
        testCenterPostalCode:this.testsiteId.zipCode ,
        testSiteId:this.testsiteId.testSiteId,
        TestCenterDirections:this.testsiteId.getMap,
        testCenterCode:this.testsiteId.testCenterCode,
        examClientEventId:this.selectedSlots.clientEventCode
      };
      this.store.dispatch<Action>(reschedule({ rescheduleBody: v }));
      this.store.select(get_rescheduledResponse).subscribe((data:number)=>{
        var datePipe = new DatePipe("en-US");
        this.time =datePipe.transform(this.recentLinkClickEvent.element.examDateTime, 'shortTime','+0000')
        if(data !=null && data){
          let body={
            body: `${this.recentLinkClickEvent.element.mode} ${this.recentLinkClickEvent.element.examName} scheduled ${this.recentLinkClickEvent.element.testCenterDetails.testCenterName?"for" + this.recentLinkClickEvent.element.testCenterDetails.testCenterName:""} on ${moment(this.recentLinkClickEvent.element.examDateTime).format('MM/DD/YYYY')} at ${this.time} ${this.recentLinkClickEvent.element.timeZoneAbbreviation}  was reschedule by ${this.global.userDetails.value.roleName}`,
            candidateId:this.candidateId,
            files: [],
            id: 0,
            noteTypeid: 5,
            title: ` ${this.recentLinkClickEvent.element.mode} ${this.selectedExam.title} Rescheduled`,
            userId:  this.global.userDetails.value.personTenantRoleId,
            userName: `${this.recentLinkClickEvent.element.candidateName}`,
  
          }
          this.http.getAddnotes(body).subscribe(data=>{
                
          })
          this.navigateToParentUsingBreadCrumb()
        }
      })
    }

 
        // this.navigateToParentUsingBreadCrumb()
        // this.store.dispatch<Action>(
        //   getRegisteredExam({ candidateId: this.candidateId })
        // );
      
    }

    getDirection(url:string){
      window.open(url,"_blank")
     }

    retry_Schedule_Without_payment(){
      this.scheduleLoading = true;
    const subs$: Subscription = interval(500).subscribe(res => {
      this.value = this.value + 10;
      if(this.value === 150) {
        subs$.unsubscribe();
        this.scheduleLoading = false;
        this.value = 0;
        
      }
    });


    let ScheduleDetails = this.TSMSLOT == true?{
      candidateId:this.candidateId,
      examId:this.selectedExam.id,
      slotId: this.selectedSlots.siteslotid,
      timeZone: this.testsiteId.timezone,
      Slotime:this.selectedSlots.slotime,
      offSet: this.Validators.value.timezone?this.Validators.value.timezone.offset:"",
      examModeId:this.selectedSlots.examCode =='VS-PR'?1:2,
      personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
      accommodationType: "",
      accommodationItems: [],
      clientExamId: 0,
      testCenterId: (this.testsiteId.testCenterId).toString(),
      testCenterName: this.testsiteId.testCenter,
      testCenterAddress: this.testsiteId.address,
      testCenterCity: this.testsiteId.city,
      testCenterState:this.global.StateName,
      testCenterPostalCode:this.testsiteId.zipCode ,
      examDateTime:  this.examDateTime,
      scheduleId:Number(this.Reschedule),
      testSiteId:this.testsiteId.testSiteId,
      TestCenterDirections:this.testsiteId.getMap,
      testCenterCode:this.testsiteId.testCenterCode,
      examClientEventId:this.selectedSlots.clientEventCode
  }:{
    candidateId:this.candidateId,
      examId: this.selectedExam.id,
      slotId: 0,
      timeZone: "Eastern Standard Time",
      // offSet: this.Validators.value.timezone.offset,
      examModeId:this.selectedExam.title ==="Nurse Aide Skills Exam"&& this.Examtype.length > 0 && StateAllowTestCenterforOnline.includes(this.global.userDetails.getValue().stateId)?1: 2, // 1 = online, 2 = testCenter
      personTenantRoleId:  this.global.userDetails.value.personTenantRoleId,
      testCenterId: this.testsiteId.testSiteId,
      accommodationType: "",
      accommodationItems: [],
      clientExamId: 0,
      testCenterName: this.testsiteId.testSiteName,
      testCenterAddress: this.testsiteId.testSiteAddress,
      testCenterCity: this.testsiteId.city,
      testCenterState: this.testsiteId.state,
      testCenterPostalCode: this.testsiteId.postalCode,
      ExamDateTime:  this.examDateTime,
      scheduleId: Number(this.Reschedule)
    }
    this.http.get_retry_schedule(ScheduleDetails).subscribe((data:any)=>{
      if(data){
        var datePipe = new DatePipe("en-US");
        this.time =datePipe.transform(this.recentLinkClickEvent.element.examDateTime, 'shortTime','+0000')
        let body={
          body: `${this.recentLinkClickEvent.element.mode} ${this.recentLinkClickEvent.element.examName} scheduled ${this.recentLinkClickEvent.element.testCenterDetails.testCenterName?"for" + this.recentLinkClickEvent.element.testCenterDetails.testCenterName:""} on ${moment(this.recentLinkClickEvent.element.examDateTime).format('MM/DD/YYYY')} at ${this.time} ${this.recentLinkClickEvent.element.timeZoneAbbreviation}  was reschedule by ${this.global.userDetails.value.roleName}`,
          candidateId:this.candidateId,
          files: [],
          id: 0,
          noteTypeid: 5,
          title: ` ${this.recentLinkClickEvent.element.mode} ${this.selectedExam.title} Rescheduled`,
          userId:  this.global.userDetails.value.personTenantRoleId,
          userName: `${this.recentLinkClickEvent.element.candidateName}`,

        }
        this.http.getAddnotes(body).subscribe(data=>{
              
        })
        this.navigateToParentUsingBreadCrumb()
        data.scheduledMessage? this.services.callSnackbaronSuccess(data.scheduledMessage) :this.services.callSnackbaronSuccess('Exam Scheduled SuccessFully')
      }
    },(error:any)=>{
        this.services.callSnackbaronError(error.message.message.error)
    })
    }


    

  
  /**
   * Getting cart information items etc.
   */
  getCart() {
    this.store.dispatch(getCartItems({ personTenantRoleId: this.candidateId }))
    this.store.select(get_cartItems).subscribe(data => {
      if(data){
      this.cartId = data;
      }
    })
  }
  /**
   * Function for rerouting to payment
   * Called when "Pay Now" button is pressed
   */
  getPayment() {
    // this.router.navigateByUrl(`scheduleExam/${this.candidateId}/payment/page`);
    // this.store.dispatch<Action>(
    //   getCartItems({ personTenantRoleId: this.global.userDetails.value.personTenantRoleId})
    // );
    if(this.selectedExam.page!=undefined && this.selectedExam.page=="PopPage"){
      this.dialogRef.close({confirmed:true});
      this.router.navigateByUrl(`scheduleExam/${this.candidateId}/payment/page`);
    }
    this.getCart();
    if (this.cartId.length > 0) {
      this.router.navigateByUrl(`scheduleExam/${this.candidateId}/payment/page`);
    } else {
      this.services.callSnackbaronError("No item in Cart to make a Payment")
    }
  }
  // ---------------------------------- SETTER FUNCTIONS ----------------------------------
  /**
   * Initial setup for validators for search
   */
  setValidators() {
      let userDetails= this.GetUserDetailschedule?`${this.GetUserDetailschedule[0].address} ${this.GetUserDetailschedule[0]?.city} ${this.GetUserDetailschedule[0]?.zipCode}`:null
      this.Validators = this.fb.group({
        timezone: new FormControl(""),
        testCenter: new FormControl("", [Validators.required]),
      });
      this.Validators.controls['testCenter'].disable(); //Input only trough mat-menu
      this.range = new FormGroup({
        start: new FormControl("", [Validators.required]),
        end: new FormControl("", [Validators.required]),
      });
      this.testCenterLocation = new FormGroup({
        testId: new FormControl(""),
        testAddress: new FormControl(userDetails),
        testName: new FormControl(null),
        testRadius: new FormControl("100")
      })
    // this.testCenterLocation.setValidators(this.atLeastOneValidator());

    //<------------This Code was Comment due calender issue affecting the month range---------->
    // let dayInFront = new Date();
    // this.minDate.setDate(dayInFront.getDate() + 1);

    this.testCenterLocation.valueChanges.subscribe(() => {
    })
  }
  /**
   * Setup testCenter value from mat-menu
   */
  setData() {
    this.testCenterLocation.value.testAddress=this.testCenterLocation.value.testAddress?JSON.stringify(this.testCenterLocation.value.testAddress):null
    this.testCenterLocation.value.testRadius=this.testCenterLocation.value.testRadius?JSON.stringify(this.testCenterLocation.value.testRadius):null
    let stringToPatch = "";
    if (this.testCenterLocation.value.testId != null && this.testCenterLocation.value.testId.length > 0)
      stringToPatch += this.testCenterLocation.value.testId;
    if (this.testCenterLocation.value.testName != null && this.testCenterLocation.value.testName.length > 0)
      stringToPatch += stringToPatch.length > 0 ? ";" + this.testCenterLocation.value.testName : this.testCenterLocation.value.testName;
    if (this.testCenterLocation.value.testAddress != null && this.testCenterLocation.value.testAddress.length > 0)
      stringToPatch += stringToPatch.length > 0 ? ";" + this.testCenterLocation.value.testAddress : this.testCenterLocation.value.testAddress;
      if (this.testCenterLocation.value.testRadius != null && this.testCenterLocation.value.testRadius.length > 0)
      stringToPatch += stringToPatch.length > 0 ? ";" + this.testCenterLocation.value.testRadius: this.testCenterLocation.value.testRadius;
    this.Validators.patchValue({ testCenter: stringToPatch })
    this.trigger.closeMenu();
  }
  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }

  /**
   * 
   * @param $event - clicked button.
   * @param date - to set in selectedExamTime for API call
   */
  setSelectedDaySlot(slotDate: string) {
    this.selectedExamTime = new Date(slotDate);
    // Set selected
    this.selectedTimeSlot = null;
    this.displayPaymentMethods = false;
    // Fill test centers from API
    //this.setTestCentersForSelectedDate(new Date(slotDate));
  }
  /**
   * 
   * @param $event - clicked button.
   * @param time - to set in selectedTimeSlot for API call
   */
  setSelectedTimeSlot($event, time) {
    this.displayPaymentMethods = true;
    this.selectedTimeSlot = time;
  }
  /**
   * For mat-expansion-panel
   * @param index 
   */
  setStep(index: number) {
    this.displayPaymentMethods = true;
    this.expanded = index;
  }

  /**
   * For displaying calendar footer
   * @param event 
   */
  changeCalendar(event: any): void {
    const body = Array.from(
      document.querySelectorAll<HTMLDivElement>(
        '.mat-calendar .mat-calendar-content'
      )
    );
    const html = `
        <div class="inline-flex" id="calendarFooter">
        <p class="inline-flex" style="font-size: 8px;">
        <span class="inline-flex"><img src="assets/img/black_icon.svg">Available</span>&nbsp; &nbsp;
        <span class="inline-flex"><img src="assets/img/gray_icon.svg">Not available</span>&nbsp;&nbsp;
        <span class="inline-flex"><img src="assets/img/lightGreen_icon.svg">Today</span>&nbsp; &nbsp;
        <span class="inline-flex"><img src="assets/img/greenTheme_icon.svg">Selected</span>&nbsp; &nbsp;
        </p>
        </div>
        `;
    body.forEach((ele: HTMLDivElement) => {
      const element = document.createElement('div');
      element.setAttribute('class', 'availability-status');
      element.innerHTML = html;
      // el.innerHTML="<img src=\'https://domain.com/adv/banner.jpg\' width=\'400px\' height=\'150px\'>";
      ele.appendChild(element);
    });
  }

  // ---------------------------------- UTILITY FUNCTIONS ----------------------------------
  /**
   * 
   * @param date to be formatted
   * @returns formated date, example: "26. Nov, Mon"
   */
  formatDate(date: Date) {
    return moment(date).format(' MMM, DD. ddd');
  }
  formatTime(date: Date) {
    return moment(date).format('hh:mm A');
  }
  /**
   * toggle the mat-menu from test center location input
   */
  toggleMenu() {
    this.trigger['_menuOpen'] ? this.trigger.openMenu() : this.trigger.closeMenu()
  }


  ngOnDestroy(): void { //
    if (this.spinnerTimeout) {
      clearTimeout(this.spinnerTimeout);
    }

    // Your existing cleanup code

    this.store.dispatch(clearTimeSlotsTestCenter())
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}
export const allowSchedule =[70,68,9,77,78 ,79,80,81,84,85,86,87,93,94]

