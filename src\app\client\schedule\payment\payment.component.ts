import { DatePipe } from "@angular/common";
import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { MatAccordion } from "@angular/material/expansion";
import { ActivatedRoute, Router } from "@angular/router";
import { Action, Store } from "@ngrx/store";
import IMask from "imask";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
import { Observable, Subscription } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { crumb } from "src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types";
import { cellClickEvent } from "src/app/core/common-component/table/dynamic-table-types";
import { GlobalUserService, userData } from "src/app/core/global-user.service";
import { HttpService } from "src/app/core/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { moduleTypes, Roles } from "../../dynamic-component-loader/dynamic-component-loader.types";
import { setRecentLinkClickEvent } from "../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import { breadCrumbsSelector, recentLinkClickEventSelector } from "../../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import {
  getErrorMessage,
  getLoading,
  get_decodeInfo,
} from "../../state/shared/shared.selectors";
import { getCandidateDetails$ } from "../../support-ticket/state/support.selector";
import { VoucherService } from "../../voucher/voucher.service";
import { RescheduledComponent } from "../rescheduled/rescheduled.component";
import { CartItem, payVoucherAmountDetails } from "../state/models/cartItem";
import { AchChargeBody, chargeBody } from "../state/models/charge";
import { customerId } from "../state/models/customerId";
import { MakePaymentBody } from "../state/models/makePayment";
import {
  CreatePaymentCustomerIdBody,
  CreatePaymentMethod,
  paymentMethod,
} from "../state/models/paymentMethods";
import {
  VoucherCartDetailsModel,
  VoucherItemModel,
} from "../state/models/VocherModel";

import {
  clearChargeResponse,
  clearVocherResponse,
  clearVoucherCode,
  createPaymentCustomerId,
  createPaymentMethod,
  createVoucher,
  deleteCard,
  getcartId,
  getCartItems,
  getExamId,
  getMakePayment,
  getPaymentCustomerId,
  getPaymentMethod,
  getRegisteredExam,
  getScheduled,
  getVoucher,
  getVoucherApply,
  getvoucherCartDetails,
  makeCharge,
  makenull,
  paymentDetails,
  reAddData,
  removeCartItem,
  saveVocher,
  VoucherDetails,
  voucherUpdate,
} from "../state/scheduled.actions";
import { ScheduledEffects } from "../state/scheduled.effects";
import {
  createPaymentCustomerId$,
  createPaymentMethodResponse$,
  editedCartDetails,
  generatedSucessfully,
  getChargeResources$,
  getCustomerId$,
  getPaymentMethods$,
  getSavedVoucher$,
  get_cartItems,
  get_makepayment,
  gotTrainingInstStateID$,
  gotVoucherList,
  listOFVoucher,
  selectorGetScheduledExam,
  selectorGetVoucher,
  selectorGetVouchersApply,
  stausofVoucherDetail,
  transcationdetails,
} from "../state/scheduled.selectors";
import { loadScript } from "@paypal/paypal-js";
import { untilDestroyed } from "@ngneat/until-destroy";
import { PaymentService } from "../state/services/PaymentService";

@Component({
  selector: "exai-payment",
  templateUrl: "./payment.component.html",
  styleUrls: ["./payment.component.scss"],
})
export class PaymentComponent implements OnInit, AfterViewInit {
  @ViewChild(MatAccordion) accordion: MatAccordion;
  static enteredValue;
  form: FormGroup;
  finalExamvoucheramount = [];
  cardnumber: HTMLInputElement;
  expirationdate: HTMLInputElement;
  securitycode: HTMLInputElement;
  subtotal: number;
  Validator: FormGroup;
  payVoucherCart: chargeBody;
  total: any;
  closePanel: boolean = true
  event: any = null;
  cartIdVoucher: number;
  subtotalVoucherAmount: number;
  totalVoucherAmount: number;
  totalAmount: any;
  selectedPaymentMethod: PaymentOptions = PaymentOptions.Credit;
  candidateId: number;
  vocherApplied: boolean = false;
  cartId: number;
  listExam: CartItem[] = [];
  PaymentOptions: any = PaymentOptions;
  paymentOptions = [
    { id: 1, name: PaymentOptions.Credit },
    //{ id: 2, name: PaymentOptions.ACH }, moved to add in the OnInit for only training console
    //{ id: 2, name: PaymentOptions.PayPal },
  ];
  voucherCartList = [];
  personTenantRoleId: number;
  clientORtenanatId: number;
  roleId: number;
  loadspinner: boolean = false;
  trainingRoleId: number = 15;
  sponsorRoleID: number = 18;
  generateVoucher: any;
  disablePaybutton: boolean = false;
  openpopOnlyOnce: boolean = true;
  fetchVoucherItems: any;
  setEditFlag: any;
  editable: boolean = true;
  removeEdit: boolean = true;
  trainingInstituteId: number;
  paymentMethod: paymentMethod[];
  cvvForm: FormGroup;
  lineItems: any[] = [];
  hideScreen: boolean = false;
  voucherlineItems: Array<any> = [];
  noCartItemds: boolean;
  step2: number;
  customerIdObj: customerId = new customerId();
  personalInfo: PersonalInfo = new PersonalInfo();
  personId: number;
  ACHform: FormGroup;
  bankType = ["checking", "savings"];
  bank_holder_type = ["personal", "business"];
  Vocher: any;
  TrainingCode: any;
  navigate: string = null
  stateName: string = null;
  globalStateName: string = null;
  recentLinkClickEvent: cellClickEvent = null;
  breadCrumbsArray: crumb[] = null;
  constructor(
    private httpservice: HttpService,
    private dialog: MatDialog,
    private store: Store,
    private router: Router,
    private global: GlobalUserService,
    private services: SnackbarService,
    private activatedRoute: ActivatedRoute,
    private voucherService: VoucherService,
    private http: HttpClient,
    private datepipe: DatePipe,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private snackbar: SnackbarService,
    private _scheduleEffect: ScheduledEffects,
    private paymentService: PaymentService
  ) { }
  ngOnInit(): void {

    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      this.stateName = data.element.stateName;
      if (data != null && data) {
        this.navigate = data.column.linkMetaData.navigateToLabel;
      }
    })
    if (!this.global.userDetails.value) {
      this.router.navigateByUrl('/dashboard')
    }
    if (this.navigate != null) {
      if (this.global.userDetails.value.roleId == Roles.OperationStaff && this.navigate != "Candidate Details") {
        const breadcrumb = [
          {
            label: "Home",
            url: "/dashboard",
          },
          {
            label: "Schedule Accommodations",
            url: "/loader/schedule-accommodations",
          },
          {
            label: "View Details",
            url: "/loader/schedule-accommodation-details",
          },
          {
            label: "payment",
            url: "",
          },
        ];
        this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
      }
      else if (this.global.userDetails.value.roleId == Roles.OperationStaff && this.navigate == "Candidate Details") {
        const breadcrumb = [
          {
            label: "Home",
            url: "/dashboard",
          },
          {
            label: "Manage Candidate",
            url: "/loader/candidate-management",
          },
          {
            label: "Candidate Details",
            url: "/loader/candidate-details",
          },
          {
            label: "payment",
            url: "",
          },
        ];
        this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
      }
    }
    else if (this.global.userDetails.value.roleId == Roles.TrainingInstitue) {
      const breadcrumb = [
        {
          label: "Home",
          url: "/dashboard",
        },
        {
          label: "Voucher",
          url: "/voucher",
        },
        {
          label: "Buy New Voucher",
          url: "/voucher/buy-new-voucher",
        },
        {
          label: "Payment",
          url: "",
        },
      ];
      this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
    } else if (this.global.userDetails.value.roleId == Roles.Sponsor) {
      const breadcrumb = [
        {
          label: "Home",
          url: "/dashboard",
        },
        {
          label: "Voucher",
          url: "/voucher",
        },
        {
          label: "Buy New Voucher",
          url: "/voucher/buy-new-voucher",
        },
        {
          label: "Payment",
          url: "",
        },
      ];
      this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
    }



    this.store.dispatch<Action>(clearChargeResponse());

    var v = this.ngDynamicBreadcrumbService.breadcrumbLabels;
    this.cvvForm = new FormGroup({
      cvv: new FormControl("", [Validators.required]),
    });
    this.ACHform = new FormGroup({
      AccountNumber: new FormControl("", [Validators.required]),
      AccountHolderName: new FormControl("", [Validators.required]),
      RoutingNumber: new FormControl("", [Validators.required]),
      bankType: new FormControl("", [Validators.required]),
      bankName: new FormControl("", [Validators.required]),
    });
    const alphabetRegexWithSpace = /^[a-zA-Z]+\s[a-zA-Z]+\s?[a-zA-Z]*\s?[a-zA-Z]*\s?[a-zA-Z]*$/;

    this.form = new FormGroup({
      CardNumber: new FormControl("", Validators.compose([Validators.required])),
      CardName: new FormControl("", Validators.compose([Validators.required, Validators.pattern(alphabetRegexWithSpace), Validators.minLength(3)])),
      Year: new FormControl("", [Validators.required]),
      CVV: new FormControl("", [Validators.required]),
    });
    this.Validator = new FormGroup({
      code: new FormControl("", [Validators.required]),
    });

    this.global.userDetails.subscribe((user) => {
      if (user) {
        this.personId = user.personId;
        this.roleId = user.roleId;
        this.personTenantRoleId = user.personTenantRoleId;
        this.clientORtenanatId = user.clientORtenantId;
      }
    });



    this.store.select(recentLinkClickEventSelector).subscribe((x: cellClickEvent) => {
      if (x) {
        this.recentLinkClickEvent = x;
      }

    })
    this.store.select(get_decodeInfo).subscribe((data) => {
      if (data) {
        this.personalInfo = {
          firstname: data.given_name,
          lastname: data.family_name,
          email: data.email,
          method: "card",
          personId: this.personId.toString(),
        };
      }
    });
    // this.store.dispatch<Action>(
    //   getPaymentCustomerId({ PersonTenantRoleId: this.personTenantRoleId })
    // );

    this.store.select(getSavedVoucher$).subscribe((code) => {
      this.Vocher = code;
    });

    this.store.select(getCustomerId$).subscribe((obj) => {
      if (obj) {
        if (obj.fattmerchantCustomerId) {
          this.customerIdObj = obj;
          this.store.dispatch(
            getPaymentMethod({ customerId: obj.fattmerchantCustomerId })
          );
          this.store.dispatch(
            getPaymentMethod({ customerId: obj.fattmerchantCustomerId })
          );
        } else {
          var body = new CreatePaymentCustomerIdBody();
          body.personid = this.personalInfo.personId;
          body.firstname = this.personalInfo.firstname;
          body.email = this.personalInfo.email;
          body.lastname = this.personalInfo.lastname;
          //this.store.dispatch<Action>(createPaymentCustomerId({ body: body }));
        }
        this.store.select(createPaymentCustomerId$).subscribe((data) => {
          if (data) {
            this.customerIdObj.fattmerchantCustomerId = data.id;
          }
        });
      }
    });

    this.global.userDetails.subscribe((user) => {
      if (user != null) {
        (this.global.userDetails.getValue().roleId != Roles.OperationStaff && this.global.userDetails.getValue().roleId != Roles.SupportingStaff) ? this.store.dispatch(VoucherDetails({ personDetail: user })) : null;
      }
    });

    this.store.select(getPaymentMethods$).subscribe((data) => {
      if (data) {
        this.paymentMethod = [...data];
      }
    });

    // this.store.select(getLoading).subscribe(data=>{
    //   this.disablePaybutton=data
    // })

    this.store.select(getErrorMessage).subscribe((data: any) => {
      if (data) {
        var error = data.error.slice(data.error.search("Content"));
        this.snackbar.callSnackbaronError(error);
      }
    });
    this.global.userDetails.subscribe((user) => {
      if (user != null) {
        this.trainingInstituteId = user.stateId;
      }
    });

    this.store.select(breadCrumbsSelector)
      .subscribe((x: any) => {
        this.breadCrumbsArray = x
      }
      );

    this.activatedRoute.paramMap.subscribe((x: any) => {
      if (x) {
        this.candidateId = Number(x.params.candidateId);
      }
    });
    var count = 0;
    // this.listExam = this.store.select(get_cartItems);
    // this.listExam.subscribe((cartItems) => {
    //   if (cartItems.length > 0) {
    //     this.cartId = cartItems[0].cartId;
    //     this.subtotal = cartItems.reduce((acc, val) => (acc += val.amount), 0);
    //     this.total = cartItems.reduce((acc, val) => (acc += val.amount), 0);

    //     cartItems.forEach((dat) => {
    //       this.lineItems.push({
    //         id: dat.personEventId.toString(),
    //         item: dat.examName,
    //         details: dat.cartId.toString(),
    //         quantity: 1,
    //         price: dat.amount,
    //       });
    //     });
    //   } else {
    //     this.noCartItemds = true;
    //   }
    // });
    this.store.select(get_cartItems).subscribe((cartItems) => {
      this.lineItems = []
      if (cartItems.length > 0) {
        this.listExam = JSON.parse(JSON.stringify(cartItems));
        this.global.addedToCartInfo = cartItems
        this.cartId = cartItems[0].cartId;
        this.subtotal = cartItems.reduce((acc, val) => (acc += val.amount), 0);
        this.total = this.listExam.reduce((acc, val) => (acc += val.amount), 0);
        this.lineItems = []
        cartItems.forEach((dat) => {
          this.lineItems.push({
            id: dat.personEventId.toString(),
            item: dat.examCode == null ? dat.examName : dat.examCode,
            details: dat.cartId.toString(),
            quantity: 1,
            price: dat.amount,
          });
        });
      } else {
        this.noCartItemds = true;
      }
    });

    this.store.select(createPaymentMethodResponse$).subscribe((res) => {
      if (res) {
        this.services.callSnackbaronSuccess(`Card Saved Successfully`);
        this.store.dispatch(
          getPaymentMethod({
            customerId: this.customerIdObj.fattmerchantCustomerId,
          })
        );
      }
    });


    this.store.select(getChargeResources$).subscribe((response: any) => {
      if (response) {
        if (response.cartItemStatus?.find(x => x.isPreflightCompleted == false && x.preFlightCheckResponses != null)) {
          //Get List of Failed Exam
          let PerfilghtFailedExam = [];
          let isItemDeletedFromCart = []
          PerfilghtFailedExam = response.cartItemStatus?.filter(x => x.isPreflightCompleted == false);
          PerfilghtFailedExam.forEach(element => {
            PerfilghtFailedExam.push(element.preFlightCheckResponses.filter(x => x.isPreFlightCheckPassed == false))
            isItemDeletedFromCart.push(element.preFlightCheckResponses.filter(x => x.isItemDeletedFromCart == true))
          })
          //get  the failed exam in cart
          PerfilghtFailedExam = this.global.addedToCartInfo.filter(y => PerfilghtFailedExam[1].find(z => y.personEventId == z.personEventId))
          if (response.isPaymentCompleted == false) {
            if (PerfilghtFailedExam.length > 0 && isItemDeletedFromCart.length == 0) {
              PerfilghtFailedExam.forEach(x => {
                setTimeout(() => {
                  this.store.dispatch<Action>(
                    removeCartItem({ personTenantRoleId: this.candidateId, cartItemId: Number(x.personEventCartId) })
                  );
                }, 1000);
              })
            }

          }
          if (this.openpopOnlyOnce && this.dialog.openDialogs.length == 0) {
            const dialogRef = this.dialog.open(RescheduledComponent, {
              width: '1000px',
              height: '500px',
              disableClose: true,
              data: PerfilghtFailedExam
            })
              .afterClosed().subscribe(data => {
                if (data || data.confirmed == true) {
                  this.openpopOnlyOnce = true
                }
              })
            this.openpopOnlyOnce = false
          }

        }

        this.event?.unsubscribe();
        this.form.reset();

        if (response.isPaymentCompleted) {
          try {
            response.cartItemStatus?.forEach((element, i) => {
              let ScheduleExamMessage = element.scheduledMessage != null ? element.scheduledMessage : "Exam Scheduled Sucessfully"
              setTimeout(() => {
                element.isScheduled ? this.callsnackbarMessage(ScheduleExamMessage) : element.isScheduled == false ? this.services.callSnackbaronError(element.preflightMessage) : null
              }, i * 5000)

            })

            this.router.navigateByUrl('loader/candidate-details')



          }
          catch (e) {



            this.router.navigateByUrl('loader/candidate-details')




          }
        }
        else if (response.isPaymentCompleted == false) {
          try {
            if (response.paymentMessage) {
              let placeValue = response.paymentMessage?.search("Content");
              placeValue == -1 ? this.services.callSnackbaronWarning(`${response.paymentMessage}`) : this.services.callSnackbaronWarning(`${response.paymentMessage.slice(placeValue, -1)}`)
            }
            else {
              this.services.callSnackbaronWarning(`${response.cartItemStatus[0]?.preflightMessage}`)
            }
          }
          catch (e) {
            this.services.callSnackbaronWarning(`Payment Unsuccessful`)
          }
        }
        setTimeout(
          () =>
            this.store.dispatch<Action>(
              getCartItems({ personTenantRoleId: this.personTenantRoleId })
            ),
          1000
        );
        // const dialogRef = this.dialog.open(PaymentTranscationDetailsComponent, { data: { response: response, total: this.total }, });
        // dialogRef.afterClosed().subscribe(result => {
        // });
        if (response.success == false && response.message != null) {
          this.services.callSnackbaronError(response.message);
        }
        else {
          // this.services.callSnackbaronSuccess(`Exam Scheduled Successfully`);
          this.ACHform.reset();
          this.form.reset();
          if (this.navigate != "Candidate Details") {
            this.router.navigateByUrl("/loader/schedule-accommodation-details");
          } else if (this.navigate == "Candidate Details") {
            this.router.navigateByUrl("/loader/candidate-details");
          }
        }
        // this.store.dispatch<Action>(
        //   getPaymentCustomerId({ PersonTenantRoleId: this.personTenantRoleId })
        // );
      }
    });

    //getting voucherList from vouchercart api
    this.store.dispatch(
      getvoucherCartDetails({
        personTentantRoleId: this.personTenantRoleId,
      })
    );
    this.store.select(gotVoucherList).subscribe((data: any) => {
      try {
        this.generateVoucher = data;
        this.voucherCartList = [];
        this.voucherlineItems = [];
        this.subtotalVoucherAmount = 0;
        data.forEach((element) => {
          this.subtotalVoucherAmount += element.quantity * element.amount;
          this.cartIdVoucher = element.cartId;
          this.voucherCartList.push({
            id: element.examTypeId,
            examName: element.examName,
            price: element.amount,
            value: element.amount * element.quantity,
            personEventCartId: element.personEventCartId,
            quantity: element.quantity,
            examCode: element.examCode,
          });
          this.voucherlineItems.push({
            id: element.examCode.toString(),
            item: element.examCode.toString(),
            details: element.cartId.toString(),
            quantity: element.quantity,
            price: element.amount,
          });
        });
        this.totalVoucherAmount = this.subtotalVoucherAmount + 1;
      } catch (e) { }
    });
    this.loadValidations()
    this.payPalCall()
    if (this.global.userDetails.value.roleId === this.trainingRoleId)
      this.paymentOptions.push({ id: 2, name: PaymentOptions.ACH });

  }

  navigateToParentUsingBreadCrumb() {
    // extracting the second last 
    let crumb = this.breadCrumbsArray[this.breadCrumbsArray.length - 1];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.store.dispatch(setRecentLinkClickEvent({ event: this.recentLinkClickEvent }));
      this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
    }
    // this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
  }
  closeExapanel() {
    if (this.subtotal == 0) {
      this.closePanel = false
      return true
    }
  }

  cardnumber_mask: any;
  securitycode_mask: any;
  expirationdate_mask: any;


  public ngAfterViewInit(): void {

  }
  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    PaymentComponent.enteredValue = event.key;
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }



  }

  step = 0;
  setStep(index: number) {
    this.step = index;
  }
  setStep2(index: number) {
    this.step2 = index == this.step2 ? -1 : index;
  }

  nextStep() {
    this.step++;
  }

  prevStep() {
    this.step--;
  }
  callsnackbarMessage(message) {
    this.services.callSnackbaronSuccess(message)
  }

  cancel() {
    if (
      this.roleId != this.trainingRoleId &&
      this.roleId != this.sponsorRoleID
    ) {
      this.router.navigateByUrl(`scheduleExam/${this.candidateId}`);
      this.store.dispatch<Action>(getRegisteredExam({ candidateId: this.candidateId }));
    } else {
      this.router.navigateByUrl(`/voucher/buy-new-voucher`);
    }
  }

  deleteItem(id: any, i: number): void {
    if (!id) return;
    this._scheduleEffect.deleteItem(this.clientORtenanatId, id.personEventCartId)
      .subscribe((data: boolean) => {
        if (data) {
          this.listExam.splice(i, 1);
          const cartItems: Array<CartItem> = this.listExam;
          if (cartItems.length > 0) {
            this.cartId = cartItems[0].cartId;
            this.subtotal = cartItems.reduce((acc, val) => (acc += val.amount), 0);
            this.total = this.listExam.reduce((acc, val) => (acc += val.amount), 0);
            this.lineItems = [];
            cartItems.forEach((n) => {
              this.lineItems.push({
                id: n.personEventId.toString(),
                item: n.examCode,
                details: n.cartId.toString(),
                quantity: 1,
                price: n.amount,
              });
            });
          } else {
            this.noCartItemds = true;
            this.total = 0;
            this.subtotal = 0;
            this.lineItems = [];
          }
          this.snackbar.callSnackbaronSuccess("Successfully Deleted Cart Item")
          // this.store.dispatch(getExamId{})
          this.store.dispatch<Action>(getRegisteredExam({ candidateId: this.candidateId }));
        }
      },
        (error: HttpErrorResponse) => { },
        () => { }
      );
    /* this.store.dispatch<Action>(
      removeCartItem({
        cartItemId: this.clientORtenanatId,
        personTenantRoleId: id.personEventCartId,
      })
    );
    this.store.dispatch<Action>(
      getCartItems({ personTenantRoleId: this.personTenantRoleId })
    ); */
    this.store.dispatch<Action>(getRegisteredExam({ candidateId: this.candidateId }));
  }

  deleteVoucherItem(carteventId: any, i: number): void {
    if (!carteventId) return;
    const getVoucherItems = () => {
      this.http.get(
        `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
      )
        .subscribe((data: any) => {
          this.global.cart = data;
          try {
            this.generateVoucher = data;
            this.voucherCartList = [];
            this.voucherlineItems = [];
            this.subtotalVoucherAmount = 0;
            data.forEach((element) => {
              this.subtotalVoucherAmount += element.quantity * element.amount;
              this.cartIdVoucher = element.cartId;
              this.voucherCartList.push({
                id: element.examTypeId,
                examName: element.examName,
                price: element.amount,
                value: element.amount * element.quantity,
                personEventCartId: element.personEventCartId,
                quantity: element.quantity,
                examCode: element.examCode,
              });
              this.voucherlineItems.push({
                id: element.examCode.toString(),
                item: element.examCode.toString(),
                details: element.cartId.toString(),
                quantity: element.quantity,
                price: element.amount,
              });
            });
            this.totalVoucherAmount = this.subtotalVoucherAmount + 1;
          } catch (e) { }
        },
          (error: HttpErrorResponse) => { },
          () => { });
    }

    this._scheduleEffect.deleteVoucherItem(this.personTenantRoleId, carteventId.personEventCartId)
      .subscribe((data: boolean) => {
        if (data) {
          this.snackbar.callSnackbaronSuccess("Voucher deleted SuccessFully")
          data ? getVoucherItems() : null;
          setTimeout(() => {
            data && this.voucherCartList.length == 0 ? this.router.navigateByUrl("/voucher/buy-new-voucher") : null
          }, 1000)

        }

      }, (error: HttpErrorResponse) => { });
    /* this.store.dispatch(
      getcartId({
        personTentantRoleId: this.personTenantRoleId,
        cartId: carteventId.personEventCartId,
      })
    );
    this.store.select(stausofVoucherDetail).subscribe((data: any) => {
      if (data == true) {
        this.store.dispatch(
          getvoucherCartDetails({
            personTentantRoleId: this.personTenantRoleId,
          })
        );
      }
      let subs: Subscription = this.http
        .get(
          `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
        )
        .subscribe((cart: any) => {
          this.global.cart = cart;
          subs?.unsubscribe();
        });
    }); */
  }

  edit(event) {
    (
      document.getElementById(event.personEventCartId) as HTMLInputElement
    ).focus();
    this.editable = false;
    this.removeEdit = true;
    this.setEditFlag = event.personEventCartId;
  }
  // remove(item){
  //   item.quantity = item.quantity - 1;
  //   document.getElementById(`${item.examName}`).innerHTML = item.quantity;

  // }
  // add(item){
  //   item.quantity = item.quantity + 1;
  //   document.getElementById(`${item.examName}`).innerHTML = item.quantity;
  // }

  save(item) {
    this.editable = true;
    this.removeEdit = false;
    item.quantity = Number(
      (document.getElementById(item.personEventCartId) as HTMLInputElement)
        .value
    );
    this.store.dispatch(
      reAddData({
        itemId: Number(item.personEventCartId),
        quantity: item.quantity,
      })
    );
    this.store.select(editedCartDetails).subscribe((data) => {
      if (data != null) {
        this.store.dispatch(
          getvoucherCartDetails({
            personTentantRoleId:
              this.global.userDetails.value.personTenantRoleId,
          })
        );
      }
    });
  }

  onEnter(event, item) {
    this.store.dispatch(
      reAddData({
        itemId: Number(item.personEventCartId),
        quantity: event.target.value,
      })
    );
    this.store.select(editedCartDetails).subscribe((data) => {
      if (data != null) {
        this.store.dispatch(
          getvoucherCartDetails({
            personTentantRoleId:
              this.global.userDetails.value.personTenantRoleId,
          })
        );
        this.editable = true;
      }
    });
  }

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onChange($event) {
  }

  payment(method: PaymentOptions) {
    let firstName = this.form.value.CardName.split(" ");
    let cardNumber = this.form.value.CardNumber.includes(" ")
      ? this.form.value.CardNumber.replace(/\s+/g, "")
      : this.form.value.CardNumber;

    let voucherList = [];

    this.store.select(listOFVoucher).subscribe((data: any) => {
      voucherList = [];
      if (data) {
        voucherList = data;
      }
    });

    const voucher = this.listExam.filter((x) =>
      voucherList.find((y) => y.examTypeId == x.examTypeId)
    );

    //sponsor and training institute for card
    if (method !== PaymentOptions.ACH &&
      (this.global.userDetails.value.roleId == this.trainingRoleId ||
        this.global.userDetails.value.roleId == this.sponsorRoleID)
    ) {

      var date = this.datepipe.transform(
        this.expirationdate_mask.value,
        "MMyy"
      );
      var meta = {
        subtotal: this.subtotalVoucherAmount,
        lineItems: this.voucherlineItems,
        reference: this.global.userDetails.value.stateName,
      };
      this.payVoucherCart = {
        cartId: this.cartIdVoucher.toString(),
        personId: this.personalInfo.personId.toString(),
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        firstname: firstName[0],
        lastname: firstName.slice(1, 3).join(" "),
        email: "",
        method: "",
        card_number: cardNumber,
        card_cvv: this.form.value.CVV,
        card_exp: date,
        customer_id: "",
        payment_method_id: "",
        meta: meta,
        bank_account: "",
        bank_routing: "",
        bank_name: "",
        bank_type: "",
        bank_holder_type: "",
        total: this.subtotalVoucherAmount,
        pre_auth: 0,
      };
      this.store.dispatch(paymentDetails({
        personTentantRoleId: this.global.userDetails.value.personTenantRoleId,
        details: this.payVoucherCart,
      }));
      this.event = this.store.select(getLoading).subscribe(data => {
        this.disablePaybutton = data
      })

      let count = 0;
      this.store.select(transcationdetails).subscribe((data: any) => {
        let voucherData = {};
        try {
          if (data != null && data.success == true && count == 0) {
            count = 1;
            this.generateVoucher.forEach((voucherDetails: any) => {
              voucherList.forEach((item) => {
                if (item.voucherCode === voucherDetails.examCode) {
                  voucherData[voucherDetails.examCode] = {
                    price: JSON.stringify(voucherDetails.amount),
                    count: JSON.stringify(voucherDetails.quantity),
                    voucherName: voucherDetails.examName,
                    examTypeId: item.examTypeId,
                    voucherItemId: item.voucherItemId,
                  };
                }
              });

              this.fetchVoucherItems = {
                personTenantRoleId:
                  this.global.userDetails.value.personTenantRoleId,
                stateId: this.trainingInstituteId,
                traniningInsId:
                  this.global.userDetails.value.roleId == this.trainingRoleId
                    ? this.global.userDetails.value.clientORtenantId
                    : 0,
                voucherData,
              };
            });
            
            this.store.dispatch(
              createVoucher({ newvoucherDetails: this.fetchVoucherItems })
            );
            this.store.select(generatedSucessfully).subscribe((message) => {
              if (message != null && message.message == "Success") {

                this.generateVoucher = [];

                this.router.navigateByUrl("/voucher");
                this.event?.unsubscribe();
                let subs: Subscription = this.http
                  .get(
                    `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
                  )
                  .subscribe((cart: any) => {
                    this.global.cart = cart;
                    subs?.unsubscribe();
                  });
              }
            });
          }
        } catch (e) { }
        this.store.dispatch(makenull());
      });
    }
    //operation staff for card
    else if (method === PaymentOptions.Credit &&
      (this.global.userDetails.value.roleId != this.trainingRoleId ||
        this.global.userDetails.value.roleId != this.sponsorRoleID)
    ) {
      var date = this.datepipe.transform(
        this.expirationdate_mask.value,
        "MMyy"
      );
      var meta = {
        subtotal: this.subtotal,
        lineItems: this.lineItems,
        reference: this.stateName,
      };
      var body: chargeBody = {
        cartId: this.cartId.toString(),
        personId: this.personalInfo.personId.toString(),
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        firstname: firstName[0],
        lastname: firstName.slice(1, 3).join(" "),
        email: "",
        method: "",
        card_number: this.form.value.CardNumber,
        card_cvv: this.form.value.CVV,
        card_exp: date,
        customer_id: this.customerIdObj.fattmerchantCustomerId,
        payment_method_id: "",
        meta: meta,
        bank_account: this.ACHform.value.AccountNumber,
        bank_routing: this.ACHform.value.RoutingNumber,
        bank_name: "HGFC",
        bank_type: this.ACHform.value.bankType,
        bank_holder_type: this.ACHform.value.bankHoldeType,
        total: this.total,
        pre_auth: 0,
      };
      this.store.dispatch<Action>(
        makeCharge({
          chargeBodu: body,
          personTenantRole:
            this.recentLinkClickEvent.element.personTenantRoleId,
        })
      );
      this.event = this.store.select(getLoading).subscribe(data => {
        this.disablePaybutton = data
      })
    }
    else if (method === PaymentOptions.ACH
    ) {

      let name = this.ACHform.value.AccountHolderName.split(" ");
      var meta = {
        subtotal: this.subtotalVoucherAmount,
        lineItems: this.voucherlineItems,
        reference: this.global.userDetails.value.stateName,
      };

      var achChargeBody: AchChargeBody = {
        personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
        personId: this.personalInfo.personId.toString(),
        firstname: name[0],
        lastname: name[1],
        email: this.personalInfo.email,
        accountType: this.ACHform.value.bankType,
        routingNumber: this.ACHform.value.RoutingNumber,
        accountNumber: this.ACHform.value.AccountNumber,
        nameOnAccount: this.ACHform.value.AccountHolderName,
        bankName: this.ACHform.value.bankName,
        meta: meta
      };
      let count = 0;
      // this.store.dispatch<Action>(makeChargeACH({ AchChargeBody: achChargeBody, personTenantRole: this.global.userDetails.value.personTenantRoleId }));

      this.paymentService.makeAchPayment(this.global.userDetails.value.personTenantRoleId, achChargeBody).subscribe((data) => {
        let voucherData = {};
        try {
          if (data != null && data.success == true && count == 0) {
            count = 1;
            this.generateVoucher.forEach((voucherDetails: any) => {
              voucherList.forEach((item) => {
                if (item.voucherCode === voucherDetails.examCode) {
                  voucherData[voucherDetails.examCode] = {
                    price: JSON.stringify(voucherDetails.amount),
                    count: JSON.stringify(voucherDetails.quantity),
                    voucherName: voucherDetails.examName,
                    examTypeId: item.examTypeId,
                    voucherItemId: item.voucherItemId,
                  };
                }
              });

              this.fetchVoucherItems = {
                personTenantRoleId:
                  this.global.userDetails.value.personTenantRoleId,
                stateId: this.trainingInstituteId,
                traniningInsId:
                  this.global.userDetails.value.roleId == this.trainingRoleId
                    ? this.global.userDetails.value.clientORtenantId
                    : 0,
                voucherData,
              };
            });
            this.store.dispatch(
              createVoucher({ newvoucherDetails: this.fetchVoucherItems })
            );
            this.store.select(generatedSucessfully).subscribe((message) => {
              if (message != null && message.message == "Success") {
                this.generateVoucher = [];

                this.router.navigateByUrl("/voucher");
                let subs: Subscription = this.http
                  .get(
                    `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
                  )
                  .subscribe((cart: any) => {
                    this.global.cart = cart;
                    subs?.unsubscribe();
                  });
              }
            });
          }
          else {
            this.snackbar.callSnackbaronWarning(data.message);
          }
        } catch (e) { }
        this.store.dispatch(makenull());
      });
    }

  }



  public schedule(): void {
    let ScheduleDetails = {
      personTentantRole: this.candidateId,
      cartId: this.listExam[0].cartId,
    }
    this.httpservice.getSchedule(this.candidateId, this.cartId, ScheduleDetails).subscribe((data: any) => {
      if (data) {
        if (data.cartItemStatus?.find(x => x.isPreflightCompleted == false && x.preFlightCheckResponses != null)) {
          //Get List of Failed Exam
          let PerfilghtFailedExam = [];
          let isItemDeletedFromCart = []
          PerfilghtFailedExam = data.cartItemStatus?.filter(x => x.isPreflightCompleted == false);
          PerfilghtFailedExam.forEach(element => {
            PerfilghtFailedExam.push(element.preFlightCheckResponses.filter(x => x.isPreFlightCheckPassed == false))
            isItemDeletedFromCart.push(element.preFlightCheckResponses.filter(x => x.isItemDeletedFromCart == true))
          })
          //get  the failed exam in cart
          PerfilghtFailedExam = this.global.addedToCartInfo.filter(y => PerfilghtFailedExam[1].find(z => y.personEventId == z.personEventId))
          if (PerfilghtFailedExam.length > 0 && isItemDeletedFromCart.length == 0) {
            PerfilghtFailedExam.forEach(x => {
              setTimeout(() => {
                this.store.dispatch<Action>(
                  removeCartItem({ personTenantRoleId: this.candidateId, cartItemId: Number(x.personEventCartId) })
                );
              }, 1000);
            })
          }

          const dialogRef = this.dialog.open(RescheduledComponent, {
            width: '1000px',
            height: '500px',
            disableClose: true,
            data: PerfilghtFailedExam
          }).afterClosed().subscribe(data => {
            if (data || data.confirmed == true) {

            }
          })
        }

        if (this.Vocher) {
          let VoucherUpdate = {
            persontenantRoleId: this.candidateId,
            voucherCodes: [this.Vocher],
          }
          if (data.isPaymentCompleted) {
            let isScheduled = data.cartItemStatus.every(x => x.isScheduled);
            if (isScheduled) {
              this.httpservice.getVoucherUpdate(VoucherUpdate).subscribe((data) => {
                if (data != null && data) {
                  this.store.dispatch<Action>(
                    getCartItems({
                      personTenantRoleId:
                        this.global.userDetails.value.personTenantRoleId,
                    })
                  )
                }
              })

            }
          }
        }
        if (data.isPaymentCompleted) {
          try {
            data.cartItemStatus?.forEach((element, i) => {
              let ScheduleExamMessage = element.scheduledMessage != null ? element.scheduledMessage : "Exam Scheduled Sucessfully"
              setTimeout(() => {
                element.isScheduled ? this.callsnackbarMessage(ScheduleExamMessage) : element.isScheduled == false ? this.services.callSnackbaronError(element.preflightMessage) : null
              }, i * 5000)

            })

            this.router.navigateByUrl('loader/candidate-details')



          }
          catch (e) {



            this.router.navigateByUrl('loader/candidate-details')




          }
        }

        else if (data.isPaymentCompleted == false) {
          try {
            if (data.paymentMessage) {
              let placeValue = data.paymentMessage.search("Content");
              placeValue == -1 ? this.services.callSnackbaronWarning(`${data.paymentMessage}`) : this.services.callSnackbaronWarning(`${data.paymentMessage.slice(placeValue, -1)}`)
            }
            else {
              this.services.callSnackbaronWarning(`${data.cartItemStatus[0].preflightMessage}`)
            }
          }
          catch (e) {
            this.services.callSnackbaronWarning('Payment Unsuccessful')
          }
        }
        else {
          this.services.callSnackbaronWarning(`Payment Unsuccessful`)
        }
      }
    }, (error: any) => {
      this.snackbar.callSnackbaronError(`${error.message.message.error}`)
    })
  }

  saveCards(card: paymentMethod, method: string) {
    if (card == null && method == null) {
      var date = this.datepipe.transform(this.expirationdate_mask.value, "MMyy");
      var body1: CreatePaymentMethod = {
        method: "card",
        person_name: this.form.value.CardName,
        lastname: "",
        card_number: this.cardnumber_mask.value,
        card_cvv: this.securitycode_mask.value,
        card_exp: date,
        bank_account: "",
        bank_routing: "",
        bank_name: "",
        bank_type: "",
        bank_holder_type: "",
        customer_id: this.customerIdObj.fattmerchantCustomerId,
      };
      this.store.dispatch<Action>(
        createPaymentMethod({ CreatePaymentMethod: body1 })
      );
    }
    else if (card == null && method == "bank") {
      var meta = {
        tax: 0,
        subtotal: this.subtotal,
        lineItems: this.lineItems,
        reference: this.global.userDetails.value.stateName,
      };
      var body: chargeBody = {
        cartId: this.listExam[0].cartId.toString(),
        personId: "",
        firstname: this.ACHform.value.AccountHolderName,
        lastname: "",
        email: "",
        method: "bank",
        card_number: "",
        card_cvv: "",
        card_exp: "",
        payment_method_id: "",
        bank_account: this.ACHform.value.AccountNumber,
        bank_routing: this.ACHform.value.RoutingNumber,
        bank_name: "HGFC",
        bank_type: this.ACHform.value.bankType,
        bank_holder_type: this.ACHform.value.bankHoldeType,
        customer_id: this.customerIdObj.fattmerchantCustomerId,
        meta: meta,
        total: this.total,
        pre_auth: 0,
      };
      this.store.dispatch<Action>(makeCharge({ chargeBodu: body }));
    }
    else {
      var voucherList;
      this.store.select(listOFVoucher).subscribe((data: any) => {
        voucherList = [];
        if (data) {
          voucherList = data;
        }
      });
      var meta = {
        tax: 0,
        subtotal: this.subtotalVoucherAmount,
        lineItems: this.voucherlineItems,
        reference: this.global.userDetails.value.stateName,
      };
      this.payVoucherCart = {
        cartId: this.cartIdVoucher.toString(),
        personId: this.personalInfo.personId.toString(),
        firstname: this.personalInfo.firstname,
        lastname: this.personalInfo.lastname,
        email: this.personalInfo.email,
        method: this.personalInfo.method,
        card_number: "",
        card_cvv: "",
        card_exp: "",
        customer_id: card.customer_id,
        payment_method_id: card.id,
        meta: meta,
        bank_account: "",
        bank_routing: "",
        bank_name: "",
        bank_type: "",
        bank_holder_type: "",
        total: this.subtotalVoucherAmount,
        pre_auth: 0,
      };
      this.store.dispatch(
        paymentDetails({
          personTentantRoleId: this.global.userDetails.value.personTenantRoleId,
          details: this.payVoucherCart,
        })
      );
      let count = 0;
      this.store.select(transcationdetails).subscribe((data: any) => {
        let voucherData = {};
        try {
          if (data != null && data.success == true && count == 0) {
            count = 1;
            this.generateVoucher.forEach((voucherDetails: any) => {
              voucherList.forEach((item) => {
                if (item.voucherCode === voucherDetails.examCode) {
                  voucherData[voucherDetails.examCode] = {
                    price: JSON.stringify(voucherDetails.amount),
                    count: JSON.stringify(voucherDetails.quantity),
                    voucherName: voucherDetails.examName,
                    examTypeId: item.examTypeId,
                    voucherItemId: item.voucherItemId,
                  };
                }
              });

              this.fetchVoucherItems = {
                personTenantRoleId:
                  this.global.userDetails.value.personTenantRoleId,
                stateId: this.trainingInstituteId,
                traniningInsId:
                  this.global.userDetails.value.roleId == this.trainingRoleId
                    ? this.global.userDetails.value.clientORtenantId
                    : 0,
                voucherData,
              };
            });
            this.store.dispatch(
              createVoucher({ newvoucherDetails: this.fetchVoucherItems })
            );
            this.store.select(generatedSucessfully).subscribe((message) => {
              if (message != null && message.message == "Success") {
                this.generateVoucher = [];
                this.router.navigateByUrl("/voucher");
                let subs: Subscription = this.http
                  .get(
                    `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
                  )
                  .subscribe((cart: any) => {
                    this.global.cart = cart;
                    subs?.unsubscribe();
                  });
              }
            });
          }
        } catch (e) { }
        this.store.dispatch(makenull());
      });
    }
    this.form.reset();
  }
  achPayment(card: paymentMethod, method: string) {
    if (card == null && method == "bank") {
      var voucherList;
      this.store.select(listOFVoucher).subscribe((data: any) => {
        voucherList = [];
        if (data) {
          voucherList = data;
        }
      });
      var meta = {
        tax: 0,
        subtotal: this.subtotalVoucherAmount,
        lineItems: this.voucherlineItems,
        reference: this.global.userDetails.value.stateName,
      };
      this.payVoucherCart = {
        cartId: this.cartIdVoucher.toString(),
        personId: "",
        firstname: this.ACHform.value.AccountHolderName,
        lastname: "",
        email: "",
        method: "bank",
        card_number: "",
        card_cvv: "",
        card_exp: "",
        payment_method_id: "",
        bank_account: this.ACHform.value.AccountNumber,
        bank_routing: this.ACHform.value.RoutingNumber,
        bank_name: "HDFC",
        bank_type: this.ACHform.value.bankType,
        bank_holder_type: this.ACHform.value.bankHoldeType,
        customer_id: this.customerIdObj.fattmerchantCustomerId,
        meta: meta,
        total: this.subtotalVoucherAmount,
        pre_auth: 0,
      };
      this.store.dispatch(
        paymentDetails({
          personTentantRoleId: this.global.userDetails.value.personTenantRoleId,
          details: this.payVoucherCart,
        })
      );
      let count = 0;
      this.store.select(transcationdetails).subscribe((data: any) => {
        let voucherData = {};
        try {
          if (data != null && data.success == true && count == 0) {
            count = 1;
            this.generateVoucher.forEach((voucherDetails: any) => {
              voucherList.forEach((item) => {
                if (item.voucherCode === voucherDetails.examCode) {
                  voucherData[voucherDetails.examCode] = {
                    price: JSON.stringify(voucherDetails.amount),
                    count: JSON.stringify(voucherDetails.quantity),
                    voucherName: voucherDetails.examName,
                    examTypeId: item.examTypeId,
                    voucherItemId: item.voucherItemId,
                  };
                }
              });

              this.fetchVoucherItems = {
                personTenantRoleId:
                  this.global.userDetails.value.personTenantRoleId,
                stateId: this.trainingInstituteId,
                traniningInsId:
                  this.global.userDetails.value.roleId == this.trainingRoleId
                    ? this.global.userDetails.value.clientORtenantId
                    : 0,
                voucherData,
              };
            });
            this.store.dispatch(
              createVoucher({ newvoucherDetails: this.fetchVoucherItems })
            );
            this.store.select(generatedSucessfully).subscribe((message) => {
              if (message != null && message.message == "Success") {
                this.generateVoucher = [];

                this.router.navigateByUrl("/voucher");
                let subs: Subscription = this.http
                  .get(
                    `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
                  )
                  .subscribe((cart: any) => {
                    this.global.cart = cart;
                    subs?.unsubscribe();
                  });
              }
            });
          }
        } catch (e) { }
        this.store.dispatch(makenull());
      });
    }
  }

  async payPalCall() {
    let paypal;

    try {
      paypal = await loadScript({
        clientId: environment.PayPalConfigurationId,
      });
    } catch (error) {
      console.error("failed to load the PayPal JS SDK script", error);
    }

    if (paypal) {
      try {
        await paypal
          .Buttons({
            style: {
              layout: "horizontal",
              height: 35,
              color: "silver",

            },
            onClick() {
              let Values: any = document.getElementById('payPalButton').children[0]
              Values.style.border = "1px solid #0076c1"
              Values.style.borderRadius = "5px"
            },
            createOrder: (_) => {
              if (
                this.global.userDetails.value.roleId == this.trainingRoleId ||
                this.global.userDetails.value.roleId == this.sponsorRoleID
              ) {
                var meta = {
                  subtotal: this.subtotalVoucherAmount,
                  lineItems: this.voucherlineItems,
                  reference: this.global.userDetails.value.stateName,
                };
                const body = {
                  meta: meta,
                  personId: this.personId.toString(),
                  personTenantRoleId: this.candidateId,
                };
                return this.httpservice
                  .getPayPalVoucherPayment(body)
                  .toPromise()
                  .then((res: paypal) => res.id);
              } else {
                const meta = {
                  subtotal: this.subtotal,
                  lineItems: this.lineItems,
                  reference: this.global.userDetails.value.stateCode ?? "?",
                };
                const body = {
                  meta: meta,
                  personId: this.global.personId.toString(),
                  personTenantRoleId: this.candidateId,
                };
                return this.httpservice
                  .getPayPalPayment(body)
                  .toPromise()
                  .then((res: paypal) => res.id);
              }
            },
            onApprove: (data) => {
              if (
                this.global.userDetails.value.roleId == this.trainingRoleId ||
                this.global.userDetails.value.roleId == this.sponsorRoleID
              ) {
                return this.httpservice
                  .confirmPayPalVoucherPayment(data.orderID, data.payerID, {})
                  .toPromise()
                  .then((_) => {
                    var voucherList;
                    this.store.select(listOFVoucher).subscribe((data: any) => {
                      voucherList = [];
                      if (data) {
                        voucherList = data;
                      }
                    });
                    let voucherData = {};
                    try {
                      this.generateVoucher.forEach((voucherDetails: any) => {
                        voucherList.forEach((item) => {
                          if (item.voucherCode === voucherDetails.examCode) {
                            voucherData[voucherDetails.examCode] = {
                              price: JSON.stringify(voucherDetails.amount),
                              count: JSON.stringify(voucherDetails.quantity),
                              voucherName: voucherDetails.examName,
                              examTypeId: item.examTypeId,
                              voucherItemId: item.voucherItemId,
                            };
                          }
                        });

                        this.fetchVoucherItems = {
                          personTenantRoleId:
                            this.global.userDetails.value.personTenantRoleId,
                          stateId: this.trainingInstituteId,
                          traniningInsId:
                            this.global.userDetails.value.roleId ==
                              this.trainingRoleId
                              ? this.global.userDetails.value.clientORtenantId
                              : 0,
                          voucherData,
                        };
                      });
                      this.store.dispatch(
                        createVoucher({
                          newvoucherDetails: this.fetchVoucherItems,
                        })
                      );
                      this.store
                        .select(generatedSucessfully)
                        .subscribe((message) => {
                          if (message != null && message.message == "Success") {
                            this.generateVoucher = [];
                            this.router.navigateByUrl("/voucher");
                            let subs: Subscription = this.http
                              .get(
                                `${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`
                              )
                              .subscribe((cart: any) => {
                                this.global.cart = cart;
                                subs?.unsubscribe();
                              });
                          }
                        });
                    } catch (e) { }
                    this.store.dispatch(makenull());
                  });
              } else {
                return this.httpservice
                  .confirmPayPalPayment(data.orderID, data.payerID, {})
                  .toPromise()
                  .then((_) => {
                    this.router.navigateByUrl("/loader/candidate-details");
                    this.services.callSnackbaronSuccess(
                      "Payment completed successfully"
                    );
                    this.store.dispatch(
                      getCartItems({ personTenantRoleId: this.candidateId })
                    );
                  });
              }
            },
            onCancel: (data) => {
              let Values: any = document.getElementById('payPalButton').children[0]
              Values.style.border = "none"
              Values.style.borderRadius = "none"
              return this.httpservice
                .cancelPayPalPayment(data.orderID, {})
                .toPromise();
            },
            onError: (error) => {
              this.services.callSnackbaronError(error.message);
            },
          })
          .render("#payPalButton");
      } catch (error) {
        console.error("failed to render the PayPal Buttons", error);
      }
    }
  }

  async selectrangeactive(event) {
    this.selectedPaymentMethod = event.name;
    if (event.name === PaymentOptions.ACH || event.name === PaymentOptions.Credit) {
      this.loadValidations();
    } else {
      this.form.reset();
    }
  }


  untilPaymentProcess(item) {
    if (item == 0) {
      if (this.disablePaybutton) {
        this.hideScreen = true
        return false;
      }
      else {
        return true;
      }

    }
    else if (item == 1) {
      if (this.disablePaybutton) {
        return this.disablePaybutton;
      }
      else {
        return false;
      }
      //   // return true
    }
  }

  loadValidations() {
    setTimeout(() => {
      this.cardnumber = <HTMLInputElement>document.getElementById("cardnumber");
      this.expirationdate = <HTMLInputElement>(
        document.getElementById("expirationdate")
      );
      this.securitycode = <HTMLInputElement>(
        document.getElementById("securitycode")
      );
      this.cardnumber_mask = IMask(this.cardnumber, {
        mask: [
          {
            mask: "000000000000000",
            regex: "^3[47]\\d{0,13}",
            cardtype: "american express",
          },
          {
            mask: "0000000000000000",
            regex: "^(?:6011|65\\d{0,2}|64[4-9]\\d?)\\d{0,12}",
            cardtype: "discover",
          },
          {
            mask: "00000000000000",
            regex: "^3(?:0([0-5]|9)|[689]\\d?)\\d{0,11}",
            cardtype: "diners",
          },
          {
            mask: "0000000000000000",
            regex: "^(5[1-5]\\d{0,2}|22[2-9]\\d{0,1}|2[3-7]\\d{0,2})\\d{0,12}",
            cardtype: "mastercard",
          },
          {
            mask: "000000000000000",
            regex: "^(?:2131|1800)\\d{0,11}",
            cardtype: "jcb15",
          },
          {
            mask: "0000000000000000",
            regex: "^(?:35\\d{0,2})\\d{0,12}",
            cardtype: "jcb",
          },
          {
            mask: "0000000000000000",
            regex: "^(?:5[0678]\\d{0,2}|6304|67\\d{0,2})\\d{0,12}",
            cardtype: "maestro",
          },
          {
            mask: "0000000000000000",
            regex: "^4\\d{0,15}",
            cardtype: "visa",
          },
          {
            mask: "0000000000000000",
            regex: "^62\\d{0,14}",
            cardtype: "unionpay",
          },
          {
            mask: "0000000000000000",
            cardtype: "Unknown",
          },
        ],
        dispatch: function (appended, dynamicMasked) {
          var number = (dynamicMasked.value + appended).replace(/\D/g, '');
          for (var i = 0; i < dynamicMasked.compiledMasks.length; i++) {
            let re = new RegExp(dynamicMasked.compiledMasks[i].regex);
            if (number.match(re) != null) {
              return dynamicMasked.compiledMasks[i];
            }
          }
        }
      });

      this.expirationdate_mask = IMask(this.expirationdate, {
        mask: "YYYY{-}MM",
        blocks: {
          YYYY: {
            mask: IMask.MaskedRange,
            from: new Date().getFullYear(),
            to: 2099,
          },
          MM: {
            mask: IMask.MaskedRange,
            from: 1,
            to: 12,
          },
        },
      });
      this.securitycode_mask = IMask(this.securitycode, {
        mask: "0000",
      });
    })


  }

  public SponsorVocherCode(): void {
    this.vocherApplied = true;
    var VocherValidator = {
      personTenantRoleId: this.candidateId,
      voucherCode: this.Validator.value.code,
      examTypeId: this.listExam.map((x) => x.examTypeId),
      examId: this.listExam.map((x) => x.examId),
      examPrice: 0,
    };
    this.Vocher = this.Validator.value.code;
    // this.store.dispatch<Action>(saveVocher(this.Validator.value.code));
    this.store.dispatch(getVoucher({ VocherValidator }));
    this.store.select(selectorGetVoucher).subscribe((data) => {
      if (data != null && data.response[0].isValidExam == true) {
        let examsTypes = this.listExam.filter((x) =>
          data.response.find((y) => y.examId === x.examId)
        );
        let VocherDetails = new VoucherCartDetailsModel();
        VocherDetails.cartId = this.listExam[0].cartId;
        VocherDetails.voucherCode = this.Vocher;
        VocherDetails.userId = this.global.personEventId.personId;
        let voucherItems: VoucherItemModel[] = [];
        for (let i = 0; i < examsTypes.length; i++) {
          if (examsTypes.length > 0) {
            voucherItems.push(
              new VoucherItemModel({
                personEventCartId: parseInt(examsTypes[i].personEventCartId),
                voucherAmount: parseInt(data.voucherValue),
              })
            );
          }
        }
        VocherDetails.voucherItems = voucherItems;
        this.store.dispatch(getVoucherApply({ VocherDetails }));
        this.store.dispatch(clearVocherResponse());
        this.Validator.reset();
        this.store.select(selectorGetVouchersApply).subscribe((data) => {
          if (data != null && data == 0) {
            setTimeout(
              () =>
                this.store.dispatch<Action>(
                  getCartItems({
                    personTenantRoleId:
                    this.candidateId,
                  })
                ),
              1000
            );
            this.store.dispatch(clearVocherResponse());
            this.services.callSnackbaronSuccess("Applied Code Successfully");
          }
        });
      }
    });
  }
  deleteCard(card) {
    this.store.dispatch<Action>(deleteCard({ id: card.id }))
    setTimeout(() => {
      this.store.dispatch(getPaymentMethod({ customerId: this.customerIdObj.fattmerchantCustomerId }));
    }, 500);
  }
  close() {
    if (this.subtotal == 0) {
      return false
    }
    return true
  }
}

export class PersonalInfo {
  firstname: string = "";
  lastname: string = "";
  email: string = "";
  method: string = "";
  personId: string = "";
}

export class paypal {
  id: string;
}

export enum PaymentOptions {
  PayPal = 'PayPal',
  Credit = 'Credits/Debit Cards',
  ACH = 'ACH Bank'
}
