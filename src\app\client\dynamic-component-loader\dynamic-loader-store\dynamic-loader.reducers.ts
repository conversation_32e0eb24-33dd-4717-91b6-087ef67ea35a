import { createReducer, on } from "@ngrx/store";
import {
  DynamicComponentLoaderState,
  dynamicComponentLoaderInitialState,
} from "./dynamic-loader.state";
import {
  setPopupConfig,
  clearBreadCrumbs,
  creationResultReset,
  creationSuccessfull,
  UpdateData,
  creationUnsuc<PERSON>full,
  fetchedData,
  PushIntoBreadCrumb,
  setRecentLinkClickEvent,
  statusChangeSuccessfull,
  statusChangeUnsuccessfull,
  deleteSuccessfull,
  deleteUnsuccessfull,
  recheckCurModuleIndex,
  PopFromBreadCrumbUptoIndex,
  fetchedStates,
  clearData,
  updatedAccount,
} from "./dynamic-loader.actions";
import { widgetTypes } from "../dynamic-component-loader.types";

// Helper function to group users with multiple training institutes
function groupUsersByTrainingInstitutes(tableData: any[]): any[] {
  const userMap = new Map();

  tableData.forEach((user, index) => {
    // Create a more specific unique key - prioritize personId for users without email
    let userKey: string;

    if (user.emailId) {
      // Users with email: use email as primary key
      userKey = `email_${user.emailId}`;
    } else if (user.personId) {
      // Users without email: use personId as primary key
      userKey = `person_${user.personId}`;
    } else if (user.id) {
      // Fallback to id
      userKey = `id_${user.id}`;
    } else {
      // Last resort: use name and contact with index
      userKey = `name_${user.firstName}_${user.lastName}_${
        user.contactNumber || "nocontact"
      }_${index}`;
    }

    if (userMap.has(userKey)) {
      // User already exists, add training institute to the array
      const existingUser = userMap.get(userKey);

      // Ensure trainingInstitutes array exists
      if (!existingUser.trainingInstitutes) {
        existingUser.trainingInstitutes = [];
      }

      // Add training institute if it's not already in the array
      const trainingInstitute = {
        tenantName: user.tenantName,
        tenantId: user.tenantId,
      };

      const exists = existingUser.trainingInstitutes.some(
        (ti: any) =>
          ti.tenantName === trainingInstitute.tenantName &&
          ti.tenantId === trainingInstitute.tenantId
      );

      if (!exists && user.tenantName) {
        existingUser.trainingInstitutes.push(trainingInstitute);
      }
    } else {
      // First time seeing this user, create entry
      const newUser = {
        ...user, // Spread all user properties
        trainingInstitutes: user.tenantName
          ? [
              {
                tenantName: user.tenantName,
                tenantId: user.tenantId,
              },
            ]
          : [],
      };
      userMap.set(userKey, newUser);
    }
  });

  // Return array with proper deep copy to avoid reference issues
  const result = Array.from(userMap.values()).map((user) => ({
    ...user,
    trainingInstitutes: user.trainingInstitutes
      ? [...user.trainingInstitutes]
      : [],
  }));

  return result;
}

// Helper function to group users with multiple roles
function groupUsersByRoles(tableData: any[]): any[] {
  const userMap = new Map();

  tableData.forEach((user, index) => {
    // Create a unique key based on personId (since same person can have multiple roles)
    let userKey: string;

    if (user.personId) {
      // Use personId as primary key for grouping
      userKey = `person_${user.personId}`;
    } else if (user.emailId) {
      // Fallback to email if personId is not available
      userKey = `email_${user.emailId}`;
    } else {
      // Last resort: use name and contact with index
      userKey = `name_${user.firstName}_${user.lastName}_${
        user.contactNumber || "nocontact"
      }_${index}`;
    }

    if (userMap.has(userKey)) {
      // User already exists, add the role to their roles array
      const existingUser = userMap.get(userKey);

      // Check if this role is already added (avoid duplicates)
      // For Training Institute role, only check by roleName since multiple institutes
      // should show as one role entry
      const roleExists = existingUser.userRoles.some((role: any) => {
        if (
          user.roleName === "Training Institute" ||
          user.roleName === "Training Program"
        ) {
          // For training roles, only check by role name to avoid duplicates
          return role.roleName === user.roleName;
        } else {
          // For other roles, check both roleName and personRoleId (or roleId as fallback)
          return (
            role.roleName === user.roleName &&
            role.personRoleId === (user.personRoleId || user.roleId)
          );
        }
      });

      if (!roleExists) {
        existingUser.userRoles.push({
          roleName: user.roleName,
          personRoleId: user.personRoleId || user.roleId,
          personTenantRoleId: user.personTenantRoleId,
        });
      }
    } else {
      // First time seeing this user, create entry with roles array
      const newUser = {
        ...user, // Spread all user properties
        userRoles: [
          {
            roleName: user.roleName,
            personRoleId: user.personRoleId || user.roleId,
            personTenantRoleId: user.personTenantRoleId,
          },
        ],
      };
      userMap.set(userKey, newUser);
    }
  });

  // Return array with proper deep copy to avoid reference issues
  const result = Array.from(userMap.values()).map((user) => ({
    ...user,
    userRoles: user.userRoles ? [...user.userRoles] : [],
    // Also create a combined roleName field for backward compatibility
    // Remove duplicate role names for display
    roleName: user.userRoles
      ? [...new Set(user.userRoles.map((role: any) => role.roleName))].join(
          ", "
        )
      : user.roleName,
  }));

  return result;
}

// Helper function to detect if there are duplicate users (same personId or emailId)
function hasDuplicateUsers(tableData: any[]): boolean {
  const seenPersonIds = new Set();
  const seenEmails = new Set();

  for (const item of tableData) {
    // Check for duplicate personIds
    if (item.personId) {
      if (seenPersonIds.has(item.personId)) {
        return true; // Found duplicate personId
      }
      seenPersonIds.add(item.personId);
    }

    // Check for duplicate emails
    if (item.emailId) {
      if (seenEmails.has(item.emailId)) {
        return true; // Found duplicate email
      }
      seenEmails.add(item.emailId);
    }
  }

  return false; // No duplicates found
}

const _dynamicComponentLoaderReducers =
  createReducer<DynamicComponentLoaderState>(
    dynamicComponentLoaderInitialState,
    on(fetchedData, (state, action) => {
      switch (action.widgetType) {
        case widgetTypes.DynamicTable:
          // Get the table data
          let tableData = action?.response?.result
            ? action.response.result
            : action?.response
            ? action.response
            : [];

          // Handle different table data processing based on structure
          if (Array.isArray(tableData) && tableData.length > 0) {
            // Force multi-role detection for manage-all-users-superadmin based on endpoint
            // This needs to be declared early since it's used in other detection logic
            let forceMultiRoleDetection = false;

            // Check if this is the getusers endpoint
            if (action?.requestDetails) {
              const requestDetails = Array.isArray(action.requestDetails)
                ? action.requestDetails[0]
                : action.requestDetails;

              if (requestDetails?.fetchUrl) {
                const fetchUrlString = Array.isArray(requestDetails.fetchUrl)
                  ? requestDetails.fetchUrl.join('/')
                  : requestDetails.fetchUrl;

                if (fetchUrlString.includes('getusers')) {
                  forceMultiRoleDetection = true;
                }
              }
            }

            // Check if this looks like the manage-all-access-superadmin data structure
            const isUserAccessTable = tableData.some(
              (item: any) =>
                item.hasOwnProperty("userRoles") &&
                item.hasOwnProperty("emailId") &&
                item.hasOwnProperty("firstName")
            );

            // Check if this looks like the manage-all-users-superadmin data structure (users with multiple roles)
            // More flexible detection for superadmin manage users table
            const isMultiRoleUserTable = tableData.some(
              (item: any) =>
                item.hasOwnProperty("roleName") &&
                (item.hasOwnProperty("personId") || item.hasOwnProperty("emailId")) &&
                item.hasOwnProperty("personTenantRoleId") &&
                !item.hasOwnProperty("userRoles") &&
                !item.hasOwnProperty("tenantName") // Exclude training program table
            ) ||
            // Alternative detection: Check if we have duplicate personIds/emails (indicating multi-role users)
            (tableData.length > 1 &&
             tableData.some((item: any) => item.hasOwnProperty("roleName") && item.hasOwnProperty("emailId")) &&
             hasDuplicateUsers(tableData));

            // Check if this looks like the manage-all-users-TP data structure (training program users)
            // This should be more specific to avoid conflicts with manage-all-users-superadmin
            // Only consider it a training program table if it has tenantName AND is NOT a getusers endpoint
            const isTrainingProgramTable = tableData.some(
              (item: any) =>
                item.hasOwnProperty("tenantName") &&
                item.hasOwnProperty("emailId") &&
                item.hasOwnProperty("firstName") &&
                !item.hasOwnProperty("userRoles") &&
                // Additional check: training program tables typically have specific tenant-related fields
                (item.tenantName !== "Credentia" && item.tenantName !== null)
            ) && !forceMultiRoleDetection; // Don't treat as training program if we detected getusers endpoint





            if (isUserAccessTable) {
              // For user access management table - ensure all users have active: true
              // and deduplicate role names in userRoles array
              tableData = tableData.map((user: any) => {
                let processedUserRoles = user.userRoles || [];

                // Create display role names by deduplicating manually
                const seenRoles = new Set();
                const uniqueRoleNames = [];

                processedUserRoles.forEach((role: any) => {
                  if (!seenRoles.has(role.roleName)) {
                    uniqueRoleNames.push(role.roleName);
                    seenRoles.add(role.roleName);
                  }
                });

                return {
                  ...user,
                  active: true, // Always set active to true for user access management
                  userRoles: processedUserRoles, // Keep all roles for edit functionality
                  // Update the roleName field for display (deduplicated)
                  roleName: uniqueRoleNames.join(", "),
                };
              });
            } else if (isTrainingProgramTable) {
              // Group users with multiple training institutes
              tableData = groupUsersByTrainingInstitutes(tableData);
            } else if (isMultiRoleUserTable || forceMultiRoleDetection) {
              // Group users with multiple roles
              tableData = groupUsersByRoles(tableData);
            }
          }

          return {
            ...state,
            currentModuleIndex: action.moduleIndex,
            tableData: tableData,
            tableDataTotalRecords: action?.response?.totalRecords
              ? action.response.totalRecords
              : null,
          };
        case widgetTypes.DynamicSingleEntryTables:
          return {
            ...state,
            currentModuleIndex: action.moduleIndex,
            singleEntryTableData: action.response,
          };
        case widgetTypes.DynamicProcessLogs:
          return {
            ...state,
            currentModuleIndex: action.moduleIndex,
            processLogData: action.response,
          };
        case widgetTypes.DynamicForms:
          if (action.response.length == 1)
            return {
              ...state,
              currentModuleIndex: action.moduleIndex,
              formJSON: JSON.parse(action.response[0].formJson),
            };
          else
            return {
              ...state,
              currentModuleIndex: action.moduleIndex,
              formJSON: JSON.parse(action.response[0].formJson),
              userResponse: JSON.parse(action.response[1][0].formResponse)
                .formValue
                ? JSON.parse(action.response[1][0].formResponse).formValue
                : JSON.parse(action.response[1][0].formResponse),
            };
      }
    }),
    on(PushIntoBreadCrumb, (state, action) => {
      return {
        ...state,
        breadCrumbs: [...state.breadCrumbs, action.crumb],
      };
    }),
    on(PopFromBreadCrumbUptoIndex, (state, action) => {
      return {
        ...state,
        breadCrumbs: [...state.breadCrumbs.slice(0, action.indexToPopUpto)],
      };
    }),
    on(clearBreadCrumbs, (state, action) => {
      return {
        ...state,
        breadCrumbs: [...state.breadCrumbs.slice(0, 1)],
      };
    }),
    on(setRecentLinkClickEvent, (state, action) => {
      return {
        ...state,
        recentLinkClickEvent: action.event,
      };
    }),
    on(statusChangeSuccessfull, (state, action) => {
      return {
        ...state,
        statusChangeResult: {
          success: true,
          cellClickEvent: action.cellClickEvent,
        },
      };
    }),
    on(statusChangeUnsuccessfull, (state, action) => {
      return {
        ...state,
        statusChangeResult: {
          success: false,
          cellClickEvent: action.cellClickEvent,
        },
      };
    }),
    on(deleteSuccessfull, (state, action) => {
      return {
        ...state,
        deleteResult: {
          success: action.success,
          performedAction: action.performedAction,
          widgetRowIndex: action.widgetRowIndex,
          widgetColumnIndex: action.widgetColumnIndex,
        },
      };
    }),
    on(deleteUnsuccessfull, (state, action) => {
      return {
        ...state,
        deleteResult: {
          success: action.success,
          performedAction: action.performedAction,
          widgetRowIndex: action.widgetRowIndex,
          widgetColumnIndex: action.widgetColumnIndex,
        },
      };
    }),
    on(creationSuccessfull, (state, action) => {
      return {
        ...state,
        creationSuccess: true,
      };
    }),
    on(updatedAccount, (state, action) => {
      return {
        ...state,
        data: action.details,
      };
    }),
    on(creationUnsuccessfull, (state, action) => {
      return {
        ...state,
        creationSuccess: false,
      };
    }),
    on(creationResultReset, (state, action) => {
      return {
        ...state,
        creationSuccess: null,
      };
    }),
    on(setPopupConfig, (state, action) => {
      return {
        ...state,
        popupConfig: action.popupConfig,
      };
    }),
    on(recheckCurModuleIndex, (state, action) => {
      return {
        ...state,
        currentModuleIndex: null,
      };
    }),
    on(fetchedStates, (state, action) => {
      return {
        ...state,
        states: [{ id: null, stateName: "Clear Selection" }, ...action.states],
      };
    }),
    on(clearData, (state, action) => {
      return {
        ...state,
        statusChangeResult: null,
        deleteResult: null,
      };
    }),
    on(UpdateData, (state, action) => {
      return {
        ...state,
        OnupdateSucess: !state.OnupdateSucess,
      };
    })
  );

export function DynamicComponentLoaderReducers(state, action) {
  return _dynamicComponentLoaderReducers(state, action);
}
