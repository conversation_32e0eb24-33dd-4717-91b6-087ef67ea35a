import {
  HttpClient,
  HttpErrorResponse,
  HttpHeaders,
} from "@angular/common/http";
import { Component, DebugElement, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormArray, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { forkJoin } from "rxjs";
import { Router } from "@angular/router";

import { addUserForm } from "../custom-popup-for-manage-users/custom-popup-for-manage-users.component";
import { button, customPopup, submitEvent } from "../pop-up.types";
import { URL } from "src/app/core/url";
import { requestDetails } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { ISnackBarResult, SnackBarResult, SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { MatDialogRef } from "@angular/material/dialog";
import { Action, Store } from "@ngrx/store";
import {  UpdateData } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import { GlobalUserService } from "src/app/core/global-user.service";
import { analyzeAndValidateNgModules } from "@angular/compiler";
import { stringify } from "@angular/compiler/src/util";
import { HttpService } from "src/app/core/http.service";
import { cellClickEvent } from "../../table/dynamic-table-types";
import { MatTableDataSource } from "@angular/material/table/table-data-source";

@Component({
  selector: "app-add-training-institute-popup",
  templateUrl: "./add-training-institute-popup.component.html",
  styleUrls: ["./add-training-institute-popup.component.scss"],
})
export class AddTrainingInstitutePopupComponent implements OnInit, customPopup {
  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> =
    new EventEmitter<submitEvent>();

  userRoles: Array<any> = [];
  states: Array<any> = [];
  examTypesDrop: Array<any> = [];
  stateName:any;
  codeList:any;
  isLinear = false;
  tenantId: number;
  tabledata:any;
  updatedData:any;
  eligibilityroutes: Array<ER> = [];
  trnInsDetailsForm: FormGroup;
  trnInsAddrForm: FormGroup[];
  adminDetailsForm: FormGroup;
  userData: any;
  isactive:boolean=false;
  selecteddetails: any;
  allTrainingUser:any;
  selectedUser: any = null;
  state:string
  hideAdminDetails: boolean = true;
  emptyAddressDetailsGroup: FormGroup;
  stateCode: any;
  stateIdValue: number;
  trainingInstituteUsers: any;
  displayedColumns: Array<string> = ['name', 'phoneNumber', 'emailId', 'deleteuser'];
  displayedColumnSerach: Array<string> = ['firstName', 'lastName', 'emailID'];
  dataSource: Array<any> = [];

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private snackbar: SnackbarService,
    private store: Store,
    private global: GlobalUserService,
   
  ) { }
  ngOnInit(): void {
    

    if (this.preResponse) {
  
      this.preResponse.users.forEach(user => {
            this.dataSource.push({
              id: user.personId,
              firstName: user.firstName,
              lastName: user.lastName,
              middleName: user.middleName,
              phoneNumber: user.phoneNumber,
              email: user.emailId,
              fax: '',
              website: ''
            })
          });
      forkJoin([
        this.http.get(environment.baseUrl + `client/api/exam/examtype`),
        this.http.get(environment.baseUrl + `client/api/state/3`),
   
      ]).subscribe((x: Array<any>) => {
      
        this.examTypesDrop = x[0];
        this.states = x[1];
     
        let state = this.states.filter(x=>this.preResponse.stateId === x.id)
       this.state = state[0].stateCode;
  
        if (!this.preResponse.personId) {
            
          this.http.get(`${environment.baseUrl}client/api/traininginstitute/user?stateId=${this.preResponse.stateId}&showAll=true`).subscribe((data:any) => {
            if(data){
              
              this.allTrainingUser=data;
              this.trainingInstituteUsers = data;
              this.userData = data;
            if (this.dataSource.length>0) {
              
              let a=data
              let b=this.dataSource;
              let valuesA = a.reduce((a,{emailId}) => Object.assign(a, {[emailId]:emailId}), {});
              let valuesB = b.reduce((a,{email}) => Object.assign(a, {[email]:email}), {});
              let result = [...a.filter(({emailId}) => !valuesB[emailId]), ...b.filter(({email}) => !valuesA[email])];
              let filterResult=[...new Map(result.map(item => [item['emailId'], item])).values()]
              this.userData=filterResult;
              this.trainingInstituteUsers=filterResult;
            }
          }
            })
          forkJoin([
            this.http.get(
              environment.baseUrl +
              `client/api/traininginstitute/${this.preResponse.id}`
            ),
            this.http.get(
              environment.baseUrl +
              `client/api/eligibility/eligibilityroutesbystate?stateId=${this.preResponse.stateId}`
            ),
          ]).subscribe((x: any) => {
            this.preResponse = { ...this.preResponse, ...x[0] };
            this.eligibilityroutes = x[1];
            this.setForm();
          });
          this.stateDrop(this.preResponse.stateId)
       
        } else {
          forkJoin([
            this.http.get(
              environment.baseUrl +
              `client/api/account/getuser?id=${this.preResponse.personId}`
            ),
            this.http.get(
              environment.baseUrl +
              `client/api/traininginstitute/${this.preResponse.id}`
            ),
            this.http.get(
              environment.baseUrl +
              `client/api/eligibility/eligibilityroutesbystate?stateId=${this.preResponse.stateId}`
            ),
          ]).subscribe((x: any) => {
            this.preResponse = { ...this.preResponse, ...x[0], ...x[1] };
            // this.stateCode =this.states[this.states.findIndex(x=>this.preResponse.stateId==x.id)].sateCode;
            this.eligibilityroutes = x[2];
            this.setForm();
          });
        }
      });
    } else {
      
      forkJoin([
        this.http.get(environment.baseUrl + `client/api/exam/examtype`),
        this.http.get(environment.baseUrl + `client/api/state/3`),
     
        
      ]).subscribe((x: Array<any>) => {
   
        this.examTypesDrop = x[0];
        this.states = x[1];

        this.setForm();
      });
    }

   
  }
stateDrop(event:any){
  
  this.stateName=event.id?event.id:event;
  let id =event.id?event.id:event
     this.http.get(environment.baseUrl +`client/api/traininginstitute/codes?stateId=${id}`).subscribe(data=>{
    
       this.codeList=data;
     })
  

}
  setForm() {
    this.trnInsDetailsForm = this.formBuilder.group({
      id: [this.preResponse ? this.preResponse.id : ""],
      name: [
        this.preResponse ? this.preResponse.name : "",
        [Validators.required],
      ],
      active:this.preResponse?this.preResponse.active:true,
      code: [
        this.preResponse ? this.preResponse.code : "",
        [Validators.required],
      ],
      stateId: [
        this.preResponse ? this.states.find(x => x.id == this.preResponse.stateId) : "",
        [Validators.required],
      ],
      TrainingProgramCodeId:[
        this.preResponse?this.preResponse.trainingProgramCodeId : null,
        
      ],


      eligibilityrouteids: [
        this.preResponse?.eligibilityRouteIds !=null ? [...this.preResponse?.eligibilityRouteIds] : [],
        [Validators.required],
      ],
      expiryDate: [
        this.preResponse ? this.preResponse.expirydate : "",
        [Validators.required],
      ],
      examType: [
        this.preResponse ? [...this.preResponse.examTypes] : [],
        [Validators.required],
      ],
      description: [this.preResponse ? this.preResponse.description : ""],
    });
    this.preResponse?.address?.forEach((address) => {
      if (!this.trnInsAddrForm || this.trnInsAddrForm.length == 0) {
        this.trnInsAddrForm = [];
      }
      this.trnInsAddrForm.push(
        this.formBuilder.group({
          addressLine1: [
            address ? address.addressLine1 : "",
            [Validators.required],
          ],
          addressLine2: [
            address ? address.addressLine2 : "",
            // [Validators.required],
          ],
          city: [address ? address.city : "", [Validators.required]],
          zipCode: [address ? address.zipCode : "", [Validators.required]],
          isBillingAddress: [
            address ? address.isBillingAddress : "",
            [Validators.required],
          ],
        })
      );
    });
    if (
      !this.preResponse ||
      !this.preResponse.address ||
      this.preResponse.address.length == 0
    ) {
      this.trnInsAddrForm = [
        this.formBuilder.group({
          addressLine1: ["", [Validators.required]],
          addressLine2: ["", [Validators.required]],
          city: ["", [Validators.required]],
          zipCode: ["", [Validators.required]],
          isBillingAddress: [true, [Validators.required]],
        }),
      ];
    }

    this.adminDetailsForm = this.formBuilder.group({
      id: [
        this.preResponse
          ? this.preResponse.personId
            ? this.preResponse.personId
            : 0
          : 0,
        Validators.required,
      ],
      email: [
        this.preResponse ? this.preResponse.emailId : "",
        [Validators.required, Validators.email],
      ],
      firstName: [
        this.preResponse ? this.preResponse.firstName : "",
        [Validators.required],
      ],
      middleName: [
        this.preResponse ? this.preResponse.middleName : "",
        // [Validators.required],
      ],
      lastName: [
        this.preResponse ? this.preResponse.lastName : "",
        [Validators.required],
      ],
      phoneNumber: [
        this.preResponse ? this.preResponse.phoneNumber : "",
        [Validators.required],
      ],
      fax: [
        this.preResponse ? this.preResponse.fax : "",
        // [Validators.required],
      ],
      website: [
        this.preResponse ? this.preResponse.webSite : "",
        // [Validators.required],
      ],
      cantest:[this.preResponse ? this.preResponse.cantest : '',]
    });


    this.trnInsDetailsForm.controls.stateId.valueChanges.subscribe(
      (x: State) => {
        this.stateIdValue = x.id
        this.stateCode = x.stateCode
        this.http
          .get(
            environment.baseUrl +
            `client/api/eligibility/eligibilityroutesbystate?stateId=${x.id}`
          )
          .subscribe((data: any) => {
            this.eligibilityroutes = data;
          });
          this.dataSource=[];
          this.selectedUser=null;
          this.userData=null;
          this.trainingInstituteUsers=null;
          setTimeout(() => {
          this.http.get(`${environment.baseUrl}client/api/traininginstitute/user?stateId=${x.id}&showAll=true`).subscribe((data:any) => {
              if (data) {
                
                this.allTrainingUser=data;
                this.trainingInstituteUsers = data;
                this.userData = data;
                if(this.dataSource.length > 0) {
                  
                  let a=data
                  let b=this.dataSource;
                  let valuesA = a.reduce((a,{emailId}) => Object.assign(a, {[emailId]:emailId}), {});
                  let valuesB = b.reduce((a,{email}) => Object.assign(a, {[email]:email}), {});
                  let result = [...a.filter(({emailId}) => !valuesB[emailId]), ...b.filter(({email}) => !valuesA[email])];
                  let filterResult=[...new Map(result.map(item => [item['emailId'], item])).values()]
                  this.userData=filterResult;
                  this.trainingInstituteUsers=filterResult;
                }
              }
            });
          },1000)
      }
      
    );
  }


  addAddressToTrns() {
    this.trnInsAddrForm.push(
      this.formBuilder.group({
        addressLine1: ["", [Validators.required]],
        addressLine2: ["", [Validators.required]],
        city: ["", [Validators.required]],
        zipCode: ["", [Validators.required]],
        isBillingAddress: [false, [Validators.required]],
      })
    );
  }
  getAddressFormValidity() {
    var validity = true;
    this.trnInsAddrForm.forEach((x: FormGroup) => {
      validity = validity && x.valid;
    });
    return (
      validity && this.trnInsAddrForm.find((x: any) => x.value.isBillingAddress)
    );
  }
  getIsBillingDisabled(groupIndex: number) {
    let index = this.trnInsAddrForm.findIndex((x: any) => {
      return x.value.isBillingAddress;
    });

    return !(index == groupIndex) && index > -1;
  }

  addTI() {
    if (this.dataSource.length > 0) {
      this.isactive=false;
      //delete this.trnInsDetailsForm.value['stateId']
      var v: TIDetails = { ...this.trnInsDetailsForm.value, stateId: this.stateIdValue };
      v.address = [
        ...this.trnInsAddrForm.map((x: FormGroup) => {
          x.value.zipCode = x.value.zipCode?x.value.zipCode.toString():"";
          return x.value;
        }),
      ];
      if (this.preResponse) {
        v = {
          ...v,
          ...{
            parentId: this.preResponse.parentId,
            stateId: this.preResponse.parentId,
          },
        };
        this.http
          .put(environment.baseUrl + "client/api/traininginstitute", v)
          .subscribe(
            (res: number) => {
              if (res) {
                
                this.tenantId = res;
                this.editAdminUser();
                (this.trnInsAddrForm[0].touched || this.trnInsDetailsForm.touched) ?this.snackbar.callSnackbaronSuccess("Updated Successfully"):"";
                this.dialogRef.close();
               
              }
            },
            (error) => {
              this.snackbar.callSnackbaronError("something went wrong");
            }
          );
      } else {
        this.http
          .post(environment.baseUrl + "client/api/traininginstitute", v)
          .subscribe(
            (res: number) => {
              if (res) {
                this.tenantId = res;
                this.addAdminUser();
               
              }
            },
            (error) => {
              this.snackbar.callSnackbaronError(error.message.message.error); 
            }
          );
      }
     
    }
 
  }


  editAdminUser() { 
    var a = { address: "", gender: "" };
    var v = { role: [15] };
    var t = { tenantId: this.tenantId };
    var p = { isPrimary: true }
    var ed = { personId: this.preResponse.id }
    var admin: Array<any> = [];

    this.dataSource.forEach((details: any) => {
      admin.push({ ...details, ...v, ...t, ...a, ...p, clientStateCode: this.state, ...ed,stateName:this.trnInsDetailsForm.value.stateId.stateName });
    });
    this.http
      .post(environment.baseUrl + `client/api/Account/saveusers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`, admin)
      .subscribe(
         (result:any) => {
          if(result !=""){
           var snackBarResult : Array<ISnackBarResult> = [];
           result.forEach(element => {
         
          snackBarResult.push({
            message : element.message,
            resultFlag : (element.isUserAdded || element.isUserDeleted || element.isUserRoleAdded)
          });
        });

        this.snackbar.openSnackBar(snackBarResult, "success-snackbar")
          this.dialogRef.close();
         }
           result.length > 0?this.snackbar.openSnackBar(snackBarResult, "success-snackbar"):this.store.dispatch<Action>(UpdateData());
           this.dialogRef.close();
            this.store.dispatch<Action>(UpdateData());
            //this.store.dispatch<Action>(creationSuccessfull())
            this.preResponse?null:this.snackbar.callSnackbaronSuccess("Created SuccessFully")
          },
         
        (error: HttpErrorResponse) => {
          this.isactive=false;
          this.snackbar.callSnackbaronError("Admin couldn't add");
        }

      );
    
  }


  addAdminUser() {
    var a = { address: "", gender: "" };
    var v = { role: [15] };
    var t = { tenantId: this.tenantId };
    var p = { isPrimary: true }
    var admin: Array<any> = [];
    this.dataSource.forEach((details: any) => {


      admin.push({ ...details, ...v, ...t, ...a, ...p, clientStateCode: this.stateCode,stateName:this.trnInsDetailsForm.value.stateId.stateName  })
    })
    // if (admin.id == null) {
    //   admin.id = 0;
    // }
    const headers = new HttpHeaders().set(
      "Authorization",
      `Bearer eyJraWQiOiJIWDMxNFhhdDdcLzJRdm9jblNON3pkeWNYZW03cUI5OWMrbkRtTUJkTmpCWT0iLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GPFCJhQOmEqs8RY7CpOgksgJoW15H-fpuZuSWQI0cOWgJjbYN0vmslItOMGFxft7ExvLdlWGW2trIvF6v8Wu6thfKsxYdJUkVio6QfCzon1GR6K1T5z_JmwJA4h9QgnF1Fx63Uue-DivT1E4gRTCiq-lx-IgwiM6KJATr3fK7Ew06Az14iWp9JX7OQMKBkYFKAcZa_M06d0L_5O4WG9KiJb3kT9GWLKbVo1bVvFAeiKOas-tVd9aNPiKuuHmCen_r-jN97AYSoIlWqCc_maXeGuT_Nc3KICYmoiMs211hB2HHcDTsckeZ-tCJPfWdvxdkMZryVt8FpLZdoFlmtHYsg`
    );
    this.http
      .post(environment.baseUrl + `client/api/Account/saveusers?personTenantRoleId=${this.global.userDetails.value.personTenantRoleId}`, admin, {
        headers: headers,
      })
      .subscribe(
        (result) => {
          if (result) {
            
            this.isactive=true;
            this.store.dispatch<Action>(UpdateData());
            this.preResponse?this.snackbar.callSnackbaronSuccess("Updated SuccessFully"):this.snackbar.callSnackbaronSuccess("Created SuccessFully")
             this.dialogRef.close()
            
          }
        },
        (error: HttpErrorResponse) => {
          this.isactive=false;
          this.snackbar.callSnackbaronError("admin couldnt add");
        }
      );
     
      
  }

  filterUsers() {
    this.hideAdminDetails = true;
    
    if (this.selectedUser == "") {
      this.userData = this.trainingInstituteUsers;
    }
    else {
      this.userData = this.trainingInstituteUsers.filter(
        (item) =>{
          return (
            item.firstName?.toLowerCase().includes(this.selectedUser.toLowerCase())
            ||
            item.lastName?.toLowerCase().includes(this.selectedUser.toLowerCase())
            ||
            item.emailId?.toLowerCase().includes(this.selectedUser.toLowerCase())
          )
        }
      );
    }
  }

  selectedId(item) {
    
    this.selectedUser = item.emailId;
    this.selecteddetails = item;
    this.hideAdminDetails = false;
  }


  addDetailsForm() {
    const email = this.adminDetailsForm.value.email

    this.http.post(`${environment.baseUrl}api/Client/user/verifyUserRole?email=${email}`, email)
    .subscribe(
      (res: any)=> {
        if (res.userExists) {
          if (res.roleId === 15) {
            this.snackbar.callSnackbaronError('Please add user using the "Select Existing User" field ');
          }else{
            this.snackbar.callSnackbaronError(`There is already an user with this email as “${res.firstName} ${res.lastName}" and they already have registered into the system as ${res.roleName}. Please contact System Administrator.` );
          }
          
        } 
    }, (error : HttpErrorResponse)=> {
     
    })



    // JHFJDSFHSJDF?email=this.adminDetailsForm.value.email

    // res{success='false', message:"error"}


    if (this.adminDetailsForm.valid && this.adminDetailsForm.value.email!=null) {
      this.dataSource.push({
        id: this.adminDetailsForm.value.id,
        firstName: this.adminDetailsForm.value.firstName,
        lastName: this.adminDetailsForm.value.lastName,
        middleName: this.adminDetailsForm.value.middleName,
        phoneNumber: this.adminDetailsForm.value.phoneNumber,
        email: this.adminDetailsForm.value.email,
        fax: this.adminDetailsForm.value.fax,
        website: this.adminDetailsForm.value.website,
        cantest:(this.adminDetailsForm.value.cantest !=null && this.adminDetailsForm.value.cantest !='' && this.adminDetailsForm.value.cantest!=undefined)?this.adminDetailsForm.value.cantest:false
      })
      this.dataSource = [...this.dataSource];
      this.adminDetailsForm.setValue({
        id: 0,
        firstName: null,
        email: null,
        middleName: null,
        lastName: null,
        phoneNumber: " ",
        fax: null,
        website: null,
        cantest:null
      });
      this.adminDetailsForm.markAsUntouched();
      this.adminDetailsForm.controls['id'].setErrors(null);
      this.adminDetailsForm.controls['firstName'].setErrors(null);
      this.adminDetailsForm.controls['email'].setErrors(null);
      this.adminDetailsForm.controls['lastName'].setErrors(null);
      this.adminDetailsForm.controls['phoneNumber'].setErrors(null);
    }
  }

  addDetailsDD() {
    if (this.selecteddetails != null) {
      this.trainingInstituteUsers=this.trainingInstituteUsers.filter(item=>item!=undefined)
      this.trainingInstituteUsers = this.trainingInstituteUsers.filter((item) => item.emailId != this.selecteddetails.emailId)
     this.userData=this.trainingInstituteUsers;
      this.dataSource.push({
                id: this.selecteddetails.personId,
                firstName: this.selecteddetails.firstName,
                lastName: this.selecteddetails.lastName,
                middleName: this.selecteddetails.middleName,
                phoneNumber: this.selecteddetails.phoneNumber,
                email: this.selecteddetails.emailId,
                fax: '',
                website: ''
              })
      this.dataSource = [...this.dataSource];
      this.selectedUser = null;
      this.selecteddetails=null;
    }
  }

  deleteUser(userDetails) {
    
    this.dataSource = this.dataSource.filter(item =>
      item.email!= userDetails.email
    )

    this.trainingInstituteUsers.push(this.allTrainingUser.filter(item=>item.emailId == userDetails.email)[0]);
    this.dataSource = [...this.dataSource];
    this.userData=this.trainingInstituteUsers;
    this.userData=this.userData.filter(item=>item!=undefined);
  }
}
export interface State {
  id: number;
  stateName: string;
  stateCode: string;
  lastUpdatedDate: Date;
  eligibilityRoutes: number;
  active: boolean;
}
export interface ER {
  id: number;
  name: string;
  candidateCount: number;
  personId: number;
  lastUpdatedDate: Date;
}

export interface examType {
  id: number;
  name: string;
  active: boolean;
  createdOn: Date;
}
export interface Address {
  addressLine1: string;
  addressLine2: string;
  city: string;
  state?: string;
  zipCode: string;
  country?: string;
  isBillingAddress: boolean;
  id?: number;
  tenantId?: number;
}

export interface TIDetails {
  name: string;
  code: string;
  active: boolean;
  stateId: number;
  eligibilityrouteids: number[];
  expiryDate: Date;
  examType: number[];
  description?: string;
  address: Address[];
  tenantId: number;
}

export interface Admin {
  firstName: string;
  middleName: string;
  lastName: string;
  phoneNumber: string;
  fax: string;
  email: string;
  webSite: string;
  isPrimary: boolean;
  clientCandidateId?: string;
  tenantCode?: string;
  tenantId?: number;
  id?: number;
  password?: any;
  ssn?: string;
  countryCode?: string;
  country?: string;
  state?: string;
  city?: string;
  zipCode?: string;
  accommodation?: string;
  createdBy?: number;
  address?: string;
  dateofBirth?: string;
  gender?: string;
  role?: number[];
}
