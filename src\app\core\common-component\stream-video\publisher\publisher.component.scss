.height {
  height: 31em !important;
}
.switchButton{
z-index: 999;
//  margin-left: 32em !important;
}
.RECORDING{
    z-index: 999 !important;
    color: #ed1b0c;
}
.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  }

  @keyframes ping {
    0% {
      transform: scale(0.9);
      opacity: 0.5;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.6;
    }
    100% {
      transform: scale(0.9);
      opacity: 0.5;
    }
  }

  :host ::ng-deep .animate-pulse {
    animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

::ng-deep svg {
  width: 48px !important;
}

