import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Manage Types of Exams",
            moduleID: "manage-types-exam",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageTypeExamIcon",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",

                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ["tab-col"],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter:true,
                                    showAddButton: [false],
                                    showMatTabs: false,
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "name",
                                            name: "Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "createdOn",
                                            name: "Created Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "active",
                                            name: "Status",
                                            cellType: "toggle",
                                            showOnlyData:true,
                                            statusUpdateDetails: {
                                                fetchUrl: ["client", "api", "exam", "examtype", "status"],
                                                requestBody: null,
                                                requestParams: null,
                                                method: "PUT",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['w-12'],
                                            cellCssClasses: [],
                                        },
                                    ],
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: ["client", "api", "exam", "examtype"],
                                        requestBody: null,
                                        requestParams: [],
                                        method: "GET",
                                    },
                                ],
                            },
                        },
                    ],
                },
            ],
        }
    ]
}