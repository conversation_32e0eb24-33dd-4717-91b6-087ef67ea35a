<!-- certificate 3 -->
<div style="overflow:auto; height:95vh!important;">
    <div id="fullDiv">

        <!-- Skills Exam - Fail -->
        <table *ngIf="isSkills && scoresDetails && !scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                            padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                            min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                                color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS
                                            </legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Skills Exam-Fail</span><br /><br />
                                        <span *ngIf="scoresDetails.skillPerformance !=null"
                                            style="color: #11263c; font-size: 14px; line-height: 1.6; font-weight: 500; padding-top: 20px;">Skills
                                            Performance:</span>
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="color:#7D7D7D">
                            <tbody>
                                <tr>
                                    <td>
                                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                            style=" color:#7D7D7D;font-size: 14px;line-height: 1.4; border: 0.1px solid #0077c183;"
                                            bgcolor="#FAFAFA">
                                            <tbody>
                                                <ng-container *ngFor="let data of scoresDetails.skillPerformance">
                                                    
                                                    <tr>
                                                        <!-- <td width="10"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : '#7D7D7D','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330">{{data.question}}</td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330" align="right">{{data.result}}</td>
                                                        <!-- <td width="10"></td> -->
                                                    </tr>
                                                    <tr *ngIf="data.steps && data.steps.length > 0"
                                                        style="font-size: 8pt;">
                                                        <!-- <td width="10"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            style="padding-top: 0px;" width="330">
                                                            <span
                                                                style="padding-left: 7px; color: #c72b40; font-weight: 500;">Failed
                                                                Steps:</span>
                                                            <span style="color: #c72b40; font-weight: 500;"
                                                                *ngFor="let step of data.steps">{{step}}&nbsp;</span>
                                                        </td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            style="padding-top: 0px;" width="330" align="right"></td>
                                                        <!-- <td width="10"></td> -->
                                                    </tr>
                                                </ng-container>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span *ngIf="this.global.userDetails.getValue().stateId !=19"
                                            style="color: #11263cbd; font-size: 14px; line-height: 1.6; font-weight: 500;">
                                            This Score Report confirms you DID NOT PASS the SKILLS
                                            Evaluation.</span><br />
                                        <span
                                            style="color: #7d7d7d; font-size: 12px; line-height: 1.6; font-weight: 600; text-decoration: underline;">
                                            Next Steps:</span>
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                            <span
                                                *ngIf=" this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19">
                                                Please refer to your current candidate handbook on the Credentia website
                                                at</span>
                                            <span
                                                *ngIf=" this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19"
                                                style="color: #0076C1;word-break: break-word;" >
                                                <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a>
                                                </span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>
                                            <br />

                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</span><br />
                                        </span>
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                            Please refer "Skills Listing" in your candidate handbook, available online
                                            at
                                            <span
                                                *ngIf="this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19 "
                                                >
                                                <a href="{{URL}}"  target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a>
                                                </span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>
                                            <a href="https://credentia.com/test-takers/ak" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==19"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>
                                            <br />

                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                for a description of any listed failed step(s) identified by the
                                                number(s) under
                                                the skills name
                                            </span> <br />

                                            <span *ngIf="this.global.userDetails.getValue().stateId ==19"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}} <a href="https://credentia.com/test-takers/ak"
                                                    target='_blank' style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}</a></span><br />
                                            <span *ngIf="this.global.userDetails.getValue().stateId ==19"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].link}}: <a href="mailto:<EMAIL>" target='_blank'
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url1}}</a></span>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Skills Exam - PASS -->
        <table *ngIf="isSkills && scoresDetails && scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                        <!-- <span
                                        style="color: #00AB72; font-size: 14px; line-height: 1.6; font-weight: 700;">Oral
                                        Exam - Pass</span><br /> -->
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                        padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                        min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                            color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Skills Exam-PASS</span><br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700; text-decoration: underline;">Next
                                            Steps:</span><br />
                                        <span *ngIf="global.userDetails.getValue().stateId !=18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP && global.userDetails.getValue().stateId !=19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the NNAAP&#174; Skills Evaluation in {{stateName}}
                                            </span>
                                            <span *ngIf="isNNAP && global.userDetails.getValue().stateId ==19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations! <span
                                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                        {{nextSteps[0].value}}</span>
                                                </span>
                                            </span>
                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/cbpl/ProfessionalLicensing/NurseAideRegistry.aspx"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url1}}</a><span>{{nextSteps[0].link}}</span>

                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content1}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/Portals/5/pub/My-License-Instructions.pdf"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url2}}</a><span>{{nextSteps[0].content2}}</span><span><a
                                                    href="mailto:<EMAIL>" target="_blank"
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url3}}</a></span><span
                                                class="ml-1">{{nextSteps[0].content3}}</span><span><a
                                                    href="https://www.commerce.alaska.gov/cbp/main/Search/Professional"
                                                    target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url4}}</a></span>


                                        </span>
                                        <span *ngIf="global.userDetails.getValue().stateId ==18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the {{stateName}} CNA Skills Examination
                                            </span>


                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</p><a
                                                href="https://nvbn.boardsofnursing.org/licenselookup" target="_blank"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a> <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</span>

                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Written Exam Pass -->
        <table *ngIf="isWritten && scoresDetails && scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP && !isE12Route && this.scoresDetails['stateCode']!='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td *ngIf="isNNAP  && this.scoresDetails['stateCode']=='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP && !isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span *ngIf="isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide Exam </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                        padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                        min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                            color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white;  padding: 16px 40px; border-radius: 4px;">
                                            Written Exam-PASS</span><br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700; text-decoration: underline;">Next
                                            Steps:</span><br />
                                        <span *ngIf="global.userDetails.getValue().stateId !=18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP && !isE12Route && global.userDetails.getValue().stateId !=19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the NNAAP&#174; Written Evaluation in {{stateName}}
                                            </span>
                                            <span *ngIf="isNNAP && global.userDetails.getValue().stateId ==19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations! <span
                                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                        {{nextSteps[0].value}}</span>
                                                </span>
                                            </span>
                                            <span *ngIf="isE12Route && global.userDetails.getValue().stateId !=19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the Written Evaluation in {{stateName}}
                                            </span>
                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/cbpl/ProfessionalLicensing/NurseAideRegistry.aspx"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url1}}</a><span>{{nextSteps[0].link}}</span>

                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content1}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/Portals/5/pub/My-License-Instructions.pdf"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url2}}</a><span>{{nextSteps[0].content2}}</span><span><a
                                                    href="mailto:<EMAIL>" target="_blank"
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url3}}</a></span><span
                                                class="ml-1">{{nextSteps[0].content3}}</span><span><a
                                                    href="https://www.commerce.alaska.gov/cbp/main/Search/Professional"
                                                    target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url4}}</a></span>


                                        </span>
                                        <span *ngIf="global.userDetails.getValue().stateId ==18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the
                                                {{stateName}} CNA Written Examination
                                            </span>
                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</p><a
                                                href='https://nvbn.boardsofnursing.org/licenselookup' target="_blank"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a> <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</span>

                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- Written Exam Fail -->
        <table *ngIf="isWritten && scoresDetails && !scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP && !isE12Route && this.scoresDetails['stateCode'] !='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td *ngIf="isNNAP  && this.scoresDetails['stateCode'] =='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP && !isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span *ngIf="isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide Exam </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                        padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                        min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                            color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Written Exam-Fail</span><br /><br />
                                        <span *ngIf="scoresDetails.skillPerformance !=null"
                                            style="color: #11263c; font-size: 14px; line-height: 1.6; font-weight: 500;">Content
                                            Area Performance:</span><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D">
                            <tbody>
                                <tr>
                                    <td>
                                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                            style=" color:#7D7D7D;font-size: 14px;line-height: 1.4; border: 0.1px solid #0077c183;"
                                            bgcolor="#FAFAFA">
                                            <tbody>
                                                <ng-container *ngFor="let data of scoresDetails.skillPerformance">
                                                    <tr>
                                                        <!-- <td width="10"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : '#7D7D7D','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330">{{data.question}}</td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330" align="right">{{data.result}}</td>
                                                        <!-- <td width="10"></td> -->
                                                    </tr>
                                                    <tr *ngIf="data.steps && data.steps.length > 0"
                                                        style="font-size: 8pt;">
                                                        <!-- <td width="7"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            style="padding-top: 0px;" width="330">
                                                            <!-- <span
                                                                style="padding-left: 7px; color: #c72b40; font-weight: 500;">Failed
                                                                Steps:</span>
                                                            <span style="color: #c72b40; font-weight: 500;"
                                                                *ngFor="let step of data.steps">{{step}}&nbsp;</span> -->
                                                        </td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            style="padding-top: 0px;" width="330" align="right"></td>
                                                        <!-- <td width="7"></td> -->
                                                    </tr>
                                                </ng-container>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span *ngIf="this.global.userDetails.getValue().stateId !=19"
                                            style="color: #11263cbd; font-size: 14px; line-height: 1.6; font-weight: 500;word-break: break-word;">
                                            This Score Report confirms you DID NOT PASS the Written Test
                                            {{Exams}}.</span><br />
                                        <span
                                            style="color: #7d7d7d; font-size: 12px; line-height: 1.6; font-weight: 600; text-decoration: underline;">
                                            Next Steps:</span>
                                        <span *ngIf="this.global.userDetails.getValue().stateId !=19"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                            Please refer to your current candidate handbook on the Credentia website at
                                            <span
                                                *ngIf=" this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19"
                                                style="color: #0076C1;word-break: break-word;">
                                                <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a>
                                                </span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>



                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</span><br />
                                        </span>
                                        <span *ngIf="this.global.userDetails.getValue().stateId ==19"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">

                                            <span
                                                *ngIf=" this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19"
                                                >
                                                <a href="{{URL}}"  target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a></span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>

                                            <br />

                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</span><br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content1}}</span><br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}} <a href="https://credentia.com/test-takers/ak"
                                                    target='_blank' style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}</a></span>


                                            <br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].link}}: <a href="mailto:<EMAIL>" target='_blank'
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url1}}</a></span>
                                        </span>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- Oral Exam - Fail -->
        <table *ngIf="isOral && scoresDetails && !scoresDetails.isPassed" align="center" cellpadding="0" cellspacing="0"
            width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP && !isE12Route && this.scoresDetails['stateCode']!='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td *ngIf="isNNAP  && this.scoresDetails['stateCode']=='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP && !isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span *ngIf="isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide Exam </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                        padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                        min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                            color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Oral Exam-Fail</span><br /><br />
                                        <span *ngIf="scoresDetails.skillPerformance !=null"
                                            style="color: #11263c; font-size: 14px; line-height: 1.6; font-weight: 500;">Content
                                            Area Performance:</span><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D">
                            <tbody>
                                <tr>
                                    <td>
                                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                            style=" color:#7D7D7D;font-size: 14px;line-height: 1.4; border: 0.1px solid #0077c183;"
                                            bgcolor="#FAFAFA">
                                            <tbody>
                                                <ng-container *ngFor="let data of scoresDetails.skillPerformance">
                                                    <tr>
                                                        <!-- <td width="10"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : '#7D7D7D','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330">{{data.question}}</td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330" align="right">{{data.result}}</td>
                                                        <!-- <td width="10"></td> -->
                                                    </tr>
                                                    <tr *ngIf="data.steps && data.steps.length > 0 && data.steps !='' && data.steps !=null"
                                                        style="font-size: 8pt;">
                                                        <!-- <td width="7"></td> -->
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            style="padding-top: 10px; color: #c72b40; font-weight: 500;"
                                                            width="330">
                                                            <span
                                                                style="padding-left: 7px; color: #c72b40; font-weight: 500;">Failed
                                                                Steps:</span>
                                                            <span *ngFor="let step of data.steps">{{step}}&nbsp;</span>
                                                        </td>
                                                        <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                            [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : 'black','border-bottom':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }"
                                                            width="330" align="right"></td>
                                                        <!-- <td width="7"></td> -->
                                                    </tr>
                                                </ng-container>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span *ngIf="this.global.userDetails.getValue().stateId !=19"
                                            style="color: #11263cbd; font-size: 14px; line-height: 1.6; font-weight: 500;">
                                            This Score Report confirms you DID NOT PASS the ORAL {{Exams}}
                                            .</span><br />
                                        <span
                                            style="color: #7d7d7d; font-size: 12px; line-height: 1.6; font-weight: 600; text-decoration: underline;">
                                            Next Steps:</span><br />
                                        <span *ngIf="this.global.userDetails.getValue().stateId !=19"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                            Please refer to your current candidate handbook on the Credentia website at
                                            <span *ngIf=" this.global.userDetails.getValue().stateId !=18"
                                               >  <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a></span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>
                                            <br />

                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</span><br />
                                        </span>

                                        <span *ngIf="this.global.userDetails.getValue().stateId ==19"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">

                                            <span
                                                *ngIf=" this.global.userDetails.getValue().stateId !=18 && this.global.userDetails.getValue().stateId !=19"
                                                >
                                                <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}
                                                </a></span>
                                            <a href="https://credentia.com/test-takers/nv" target='_blank'
                                                *ngIf="this.global.userDetails.getValue().stateId ==18"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a>

                                            <br />

                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</span><br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content1}}</span><br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}} <a href="https://credentia.com/test-takers/ak"
                                                    target='_blank' style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url}}</a></span>


                                            <br />
                                            <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].link}}: <a href="mailto:<EMAIL>" target='_blank'
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url1}}</a></span>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Oral Exam Pass -->
        <table *ngIf="isOral && scoresDetails && scoresDetails.isPassed" align="center" cellpadding="0" cellspacing="0"
            width="842" border="0">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="isNNAP && !isE12Route && this.scoresDetails['stateCode']!='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td *ngIf="isNNAP  && this.scoresDetails['stateCode']=='IA'" align="left" valign="middle" style="padding-left: 30px;">
                                        <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="isNNAP && !isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide NNAAP&#174; </span><br />
                                        <span *ngIf="!isNNAP"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Home Health Aide </span><br />
                                        <span *ngIf="isE12Route"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Nurse Aide Exam </span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset
                                            style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                        padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                        min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                            color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr *ngIf="scoresDetails.candidateId">
                                                        <td align="right" width="330" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Paper Testing
                                                                Id: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.paperTestId
                                                                }} </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!scoresDetails.candidateId">
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Date of
                                                                Birth: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.dateOfBirth
                                                                |date:'shortDate':'+0000'}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span style="color: #7d7d7d;font-size: 12px;">Answer sheet
                                                                no: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.answerSheetNumber}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Oral Exam-PASS</span><br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style="color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700; text-decoration: underline;">Next
                                            Steps:</span><br />
                                        <span *ngIf="global.userDetails.getValue().stateId !=18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP && !isE12Route && global.userDetails.getValue().stateId !=19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the NNAAP&#174; Oral Evaluation in {{stateName}}
                                            </span>
                                            <span *ngIf="isNNAP && global.userDetails.getValue().stateId ==19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations! <span
                                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                        {{nextSteps[0].value}}</span>
                                                </span>
                                            </span>
                                            <span *ngIf="isE12Route && global.userDetails.getValue().stateId !=19">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the Written Evaluation in {{stateName}}
                                            </span>
                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/cbpl/ProfessionalLicensing/NurseAideRegistry.aspx"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url1}}</a><span>{{nextSteps[0].link}}</span>

                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content1}}</p>
                                            <a href="https://www.commerce.alaska.gov/web/Portals/5/pub/My-License-Instructions.pdf"
                                                target="_blank" style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url2}}</a><span>{{nextSteps[0].content2}}</span><span><a
                                                    href="mailto:<EMAIL>" target="_blank"
                                                    style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url3}}</a></span><span
                                                class="ml-1">{{nextSteps[0].content3}}</span><span><a
                                                    href="https://www.commerce.alaska.gov/cbp/main/Search/Professional"
                                                    target="_blank" style="color: #0076C1;word-break: break-word;">
                                                    {{nextSteps[0].url4}}</a></span>


                                        </span>
                                        <span *ngIf="global.userDetails.getValue().stateId ==18"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span *ngIf="isNNAP">
                                                <span
                                                    style="color: #1B75BB; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                                    Congratulations!
                                                </span>
                                                You have passed the {{stateName}} CNA ORAL Examination!
                                            </span>
                                            <br />
                                            <p
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</p><a
                                                href="https://nvbn.boardsofnursing.org/licenselookup" target="_blank"
                                                style="color: #0076C1;word-break: break-word;">
                                                {{nextSteps[0].url}}</a> <span
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].content}}</span>

                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                            
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- Medication Exam Pass-->
        <table *ngIf="isMedication && scoresDetails && scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0" style="overflow: auto;height:90vh">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="!isAdult && data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Med Aide–Nursing Home&#174; </span><br />
                                        <span *ngIf="isAdult && data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} Med Aide–Adult Care</span><br />
                                            <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'"
                                            style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                            {{stateName}} {{data.examName =='Medication Assistant MACE Examination'?"Medication Assistant":data.examName =='Medication Assistant Endorsement'?'Medication Assistant Endorsement':'Medication Aide Certification'}}</span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">

                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;" >
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                    color: #11263c; font-size: 12px; font-weight: 600;">{{Details}}</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Candidate
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateFirstName}}
                                                                {{scoresDetails.candidateLastName}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Candiate
                                                                ID:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateId}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Address
                                                            </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateAddress}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                           
                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <tbody *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' || data.examName =='Medication Assistant Endorsement'">
                                                    <tr>
                                                        <td align="center" class="table-text"  valign="center">
                                                            <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                Name:<span
                                                                style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateFirstName}}
                                                                {{scoresDetails.candidateLastName}}</span></div>
                                                                <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                    Address:<span
                                                                    style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateAddress}}
                                                                    </span></div>
                                                                    <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                        CandidateID:<span
                                                                        style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateId}}
                                                                        </span></div>
                                                                        <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                            Examination Date:<span
                                                                            style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.examinationDate|date}}
                                                                            </span></div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">City:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateCity}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">State
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateState}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Postal
                                                                Code:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidatePostalCode}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                             
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' || data.examName =='Medication Assistant Endorsement'">
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white;  padding: 16px 40px; border-radius: 4px;">
                                            Exam-Pass</span><br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;" *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                    color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examName}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Test
                                                                Center ID:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.testCenterId}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white;  padding: 16px 40px; border-radius: 4px;">
                                            Examination Results-Pass</span><br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                            Your score below indicates that you have passed the
                                            {{scoresDetails.examName}}
                                        </span>
                                        <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination'"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                            Congratulations on passing the National Medication Aide Certification Examination (MACE&#174;)!
                                        </span>

                                        <span *ngIf="data.examName =='Medication Assistant Endorsement'"
                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                        Congratulations on passing the National Medication Assistant Endorsement  Examination (MACE&#174;)!
                                    </span>
                                        <!-- <span *ngIf="data.examName =='Medication Assistant MACE Examination'"
                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                        Next Steps:
                                    </span> -->
                                    <br /> 
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <p *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                Your name will be submitted to the North Carolina Medication Aide
                                                Registry.Once listed,
                                                you may view your name on the registry by going www.ncnar.org. If you
                                                have any questions
                                                regarding your listing on the registry,you may contact the North
                                                Carolina Division of Health Service
                                                Regulation,Monday through Friday from 9:00 am to 3:00 pm (EST),at
                                                (919)855-3969 or (919)715-0562</p><br />

                                            <p *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                You may not work as a medication aide in a skilled nursing facility
                                                unless you have a current,unrestricted listing on the Nurse Aide |
                                                Registry and your employer verifies your listing on Nurse Aide |
                                                Registry and Medication Aide Registry and verifies your skills.
                                                A medication aide with a substantiated finding on the Health Care
                                                Personnel Registry is
                                                prohibited from working in a skilled nursing facility.</p>

                                            <p *ngIf="(data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination') && this.scoresDetails['stateCode'] !='UT' && this.scoresDetails['stateCode'] !='KY'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                Please allow 7-10 business days for your registration to be viewable in
                                                the public {{stateName}} {{data.examName =='Medication Assistant MACE Examination'?"Medication Assistant":'Medication Aide Certification'}} Registry. You may login
                                                to Credentia at any time to download a copy of your registration</p>
                                                <p *ngIf="(data.examName =='Medication Aide Certification Examination') &&  this.scoresDetails['stateCode'] =='KY'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                Please allow 7-10 business days for your endorsement to be viewable in the Kentucky license lookup.<br> <a style="color:#0076c1;" href="https://kybn.boardsofnursing.org/licenselookup " target="_blank">https://kybn.boardsofnursing.org/licenselookup </a></p>
                                                <p *ngIf="data.examName =='Medication Aide Certification Examination' && this.scoresDetails['stateCode'] =='UT' "
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                To be licensed as a Medication Aide in Utah, you must apply with the Utah Division of Professional Licensing with all application requirements completed, including your Credentia Candidate ID.</p>
                                                <p *ngIf="data.examName =='Medication Aide Certification Examination' && this.scoresDetails['stateCode'] =='UT' "  style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                    Application instructions can be found on the Utah Division of Professional Licensing website: <a style="color:#0076c1;" href="https://dopl.utah.gov/nursing/apply-for-a-license/medication-aide/" target="_blank">https://dopl.utah.gov/nursing/apply-for-a-license/medication-aide/</a>
                                                </p>
                                                <p *ngIf="data.examName =='Medication Aide Certification Examination' && this.scoresDetails['stateCode'] =='UT' "  style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                    If all application requirements have been met, please allow 21 days for your application to be processed and your certificate to be issued.
                                                </p>

                                                <p *ngIf="data.examName =='Medication Assistant Endorsement' && this.scoresDetails['stateCode'] =='WA' "  style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                    Please allow 7-10 business days for your endorsement to be viewable in the Washington license lookup.
                                                    <a style="color:#0076c1;" href="https://fortress.wa.gov/doh/providercredentialsearch/default.aspx?AspxAutoDetectCookieSupport=1" target="_blank">Washington State Department of Health Provider Credential Search</a>                                                 </p>

                                               
                                            <br />
                                            <br />
                                        </span>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important; 
                                padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;" *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                                            <!-- <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                    color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend> -->
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr>
                                                        <td align="right" width="360" height=30 valign="center">
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">Examination:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.eligibilityRouteDescription}}
                                                            </span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Passing
                                                                Score:</span>
                                                            <span
                                                                style="color: black;font-size: 12px; font-weight: 700; padding-right: 16px">{{scoresDetails.passingScore}}</span>
                                                            <!-- <span style="color: black" *ngIf="!isAdult">%</span> -->
                                                            &ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">Result:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">
                                                                <span
                                                                    style="color:#00AB72;font-size: 12px; font-weight: 700;padding-right: 12px;">PASS
                                                                </span>&ensp;
                                                            </span>
                                                            <span style="color: #7d7d7d;font-size: 12px;">Your
                                                                Score:</span>
                                                            <span
                                                                style="color: black;font-size: 12px; font-weight: 700;">
                                                                {{scoresDetails.score}}
                                                            </span>
                                                            <!-- <span style="color: black" *ngIf="!isAdult">%</span> -->
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                        <br />
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </td>
                </tr>
            </tbody>
        </table>

        <!-- Medication Exam Fail -->
        <table *ngIf="isMedication && scoresDetails && !scoresDetails.isPassed" align="center" cellpadding="0"
            cellspacing="0" width="842" border="0" style="overflow: auto;height:90vh">
            <tbody>
                <tr>
                    <td align="center" width="842" style="max-width:842px;">
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#000000; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'" align="left" valign="middle" style="padding-right: 30px;width: 220px;">
                                        <img src="assets/img/Icons/mace_logo.png" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                    <td align="right" valign="middle" style="padding-right: 30px;">
                                        <img src="assets/img/Icons/Credentia-logo.svg" alt="credentia-logo"
                                            alt="credentia-logo" />
                                    </td>
                                </tr>
                                <tr height="5"></tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style=" color:#7D7D7D; background: #c7c7c725;">
                            <tbody>
                                <tr height="10"></tr>
                                <tr>
                                    <td align="center" valign="start">
                                        <span *ngIf="!isAdult && data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                        style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                        {{stateName}} Med Aide–Nursing Home&#174; </span><br />
                                    <span *ngIf="isAdult && data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                        style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                        {{stateName}} Med Aide–Adult Care</span><br />
                                        <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'"
                                        style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                        {{stateName}} {{data.examName =='Medication Assistant MACE Examination' ?"Medication Assistant": data.examName =='Medication Assistant Endorsement'?'Medication Assistant Endorsement':'Medication Aide Certification'}}</span><br />
                                        <span
                                            style="line-height: 1.6; font-size: 14px; font-weight: 600; color:#11263c; letter-spacing: 3px">
                                            Examination Results
                                        </span>
                                    </td>
                                </tr>
                                <tr height="10"></tr>
                            </tbody>
                        </table>
                        <hr style="border: 0.1px solid #0077c183;">

                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0" 
                            style="margin-top: 20px;">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                            padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                            min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                color: #11263c; font-size: 12px; font-weight: 600;">{{Details}}</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Candidate
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateFirstName}}
                                                                {{scoresDetails.candidateLastName}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Candiate
                                                                ID:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateId}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Address
                                                            </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateAddress}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                           
                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <tbody *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'">
                                                    <tr>
                                                        <td align="center" class="table-text"  valign="center">
                                                            <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                Name:<span
                                                                style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateFirstName}}
                                                                {{scoresDetails.candidateLastName}}</span></div>
                                                                <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                    Address:<span
                                                                    style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateAddress}}
                                                                    </span></div>
                                                                    <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                        CandidateID:<span
                                                                        style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.candidateId}}
                                                                        </span></div>
                                                                        <div style="color: #7d7d7d;font-size: 12px;" class="flex-none ml-3">
                                                                            Examination Date:<span
                                                                            style="color: black;font-size: 12px; font-weight: 700;" class="ml-1">{{scoresDetails.examinationDate|date}}
                                                                            </span></div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'" >
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">City:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateCity}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">State
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidateState}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Postal
                                                                Code:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.candidatePostalCode}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            
                        
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'"
                                            style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Exam-Fail</span>
                                            
                                            <br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>
                            </tbody>
                        </table>
                        <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' || data.examName =='Medication Assistant Endorsement'"
                        style=" text-align:center;color:  #7d7d7d;font-weight:500;font-size: 14px; line-height: 1.6; font-weight: 500;">
                         Performance:</span>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                        style=" color:#7D7D7D" *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination' ||  data.examName =='Medication Assistant Endorsement'" class="mt-4">
                        <tbody>
                            <tr>
                                <td>
                                    <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                        style=" color:#7D7D7D;font-size: 14px;line-height: 1.4; border: 0.1px solid #0077c183;"
                                        bgcolor="#FAFAFA">
                                        <tbody>
                                            <ng-container  *ngFor="let data of scoresDetails.skillPerformance;let i=index;">
                                                <tr>
                                                    <!-- <td width="10"></td> -->
                                                    <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                        [ngStyle]="{'color':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '#c72b40' : '#7D7D7D','border-top':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '1px  solid #EFC7CD':'#7D7D7D','border-bottom':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass')?'1px  solid #EFC7CD' : 'none' }"
                                                        width="500" >
                                                         <b>{{convert(i +1)}}.</b>&nbsp;&nbsp;{{data.question}}</td>
                                                        <hr>
                                                       
                                                    <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                    [ngStyle]="{'color':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '#c72b40' : '#7D7D7D','border-top':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '1px  solid #EFC7CD':'#7D7D7D','border-bottom':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass')?'1px  solid #EFC7CD' : 'none' }"
                                                    width="330" align="right">{{data.result}}</td>
                                                    <!-- <td width="10"></td> -->
                                                </tr>
                                                <tr *ngIf="data.steps && data.steps.length > 0 && data.steps !='' && data.steps !=null"
                                                    style="font-size: 8pt;">
                                                    <!-- <td width="7"></td> -->
                                                    <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                    [ngStyle]="{'color':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '#c72b40' : '#7D7D7D','border-top':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '1px  solid #EFC7CD':'#7D7D7D','border-bottom':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass')?'1px  solid #EFC7CD' : 'none' }"
                                                    style="padding-top: 10px; color: #c72b40; font-weight: 500;"
                                                        width="330">
                                                        <span
                                                            style="padding-left: 7px; color: #c72b40; font-weight: 500;">Failed
                                                            Steps:</span>
                                                        <span *ngFor="let step of data.steps">{{step}}&nbsp;</span>
                                                    </td>
                                                    <td style="padding-left: 10px; padding-right: 20px; color: #c72b40; font-weight: 500;"
                                                    [ngStyle]="{'color':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '#c72b40' : '#7D7D7D','border-top':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass') ? '1px  solid #EFC7CD':'#7D7D7D','border-bottom':(data.result === 'Unsatisfactory'|| data.result =='Below Pass' || data.result =='Near Pass')?'1px  solid #EFC7CD' : 'none' }"
                                                    width="330" align="right"></td>
                                                    <!-- <td width="7"></td> -->
                                                </tr>
                                            </ng-container>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                            style="margin-top: 20px;" *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                            <tbody>
                                <tr>
                                    <td width="50"></td>
                                    <td>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                            padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                            min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                            <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend>
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr>
                                                        <td align="center" width="286" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Name:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examName}}</span>
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="586" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Examination
                                                                Date: </span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.examinationDate|date}}
                                                            </span>&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;padding-left: 40px">Test
                                                                Center ID:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.testCenterId}}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                    </td>
                                    <td width="50"></td>
                                </tr>
                                <tr height="25"></tr>
                            </tbody>
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td width="20"></td>
                                    <td align="center" valign="start">
                                        <span
                                            style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                            Examination Results-Fail</span>
                                            
                                            <br /><br />
                                    </td>
                                    <td width="20"></td>
                                </tr>
                                <tr height="20"></tr>

                            </tbody>
                        </table>
                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                            style=" color:#7D7D7D; margin-bottom: 20px;">
                            <tbody>
                                <tr height="20"></tr>
                                <tr>
                                    <td align="left">
                                        <span *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                            Your score below indicates that you have failed the
                                            {{scoresDetails.examName}}
                                        </span>
                                        <span *ngIf="data.examName =='Medication Assistant MACE Examination' || data.examName =='Medication Aide Certification Examination'"
                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                        This Score Report confirms you DID NOT PASS the National Medication Aide Certification Examination
                                        (MACE&#174;)
                                    </span>
                                    <span *ngIf="data.examName =='Medication Assistant Endorsement'"
                                    style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 700;">
                                    This Score Report confirms you DID NOT PASS the National Medication Assistant Endorsement Examination
                                    (MACE&#174;)
                                </span>
                                        <br /> <br />
                                        <span
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;">
                                            <span>
                                                
                                            </span>
                                            <p *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                {{nextSteps[0].value}}</p>
                                                <p *ngIf="data.examName =='Medication Assistant MACE Examination'"
                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                <strong>Next Steps : </strong> {{nextSteps[0].value}}
                                                <a href="https://credentia.com/test-takers/sc" target='_blank'
                                                style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> {{nextSteps[0].content}}
                                            
                                            </p>

                                            <p *ngIf="data.examName =='Medication Aide Certification Examination'"
                                            style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                            <strong>Next Steps : </strong> {{nextSteps[0].value}}
                                            <a *ngIf="this.scoresDetails['stateCode'] == 'CO'" href="https://credentia.com/test-takers/comace" target='_blank'
                                            style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> 
                                            <a *ngIf="this.scoresDetails['stateCode'] == 'UT'" href="https://credentia.com/test-takers/UTmace" target='_blank'
                                            style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> 
                                            <a *ngIf="this.scoresDetails['stateCode'] == 'KY'" href="https://credentia.com/test-takers/KYmace" target='_blank'
                                            style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> 
                                            <a *ngIf="this.scoresDetails['stateCode'] == 'WA'" href="https://credentia.com/test-takers/wamace" target='_blank'
                                            style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> 
                                            
                                            {{nextSteps[0].content}}
                                        
                                        </p>
                                        <p *ngIf="data.examName =='Medication Assistant Endorsement'"
                                        style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                        <strong>Next Steps : </strong> {{nextSteps[0].value}}
                                        <a href="https://credentia.com/test-takers/wamace" target='_blank'
                                        style="color: #0076C1;word-break: break-word;">{{nextSteps[0].url}}</a> {{nextSteps[0].content}}
                                    
                                    </p>
                                                <br />
                                        </span>
                                        <fieldset style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                            padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                            min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;" *ngIf="data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'">
                                            <!-- <legend style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                color: #11263c; font-size: 12px; font-weight: 600;">EXAM DETAILS</legend> -->
                                            <table style="line-height: 1.6;font-size: 14px;">
                                                <tbody>
                                                    <tr >
                                                        <td align="right" width="360" height=30 valign="center">
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">Examination:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;">{{scoresDetails.eligibilityRouteDescription}}</span>&ensp;
                                                        </td>
                                                        <td width="15"></td>
                                                        <td align="left" width="386" height=30 valign="center">
                                                            <span style="color: #7d7d7d;font-size: 12px;">Passing
                                                                Score:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700; padding-right: 16px">{{scoresDetails.passingScore}}</span>
                                                            <!-- <span style="color: black" *ngIf="!isAdult">%</span> -->
                                                            &ensp; &ensp;
                                                            <span
                                                                style="color: #7d7d7d;font-size: 12px;">Result:</span><span
                                                                style="color: black;font-size: 12px; font-weight: 700;padding-right: 12px;">
                                                                <span
                                                                    style="color:#D02F44;font-size: 12px; font-weight: 700;">FAIL
                                                                </span>&ensp;
                                                            </span>
                                                            <span style="color: #7d7d7d;font-size: 12px;">Your
                                                                Score:</span>
                                                            <span
                                                                style="color: black;font-size: 12px; font-weight: 700;">
                                                                {{scoresDetails.score}}
                                                            </span>
                                                            <!-- <span style="color: black" *ngIf="!isAdult">%</span>
                                                  -->
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </fieldset>
                                        <br />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table align="center" cellpadding="0" cellspacing="0" width="842" border="0" *ngIf="scoresDetails.skillPerformance !=null && data.examName !='Medication Assistant MACE Examination' && data.examName !='Medication Aide Certification Examination' && data.examName !='Medication Assistant Endorsement'"
                            style=" color:#7D7D7D">
                            <tbody>
                                <tr>
                                    <td>
                                        <span 
                                            style=" padding-left: 90px;color: #7d7d7d;font-weight:500;font-size: 14px; line-height: 1.6; font-weight: 500;">
                                            Content Area Performance:</span>
                                        <span
                                            style=" padding-left: 180px;color: #7d7d7d;font-weight:500;font-size: 14px; line-height: 1.6; font-weight: 500;">
                                            Low</span>
                                        <span
                                            style=" padding-left: 240px;color: #7d7d7d;font-weight:500;font-size: 14px; line-height: 1.6; font-weight: 500;">
                                            High</span>
                                        <br />
                                        <br />
                                        <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                            style=" color:black;font-size: 14px;font-weight:500;">
                                            <tbody>
                                                <ng-container *ngFor="let data of scoresDetails.skillPerformance">
                                                    <tr>
                                                        <td style="padding-left: 10px; padding-right: 20px; color:black;font-size: 14px;font-weight:500;"
                                                            width="330">{{data.question}}</td>
                                                        <td style="padding-left: 10px; padding-right: 20px;">
                                                            <mat-progress-bar mode="determinate"
                                                                [value]="data.score"></mat-progress-bar>
                                                        </td>
                                                    </tr>
                                                </ng-container>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div style="display: flex; justify-content: center; margin-bottom: 10px">
        <button mat-button class="btn-1 text-xs mx-2" (click)="toPdf();">Download</button>
    </div>
</div>