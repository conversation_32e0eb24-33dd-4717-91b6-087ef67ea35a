.header-container {
    display: flex;
    justify-content: space-between;
    .details {
        display: flex;
        justify-content: space-between;
        width: 75%;
        margin-right: 16px;
        .value {
            margin-left: 8px;
        }
    }
}
 
.divider {
    border: 1px solid lightgrey;
    margin: 4px 0px 12px 0px;
}
 
.dialog-container {
    display: flex;
    width: 100%;
    height: 78vh;
    overflow: hidden;
    .omr {
        width: 55%;
        .image-container {
            width: 100%;
            height: 100%;
            overflow-y: scroll;
            .image {
                width: 100%;
                height: auto;
            }
        }
    }
 
    .options {
        width: 43%;
        display: flex;
        justify-content: space-between;
        overflow: scroll;
 
        tr {
            display: flex;
            align-items: start;
        }
 
        .option {
            display: flex;
            align-items: center;
            margin: 10px 5px;
 
            .label-text {
                width: 24px;
                height: 24px;
                display: flex;
                justify-content: end;
                align-items: center;
                font-size: 12px;
                font-weight: bold;
            }
 
            .label,
            .select {
                margin: 0px 5px;
            }
        }
 
        select {
            border: 1px solid;
            padding: 2px 4px;
            margin: 2px;
        }
    }
 
    ::ng-deep .mat-dialog-content {
        max-height: 78vh;
    }
 
    .mat-divider.mat-divider-vertical {
        border-width: 3px;
        margin: 0px 5px;
    }
}

::ng-deep .mat-dialog-container#reEvaluationDialogComponent {
    overflow: unset;
}
::ng-deep .mat-typography.mat-dialog-container {
    overflow: unset;
}
 
::ng-deep .mat-dialog-content {
    max-height: 78vh;
}
 
::ng-deep .mat-dialog-actions {
    margin-right: 45px;
 
    .cancel-btn {
        margin-right: 12px;
    }
}
 
.mat-dialog-title {
    margin: 0px 24px;
}
 
.hide {
    background: #e9e9e9;
    display: block !important;
    filter: blur(10px) !important;
    pointer-events: none !important;
    user-select: none !important;
    opacity: 0.5;
}
 
.hide1 {
    // width: 100vw !important;
    // height: 100vh !important;
    display: none !important;
}
 
 
.loading-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
 
.mat-progress-spinner {
    left: 5%;
}
 
.height {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 0.25rem) !important;
    overflow: auto !important;
   
}