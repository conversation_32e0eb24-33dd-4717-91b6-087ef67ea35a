import { state } from "@angular/animations";
import { createReducer, on } from "@ngrx/store";
import {
  Examcancelled,
  gotCart,
  gotEligibilityRoutes,
  gotExamId,
  gotMakePayment,
  gotCartItems,
  gotMonthlySlots,
  gotRegisteredExam,
  gotrescheduled,
  gotSchedule,
  gotTimeSlots,
  gotTimezones,
  isPayment,
  setCandidateId,
  setTimezome,
  vouchercartDetails,
  deletedCartId,
  successPayment,
  gotNewVoucher,
  reAddedSuccess,
  trainingStateId$,
  madenull,
  gotPaymentMethod,
  madeCharge,
  gotPaymentCustomerId,
  paymentMethodCreated,
  getVoucherDetails,
  gotTimeSlotsTestCenter,
  gotVoucher,
  gotVoucherApply,
  clearVocherResponse,
  gotScheduled,
  clearChargeResponse,
  voucherUpdated,
  saveVocher,
  clearVoucherCode,
  paymentCustomerIdCreated,
  clearTimeSlotsTestCenter,
  ClearTimeslots,
  getTimeSlotsTestCenterFailure,
} from "./scheduled.actions";
import { initScheduledState, ScheduledState } from "./scheduled.state";

const _scheduledReducer = createReducer<ScheduledState>(
  initScheduledState,
  on(gotTimezones, (state, action) => {
    return {
      ...state,
      timezones: action.Timeszones,
    };
  }),
  on(gotTimeSlots, (state, action) => {
    return {
      ...state,
      timeslots: action.Slots,
    };
  }),
  on(gotCart, (state, action) => {
    return {
      ...state,
      cart: action.cart,
      isPayment: action.isPayment,
    };
  }),
  on(gotMonthlySlots, (state, action) => {
    return {
      ...state,
      monthlySlots: action.monthlySlots,
    };
  }),
  on(gotExamId, (state, action) => {
    return {
      ...state,
      examId: action.examdata,
      loading: false,
    };
  }),
  on(gotEligibilityRoutes, (state, action) => {
    return {
      ...state,
      route: action.route,
      loading: false,
    };
  }),
  on(gotRegisteredExam, (state, action) => {
    return {
      ...state,
      registeredExams: action.registeredExams,
      loading: false,
    };
  }),
  on(gotMakePayment, (state, action) => {
    return {
      ...state,
      makepaymentresponse: action.makePaymentResponse,
      loading: false,
    };
  }),

  on(gotVoucherApply, (state, action) => {
    return {
      ...state,
      VochersApply: action.VocherApplyResponse,
    };
  }),
  on(gotCartItems, (state, action) => {
    return {
      ...state,
      cartItems: action.cartItems,
    };
  }),
  on(gotSchedule, (state, action) => {
    return {
      ...state,
      schedule: action.scheduleres,
    };
  }),
  on(Examcancelled, (state, action) => {
    return {
      ...state,
      isCancelled: action.isCancelled,
    };
  }),
  on(gotrescheduled, (state, action) => {
    return {
      ...state,
      rescheduleResponse: action.rescheduleResponse,
    };
  }),
  on(gotVoucher, (state, action) => {
    return {
      ...state,
      VocherResponse: action.VocherResponse,
    };
  }),

  on(isPayment, (state, action) => {
    return {
      ...state,
      isPayment: false,
    };
  }),
  on(setCandidateId, (state, action) => {
    return {
      ...state,
      candidateId: action.candidateId,
    };
  }),
  on(setTimezome, (state, action) => {
    return {
      ...state,
      timezone: action.timezone,
    };
  }),
  on(vouchercartDetails, (state, action) => {
    return {
      ...state,
      voucherCart: action.voucherCartDeatils,
    };
  }),
  on(deletedCartId, (state, action) => {
    return {
      ...state,
      deleteVoucherCart: action.deletedCartStatus,
    };
  }),
  on(successPayment, (state, action) => {
    return {
      ...state,
      paymentDetails: action.paidCartStatus,
    };
  }),
  on(gotNewVoucher, (state, action) => {
    return {
      ...state,
      generateVoucher: action.successNewVoucher,
    };
  }),
  on(reAddedSuccess, (state, action: any) => {
    return {
      ...state,
      editCartItems: action.sucessCartmessage,
    };
  }),
  on(madenull, (state, action: any) => {
    return {
      ...state,
      paymentDetails: action.parameter,
    };
  }),
  on(trainingStateId$, (state, action) => {
    return {
      ...state,
      trainingStateID: action.gotTiId,
    };
  }),
  on(gotPaymentMethod, (state, action) => {
    return {
      ...state,
      paymentMethods: action.paymentmethods,
    };
  }),
  on(madeCharge, (state, action) => {
    return {
      ...state,
      chargeResponse: action.chargeResponse,
    };
  }),
  on(gotPaymentCustomerId, (state, action) => {
    return {
      ...state,
      customerIdObj: action.customerIdObj,
    };
  }),
  on(paymentCustomerIdCreated, (state, action) => {
    return {
      ...state,
      createPaymnetCustomerIdResponse: action.response,
    };
  }),
  on(paymentMethodCreated, (state, action) => {
    return {
      ...state,
      createPaymentMethodResponse: action.response,
    };
  }),
  on(getVoucherDetails, (state, action: any) => {
    return {
      ...state,
      voucherDetails: action.voucherDetails,
    };
  }),
  on(clearTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      timeslotsTestCenter: null,
      timeSlotsTestCenterStatus: "idle",
    };
  }),
  on(gotTimeSlotsTestCenter, (state, action) => {
    return {
      ...state,
      timeslotsTestCenter: action.slots,
      timeSlotsTestCenterStatus: "success",
    };
  }),

  on(getTimeSlotsTestCenterFailure, (state, action) => {
    return {
      ...state,
      loading: false,
      timeSlotsTestCenterStatus: "error",
      error: action.error,
    };
  }),

  on(gotScheduled, (state, action) => {
    return {
      ...state,
      scheduleResponse: action.ScheduledResponse,
    };
  }),

  on(clearVocherResponse, (state, action) => {
    return {
      ...state,
      VocherResponse: null,
      VochersApply: null,
    };
  }),
  on(voucherUpdated, (state, action) => {
    return {
      ...state,
      VocherUpdateResponse: action.response,
    };
  }),
  on(clearChargeResponse, (state, action) => {
    return {
      ...state,
      chargeResponse: null,
    };
  }),
  on(saveVocher, (state, action) => {
    return {
      ...state,
      voucherCode: action.voucherCode,
    };
  }),
  on(clearVoucherCode, (state, action) => {
    return {
      ...state,
      voucherCode: null,
    };
  }),
  on(ClearTimeslots, (state, action) => {
    return {
      ...state,
      timeslots: null,
    };
  })
);

export function scheduledReducer(state, action) {
  return _scheduledReducer(state, action);
}
