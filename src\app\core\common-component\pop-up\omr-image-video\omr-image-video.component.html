<div *ngIf="data?.actiontype != 'booklet_view' && data.id != 73 && data.actiontype !='remove_red_eye'" class="h-full">
    <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header">
        <div *ngIf="data?.actiontype == 'Bulk'" class="px-2 py-1 t-xs title">Bulk Import</div>
        <div class="text-center font-medium text-size" *ngIf="data?.actiontype == 'visibility'">Reason For Error</div>
        <div class="text-center font-medium text-size" *ngIf="data?.actiontype == 'featured_video'">Shreding Video</div>

        <div class="flex justify-end cursor-pointer" mat-dialog-close>
            <mat-icon class="text-base flex justify-end py-1">close</mat-icon>
        </div>
    </div>
    <div *ngIf="isLoading && (data?.actiontype == 'downpicture_as_pdfload' )" class="loader">
        <mat-progress-spinner class="calenderSpinner flex justify-center" color="primary" diameter="60"
            mode="indeterminate">
        </mat-progress-spinner>
    </div>
    <ng-container *ngIf="data?.actiontype == 'downpicture_as_pdfload'">
        <div *ngFor="let pdf of AnswerSheetPDF">
            <div>
                <pdf-viewer [src]='pdf.url' [render-text]="true" [original-size]="false" [zoom-scale]="'50'"
                    (after-load-complete)="onPdfLoadComplete()" style="width: 100%; height: 450px"></pdf-viewer>
            </div>
        </div>


    </ng-container>
    <ng-container *ngIf="data?.actiontype == 'image'">
        <div *ngFor="let pdf of AnswerSheetImage">
            <div>
                <img src="{{pdf.url}}" alt="">
            </div>
        </div>


    </ng-container>
<ng-container *ngIf="data?.actiontype == 'featured_video'">
    <div class="ml-2 video-container" *ngFor="let video of ShredingVideo">
        <video width="320" height="240" controls>
          <source [src]="video.url" type="video/mp4">
          Your browser does not support the video tag.
        </video>
      </div>
</ng-container>
    
    <!-- <div class="flex justify-center p-4">
        <!-- <button class="btn px-4 py-2 cursor-pointer" (click)="downloadPdf(data)">
            Download
        </button> -->
    <!-- </div> -->

    <div class="py-3 text-xs text-right  btn-2 -mt-3 " *ngIf="data?.actiontype == 'Bulk'">
        <a href="{{ url }}"
        class="cursor-pointer" 
        
        download="Training_List.xlsx">Download Template</a>
             
    </div>

    <div *ngIf="data?.actiontype == 'Bulk'" class="container px-2 py-3" appDnd (fileDropped)="onFileDropped($event)">
        <div>
            <input type="file" #fileDropRef id="fileDropRef" multiple
                (change)="fileBrowseHandler($event.target.files)" />
            <div class="mt-6">
                <img class="filesrc " src="assets/img/upload.png" width="60">
            </div>

            <h3 class="text-size"><a class="text-size text-color">Click to Upload</a> or drag and drop</h3>
        </div>

        <div *ngIf="files.length > 0" class="files-list">
            <div class="single-file" *ngFor="let file of files; let i = index">
                <div class="file-icon" style="width: 50px">

                </div>
                <div class="info">
                    <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header">
                        <div *ngIf="data?.actiontype == 'Bulk'" class="px-2 py-1 t-xs text-sizes">{{ file?.name }}</div>
                        <div class="flex justify-end cursor-pointer" mat-dialog-close>
                            <mat-icon class=" flex justify-end py-1 text-sizes" (click)="deleteFile(i)">close</mat-icon>
                        </div>
                    </div>

                    <div class="progress-cont">
                        <div class="progress" [style.width]="file?.progress + '%'">
                        </div>
                    </div>
                </div>
                <!-- <div class="delete" (click)="deleteFile(i)">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="18"
                viewBox="0 0 14 18"
              >
                <path
                  fill="#B1B1B1"
                  fill-rule="nonzero"
                  d="M1 16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v10zm3.17-7.83a.996.996 0 0 1 1.41 0L7 9.59l1.42-1.42a.996.996 0 1 1 1.41 1.41L8.41 11l1.42 1.42a.996.996 0 1 1-1.41 1.41L7 12.41l-1.42 1.42a.996.996 0 1 1-1.41-1.41L5.59 11 4.17 9.58a.996.996 0 0 1 0-1.41zM10.5 1L9.79.29C9.61.11 9.35 0 9.09 0H4.91c-.26 0-.52.11-.7.29L3.5 1H1c-.55 0-1 .45-1 1s.45 1 1 1h12c.55 0 1-.45 1-1s-.45-1-1-1h-2.5z"
                />
              </svg>
            </div> -->

            </div>
        </div>
    </div>

    <div *ngIf="data?.actiontype == 'Bulk'" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="py-3">
        <button mat-button class="buuton1 add text-xs btn-7" type="button" mat-dialog-close>Cancel</button>
        <button class="buuton2 text-xs btn-1" mat-raised-button type="button" (click)="Submit()"
            [disabled]="!FileProgress">Submit</button>
    </div>


    <div *ngIf="data?.actiontype == 'visibility'" class="flex justify-center btn-3 ">
        <ol>
            <li *ngFor="let errormessage of ErrorListArray,let i=index">{{i}} : {{errormessage}}</li>
        </ol>
    </div>
</div>
<div *ngIf="data?.actiontype == 'booklet_view'">
    <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header">
        <div *ngIf="data?.actiontype == 'Bulk'" class="px-2 py-1 t-xs title">Bulk Import</div>
        <div class="text-center font-medium text-size" *ngIf="data?.actiontype == 'visibility'">Reason For Error</div>
        <div class="text-center font-medium text-size px-4 pb-2" *ngIf="data?.actiontype == 'booklet_view'">Order Information -
            {{orderId}}</div>
        <div class="flex justify-end cursor-pointer" mat-dialog-close>
            <mat-icon class="text-base flex justify-end py-1">close</mat-icon>
        </div>
    </div>
    <div *ngIf="isLoading && (data?.actiontype == 'downpicture_as_pdfload' )" class="loader">
        <mat-progress-spinner class="calenderSpinner flex justify-center" color="primary" diameter="60"
            mode="indeterminate">
        </mat-progress-spinner>
    </div>
    <ng-container *ngIf="data?.actiontype == 'downpicture_as_pdfload'">
        <div *ngFor="let pdf of AnswerSheetPDF">
            <div>
                <pdf-viewer [src]='pdf.url' [render-text]="true" [original-size]="false" [zoom-scale]="'50'"
                    (after-load-complete)="onPdfLoadComplete()" style="width: 100%; height: 450px"></pdf-viewer>
            </div>
        </div>


    </ng-container>
    <ng-container *ngIf="data?.actiontype == 'image'">
        <div *ngFor="let pdf of AnswerSheetImage">
            <div>
                <img src="{{pdf.url}}" alt="">
            </div>
        </div>


    </ng-container>
    <!-- <div class="flex justify-center p-4">
        <!-- <button class="btn px-4 py-2 cursor-pointer" (click)="downloadPdf(data)">
            Download
        </button> -->
    <!-- </div> -->

    <div class="py-3 text-xs text-right  btn-2 -mt-3 " *ngIf="data?.actiontype == 'Bulk'">
        <a class="cursor-pointer " (click)="getDownloadTemplate()" class="cursor-pointer">Download Template</a>
    </div>

    <div *ngIf="data?.actiontype == 'Bulk'" class="container px-2 py-3" appDnd (fileDropped)="onFileDropped($event)">
        <div>
            <input type="file" #fileDropRef id="fileDropRef" multiple
                (change)="fileBrowseHandler($event.target.files)" />
            <div class="mt-6">
                <img class="filesrc " src="assets/img/upload.png" width="60">
            </div>

            <h3 class="text-size"><a class="text-size text-color">Click to Upload</a> or drag and drop</h3>
        </div>

        <div *ngIf="files.length > 0" class="files-list">
            <div class="single-file" *ngFor="let file of files; let i = index">
                <div class="file-icon" style="width: 50px">

                </div>
                <div class="info">
                    <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header">
                        <div *ngIf="data?.actiontype == 'Bulk'" class="px-2 py-1 t-xs text-sizes">{{ file?.name }}</div>
                        <div class="flex justify-end cursor-pointer" mat-dialog-close>
                            <mat-icon class=" flex justify-end py-1 text-sizes" (click)="deleteFile(i)">close</mat-icon>
                        </div>
                    </div>

                    <div class="progress-cont">
                        <div class="progress" [style.width]="file?.progress + '%'">
                        </div>
                    </div>
                </div>
                <!-- <div class="delete" (click)="deleteFile(i)">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="18"
                viewBox="0 0 14 18"
              >
                <path
                  fill="#B1B1B1"
                  fill-rule="nonzero"
                  d="M1 16c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v10zm3.17-7.83a.996.996 0 0 1 1.41 0L7 9.59l1.42-1.42a.996.996 0 1 1 1.41 1.41L8.41 11l1.42 1.42a.996.996 0 1 1-1.41 1.41L7 12.41l-1.42 1.42a.996.996 0 1 1-1.41-1.41L5.59 11 4.17 9.58a.996.996 0 0 1 0-1.41zM10.5 1L9.79.29C9.61.11 9.35 0 9.09 0H4.91c-.26 0-.52.11-.7.29L3.5 1H1c-.55 0-1 .45-1 1s.45 1 1 1h12c.55 0 1-.45 1-1s-.45-1-1-1h-2.5z"
                />
              </svg>
            </div> -->

            </div>
        </div>
    </div>

    <div *ngIf="data?.actiontype == 'Bulk'" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="py-3">
        <button mat-button class="buuton1 add text-xs btn-7" type="button" mat-dialog-close>Cancel</button>
        <button class="buuton2 text-xs btn-1" mat-raised-button type="button" (click)="Submit()"
            [disabled]="!FileProgress">Submit</button>
    </div>


    <div *ngIf="data?.actiontype == 'visibility'" class="flex justify-center btn-3 ">
        <ol>
            <li *ngFor="let errormessage of ErrorListArray,let i=index">{{i}} : {{errormessage}}</li>
        </ol>
    </div>
</div>


<!-- <ng-container *ngIf="TextDetails =='video'">
                                    <div class="img-container" *ngFor="let item of AnswerSheet  ">
                                      
                                      <video preload="auto"  class="Video" width="100%" controls #stream>
                                        <source src="{{item.url}}" alt="Video is not available" type="video/mp4">
                                      </video>
                                    </div>
                                </ng-container> -->


<table *ngIf="TextDetails =='measure'" align="center" cellpadding="0" cellspacing="0" width="842" border="0">
    <tbody *ngFor="let measurementdetails of MeasureDetails ">
        <tr>
            <td align="center" width="842" style="max-width:842px;">
                <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                    style=" color:#000000; background: #c7c7c725;">
                    <tbody>
                        <tr height="10"></tr>
                        <tr>
                            <td align="left" valign="middle" style="padding-left: 30px;">
                                <img src="assets/img/Icons/NNAP-logo.svg" alt="credentia-logo">
                            </td>
                            <td align="right" valign="middle" style="padding-right: 30px;">
                                <img src="assets/img/Icons/credentia.png" alt="credentia-logo" alt="credentia-logo" />
                            </td>
                        </tr>
                        <tr height="5"></tr>
                    </tbody>
                </table>
                <table align="center" cellpadding="0" cellspacing="0" width="842" border="0"
                    style=" color:#7D7D7D; background: #c7c7c725;">
                    <tbody>
                        <tr height="10"></tr>
                        <tr>
                            <td align="center" valign="start">
                                <span
                                    style="line-height: 1.6; font-size: 20px; font-weight: 500; color:#11263c; letter-spacing: 3px">
                                    {{data.id.element.stateName}} Nurse Aide NNAAP&#174; </span><br />

                            </td>
                        </tr>
                        <tr height="10"></tr>
                    </tbody>
                </table>
                <hr style="border: 0.1px solid #0077c183;">
                <table align="center" cellpadding="0" cellspacing="0" width="842" border="0" style="margin-top: 20px;">
                    <tbody>
                        <tr>
                            <td width="50"></td>
                            <td>
                                <fieldset
                                    style=" display: block; margin-inline-start: 2px; margin-inline-end: 2px; text-align: center!important;
                                                                padding-inline-start: 0.75em; padding-inline-end: 0.75em; padding-block-end: 0.625em;
                                                                min-inline-size: min-content; border: 0.1px solid #0076c1; border-radius: 4px;">
                                    <legend
                                        style="display: block; padding-inline-start: 5px; padding-inline-end: 5px; border-style: none;
                                                                    color: #11263c; font-size: 12px; font-weight: 600;">
                                        Candidate DETAILS
                                    </legend>
                                    <table style="line-height: 1.6;font-size: 14px;">
                                        <tbody>
                                            <tr *ngIf="measurementdetails.candidateId">
                                                <td align="center" class="table-text" valign="center">

                                                    <span style="color: #7d7d7d;font-size: 12px;">Candidate Name:
                                                    </span><span style="color: black;font-size: 12px; font-weight: 700;"
                                                        class="mr-3">{{measurementdetails.firstName}}
                                                        {{measurementdetails.lastName}} </span>&ensp;
                                                    <span style="color: #7d7d7d;font-size: 12px;">Candidate Id:
                                                    </span><span style="color: black;font-size: 12px; font-weight: 700;"
                                                        class="mr-3">{{measurementdetails.candidateId}} </span>&ensp;
                                                    <span style="color: #7d7d7d;font-size: 12px;">Exam Name:
                                                    </span><span style="color: black;font-size: 12px; font-weight: 700;"
                                                        class="mr-3">{{measurementdetails.examName
                                                        }}</span>&ensp;
                                                    <span style="color: #7d7d7d;font-size: 12px;">Email Id: </span><span
                                                        style="color: black;font-size: 12px; font-weight: 700;">{{measurementdetails.emailId
                                                        }}</span>&ensp;
                                                </td>
                                                <td width="15"></td>

                                            </tr>

                                        </tbody>


                                    </table>
                                </fieldset>
                            </td>
                            <td width="50"></td>
                        </tr>
                        <tr height="25"></tr>
                    </tbody>
                    <tbody>
                        <tr height="20"></tr>
                        <tr>
                            <td width="20"></td>
                            <!-- <td align="center" valign="start">
                                                            <span
                                                                style="background-color: #D02F44;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                                                Skills Exam-Fail</span><br /><br />
                                                            <span *ngIf="scoresDetails.skillPerformance !=null"
                                                                style="color: #11263c; font-size: 14px; line-height: 1.6; font-weight: 500; padding-top: 20px;">Skills
                                                                Performance:</span>
                                                        </td> -->
                            <td width="20"></td>
                        </tr>
                        <tr height="20"></tr>
                    </tbody>
                </table>
                <table align="center" cellpadding="0" cellspacing="0" width="842" border="0" style="margin-top: 20px;">

                    <tbody>
                        <tr height="20"></tr>
                        <tr>
                            <td width="20"></td>
                            <td align="center" valign="start">
                                <span
                                    style="background-color: #00AB72;line-height: 1.6; font-size: 20px; font-weight: 500; color:white; padding: 16px 40px; border-radius: 4px;">
                                    {{MeasureDetails[0].isPassed == true? 'Skills Exam-PASS':'Skills
                                    Exam-Fail'}}</span><br /><br />
                            </td>
                            <td width="20"></td>
                        </tr>
                        <tr height="20"></tr>
                    </tbody>
                </table>

                <table align="center" cellpadding="0" cellspacing="0" border="0" style="color:#7D7D7D">
                    <tbody>
                        <tr>
                            <td>
                                <table align="center" cellpadding="4" cellspacing="0" border="0"
                                    style=" color:#7D7D7D;font-size: 14px;line-height: 1.4; border: 0.1px solid #0077c183;"
                                    bgcolor="#FAFAFA">
                                    <tbody>

                                        <tr>
                                            <th> Skill Name</th>
                                            <th>Procedure steps</th>
                                            <th>isPassed</th>
                                        </tr>
                                        <ng-container *ngFor="let data of measurementdetails.skills;let i = index;">

                                            <tr class="border-top">

                                                <!-- <td width="10"></td> -->
                                                <td style="padding-left: 10px; padding-right: 20px; font-size: 0.75rem; font-weight: 600;"
                                                    [ngStyle]="{'color':data.result === 'Unsatisfactory' ? '#c72b40' : '#7D7D7D','border-top':data.result === 'Unsatisfactory' ? '1px  solid #EFC7CD' : 'none' }">
                                                    {{i+1}} {{data.skillName}}</td>
                                                <td style="padding-left: 10px; padding-right: 20px; font-size: 0.65rem; font-weight: 500;"
                                                    class="w-1/2">

                                                    <span [innerHTML]="steps.stepName" *ngFor="let steps of data.steps">

                                                    </span>



                                                </td>
                                                <td style="padding-left: 10px; padding-right: 20px; color: #0076C1; font-weight: 500;display: grid;
                                                                    text-align: center">
                                                    <span *ngFor="let steps of data.steps">
                                                        <mat-icon
                                                            *ngIf="steps.isPassed == true &&steps.stepName ">check</mat-icon>
                                                    </span>

                                                </td>
                                            </tr>

                                        </ng-container>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!-- <table align="center" cellpadding="4" cellspacing="0" width="675" border="0"
                                                style=" color:#7D7D7D; margin-bottom: 20px;">
                                                <tbody>
                                                    <tr height="20"></tr>
                                                    <tr>
                                                        <td align="left">
                                                            <span *ngIf="this.global.personEventId.stateId !=19"
                                                                style="color: #11263cbd; font-size: 14px; line-height: 1.6; font-weight: 500;">
                                                                This Score Report confirms you DID NOT PASS the SKILLS
                                                                Evaluation.</span><br />
                                                            <span
                                                                style="color: #7d7d7d; font-size: 12px; line-height: 1.6; font-weight: 600; text-decoration: underline;">
                                                                Next Steps:</span>
                                                            <span
                                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                <span *ngIf="this.global.personEventId.stateId !=18 && this.global.personEventId.stateId !=19">
                                                                    Please refer to your current candidate handbook on the Credentia website at
                                                                </span>
                                                               
                                                                <span *ngIf="this.global.personEventId.stateId !=18 && this.global.personEventId.stateId !=19 "  style="color: #0076C1;word-break: break-word;">
                                                                    <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                                        {{nextSteps[0].url}}
                                                                    </a></span>
                                                                    <a href="https://credentia.com/test-takers/nv" target='_blank' *ngIf="this.global.personEventId.stateId ==18"  style="color: #0076C1;word-break: break-word;">
                                                                        {{nextSteps[0].url}}</a>
                                                                    
                                                                    <br />
                    
                                                                <span
                                                                    style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                    {{nextSteps[0].value}}</span><br />
                                                            </span>
                                                            <span
                                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                <span *ngIf="this.global.personEventId.stateId!=18" >  Please refer "Skills Listing" in your candidate handbook, available online
                                                                    at</span>
                                                              
                                                                <span *ngIf="this.global.personEventId.stateId!=18 && this.global.personEventId.stateId !=19"  style="color: #0076C1;">
                                                                    <a href="{{URL}}" target="_blank" style="color: #0076C1;word-break: break-word;">
                                                                        {{nextSteps[0].url}}
                                                                    </a></span>
                                                                    <a href="https://credentia.com/test-takers/nv" target='_blank' *ngIf="this.global.personEventId.stateId ==18"  style="color: #0076C1;word-break: break-word;">
                                                                        {{nextSteps[0].url}}</a>
                                                                        <a href="https://credentia.com/test-takers/ak" target='_blank' *ngIf="this.global.personEventId.stateId ==19"  style="color: #0076C1;word-break: break-word;">
                                                                            {{nextSteps[0].url}}</a>
                                                                    <br />
                    
                                                                <span
                                                                    style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                    for a description of any listed failed step(s) identified by the
                                                                    number(s) under
                                                                    the skills name
                                                                </span>
                                                                <br />
                                                                <span *ngIf="this.global.personEventId.stateId ==19"
                                                                style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                {{nextSteps[0].content}}  <a href="https://credentia.com/test-takers/ak" target='_blank'  style="color: #0076C1;word-break: break-word;">
                                                                    {{nextSteps[0].url}}</a></span><br />
                                                                    <span *ngIf="this.global.personEventId.stateId ==19"
                                                                    style="color: #7D7D7D; font-size: 12px; line-height: 1.6; font-weight: 400;word-break: break-word;">
                                                                    {{nextSteps[0].link}}:  <a href="mailto:<EMAIL>"  target='_blank'  style="color: #0076C1;word-break: break-word;">
                                                                        {{nextSteps[0].url1}}</a></span>
                    
                                                                
                                                            </span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table> -->
            </td>
        </tr>
    </tbody>
</table>


<div class="px-gutter pb-2" *ngIf="data?.actiontype == 'booklet_view' && dataSource.length > 0" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1 /-1">
    <table mat-table [dataSource]="dataSource">

        <!--- Note that these columns can be defied in any order.
      The actual rendered columns are set as a property on the row definition" -->

        <!-- Position Column -->



        <ng-container matColumnDef="CandidateId">
            <th mat-header-cell *matHeaderCellDef> Exam Name </th>
            <td mat-cell class="text-center" *matCellDef="let element"> {{element.examTitle}} </td>
        </ng-container>
        <ng-container matColumnDef="ExamCode">
            <th mat-header-cell *matHeaderCellDef> Exam Code </th>
            <td mat-cell class="text-center" *matCellDef="let element"> {{element.examCode}} </td>
        </ng-container>

        <ng-container matColumnDef="Examtype">
            <th mat-header-cell *matHeaderCellDef> Form Code </th>
            <td mat-cell class="text-center" *matCellDef="let element"> {{element.formCode}} </td>
        </ng-container>
        <ng-container matColumnDef="Quantity">
            <th mat-header-cell *matHeaderCellDef>No of Booklet</th>
            <td mat-cell class="text-center" *matCellDef="let element">
                {{element.quantity}}

            </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayColumnPayment"></tr>
        <tr mat-row *matRowDef="let row; columns: displayColumnPayment;"></tr>
    </table>
</div>

<div class="px-4 pb-2" *ngIf="data?.actiontype == 'booklet_view' && dataSource.length > 0">
    <div class="text-sm font-semibold flex justify-between pb-2 pop-up-header">
        <div class="text-center font-medium text-size px-2 mt-2">Shipping Information</div>
    </div>
    <!-- <div class="w-full flex flex-row space-x-4 py-2">
        <div class="w-full grid grid-cols-2 gap-4">
            <div class="w-full flex flex-row space-x-4 py-1">
                <p>Name </p>
                <p>{{shippingInformation.contactName}}</p>
            </div>
            <div class="w-full flex flex-row space-x-4 py-1">
                <p>Email </p>
                <p>{{shippingInformation.contactEmail}}</p>
            </div>
            <div class="w-full flex flex-row space-x-2 py-1">
                <p>Address </p>
                <p>{{shippingInformation.addressLine1}}</p>
            </div>
        </div>
        <div class="w-full ">
            <div class="w-full flex flex-row space-x-4 py-2">
                <p>Phone </p>
                <p>{{shippingInformation.contactPhoneNumber}}</p>
            </div>
            <!-- <div class="w-full flex flex-row space-x-4 py-2">
                <p>Email </p>
                <p>{{shippingInformation.contactEmail}}</p>
            </div> -->
        <!-- </div>
    </div> --> 

    <div *ngIf="shippingInformation.contactName !='' && shippingInformation.contactName !=null && shippingInformation.contactName !=undefined" class="w-full px-2 pb-2 ">
        Name : <span class="textColor">{{shippingInformation.contactName}}</span>
    </div>
    <div *ngIf="shippingInformation.contactEmail !='' && shippingInformation.contactEmail !=null && shippingInformation.contactEmail !=undefined" class="w-full px-2 pb-2">
        Email : <span class="textColor">{{shippingInformation.contactEmail}} </span>
    </div>
    <div *ngIf="shippingInformation.addressLine1 !='' && shippingInformation.addressLine1 !=null && shippingInformation.addressLine1 !=undefined" class="w-full px-2 pb-2">
        Address : <span class="textColor">{{shippingInformation.addressLine1}} {{shippingInformation.addressLine2}}</span>
    </div>
    <div *ngIf="shippingInformation.contactPhoneNumber !='' && shippingInformation.contactPhoneNumber !=null && shippingInformation.contactPhoneNumber !=undefined" class="w-full px-2 pb-2">
        Phone Number : <span class="textColor">{{shippingInformation.contactPhoneNumber}}</span>
    </div>
    <div *ngIf="shippingInformation.postalCode !='' && shippingInformation.postalCode !=null && shippingInformation.postalCode !=undefined"class="w-full px-2 pb-2">
        Zip Code : <span class="textColor">{{shippingInformation.postalCode}}</span>
    </div>



    

    

</div>


<div *ngIf='data.id == 73' class="header overflow-auto w-full space-y-6 px-6 text-justify " gdColumn="2 / -1"
    gdColumn.lt-md="2 / -1" gdColumn.lt-sm="1">
    <div>
        <h2 class="custom">
            Training Program Agreement
        </h2>
        <hr class="hrzl">
        <p class="mb-2">
            This Agreement is made and entered into as of {{Today | date: "MM/dd/yyyy":'+0000'}} by and between <strong>{{data.name}}</strong> located at <strong>{{data.address[0].addressLine1}} {{data.address[0].addressLine2}} {{data.address[0].city}} {{data.address[0].zipCode}}</strong> and <strong>Credentia Nurse Aide,
                LLC,</strong> a Texas
            Limited Liability Company, with its principal offices at 1025 Greenwood Boulevard, STE 401, Lake
            Mary, FL 32746 <strong>("Credentia").</strong> Credentia and Training Program are collectively referred to
            as the
            “Parties”
        </p>
        <p class="mb-2">
            <strong>WHEREAS,</strong> Credentia is the authorized provider of the NNAAP® examination; and
        </p>

        <p class="mb-2">
            <strong>WHEREAS,</strong> the Training Program conducts Nurse Aide certification training and seeks to use
            its own
            facility to administer the National Nurse Aide Assessment Program (NNAAP®) examination to its
            candidates under the terms and conditions set forth below.
        </p>

        <p class="mb-2">
            <strong>NOW, THEREFORE,</strong> in consideration of the mutual covenants and agreements hereinafter set
            forth,
            the parties agree as follows
        </p>
        <h2 class="custom">Definitions</h2>
        <hr class="hrzl">
        <p>These definitions apply to this Agreement:</p>
        <p class="pl-4">
            <strong>a. Agreement -</strong> This Training Program Agreement includes the Exhibits, Schedules and other
            related
            documents referred to herein.
            <br>
            <strong>b. Candidate -</strong> An individual who is currently
            undergoing training with the Training Program and
            registers to take the Test.
            <br>
            <strong>c. Training Program -</strong>The organization contracting with Credentia to use its own facilities
            for
            administering Tests to its candidates
            <br>
            <strong>d. Effective Date -</strong> The date signed by both parties and will be effective on the date the
            earliest party
            signed.
            <br>
            <strong>e. Services -</strong> Those actions and obligations to be performed by the Training Program as
            specifically
            described in this Agreement.
            <br>
            <strong>f. Test -</strong> An administered exercise, authorized by a Sponsor, designed to examine a
            Candidate’s
            progress or test qualification or knowledge.
            <br>
    </div>
    <div>
        <h2 class="custom">Test Administration Location</h2>
        <hr class="hrzl">
        <p>
            The Training Program will administer the NNAAP® examinations in its
            existing facility on its campus (the “Testing Area”).
        </p>
    </div>

    <div>
        <h2 class="custom">Fee</h2>
        <hr class="hrzl">
        <p>
            The Training Program understands that there will be no compensation for the use of its existing
            facility for administering the exams.
        </p>
    </div>
    <div>
        <h2 class="custom">Scheduling</h2>
        <hr class="hrzl">
        <p>The Training Program agrees that it will not schedule any other activities in the Testing
            Area or allow its staff to enter the Testing Area on scheduled testing days, as this may disrupt the
            NNAAP® examination process.
        </p>
    </div>

    <div>
        <h2 class="custom">Testing Environment</h2>
        <hr class="hrzl">
        <p>
            The Training Program shall ensure that the Testing Area within its facility
            meets acceptable environmental standards, including but not limited to heating and air conditioning
            as needed. The Training Program is responsible for maintaining the facility, including parking areas,
            sidewalks, hallways, restroom facilities, and the Testing Area, ensuring they are kept in good, clean,
            and safe condition. The Training Program agrees to stock the Testing Area with all required supplies
            listed in the Equipment List, which can be accessed at
            <strong>https://credentia.com/test-centers/inf</strong> and
            was previously provided during the application submission. Additionally, the Training Program
            agrees that no operational cameras will be used in areas where skills testing, or written exams are
            conducted. Any cameras present must be covered for the duration of the testing event.
        </p>

    </div>


    <div>
        <h2 class="custom">Responsibilities of the Training Program</h2>
        <hr class="hrzl">
        <p>
            <strong>a.</strong> The Training Program must inform Credentia of the exam dates in advance.
            <br>
            <strong>b.</strong>The Training Program will be solely responsible for opening its facility and the Testing
            Area on the
            scheduled test dates at a time agreed upon by both parties to facilitate the arrival and check-in
            of candidates for the NNAAP® Examination
            <br>
            <strong>c.</strong>The Training Program is responsible for securing and locking the facility and Testing
            Area upon
            the completion of testing.
            <br>
            <strong>d.</strong> The Training Program must proactively communicate any changes to its contact
            information, such
            as phone number, address, or directions to the Testing Area, to Credentia or its subcontractor.
            <br>
            <strong>e.</strong> The Training Program warrants that the facility housing the Testing Area will be fit for
            the
            purposes of this Agreement.
            <br>
            <strong>f.</strong> Any security or safety issues that impact the ability to administer the examination must
            be
            reported immediately to Credentia. Such issues may result in the cancellation of testing events
            or the termination of this Agreement.
            <br>
        </p>
    </div>

    <div>
        <h2 class="custom">Refund Policy on Exam Vouchers</h2>
        <hr class="hrzl">
        <p>
            <strong>a. Non-Refundability -</strong> Exam vouchers purchased by the Training Program through Credentia
            for its
            candidates are strictly non-refundable. Once a voucher has been purchased, Credentia is not
            responsible for issuing a refund, regardless of the circumstances.
            <br>
            <strong>b. Expiration of Vouchers -</strong>Each exam voucher has a specific validity period, as
            communicated at
            the time of purchase. Candidates must use their vouchers within this validity period. Any voucher
            that remains unused after its expiration date will be considered expired
            <br>
            <strong>c. No Refunds for Expired Vouchers -</strong>Once a voucher has expired, no refund or credit will be
            issued,
            regardless of whether the voucher was redeemed or used. The Training Program and the
            Candidates are solely responsible for scheduling and taking the exams before the voucher’s
            expiration date.
            <br>
            <strong>d. Responsibility of Candidates -</strong>Both Candidates and the Training Program are responsible
            for
            tracking the expiration dates of vouchers and scheduling exams accordingly. Credentia will not
            be liable for expired vouchers and no extensions or reissues will be provided.
            <br>
        </p>
    </div>


    <div>
        <h2 class="custom">Term:</h2>
        <hr class="hrzl">
        <p>
            The term of this Agreement shall commence on the Effective Date and shall continue in
            full force and effect until terminated in accordance with the Termination provisions set forth
            below.
        </p>
    </div>

    <div>
        <h2 class="custom">Termination</h2>
        <hr class="hrzl">
        <p>
            <strong>a. Sanctions or Loss of Accreditation -</strong> If the Training Program receives sanctions or loses
            accreditation
            or approval from the state, Credentia may immediately terminate this Agreement upon written
            notice.
            <br>
            <strong>b. Termination Without Cause -</strong>Either party may terminate this Agreement without cause by
            providing at least thirty (30) days’ prior written notice to the other party.
            <br>
            <strong>c. Termination for Cause -</strong>Credentia may immediately terminate this Agreement for any actual
            or
            threatened breach of this Agreement or for any other cause. Termination does not limit Credentia’s
            other available remedies.
            <br>
            <strong>d. Transfer or Assignment -</strong> If the Training Program subcontracts, sells or transfers its
            business or any
            part thereof, including responsibilities under this Agreement, without providing prior written
            intimation to Credentia, Credentia may temporarily suspend testing and may immediately
            terminate this Agreement
            <br>
            <strong>e. Bankruptcy or Insolvency -</strong> Either party may immediately terminate this Agreement upon
            written
            notice to the other if the other party:
            <br>
            <strong>i.</strong> Makes an assignment for the benefit of creditors;
            <br>
            <strong>ii.</strong>Is unable to pay debts as they become due;
            <br>
            <strong>iii.</strong>Files for bankruptcy;
            <br>
            <strong>iv.</strong>Is adjudicated bankrupt or insolvent;
            <br>
            <strong>v.</strong>Files for reorganization; or
            <br>
            <strong>vi.</strong>Consents to or acquiesces in the appointment of a trustee, receiver, or liquidator.
            <br>
        </p>
        <p>
            Additionally, if an involuntary reorganization proceeding is not dismissed within one month or if a
            trustee, receiver, or liquidator is appointed without the party's consent and not vacated within one
            month, the other party may terminate without notice.
        </p>
    </div>


    <div>
        <h2 class="custom">Notices:</h2>
        <hr class="hrzl">
        <p>
            Notices should be sent to the following address:
            <br><br>
            <strong>Credentia Nurse Aide, LLC</strong> <br>
            ATTN: Program Management <br>
            1025 Greenwood Boulevard, STE 401 <br>
            Lake Mary, Florida, 32746.
        </p>
    </div>

    <div>
        <h2 class="custom">Test Dates</h2>
        <hr class="hrzl">
        <p>
            The Training Program agrees to use the designated event request software to
            schedule test dates and to inform Credentia of these dates at least thirty (30) days in advance. If any
            scheduling conflicts arise, the Training Program must report them immediately and provide a valid
            rationale. If the Training Program fails to make the Testing Area available to Credentia for testing on
            any agreed-upon date, Credentia reserves the right to cancel this Agreement immediately upon
            written notice to the Training Program. The Training Program must provide a room that can
            accommodate at least ten (10) candidates per test date. Any changes to the room configuration must
            also be reported to Credentia.
            <br> <br>
            Below is the expected schedule for a testing event:
            <br><br>
            Training Program will be open for registration at: 7:00 am <br>
            Written test will begin at: 8:00 am <br>
            Skills test will begin at: 10:00 am <br>
            Facility will close at: 5:00 pm
        </p>
    </div>

    <div>
        <h2 class="custom">Evaluators</h2>
        <hr class="hrzl">
        <p>
            The Training Program understands that Credentia, or its subcontractor, will rotate
            and assign qualified evaluators to evaluate test events. The Training Program is not permitted to
            request specific evaluators or exclude particular evaluators from being assigned. Refusal to accept the
            assigned qualified evaluators may result in termination of the Agreement. Upon the expiration or
            termination of the Agreement by either party, Credentia will remove all its property and equipment(s)
            from the Training Program’s facility.
        </p>
    </div>

    <div>
        <h2 class="custom">Indemnifications</h2>
        <hr class="hrzl">
        <p>
            Credentia agrees to defend, indemnify and hold harmless the Training
            Program against any and all losses, costs, damages (including reasonable attorneys' fees), or claims
            arising from injury or death to any person or damage to property, to the extent that such losses,
            damages, or claims are caused by Credentia's negligence, willful misconduct, or breach of this
            Agreement. This obligation includes the actions of Credentia's employees, agents, and contractors.
            The Training Program shall be solely responsible for any losses, damages or claims resulting from its
            own negligence.
        </p>
    </div>

    <div>
        <h2 class="custom">Confidentiality</h2>
        <hr class="hrzl">
        <p>
            The Training Program acknowledges the critical importance of maintaining
            the confidentiality and security of information related to Tests, Software, manuals and the identity
            and background of the Candidates. The Training Program agrees to exercise reasonable care to protect
            such confidential information and prevent unauthorized use or disclosure. This includes information
            explicitly designated as confidential, as well as information reasonably understood to be confidential
            due to its nature or context. Confidential information also includes all proprietary information derived
            from this Agreement and any software provided under this Agreement. The Training Program agrees
            not to use or disclose confidential information for any purpose outside the scope of this Agreement.
            <br> <br>
            Both parties retain the right to use each other’s confidential information solely under the terms of this
            Agreement, without acquiring ownership or title. Such information must not be used for any purpose
            beyond the Agreement’s scope. The confidentiality obligations in this Section are perpetual and will
            survive the termination of this Agreement.
            <br><br>
            If legally compelled to disclose confidential information, the Training Program will promptly notify
            Credentia to allow for protective measures. In the event of compelled disclosure of confidential
            information, the Training Program will notify Credentia and cooperate in efforts to avoid or limit such
            disclosure.
            <br><br>
            The Training Program acknowledges that unauthorized use or disclosure of confidential information
            could cause irreparable harm to Credentia and waives any objections to injunctive relief sought by
            Credentia to prevent such misuse.
        </p>
    </div>

    <div>
        <h2 class="custom"> Independent Parties</h2>
        <hr class="hrzl">
        <p>
            Nothing contained in this Agreement or in the Parties’ performance thereof shall
            be construed to constitute the Parties as partners, joint venturers, co-owners or otherwise as
            participants in a joint or common undertaking.
        </p>
    </div>

    <div>
        <h2 class="custom">Compliance with Applicable Laws</h2>
        <hr class="hrzl">
        <p>
            The Training Program shall, at its own expense, comply with all
            applicable laws and make, obtain and maintain in force at all times during the term of this Agreement
            all registrations, licenses, permits and authorizations required under applicable law, regulation, or
            order necessary for the Training Program to perform its obligations under this Agreement.
        </p>
    </div>

    <div>
        <h2 class="custom">Governing Law; Legal Actions</h2>
        <hr class="hrzl">
        <p>
            This Agreement shall be governed by and construed in accordance with
            the laws of the State of Florida, without regard to its conflict of laws provisions. The sole jurisdiction
            and venue for any action or proceeding arising out of or relating to this Agreement shall be the state
            and federal courts located in the State of Florida. The Parties hereby irrevocably consent to the
            exclusive jurisdiction and venue of such courts and agree that process may be served in the manner
            provided herein for giving of notices or otherwise as allowed by Florida law. The prevailing Party in
            any action or proceeding to enforce rights under this Agreement shall be entitled to recover costs and
            attorneys' fees.
            
        </p>
    </div>

    <!-- <div [formGroup]="Form" fxLayout="row" class="">
        <mat-form-field appearance="outline" class="w-full ">
            <mat-label>Signature</mat-label>
            <input type="text" formControlName="sign" matInput>
        </mat-form-field>
        <mat-form-field class="w-full ml-2" appearance="outline"  >
            <mat-label class="text-xs fontColor2"> Date</mat-label>
            <input matInput readonly  class="text-xs"  [matDatepicker]="picker" formControlName="date"   />
            <mat-datepicker-toggle matSuffix [for]="picker" >
            </mat-datepicker-toggle>
            <mat-datepicker #picker ></mat-datepicker>
        </mat-form-field>
    </div> -->
    
    <div class="flex flex-row mb-4">
        <input type="checkbox" name="agreement" id="" (change)="consentCheck()" color="primary" class="h-5">
        <span class="font-weight-bold signatureConsentText ml-1">
            I acknowledge and agree to the policies and terms every time I make a voucher purchase.
        </span>
      </div>

    <div  fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px" class="py-3">
        <button mat-button class="buuton1 h-8 add text-xs btn-7" type="button" mat-dialog-close>Cancel</button>
            <button mat-flat-button color="primary" [disabled]="!isConsentCheckbox"
                class="loginBtn" (click)="Save()" 
             >Submit</button>
    </div>
     
</div>
