

.display-1{
  font-size: 1.5rem !important;
}

.height{
  height: 450px !important;
}

.textValue{
 font-size: 20px !important;
 color: #0076C1;
}


.eligibility1 {
  @screen xl {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 6.5rem
    );
  }
  @screen lg {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 6.5rem
    );
  }
  @screen md {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 6.5rem
    );
  }
  @screen sm {
    height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 6.5rem
    );
  }
  @screen xs {
      height: calc(
      100vh - var(--toolbar-height) - var(--navigation-height) -
        var(--footer-height) - 3.5rem
    );
  }
}

.spinner {

}
.spinner > div {
  width: 18px;
  height: 18px;
  background-color: var(--text-color2);
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}
.spinner .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.spinner .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
@-webkit-keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
  }
}
@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

::ng-deep .success-snackbar{
  height: 74px !important;
  margin-top: 33em !important;
  margin-left: 42% !important;
  animation: fadeOut 5s forwards;
}

.loader {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 100;
  justify-content: center;
  display: flex;
}
::ng-deep svg {
  width: 48px !important;

}