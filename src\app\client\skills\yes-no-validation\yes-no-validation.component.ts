import { Component, EventEmitter, forwardRef, Input, OnInit, Output } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

const noop = () => {
};

export const CUSTOM_INPUT_CONTROL_VALUE_ACCESSOR: any = {
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => YesNoValidationComponent),
    multi: true
};

@Component({
  selector: 'app-yes-no-validation',
  templateUrl: './yes-no-validation.component.html',
  styleUrls: ['./yes-no-validation.component.scss'],
  providers: [CUSTOM_INPUT_CONTROL_VALUE_ACCESSOR]
})
export class YesNoValidationComponent implements OnInit {
    @Input() disabled = false;
    @Output() onChange = new EventEmitter<any>();

    //The internal data model
    private innerValue: any = '';
  
    //Placeholders for the callbacks which are later provided
    //by the Control Value Accessor
    private onTouchedCallback: () => void = noop;
    private onChangeCallback: (_: any) => void = noop;
  
    //get accessor
    get value(): any {
        return this.innerValue;
    };
  
    //set accessor including call the onchange callback
    set value(v: any) {
        if (v !== this.innerValue) {
            this.innerValue = v;
            this.onChangeCallback(v);
        }
    }
  
    //Set touched on blur
    onBlur() {
        this.onTouchedCallback();
    }
  
    //From ControlValueAccessor interface
    writeValue(value: any) {
        if (value !== this.innerValue) {
            this.innerValue = value;
        }
    }
  
    //From ControlValueAccessor interface
    registerOnChange(fn: any) {
        this.onChangeCallback = fn;
    }
  
    //From ControlValueAccessor interface
    registerOnTouched(fn: any) {
        this.onTouchedCallback = fn;
    }
  
    
    
    constructor() { }
  
    ngOnInit(): void {
    }
  
    changefn(): void { 
        this.onChangeCallback(this.value);
        this.onChange.emit(this.value); 
    }

}
