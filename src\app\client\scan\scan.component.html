<div
  *ngIf="
    this.globalUserService.userDetails.value.roleId == 22 ||
    this.globalUserService.userDetails.value.roleId == 4
  "
>
<ng-template #loading>
  <ng-container *ngIf="loadingObs | async">
      <div class="spinner">
          <mat-progress-spinner class="calenderSpinner flex justify-center " color="primary"
          diameter="60" mode="indeterminate" >
          </mat-progress-spinner>
      </div>
  </ng-container>
</ng-template>

  <ng-container *ngTemplateOutlet="loading"></ng-container>
</div>

<div
  *ngIf="
    this.globalUserService.userDetails.value.roleId == 22 ||
    this.globalUserService.userDetails.value.roleId == 4
  "
  class="mt-6"
  gdColumns="1fr 1fr"
  gdColumns.lt-md="1fr 1fr"
  gdColumns.lt-sm="1fr 1fr"
  gdGap="16px"
  exaiContainer
>
  <mat-sidenav
    *ngIf="listExam.length > 0"
    #sidenav
    mode="over"
    class="temporary-sidenav-class cardBorder"
    fixedBottomGap="0"
    fixedTopGap="0"
    fixedInViewport="true"
    position="end"
  >
    <ng-container
      [ngTemplateOutlet]="notesSideBar"
      [ngTemplateOutletContext]="{
        viewId: { viewId: notesSidebarViewId, personId: candidateDetails }
      }"
    >
    </ng-container>
  </mat-sidenav>

  <ng-template #notesSideBar let-viewId="viewId">
    <exai-notes-sidebar [viewId]="viewId" [sidenavRef]="sidenavRef">
    </exai-notes-sidebar>
  </ng-template>
</div>

<div
  *ngIf="
    this.globalUserService.userDetails.value.roleId == 15 ||
    this.globalUserService.userDetails.value.roleId == 8 ||
    this.globalUserService.userDetails.value.roleId == 17
  "
  gdColumns="1fr 1fr"
  gdColumns.lt-md="1fr 1fr"
  gdColumns.lt-sm="1fr 1fr"
  gdGap="16px"
  exaiContainer
>
  <div
    *ngIf="this.globalUserService.userDetails.value.roleId != 17"
    class="text-sm"
    gdColumn="1/ 2"
    gdColumn.lt-md="1 / 2"
    gdColumn.lt-sm="1 /-1"
  >
    <b>Roster</b>
    <app-ng-dynamic-breadcrumb
      [lastLinkColor]="'var(--text-color2)'"
      [fontColor]="'#A7A8AC'"
      [fontSize]="'0.65rem'"
    >
    </app-ng-dynamic-breadcrumb>
  </div>

  <mat-sidenav
    *ngIf="listExam.length > 0"
    #sidenav
    mode="over"
    class="temporary-sidenav-class cardBorder"
    fixedBottomGap="0"
    fixedTopGap="0"
    fixedInViewport="true"
    position="end"
  >
    <ng-container
      [ngTemplateOutlet]="notesSideBar"
      [ngTemplateOutletContext]="{
        viewId: { viewId: notesSidebarViewId, personId: candidateDetails }
      }"
    >
    </ng-container>
  </mat-sidenav>

  <ng-template #notesSideBar let-viewId="viewId">
    <exai-notes-sidebar [viewId]="viewId" [sidenavRef]="sidenavRef">
    </exai-notes-sidebar>
  </ng-template>
</div>


<div *ngIf="(this.globalUserService.userDetails.value.roleId == 22  || this.globalUserService.userDetails.value.roleId == 4 )" class="mt-6" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px" exaiContainer >
    <mat-sidenav *ngIf="listExam.length > 0" #sidenav mode="over" class="temporary-sidenav-class cardBorder z-index" fixedBottomGap="0" fixedTopGap="0" fixedInViewport="true" position='end'>
        <ng-container [ngTemplateOutlet]="notesSideBar" [ngTemplateOutletContext]="{viewId:{viewId:notesSidebarViewId,personId:candidateDetails}}">
        </ng-container>
    </mat-sidenav>

    <ng-template #notesSideBar let-viewId="viewId">
        <exai-notes-sidebar [viewId]="viewId" [sidenavRef]="sidenavRef"  >
        </exai-notes-sidebar>
    </ng-template>
 

</div>

<div *ngIf="(this.globalUserService.userDetails.value.roleId == 15  || this.globalUserService.userDetails.value.roleId == 8 || this.globalUserService.userDetails.value.roleId == 17  )" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="16px" exaiContainer >

    <div *ngIf="this.globalUserService.userDetails.value.roleId != 17"  class="text-sm" gdColumn="1/ 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 /-1">
        <b>Roster</b>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
    </div>

    <mat-sidenav *ngIf="listExam.length > 0" #sidenav mode="over" class="temporary-sidenav-class cardBorder" fixedBottomGap="0" fixedTopGap="0" fixedInViewport="true" position='end'>
        <ng-container [ngTemplateOutlet]="notesSideBar" [ngTemplateOutletContext]="{viewId:{viewId:notesSidebarViewId,personId:candidateDetails}}">
        </ng-container>
    </mat-sidenav>

    <ng-template #notesSideBar let-viewId="viewId">
        <exai-notes-sidebar [viewId]="viewId" [sidenavRef]="sidenavRef"  >
        </exai-notes-sidebar>
    </ng-template>
 

</div>

<div class="t-xs overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer>
    <form [formGroup]="stateControl">
        <div class="justify-end"   gdColumn="1/-1 " gdColumn.lt-md="1/-1" gdColumn.lt-sm="1/-1">
       
            <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" fxLayoutAlign="end center" class=" exam naetester " fxLayoutGap.lt-sm="0">
                <!-- <button  *ngIf="this.globalUserService.userDetails.value.roleId == 22 && ShowSelectEventTypeDropdown && listExam.length > 0"
                   mat-raised-button class="ml-4 buuton1 Completed  " type="button" (click)="Absent('Complete','')"
                >
                 Event Complete
            </button> -->
                <!-- TIME ZONE PICKER -->
                <!-- <mat-form-field class="pt-2" appearance="outline" fxFlex="auto"  *ngIf="ShowSelectEventTypeDropdown && this.globalUserService.userDetails.value.roleId !=15">
                    <mat-label class="text-xs  fontColor2">Select Training Program</mat-label>
                    <mat-select [formControl]="TrainingControl" placeholder="Select Training Program"
                    class=" border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                 
                    <mat-option *ngFor="let training of TrainingListId "  [value]=training>
                        {{training.name}}
                    </mat-option>
                </mat-select>
                </mat-form-field> -->

                <!-- <mat-form-field class="pt-2 " appearance="outline" fxFlex="50%" *ngIf="ShowSelectEventTypeDropdown && this.globalUserService.userDetails.value.roleId !=15">
                    <mat-label class="text-xs fontColor2">Select Your  Date Range</mat-label>
                    <mat-date-range-input  [rangePicker]="picker">
                      <input class="text-xs" matStartDate formControlName="start" autocomplete="off" required
                        placeholder="Start date">
                      <input class="text-xs" matEndDate formControlName="end" autocomplete="off" required
                        placeholder="End date">
                    </mat-date-range-input>
                    <mat-datepicker-toggle matSuffix [for]="picker" ></mat-datepicker-toggle>
                    <mat-date-range-picker #picker></mat-date-range-picker>
                </mat-form-field> -->
                <!-- <button  *ngIf = "showAuditForm"
                    mat-raised-button class="ml-4 buuton1 Completed  " type="button" (click)="OpenAuditForm()"
                 >
                  Audit Form
             </button> -->
                <!-- <mat-form-field appearance="outline">
                    <mat-label>Selected Date</mat-label>
                    <input clas="w-full" matInput [matDatepicker]="picker1" [(ngModel)]="selectedDate" (dateChange)="onDateChange($event)" name="selectedDate" attr.data-cre-type="select_Date">
                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                    <mat-datepicker #picker1></mat-datepicker>
                </mat-form-field> -->

 
      </div>
    </div>
   
  </form>
</div>

<div class="px-gutter pt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>

<div *ngIf="testCenterName && listExam.length != 0 " style="color: #7d7d7d;font-size: 12px;">
    Test Center Name:
    <span style="color: black;font-size: 12px; font-weight: 700;"
            class="ml-1">{{ testCenterName }} 
    </span>
</div>
        <div class="overflow-y-auto overflow-x-hidden">
<div class="mb-1 mt-6 content1 hh " exaiContainer>
    <div class="" fxLayoutGap="6px grid">
       
        <div fxLayout="row wrap" fxLayoutGap="7px grid">
            <div [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%" *ngFor="let item of listExam">
  <div class="card shadow-none cardBorder h-full" fxFlex="auto">
                    <div class="bg-color px-4 py-3">
                        <div class="flex justify-between title-hed " fxLayout="row">
                            <!-- <div class="t-xs title-hed"> -->
                            <ng-container *ngFor="let items of item.exams">
                                <strong> {{ items.examName }}</strong>
                            </ng-container>

                            <div class="flex text-left text-base pb-2">
                                <button (click)="openSidenavAndSetViewId({whatToOpen:7},item)">
                                    <mat-icon  matTooltip="Add Notes" class="-mr-3 ml-2 delete" 
                                    >notes
                                </mat-icon>
                                </button>
                               
                            </div>

                            <!-- </div> -->
                        </div>

                  
                        <div class="flex justify-between -mt-3 text_centers" fxLayout="row">
                            <!-- <div class="t-xs title-hed"> -->
                            <ng-container *ngFor="let items of item.exams">
                                <span class=" pt-1 mb-2">  {{ item.firstName }} {{item.middleName}} {{ item.lastName }}</span>

                            </ng-container>


                            <!-- </div> -->
                        </div>
                        
                    

                       
                    </div>
                   
                    <div fxLayout="column" class="px-4 -mt-5">
                        <h4 class="px-4 status t-xs pt-2"></h4>
                        <div class="small-container  pb-2" fxFlexFill>
                            <span class="fontStyle state-elig pb-3 pt-1">{{ item.scheduleDateTime }}</span>
                            <ng-container *ngFor="let items of item.exams">
                                <div *ngIf=" item.appointmentIdentifier !=null && AllowExamsAppointId.includes(items.examName)" class=" mb-1"> <strong>Appointment Id :</strong> <span class="headertest">{{item.appointmentIdentifier}}</span></div>
                                <div  *ngIf="item.examFormId !=null && Allowonlyskills.includes(items.examName)" class=" mb-1"> <strong>Exam Form Id :</strong> <span class="headertest">{{item.examFormId}}</span></div>
                                <ng-container *ngFor="let incidentitems of IncidentArray">
                                    <div  *ngIf="item.eventStatusId === incidentitems.id" class=" mb-1"> <strong>Incidient :</strong> <span class="headertest">{{incidentitems.value}}</span></div>

                                </ng-container>
                            </ng-container>
                            <div *ngIf="item.bookletId !=null" class=" mb-1"> <strong>Booklet Id :</strong>
                                <span class="headertest">{{item.bookletId}}</span>
                                <div>
                                    <mat-icon matTooltip="Download" class="-mr-3 ml-2 delete" (click)="
                                    getBooklet(item)
                                  ">download
                                    </mat-icon>
                                </div>

                            </div>
                          <div *ngIf="item.taoPassword !=null && item.isOnboardingSuccessfull == true"  class=" mb-1"> <strong>Password :</strong> <span class="headertest">{{item.taoPassword}}</span></div>
                          <div
                          attr.data-cre-type="select_accom_details"
                          *ngIf="item.accomodationType != null"
                          class="mb-1 cursor-pointer textenlarger enlargeContent"
                          
                          contentType="template"
                        >
                          <strong>Accomodation Type :</strong>
                          <div class="headertest  cursor-pointer" (click)="openQACResponse(item)">{{ item.accomodationType }}</div>
                        </div>                               
                            <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr " gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                                exaiContainer>
                                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                    <ng-container *ngFor="let items of item.exams">
                                        <div *ngIf="item.omrSheet !=null && item.personScoreSuccesfully !=false &&  AllowExamsAppointId.includes(items.examName)" class="h4 status text1 t-xs">Answer sheet</div>
                                        <div *ngIf="item.omrSheet !=null && item.personScoreSuccesfully ==false &&  AllowExamsAppointId.includes(items.examName)" class="h4 status text1 t-xs">Answer sheet</div>
                                    </ng-container>

                                </div>
                               
                                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                  <div
                                  *ngIf="item.personScoreSuccesfully != false && item.eventStatusId == 8"
                                  class="h4 status text1 t-xs"
                                >
                                  PersonScore
                                </div>
                                <div
                                *ngIf="item.personScoreSuccesfully != false && item.eventStatusId == 1 && item.omrSheet != null"
                                class="h4 status text1 t-xs"
                              >
                                PersonScore
                              </div>
                                </div>
                                 <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                    <div *ngIf="item.sessionId !=null" class=" h4 status text1 t-xs ">Shred</div>
                                </div>
                            </div>
                            <div class="" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr " gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr"
                                exaiContainer>
                                <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                    <ng-container *ngFor="let items of item.exams">
                                        <div *ngIf="item.omrSheet !=null && item.personScoreSuccesfully !=false && AllowExamsAppointId.includes(items.examName)" class="h4 status1 t-xs">
                                            <mat-icon class="text1">check</mat-icon>
                                        </div>
                                        <div *ngIf="item.omrSheet !=null && item.personScoreSuccesfully ==false && AllowExamsAppointId.includes(items.examName)" class="h4  t-xs mt-1 ">
                                           <strong>Waiting for score</strong> 
                                        </div>
                                    </ng-container>
                                  
                                </div>
                                <!-- <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5" class="-mt-5">
                                     <div *ngIf="item.grievance !=null" class="status1 t-xs  text1">
                                        <mat-icon>check</mat-icon>
                                    </div>
                                </div> -->
                                <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5" class="">
                                  <div
                                  *ngIf="item.personScoreSuccesfully != false &&  item.eventStatusId == 8"
                                  class="h4 status1 t-xs text1 mt-1"
                                >
                                  <mat-icon>check</mat-icon>
                                </div>
                                <ng-container *ngFor="let items of item.exams">
                                <div
                                *ngIf="item.omrSheet != null && item.personScoreSuccesfully != false &&  item.eventStatusId == 1 && AllowExamsAppointId.includes(items.examName)"
                                class="h4 status2 t-xs  mt-1"
                              >
                                In progress
                              </div>
                              </ng-container>
                                </div>
                                <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                    <div  *ngIf="item.sessionId !=null" class=" h4 status text1 t-xs mt-1 "> <mat-icon>check</mat-icon></div>
                                    
                                    
                                </div>
                            </div>
                            

                            <div  fxLayout="row" fxLayoutAlign="end center" class="grid justify-end mb-3" fxLayoutGap="8px">
                                <ng-container *ngFor="let items of item.exams">

                                    <button *ngIf="item.personScoreSuccesfully ==false && item.confirmToArkiv != false && item.bookletId !=null " mat-raised-button type="button" class="buuton1 mr-4" 
                                    (click)="Omrstatus(item,'omr')"
                                   >
                                  Answer Sheet
                               </button>
                               <button attr.data-cre-type="mark_issue"  *ngIf="AllowExamsAppointId.includes(items.examName) && item.personScoreSuccesfully ==false && item.confirmToArkiv != false && item.bookletId !=null  && !notallowStatusId.includes(item.eventStatusId)" mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="Absent(items.examName,item)"
                               >
                                 Mark Issue
                           </button>
                           <button attr.data-cre-type="mark_issue" *ngIf="Allowonlyorals.includes(items.examName) && item.isOnboardingSuccessfull == true  && item.personScoreSuccesfully ==false && !notallowStatusId.includes(item.eventStatusId)" mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="Absent(items.examName,item)"
                           >
                             Mark Issue
                       </button>
                                    <button *ngIf="item.bookletId !=null && item.confirmToArkiv == false && item.isOnboardingSuccessfull == true" mat-raised-button type="button" class="buuton1 mr-4" 
                                    (click)="confirm(item,7)"
                                   >
                                  Cancel
                               </button>
                                    <button *ngIf="item.bookletId !=null && item.confirmToArkiv == false && item.isOnboardingSuccessfull == true" mat-raised-button type="button" class="buuton1 mr-4" 
                                         (click)="confirm(item,2)"
                                        >
                                       Confirm
                                    </button>
                                 
                                <app-video-stream *ngIf="item.personScoreSuccesfully != false && item.eventStatusId == 8 && !Allowonlyskills.includes(items.examName) && !hideShredButtonforparticularstate.includes(item.state) "  [StartStopRecording]="StartStopRecording" [BookletId]="item" (EventType)="SelectedEventType($event)"  ></app-video-stream>
                                <!-- <app-video [StartStopRecording]="StartStopRecording"></app-video> -->

                             
                                </ng-container>
                            </div>

                            <div  fxLayout="row" fxLayoutAlign="end center" class="flex justify-end mb-3" fxLayoutGap="8px">
                                <ng-container *ngFor="let items of item.exams">
                                    <button attr.data-cre-type="mark_issue_after"  *ngIf="AllowExamsAppointId.includes(items.examName) && item.bookletId ==null && item.candidateStatus !='A'  && item.isOnboardingSuccessfull == false && !notallowStatusId.includes(item.eventStatusId) && !ShowOnboardforParticularstate.includes(item.state)" mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="Absent(items.examName,item)"
                                    >
                                      Mark Issue
                                </button>
                                <button attr.data-cre-type="mark_issue_after"  *ngIf="AllowExamsAppointId.includes(items.examName) && item.bookletId !=null && item.candidateStatus !='A' && item.isOnboardingSuccessfull == false  && !notallowStatusId.includes(item.eventStatusId) && ShowOnboardforParticularstate.includes(item.state)" mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="Absent(items.examName,item)"
                                >
                                  Mark Issue
                            </button>

                                
                                <button attr.data-cre-type="mark_issue_after"   *ngIf="AllowOralskills.includes(items.examName)  && item.candidateStatus !='A' && item.isOnboardingSuccessfull == false  && !notallowStatusId.includes(item.eventStatusId)" mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="Absent(items.examName,item)"
                                >
                                  Mark Issue
                            </button>

                            <button
                            attr.data-cre-type="mark_issue_after"
                            *ngIf="Allowonlyskills.includes(items.examName) &&
                              item.candidateStatus != 'A' &&
                              item.eventStatusId == 8"
                            mat-raised-button
                            class="mr-5 buuton1 float-right"
                            type="button"
                            (click)="Absent(items.examName, item)"
                          >
                            Mark Issue
                          </button>


                            
                                <button *ngIf="Allowonlyskills.includes(items.examName) && item.allowReviewer &&item.examModeId =='1' "   mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="reviwer(item,items)"
                                >
                                  Reviewer
                            </button>
                            <button attr.data-cre-type="status_exam"  *ngIf="Allowonlyskills.includes(items.examName) && item.isOnboardingSuccessfull == true && item.examModeId =='2'"   mat-raised-button class="mr-5 buuton1 float-right " type="button" (click)="reviwer(item,items)"
                            >
                              {{items.skillExamStatus ==10?"Start Proctoring":items.skillExamStatus ==4?"Resume Proctoring":items.skillExamStatus ==5?'Update Notes' :items.skillExamStatus ==16?'Update Notes':''}}
                        </button>
<button attr.data-cre-type="onboard" mat-raised-button type="button" class="buuton1 mr-4"
                                        *ngIf="AllowExamsAppointId.includes(items.examName) && item.bookletId ==null && item.candidateStatus !='A' && item.allowonboard && !notallowStatusId.includes(item.eventStatusId) && !ShowOnboardforParticularstate.includes(item.state) "
                                        (click)="Booklet(item,1)">
                                        Onboard
                                    </button>
                                    <button attr.data-cre-type="onboard" mat-raised-button type="button" class="buuton1 mr-4"
                                        *ngIf="AllowExamsAppointId.includes(items.examName) && item.bookletId !=null && item.candidateStatus !='A' &&  item.isOnboardingSuccessfull == false && item.allowonboard && !notallowStatusId.includes(item.eventStatusId) && ShowOnboardforParticularstate.includes(item.state)"
                                        (click)="Booklet(item,1)">
                                        Onboard
                                    </button>

                                    <button attr.data-cre-type="onboard" mat-raised-button type="button" class="buuton1 mr-4"
                                    *ngIf="AllowOralskills.includes(items.examName) && item.isOnboardingSuccessfull == false && item.candidateStatus !='A' && item.allowonboard && item.examModeId =='2' && !notallowStatusId.includes(item.eventStatusId)"
                                    (click)="Booklet(item,2)">
                                    Onboard
                                </button>

                   


                        




                                </ng-container>


                                <!-- <button mat-raised-button class="mr-5 buuton1" type="button" (click)="Grievance(item)" *ngIf="item.grievance ==null">
                                    Grievance
                                </button> -->

                            </div>
                           
                            <div  fxLayout="row" fxLayoutAlign="end center" class="grid justify-end mb-3" fxLayoutGap="8px">
                                <ng-container *ngFor="let items of item.exams">
                                <button mat-raised-button type="button" class="buuton1 mr-4"
                                *ngIf="Allowonlyskills.includes(items.examName) && item.isOnboardingSuccessfull == true && item.candidateStatus !='A' && item.allowonboard && items.skillExamStatus ==10 "
                                (click)="Booklet(item,3)">
                               <mat-icon class="cancelonboard">close</mat-icon>Onboard
                            </button>
                            <button mat-raised-button type="button" class="buuton1 mr-4"
                            *ngIf="AllowExamsAppointId.includes(items.examName) && item.isOnboardingSuccessfull == true && item.candidateStatus !='A' && item.allowonboard && item.confirmToArkiv == false"
                            (click)="Booklet(item,3)">
                            <mat-icon class="cancelonboard">close</mat-icon>Onboard
                        </button>
                        <button mat-raised-button type="button" class="buuton1 mr-4"
                        *ngIf="Allowonlyorals.includes(items.examName) && item.isOnboardingSuccessfull == true && item.candidateStatus !='A' && item.allowonboard && item.taoPassword !=null"
                        (click)="Booklet(item,3)">
                        <mat-icon class="cancelonboard">close</mat-icon>Onboard
                    </button>
                    </ng-container>
                            </div>
                        </div>
                    </div>
                </div>
               
            </div>
        </div>
    </div>
</div>
</div>


<div class="shadow-none card justify-start touch-auto overflow-auto dashboard registerCard -mt-5" gdColumn="2/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1" *ngIf="listExam.length == 0 ">
    <!-- <div *ngIf="(this.globalUserService.userDetails.value.roleId != 17 && this.globalUserService.userDetails.value.roleId != 8 && this.globalUserService.userDetails.value.roleId != 15 )" class="card shadow-none cardBorder" fxFlex="auto">
        <div class="flex justify-center pt-4 h-full touch-auto overflow-auto" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr" gdRows="1fr 1fr 1fr 1fr" gdRows.lt-md="1fr 1fr 1fr" gdRows.lt-sm="1fr 1fr 1fr">
            <div class="flex item-center" fxLayoutAlign="end center" gdColumn="4 / 6" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="2 / 3" gdRow.lt-md=" 2 / 3" gdRow.lt-sm="2 / 3" fxLayout="column">
                <div class="" fxLayout="column">
                    <img src="assets/img/no_exam_icon.svg" alt="" />
                </div>
            </div>
            <div class="flex item-center" gdColumn="3 / 7" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                <div class="p-4" fxLayout="column">
                    <div class="text-center text-xs empty-eligible slots2">
                        Select Event Type
                    </div>
                </div>
            </div>
            <div class="flex" gdColumn="3 / 7" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                <div class="p-4" fxLayout="column">
                </div>
            </div>
        </div>
    </div> -->

    <!-- <div *ngIf="(this.globalUserService.userDetails.value.roleId == 17 ||  this.globalUserService.userDetails.value.roleId == 8 || this.globalUserService.userDetails.value.roleId == 15  )" class="card shadow-none " fxFlex="auto">
        <div class="flex justify-center pt-4 h-full touch-auto overflow-auto" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr" gdRows="1fr 1fr 1fr 1fr" gdRows.lt-md="1fr 1fr 1fr" gdRows.lt-sm="1fr 1fr 1fr">
            <div class="flex item-center" fxLayoutAlign="end center" gdColumn="4 / 6" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="2 / 3" gdRow.lt-md=" 2 / 3" gdRow.lt-sm="2 / 3" fxLayout="column">
                <div class="" fxLayout="column">
                    <img src="assets/img/no_exam_icon.svg" alt="" />
                </div>
            </div>
            <div class="flex item-center" gdColumn="3 / 7" gdColumn.lt-md="2 / 4" gdColumn.lt-sm="2 / 4" gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                <div class="p-4" fxLayout="column">
                    <div class="text-center text-xs empty-eligible slots2">
                        Select Event Type
                    </div>
                </div>
            </div>
        </div>
    </div> -->
</div>


<div class="imageh" *ngIf="listExam.length == 0 ">
    <div *ngIf="(this.globalUserService.userDetails.value.roleId != 17 && this.globalUserService.userDetails.value.roleId != 8 && this.globalUserService.userDetails.value.roleId != 15 )">
        <div>
        <img src="assets/img/no_exam_icon.svg" alt="" />
    </div>
    </div>

    <div *ngIf="(this.globalUserService.userDetails.value.roleId == 17 || this.globalUserService.userDetails.value.roleId == 8 || this.globalUserService.userDetails.value.roleId == 15 )">
        <img src="assets/img/no_exam_icon.svg" alt="" />
    </div>
</div>

