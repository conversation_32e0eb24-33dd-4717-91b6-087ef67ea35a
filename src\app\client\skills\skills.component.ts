
import { Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Observable } from 'rxjs';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types'
import { get_userDetails } from '../state/shared/shared.selectors';
import { NgDynamicBreadcrumbService } from 'ng-dynamic-breadcrumb';
import { VoucherService } from '../voucher/voucher.service';
import { VoucherState } from '../voucher/state/voucher.state';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { VoucherDetails, candidateCompleteList, getExamDetailtoDownload, getStateId, setStateIds } from '../voucher/state/voucher.action';
import { Roles } from '../dynamic-component-loader/dynamic-component-loader.types';
import { StateLists } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { gotTrainingInstStateID, listOFVoucher, selectorStateId } from '../voucher/state/voucher.selector';
import { recentLinkClickEventSelector } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { HttpService } from 'src/app/core/http.service';
import { HttpErrorResponse } from '@angular/common/http';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';


import { PopUpComponent as popupcomponent } from "../application/pop-up/pop-up.component";
import { LanguageService } from 'src/app/core/language.service';
import { OMRVideoImage } from 'src/app/core/common-component/pop-up/omr-image-video/omr-image-video.component';
@Component({
  selector: 'app-voucher-list',
  templateUrl: './skills.component.html',
  styleUrls: ['./skills.component.scss']
})
export class SkillsComponent implements OnInit {

  clienteventid:string
  tableOptions = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false, false, false, false, false],
    showMatTabs: false,
    matTabs: [],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
  }

  displayedColumns = [[
    <tableTypes.column>{
      visible: true,
      id: "firstName",
      name: "First Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [""],
    },
    <tableTypes.column>{
      visible: true,
      id: "lastName",
      name: "Last Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },


    <tableTypes.column>{
      visible: true,
      id: "scheduleDateTime",
      name: "Exam Date",
      cellType: "date",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },

    <tableTypes.column>{
      visible: true,
      id: "examModeId",
      name: "Exam type",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "examName",
      name: "Exam Name",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "eventId",
      name: "Event Id",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "appointmentIdentifier",
      name: "Appointment Id",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "bookletId",
      name: "Booklet number",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "action",
      name: "Action",
      cellType: "action",
      headerType: "normal",
      headerAlign: "center",
     
      actions: [
        <tableTypes.action>{
          type: tableTypes.actionTypes.Custom,
          icon: 'downpicture_as_pdfload',
          tooltip: 'Pdf',
          color: '#7d7d7d',
          visibilityRule:[{
            operator: tableTypes.Operators.AND,
            elementProperty: 'personScoreSuccesfully',
            compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
            shouldHaveValue: false,
          }],
        },
        <tableTypes.action>{
          type: tableTypes.actionTypes.Custom,
          icon: 'image',
          tooltip: 'Image',
          color: '#7d7d7d',
          visibilityRule:[{
            operator: tableTypes.Operators.AND,
            elementProperty: 'personScoreSuccesfully',
            compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
            shouldHaveValue: false,
          }],
        },
        <tableTypes.action>{
          type: tableTypes.actionTypes.Custom,
          icon: 'featured_video',
          tooltip: 'Shed Video',
          color: '#7d7d7d',
          visibilityRule:[{
            operator: tableTypes.Operators.AND,
            elementProperty: 'personScoreSuccesfully',
            compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
            shouldHaveValue: false,
          },
          {
            operator:tableTypes.Operators.AND,
            elementProperty:"sessionId",
            compareWithOperator:tableTypes.comparisionOperators.NOTEQUAL,
            shouldHaveValue:null
          }
        
        ],
        }
      ],
      cellAlign: "center",
      headerCssClasses: ["width-6"],
      cellCssClasses: ["cursor-pointer"],
    },
  ]]

  stateControl: FormControl;
  courseDetails: any = [];
  voucherListDetails: any;
  public dataSource: MatTableDataSource<Array<object>[]>;
  examType: number;
  userDetails: any;
  downloadVoucherInfo: any;
  trainingDetails
  navigate
  multipleRoleTenantId: Array<number> = [];
  personTentantRoleId: Array<number> = [];
  ExamTypes:Array<object>=[]
  form:FormGroup
  constructor(
    public global: GlobalUserService,
    public store: Store<VoucherState>,
    public voucherService: VoucherService, 
    private activteRoute:ActivatedRoute,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private route: Router,
    private http:HttpService ,
    private services:SnackbarService,
    private dialog:MatDialog,
    private lngSrvc:LanguageService,
    private router:Router
  ) {}

  ngOnInit(): void {

    this.form = new FormGroup({
      start: new FormControl(""),
      end: new FormControl(""),
    });

      const breadcrumb = [
        {
          label: "Home",
          url: "/dashboard",
        },
        {
          label: "Roster",
          url: "",
        },
      ];
     (this.global.userDetails.getValue().roleId == Roles.OperationStaff ||this.global.userDetails.getValue().roleId == Roles.SupportingStaff || (this.global.userDetails.getValue().roleId == Roles.TrainingInstitue && this.global.userDetails.getValue().cantest == true)  )? this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb):null
     this.global.userDetails.getValue().roleId == Roles.Proctor? this.http.getEventForProctor({email:''}).subscribe((data: any) => {
        if (data.length > 0) {
          this.ExamTypes = data
        }
      }, (err: HttpErrorResponse | any) => {
        this.services.callSnackbaronError(`${err.message.message.error}`)
      }):'';
    

      (this.global.userDetails.getValue().roleId == Roles.TrainingInstitue && this.global.userDetails.getValue().cantest == false) ? this.dialog.open(popupcomponent, {
        data: {
          title: this.lngSrvc.curLangObj.value.triningerror,
          cancelButton: null ,
          OkButton: null,
          OKButton: this.lngSrvc.curLangObj.value.ok
        },
      })
        .afterClosed()
        .subscribe((confirmed: any) => {
          if (confirmed == true || confirmed.confirmed == true) {
          }else{
            this.router.navigateByUrl('/training')
          }
        }):null
        this.EventType({eventSKU:''})
  
  }

  userMetrics(event){
    this.http.getEventForProctor({email:event}).subscribe((data: any) => {
      if (data.length > 0) {
        this.ExamTypes = data
      }else{
        this.ExamTypes =[]
        this.EventType({eventSKU:''})
      }
    }, (err: HttpErrorResponse | any) => {
      this.services.callSnackbaronError(`${err.message.message.error}`)
    })
  }

  EventType(event: any) {
    if ( (event.eventSKU == "")) {
      this.dataSource = new MatTableDataSource([])
      this.stateControl.setValue("");
    } else {
      this.clienteventid = this.global.userDetails.getValue().roleId == Roles.TrainingInstitue ? event : event.eventSKU
        this.http.getCandidateDetailsfromGis(this.clienteventid,this.global.userDetails.value.personId).subscribe((data: any) => {

          if (data.length > 0) {
            this.dataSource = new MatTableDataSource(data)
          }
          else {
            this.dataSource = new MatTableDataSource([])
          }
        }, (err: HttpErrorResponse | any) => {
          this.services.callSnackbaronError(`${err.message.message.error}`)
        })
    }
  }

  downloadVoucher(event){
      const dialog = this.dialog.open(OMRVideoImage,
        {
          data: { actiontype:event.action.icon,element:{personEventId:event.element.personEventID} },
          width: '400px',

        });
      dialog.afterClosed().subscribe((data) => {
        // this.EventType({ eventSku: data.clientId })
      });

}


}
