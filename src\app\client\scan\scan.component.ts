import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild } from "@angular/core";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
import { BehaviorSubject, Observable, Subscription, interval } from "rxjs";
import { HttpService } from "src/app/core/http.service";
import { ActivatedRoute, ParamMap, Params, Router } from "@angular/router";
import { GlobalUserService, userData } from "src/app/core/global-user.service";
import { Roles } from "../dynamic-component-loader/dynamic-component-loader.types";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MatSidenav } from "@angular/material/sidenav";
import { ReplaySessionService } from "src/app/core/openreplay.services";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { PaymentRefundPopupComponent } from "src/app/core/common-component/pop-up/payment-refund-popup/payment-refund-popup.component";
import { LanguageService } from "src/app/core/language.service";
import { PopUpComponent as popupcomponent } from "../application/pop-up/pop-up.component";
import { Store } from "@ngrx/store";
import { UpdateAbsentDetails } from "../state/shared/shared.actions";
import { getLoading, updateAbsent } from "../state/shared/shared.selectors";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import {
  HttpClient,
  HttpErrorResponse,
  HttpHeaders,
} from "@angular/common/http";
import { OnboardDetailsComponent } from "./onboard-details/onboard-details.component";
import { DatePipe } from "@angular/common";
import { debounceTime } from "rxjs/operators";
import {
  Allonlyskills,
  Allowonlyoral,
  Alloworalskills,
  HideShredButtonforparticularstate,
  Role,
  ShowOnboardforParticularstate,
} from "src/app/core/common-component/examroom-formbuilder/form-builder.types";
import { AllowWritten } from "../../core/common-component/examroom-formbuilder/form-builder.types";
import { Param } from "@thoughtspot/visual-embed-sdk/lib/src/types";
import { MatDatepickerInputEvent } from "@angular/material/datepicker";
import { QacAccomodationComponent } from "src/app/core/common-component/pop-up/qac-accomodation/qac-accomodation.component";

@Component({
  selector: "app-scan",
  templateUrl: "./scan.component.html",
  styleUrls: ["./scan.component.scss"],
})
export class ScanComponent implements OnInit, OnDestroy {
  IsSkills: boolean;
  listExam: Array<object> = [];
  ShowOnboardforParticularstate = ShowOnboardforParticularstate
  loadingObs: Observable<boolean>;
  hideShredButtonforparticularstate = HideShredButtonforparticularstate;
  stateControl: FormControl;
  TrainingControl: FormControl;
  ShowSelectEventTypeDropdown: boolean = false;
  notallowStatusId = NotAllowOnbardforIncident;
  ExamTypes: Array<object> = [];
  AllowExamsAppointId = AllowWritten;
  AllowOralskills = Alloworalskills;
  Allowonlyskills = Allonlyskills;
  Allowonlyorals = Allowonlyoral;
  EventCompleted: boolean = false
  latitude: string | number;
  StartStopRecording: boolean = false;
  longitude: string | number;
  clienteventid;
  TrainingListId: Array<object> = [];
  IncidentArray: Array<object> = [
    { id: "79", value: "FailedHealthScreening" },
    { id: "80", value: "Otherfacilityissue" },
    { id: "81", value: "TurnAwayNotCandidateFault" },
    { id: "82", value: "TurnAwayCandidateFault" },
    { id: "83", value: "WrongID" },
    { id: "84", value: "Relationship" },
    { id: "85", value: "LanguageIssue" },
    { id: "86", value: "HealthIllness" },
    { id: "87", value: "Accommodation" },
    { id: "88", value: "Before Exam Cheating" },
    { id: "10", value: "No Show" },
    { id: "89", value: 'Misconduct between Exam' },
    { id: "92", value: "Candidate ILL/WALK AWAY" },
    { id: "93", value: "Facility Issue" },
    { id: "94", value: "Safety Stop" },
  ];
  @ViewChild("sidenav") sidenavRef: MatSidenav;
  CheckValue: string = "Check-in";
  notesSidebarViewId;
  candidateDetails: object;
  startTime: string;
  endTime: string;
  currentTime: Date = new Date();
  timerSubscription: Subscription;
  counter: number = 0;
  personTenanatroleId: userData;
  UserDetails;
  selectedDate: Date = new Date();
  eventDate: string;
  currentEvent = [];
  eventSelected = false;
  TsmHeader: string;
  testCenterName = "";
  Incidient: Array<object> = [];
  tomorrow = new Date();
  minDate = new Date();
  showAuditForm : boolean = false;
  eventSku : string;
  formTypeId :number= 18;
  stateId:number = 4;
  eligibilityRouteId : number = 21;
  candidateId:number = 163;
  code:string = 'PA';
  personEventId  ;

  constructor(
    private http: HttpService,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private router: Router,
    public globalUserService: GlobalUserService,
    private timerService: ReplaySessionService,
    private dialog: MatDialog,
    private lngSrvc: LanguageService,
    private store: Store,
    private services: SnackbarService,
    private Activate: ActivatedRoute,
    private https: HttpClient,
    private fb: FormBuilder
  ) {
    this.Activate.queryParamMap.subscribe((params: Params) => {
      params.keys.length === 0 ? null : sessionStorage.setItem('legacyRoaster', params.params.legacyRoaster)
      sessionStorage.setItem('scanCurrentURL', window.location.href);
      this.UserDetails = {
        roleId: params.params.roleName == 'Proctor' || params.params.roleName == 'proctor' ? Roles.Proctor : params.params.roleId,
        emailId: params.params.tsmemail,
        rolename: params.params.roleName,
        legacyRoaster: params.keys.length === 0 ? sessionStorage.getItem("legacyRoaster") : params.params.legacyRoaster,
        clientEventId:params.params.clientEventId,
        check:params.params.check
      };
      this.globalUserService.userDetails.subscribe((data) => {
        if (data) {
          this.personTenanatroleId = data;
          // (this.ShowSelectEventTypeDropdown =
          //   this.globalUserService.userDetails.value.roleId ==
          //     Roles.TrainingInstitue ||
          //     this.globalUserService.userDetails.value.roleId == Roles.Proctor ||
          //     this.globalUserService.userDetails.value.roleId ==
          //     Roles.OperationStaff ||
          //     this.globalUserService.userDetails.value.roleId ==
          //     Roles.SupportingStaff
          //     ? true
          //     : false),
            (this.UserDetails.rolename === "Proctor" ||
              this.UserDetails.rolename === "proctor") &&
              this.globalUserService.userDetails.value.roleId == Roles.Proctor
              ? this.getTSMToken()
              : null;
          this.globalUserService.userDetails.value.roleId != Roles.Proctor
            ? this.EventType({ eventSku: this.UserDetails.clientEventId })
            : this.EventType({ eventSKU: this.UserDetails.clientEventId });
          this.getUserLocation();
        }
      });
    });
    // this.stateControl = this.fb.group({
    //   stateControl: [null],
    // });

    this.globalUserService.userDetails.subscribe((data) => {
      if (data) {
        this.personTenanatroleId = data;
        this.ShowSelectEventTypeDropdown =
          this.globalUserService.userDetails.value.roleId ==
            Roles.TrainingInstitue ||
            this.globalUserService.userDetails.value.roleId == Roles.Proctor ||
            this.globalUserService.userDetails.value.roleId ==
            Roles.OperationStaff ||
            this.globalUserService.userDetails.value.roleId ==
            Roles.SupportingStaff
            ? true
            : false;

        // this.selectExamtypes();
        this.getUserLocation();
      }
    });


    if (this.UserDetails.legacyRoaster == 'true') {
      this.tomorrow.setDate(this.tomorrow.getDate() - 1);
      this.selectedDate.setDate(this.tomorrow.getDate());
    }
    this.minDate.setDate(this.minDate.getDate() - 5)



  }

  ngOnInit(): void {
    const breadcrumb = [
      {
        label: "Home",
        url: "/dashboard",
      },
      {
        label: "Roster",
        url: "/Skills",
      },
    ];
    this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);

    this.loadingObs = this.store.select(getLoading).pipe(debounceTime(50));

    this.timerSubscription = interval(1000).subscribe(() => {
      this.currentTime = new Date(); // Update the current time every second
    });
  }

  onDateChange(event: MatDatepickerInputEvent<Date>) {
    this.selectedDate = event.value;
    this.filterEventsByDate();
  }

  filterEventsByDate() {
    this.currentEvent = [];
    this.ExamTypes.map((exam: any) => {
      if (exam.eventDate) {
        const backendDate = new Date(exam.eventDate);
        const normalizeDate = (date: Date) => {
          return new Date(date.getFullYear(), date.getMonth(), date.getDate());
        };
        const normalizedDate1 = normalizeDate(this.selectedDate);
        const normalizedBackendDate = normalizeDate(backendDate);

        if (normalizedDate1.getTime() === normalizedBackendDate.getTime()) {
          this.currentEvent.push(exam);
          this.testCenterName = this.currentEvent[0].testCenterName;
        }
      }
    });
  }

  Omrstatus(event, type) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.disableClose = true;
    dialogConfig.data = {
      id: 3,
      name: "Answer",
      bookletId: event.bookletId,
      Confirm: event,
      clienteventid: this.clienteventid,
      firstName:event.firstName,
      email:event.email,
      personId:event.candidateIdentifier,
      appointmentId:event.appointmentIdentifier,
      personTenanatroleId:event.personTenantRoleID,
      personEventID:event.personEventID
      
    };
    dialogConfig.width = "400px";
    this.dialog
      .open(PaymentRefundPopupComponent, dialogConfig)
      .afterClosed()
      .subscribe((data) => {
        type == "omr" && data.confirmed == true
          ? this.router.navigateByUrl(
            `/Scan/Evaluator/${this.clienteventid}/${event.email}/${event.firstName}/1/${event.bookletId}/${event.candidateIdentifier}/${event.personEventID}/${event.personTenantRoleID}/${event.appointmentIdentifier}`
          )
          : null;
      });
  }

  getUserLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        this.latitude = position.coords.latitude;
        this.longitude = position.coords.longitude;
      });
    }
  }

  reviwer(item, ExamDetails) {
    this.https
      .post(`${environment.GisApiUrl}EventOffer/CheckEventScoreResponsible`, {
        eventSKU: item.clientEventId,
        email: this.globalUserService.PublishEmail,
      })
      .subscribe((data: boolean) => {
        if (data) {
          this.router.navigateByUrl(
            `/SkillNAE/${item.candidateIdentifier}/${item.eventId}/${item.vMasterID
            }/${this.globalUserService.userDetails.getValue().personId}/${data}`
          );
        } else {
          this.router.navigateByUrl(
            `/SkillNAE/${item.candidateIdentifier}/${item.eventId}/${item.vMasterID
            }/${this.globalUserService.userDetails.getValue().personId}/${data}`
          );
        }
      });
  }

  Booklet(item, exammode: number) {
    if (this.UserDetails.check === "true" || (this.UserDetails.check ==='false' && this.UserDetails.rolename ==='Proctor')) {
      let body = {
        candidateId: Number(item.candidateIdentifier),
        testCenterId: item.testCenterId,
        personEventId: item.personEventID,
        naeUserId: this.personTenanatroleId.personId,
        firstName: item.firstName,
        lastName: item.lastName,
        appointmentId: item.appointmentIdentifier,
        clientEventId: item.clientEventId,
        email: item.email,
        personTenantRoleID: item.personTenantRoleID,
        id: item.clientEventId,
        mode: Number(exammode),
        state: item.state
      };
      if (Number(exammode) === 3) {
        const dialogRef = this.dialog.open(popupcomponent, {
          data: {
            title: `${item.exams[0].examName}`,
            message: this.lngSrvc.curLangObj.value.cancelonboard,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
          },
        });
        dialogRef.afterClosed().subscribe((data) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            this.https
              .put(
                `${environment.baseUrl
                }client/api/Candidate/CancelOnboard?appointmentId=${Number(
                  item.appointmentIdentifier
                )}&personEventId=${item.personEventID}`,
                {}
              )
              .subscribe(
                (res: { message: string }) => {
                  if (res) {
                    this.EventType({ eventSku: this.clienteventid });
                    this.services.callSnackbaronSuccess(
                      " Cancel Onboard Successfully "
                    );
                  }
                },
                (err: HttpErrorResponse | any) => {
                  this.services.callSnackbaronError(
                    `${err.message.message.error}`
                  );
                }
              );

            // var requestDetailsClone = lodash.cloneDeep($event.action.requestDetails[0]);
            // this.setParamValues(requestDetailsClone, $event);
            // this.store.dispatch(genericDelete({
            //   performedAction: $event,
            //   requestDetails: requestDetailsClone,
            //   widgetRowIndex: rowIndex,
            //   widgetColumnIndex: columnIndex
            // }));
          }
        });
      } else {
        const dialogConfig = new MatDialogConfig();
        dialogConfig.data = body;
        dialogConfig.disableClose = true;
        this.dialog
          .open(OnboardDetailsComponent, dialogConfig)
          .afterClosed()
          .subscribe((confirmed: any) => {
            confirmed.clientEventId != undefined
              ? this.globalUserService.userDetails.getValue().roleId ==
                Roles.Proctor
                ? this.EventType({ eventSKU: item.clientEventId })
                : this.EventType({ eventSku: item.clientEventId })
              : null;
          });
      }
    } else {
      this.services.callSnackbaronError("Kindly be sure to check in.");
    }
  }

  // getTimer(personTenantRoleId: number) {
  //   personTenantRoleId != null
  //     ? this.http.getTimer(personTenantRoleId).subscribe(
  //       (data: boolean) => {
  //         if (data == true) {
  //           this.GetTimerValue = data;
  //           let Values: any[] = Array.from(
  //             document.querySelectorAll("button")
  //           );
  //           let filteredValues = Values.filter((x) => x.id === "buttonCheck");
  //           filteredValues[0].classList.remove("buttonstatustimer");
  //           filteredValues[0].classList.add("endTime");
  //           this.CheckValue = "Check-out";
  //           this.ShowSelectEventTypeDropdown = true;
  //         } else {
  //           this.CheckValue = "Check-in";
  //         }
  //       },
  //       (err: HttpErrorResponse | any) => {
  //         this.services.callSnackbaronError(`${err.message.message.error}`);
  //       }
  //     )
  //     : null;
  // }

  confirm(items, value: number) {
    if (value != 7) {

      const dialog = this.dialog.open(popupcomponent, {
        data: {
          title: `${items.bookletId}`,
          message: this.lngSrvc.curLangObj.value.Confirm_Booklet,
          cancelButton: this.lngSrvc.curLangObj.value.cancel,
          OKButton: this.lngSrvc.curLangObj.value.cancel,
          OkButton: this.lngSrvc.curLangObj.value.save,
        },
      })
      dialog.afterClosed().subscribe(data => {
        if ((data.confirmed == true || data.confirmed.confirmed == true)) {
          let body = {
            bookletNumber: items.bookletId,
            appointmentNumber: items.appointmentIdentifier,
            firstName: items.firstName,
            lastName: items.lastName,
            personTenantRoleId: Number(items.personTenantRoleID)
          };
          this.http.getConfirm(body).subscribe((data) => {
            if (data) {
              this.globalUserService.userDetails.getValue().roleId == Roles.Proctor
                ? this.EventType({ eventSKU: items.clientEventId })
                : this.EventType({ eventSku: items.clientEventId });
            }
          });
        }
      });
    } else {
      const dialog = this.dialog.open(PaymentRefundPopupComponent, {
        data: {
          bookletId: items.bookletId,
          emailId: items.email,
          clienteventid: items.clientEventId,
          id: value,
          candidateId: Number(items.candidateIdentifier),
          testCenterId: items.testCenterId,
          naeUserId: this.personTenanatroleId.personId,
          personEventId: items.personEventID,
        },
        width: "400px",
      });
      dialog.afterClosed().subscribe((data) => {
        data.clientId != undefined
          ? this.globalUserService.userDetails.getValue().roleId ==
            Roles.Proctor
            ? this.EventType({ eventSKU: data.clientEventId })
            : this.EventType({ eventSku: data.clientEventId })
          : null;
      });
    }
  }

  openSidenavAndSetViewId(event: any, item) {
    this.notesSidebarViewId = event.whatToOpen;
    this.candidateDetails = {
      personTenantRoleID: item.personTenantRoleID,
      personFormID: item.personFormID,
      personEventID: item.personEventID,
    };
    this.sidenavRef.toggle();
  }

  getTSMToken() {
    let body = {
      accessedByUserID: this.UserDetails.emailId,
      accessedByName: "proctor",
      roleID: this.globalUserService.userDetails.value.roleId,
      roleName: this.UserDetails.rolename,
    };
    this.https
      .post(`${environment.GisApiUrl}Get_GISAccessToken`, body)
      .subscribe((data: string) => {
        if (data) {
          this.TsmHeader = data;
          // this.selectExamtypes();
        }
      });
  }

  selectExamtypes() {
    const headers = new HttpHeaders({
      Authorization: `Bearer ${this.TsmHeader}`, // Replace with your actual authorization token
      "Content-Type": "application/json", // Adjust content type if needed
    });

    const EmulateGisheader = new HttpHeaders({
      Authorization: `Bearer ${sessionStorage.getItem("Gis")}`, // Replace with your actual authorization token
      "Content-Type": "application/json", // Adjust content type if needed
    });

    // this.globalUserService.userDetails.getValue().roleId != Roles.TrainingInstitue && this.globalUserService.userDetails.getValue().roleId != Roles.Proctor && this.UserDetails.legacyRoaster == true ?
    this.globalUserService.userDetails.getValue().roleId !=
      Roles.TrainingInstitue &&
      this.globalUserService.userDetails.getValue().roleId != Roles.Proctor
      ? this.http
        .getScheduleGistNAE(this.UserDetails.legacyRoaster, EmulateGisheader)
        .subscribe(
          (data: Array<any>) => {
            if (data.length > 0) {
              let events = [];
              if (this.UserDetails.legacyRoaster == "true") {
                events = data;
              } else {
                events = data.filter((x) => x.status == 4);
              }

              this.ExamTypes = [
                { id: null, eventSku: "Clear Selection" },
                ...events,
              ];
              this.filterEventsByDate()
            }
          },
          (err: HttpErrorResponse | any) => {
            this.services.callSnackbaronError(`${err.message.message.error}`);
          }
        )
      : this.globalUserService.userDetails.getValue().roleId == Roles.Proctor &&
        this.TsmHeader != undefined &&
        this.TsmHeader != null &&
        this.TsmHeader != ""
        ? this.http.getScheduleGistProctor(headers).subscribe(
          (data: any) => {
            if (data.length > 0) {
              this.ExamTypes = data;
            }
          },
          (err: HttpErrorResponse | any) => {
            this.services.callSnackbaronError(`${err.message.message.error}`);
          }
        )
        : this.globalUserService.userDetails.getValue().roleId == Roles.Proctor
          ? this.http
            .getScheduleGistNAE(this.UserDetails.legacyRoaster, EmulateGisheader)
            .subscribe(
              (data: any) => {
                if (data.length > 0) {
                  this.ExamTypes = data;
                }
              },
              (err: HttpErrorResponse | any) => {
                this.services.callSnackbaronError(`${err.message.message.error}`);
              }
            )
          : this.http.getEventTP(this.globalUserService.email).subscribe(
            (data: any) => {
              if (data.length > 0) {
                this.ExamTypes = data;
              }
            },
            (err: HttpErrorResponse | any) => {
              this.services.callSnackbaronError(`${err.message.message.error}`);
            }
          );
  }

  // AddTimer(time: string, Id: number, index: number, Clienteventid: string) {
  //   let body = {
  //     latitude: this.latitude,
  //     longitude: this.longitude,
  //   };
  //   this.http
  //     .AddTimer(
  //       this.personTenanatroleId.personTenantRoleId,
  //       time,
  //       Id,
  //       Clienteventid,
  //       body
  //     )
  //     .subscribe(
  //       (data) => {
  //         if (data) {
  //           Id === 3
  //             ? this.https
  //               .post(
  //                 `${environment.baseUrl}schedulemsvc/api/scheduler/GetNaeCandidateCounts?clientEventId=${this.clienteventid}&emailId=${this.globalUserService.PublishEmail}`,
  //                 {}
  //               )
  //               .subscribe(
  //                 (data) => {
  //                   if (data) {
  //                     this.services.callSnackbaronSuccess(
  //                       "Exam Completed SuccessFuly"
  //                     );
  //                     this.clienteventid = "";
  //                     this.stateControl.setValue("");
  //                     this.listExam = [];
  //                   }
  //                 },
  //                 (err) => {
  //                   this.services.callSnackbaronError(
  //                     `${err.message.message.error}`
  //                   );
  //                 }
  //               )
  //             : null;
  //           this.ShowSelectEventTypeDropdown = Id == 1 ? true : false;
  //           let Values: any[] = Array.from(document.querySelectorAll("button"));
  //           let filteredValues = Values.filter((x) => x.id === "buttonCheck");
  //           Id == 1
  //             ? filteredValues[0].classList.remove("buttonstatustimer")
  //             : filteredValues[0].classList.add("buttonstatustimer");
  //           Id == 1
  //             ? filteredValues[0].classList.add("endTime")
  //             : filteredValues[0].classList.remove("endTime");
  //           this.GetTimerValue = Id == 2 ? false : true;
  //           Id == 2 || Id == 3 ? (this.counter = 0) : this.counter;
  //           Id == 2 || Id == 3
  //             ? this.stateControl.setValue("")
  //             : null;
  //         }
  //       },
  //       (err: HttpErrorResponse | any) => {
  //         this.services.callSnackbaronError(`${err.message.message.error}`);
  //       }
  //     );
  // }

  EventType(event: any) {
    if(event?.person?.canCofacilitate || event?.person?.canAudit){
      this.showAuditForm = true,
      this.eventSku = event.eventSku      
    }
    if (this.globalUserService.userDetails.getValue().roleId != Roles.TrainingInstitue ? (event.eventSku == "Clear Selection") :this.globalUserService.userDetails.getValue().roleId == Roles.Proctor?(event.eventSKU == "Clear Selection"): event == "") {
      this.listExam = [];
      this.stateControl.setValue("");
    } else {
      this.clienteventid = this.globalUserService.userDetails.getValue().roleId == Roles.TrainingInstitue && this.globalUserService.userDetails.getValue().roleId != Roles.Proctor ? event : this.globalUserService.userDetails.getValue().roleId == Roles.Proctor ? event.eventSKU : event.eventSku
      this.globalUserService.userDetails.getValue().roleId == Roles.TrainingInstitue ?
        this.http.getCandidateTPfromGis(this.globalUserService.email, this.clienteventid).subscribe((data: Array<object>) => {
          if (data.length > 0) {
            this.listExam = data.filter((x: any) => x.exams.find((y) => y.examCode != "NA-PR"))
            const _exams = data.map((ele) =>
              Object.assign(ele));
            // Can't onboard and absent when schedule is grater than current date
            _exams.forEach((ele, i: number) => {
              var datePipe = new DatePipe("en-US");
              let today = datePipe.transform(new Date(), 'yyyy-MM-dd');
              let examDateTime = datePipe.transform(new Date(ele.scheduleDateTime), 'yyyy-MM-dd');
              if (today === examDateTime) {
                _exams[i].allowonboard = true
                _exams[i].allowabsent = true
              } else {
                _exams[i].allowonboard = false
                _exams[i].allowabsent = false
              }
            });

            this.listExam = _exams






          } else {
            this.listExam = []
          }
        },
          (err: HttpErrorResponse | any) => {
            this.services.callSnackbaronError(`${err.message.message.error}`)
          }) :
          this.http.getCandidateDetailsfromGis(this.clienteventid,this.globalUserService.userDetails.value.personId).subscribe((data: any) => {
          if (data.length > 0) {

            // this.getTimer(this.globalUserService.userDetails.value.personTenantRoleId)


            // const personEventID: string = data[0].personEventID;
            data.personEventID
            const _exams = data.map((ele) =>
              Object.assign(ele));
            // Can't onboard and absent when schedule is grater than current date
            _exams.forEach((ele, i: number) => {
              var datePipe = new DatePipe("en-US");
              let today = datePipe.transform(new Date(), 'yyyy-MM-dd');
              let examDateTime = datePipe.transform(new Date(ele.scheduleDateTime), 'yyyy-MM-dd');
              if (today === examDateTime) {
                _exams[i].allowonboard = true
                _exams[i].allowabsent = true
              } else {
                _exams[i].allowonboard = false
                _exams[i].allowabsent = false
              }
            });

            this.listExam = _exams

            // let RemoveNoshow =_exams.filter((x)=>!Removestatus.includes(x.eventStatusId))
            //   this.EventCompleted =RemoveNoshow.every((y)=>Number(y.eventStatusId)=== 8)


            // let RemoveNoshow = _exams.filter(
            //   (x) => !Removestatus.includes(x.eventStatusId)
            // );
            // this.EventCompleted = RemoveNoshow.every(
            //   (y) => Number(y.eventStatusId) === 8
            // );

            this.StartStopRecording = true
          }
          else {
            this.listExam = []
          }
        }, (err: HttpErrorResponse | any) => {
          this.services.callSnackbaronError(`${err.message.message.error}`)
        })
    }
  }


  // start(i) {
  //   if (i == 1) {
  //     this.startTime = new Date().toLocaleTimeString();
  //     this.timerService.startTimer();
  //     this.CheckValue = "Check-out";
  //     this.AddTimer(this.startTime, 1, 0, this.clienteventid);
  //   } else if (i == 3) {
  //     this.endTime = new Date().toLocaleTimeString();
  //     this.CheckValue = "Check-in";
  //     this.AddTimer(this.endTime, i, 0, this.clienteventid);
  //     this.listExam = [];
  //   } else {
  //     this.endTime = new Date().toLocaleTimeString();
  //     this.CheckValue = "Check-in";
  //     this.AddTimer(this.endTime, 2, 0, this.clienteventid);
  //     this.listExam = [];
  //   }
  // }

  Absent(examName: string, item) {
    if (examName == "Complete") {
      // if (
      //   this.clienteventid != "" &&
      //   this.clienteventid != null &&
      //   this.clienteventid != undefined &&
      //   this.EventCompleted == true
      // ) {
      //   const dialogNoshow = this.dialog.open(popupcomponent, {
      //     data: {
      //       title: `${this.clienteventid}`,
      //       message: this.lngSrvc.curLangObj.value.EventComplete,
      //       cancelButton: this.lngSrvc.curLangObj.value.cancel,
      //       OKButton: this.lngSrvc.curLangObj.value.cancel,
      //       OkButton: this.lngSrvc.curLangObj.value.save,
      //     },
      //   });
      //   dialogNoshow.afterClosed().subscribe((data) => {
      //     if (data.confirmed == true || data.confirmed.confirmed == true) {
      //       // this.start(3);
      //     }
      //   });
      // } else {
      //   this.services.callSnackbaronError(
      //     this.clienteventid == null &&
      //       this.clienteventid == "" &&
      //       this.clienteventid == undefined
      //       ? "Please select the Event"
      //       : this.clienteventid != null &&
      //         this.clienteventid != "" &&
      //         this.clienteventid != undefined &&
      //         this.EventCompleted == false
      //         ? "Please complete all  the candidates,then only you can  complete the event."
      //         : ""
      //   );
      // }
    } 
   else {
      if (
        (this.UserDetails.check === "true" || (this.UserDetails.check ==='false' && this.UserDetails.rolename ==='Proctor')) && item.eventStatusId != "8"

      ) {
        const dialog = this.dialog.open(PaymentRefundPopupComponent, {
          data: {
            bookletId: item.bookletId,
            emailId: item.email,
            clienteventid: item.clientEventId,
            id: 6,
            candidateId: Number(item.candidateIdentifier),
            testCenterId: item.testCenterId,
            naeUserId: this.personTenanatroleId.personId,
            personEventId: item.personEventID,
            password: item.taoPassword,
            examName: examName,
            onboard:item.isOnboardingSuccessfull,
            state:item.state
          },
          width: "400px",
          disableClose: true,
        });
        dialog.afterClosed().subscribe((data) => {
          if (data.confirmed == true) {
            let body = {
              candidateId: Number(item.candidateIdentifier),
              personEventId: item.personEventID,
              testCenterId: item.testCenterId,
              naeUserId: this.personTenanatroleId.personId,
              IncidentId: data.incidentId,
              IncidentName: data.incidentName,
              IncidentType: data.incidentType,
              IncidentTypeName: data.incidentTypeName,
              IncidentComments: data.notes,
              IncidentDescription: data.notes,
              IncidentStatus: 1,
              examType: 3,
            };
            this.https
              .post(`${environment.baseUrl}client/api/arkiv/absent`, body)
              .subscribe(
                (data: any) => {
                  if (data) {
                    this.services.callSnackbaronSuccess(`${data.message}`);
                    setTimeout(() => {
                      let scanUrl = sessionStorage.getItem('scanCurrentURL');
                      window.location.href = scanUrl;
                    },5000);
                    this.globalUserService.userDetails.getValue().roleId ==
                      Roles.Proctor
                      ? this.EventType({ eventSKU: item.clientEventId })
                      : this.EventType({ eventSku: item.clientEventId });
                  }
                },
                (err: HttpErrorResponse | any) => {
                  this.services.callSnackbaronError(
                    `${err.message.message.error}`
                  );
                }
              );
          }
        });
      } else {
        if((this.globalUserService.userDetails.value.roleId === Roles.NAE ||
        this.globalUserService.userDetails.value.roleId === Roles.Proctor ||
        this.globalUserService.userDetails.value.roleId ===
        Roles.TrainingInstitue) && this.UserDetails.legacyRoaster =='true' && item.eventStatusId != "8"){
          const dialog = this.dialog.open(PaymentRefundPopupComponent, {
            data: {
              bookletId: item.bookletId,
              emailId: item.email,
              clienteventid: item.clientEventId,
              id: 6,
              candidateId: Number(item.candidateIdentifier),
              testCenterId: item.testCenterId,
              naeUserId: this.personTenanatroleId.personId,
              personEventId: item.personEventID,
              password: item.taoPassword,
              examName: examName,
              onboard:item.isOnboardingSuccessfull,
              state:item.state
            },
            width: "400px",
            disableClose: true,
          });
          dialog.afterClosed().subscribe((data) => {
            if (data.confirmed == true) {
              let body = {
                candidateId: Number(item.candidateIdentifier),
                personEventId: item.personEventID,
                testCenterId: item.testCenterId,
                naeUserId: this.personTenanatroleId.personId,
                IncidentId: data.incidentId,
                IncidentName: data.incidentName,
                IncidentType: data.incidentType,
                IncidentTypeName: data.incidentTypeName,
                IncidentComments: data.notes,
                IncidentDescription: data.notes,
                IncidentStatus: 1,
                examType: 3,
              };
              this.https
                .post(`${environment.baseUrl}client/api/arkiv/absent`, body)
                .subscribe(
                  (data: any) => {
                    if (data) {
                      this.services.callSnackbaronSuccess(`${data.message}`);
                      setTimeout(() => {
                        let scanUrl = sessionStorage.getItem('scanCurrentURL');
                        window.location.href = scanUrl;
                      },5000);
                      this.globalUserService.userDetails.getValue().roleId ==
                        Roles.Proctor
                        ? this.EventType({ eventSKU: item.clientEventId })
                        : this.EventType({ eventSku: item.clientEventId });
                    }
                  },
                  (err: HttpErrorResponse | any) => {
                    this.services.callSnackbaronError(
                      `${err.message.message.error}`
                    );
                  }
                );
            }
          });
        }
        else if(item.eventStatusId == "8"){
          const dialog = this.dialog.open(PaymentRefundPopupComponent, {
            data: {
              bookletId: item.bookletId,
              emailId: item.email,
              clienteventid: item.clientEventId,
              id: 6,
              candidateId: Number(item.candidateIdentifier),
              testCenterId: item.testCenterId,
              naeUserId: this.personTenanatroleId.personId,
              personEventId: item.personEventID,
              password: item.taoPassword,
              examName: examName,
              onboard:item.isOnboardingSuccessfull,
              state:item.state,
              eventStatusId: item.eventStatusId 
            },
            width: "400px",
            disableClose: true,
          });
          dialog.afterClosed().subscribe((data) => {
            if (data) {
       this.globalUserService.userDetails.getValue().roleId ==
                Roles.Proctor
                ? this.EventType({ eventSKU: item.clientEventId })
                : this.EventType({ eventSku: item.clientEventId });
            }
          });
        }
        else{
          this.services.callSnackbaronError("Kindly be sure to check in.");
        }
        
      }
    }
  }



  SelectedEventType($event) {
    this.EventType({ eventSku: $event.eventSku });
  }

  openQACResponse(items){
    this.dialog.open(QacAccomodationComponent,{data:{id:1,items:items}});
  }





  RouteGisTsm() {
    const dialogNoshow = this.dialog.open(popupcomponent, {
      data: {
        title: "Event management",
        message: this.lngSrvc.curLangObj.value.GIS,
        cancelButton: this.lngSrvc.curLangObj.value.cancel,
        OKButton: this.lngSrvc.curLangObj.value.cancel,
        OkButton: this.lngSrvc.curLangObj.value.save,
      },
    });
    dialogNoshow.afterClosed().subscribe((data) => {
      if (data.confirmed == true || data.confirmed.confirmed == true) {
        const token = sessionStorage.getItem("token");
        window.open(`${environment.gistUrl}?token=${token}`, "_blank");
      }
    });
  }
  OpenAuditForm(){
    this.router.navigateByUrl(`/Scan/Auditform/${this.eventSku}/${this.formTypeId}/${this.stateId}/${this.eligibilityRouteId}/${this.candidateId}/${this.code}/${this.personEventId}`)
  }

  getBooklet(item: any) {
    this.http.getBookletUrl(
      this.clienteventid,
      this.globalUserService.userDetails.value.personId,
      item.candidateIdentifier,
      this.globalUserService.PublishEmail
    ).subscribe((result: string) => {
      if (!result) return;
      this.downloadPdf(result, item.bookletId);
    })
  }

  downloadPdf(url: string, bookletId: number) {
    const binaryString = window.atob(url);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);

    for (let i = 0; i < len; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const blob = new Blob([bytes], { type: 'application/pdf' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${bookletId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }



  ngOnDestroy(): void {
    this.timerSubscription.unsubscribe(); // Unsubscribe to prevent memory leaks
  }
}

export enum ExamTypeEnum {
  Written = "Nurse Aide Written Exam",
  EnglishOral = "Nurse Aide Oral English Exam",
  SpanishOral = "Nurse Aide Oral Spanish Exam",
  Skill = "Nurse Aide Skills Exam",
}

export const NotAllowOnbardforIncident = [
  "79",
  "80",
  "81",
  "82",
  "83",
  "84",
  "85",
  "86",
  "87",
  "88",
  "89",
  "10",
  "8",
  "88"
];
export const Removestatus = [
  "79",
  "80",
  "81",
  "82",
  "83",
  "84",
  "85",
  "86",
  "87",
  "88",
  "89",
  "10",
];

export interface IncideintResponse {
  incidentId: number;
  incidentName: string;
  incidentType: string;
  incidentTypeName: string;
  isActive: boolean;
}
