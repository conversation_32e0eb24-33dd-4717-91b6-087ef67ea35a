// imports for consoleLoaderJson
import { customModulesJSON as customModules } from 'src/app/core/jsonsfordev/customModules';
import { moduleJSON as json2 } from 'src/app/core/jsonsfordev/candidate-details';
// import { moduleJSON as json3 } from 'src/app/core/jsonsfordev/candidate-info';
import { moduleJSON as json4 } from 'src/app/core/jsonsfordev/candidate-management';
import { moduleJSON as json5 } from 'src/app/core/jsonsfordev/candidate-search';
import { moduleJSON as json6 } from 'src/app/core/jsonsfordev/cms';
import { moduleJSON as json7 } from 'src/app/core/jsonsfordev/demographic-management';
import { moduleJSON as json8 } from 'src/app/core/jsonsfordev/eligibility-route-details';
import { moduleJSON as json9 } from 'src/app/core/jsonsfordev/available-forms';
import { moduleJSON as json10 } from 'src/app/core/jsonsfordev/eligibility-routes';
import { moduleJSON as json11 } from 'src/app/core/jsonsfordev/manage-accommodation';
import { moduleJSON as json13 } from 'src/app/core/jsonsfordev/manage-applications';
import { moduleJSON as json14 } from 'src/app/core/jsonsfordev/manage-attempts';
import { moduleJSON as json15 } from 'src/app/core/jsonsfordev/manage-forms';
import { moduleJSON as json16 } from 'src/app/core/jsonsfordev/manage-states';
import { moduleJSON as json17 } from 'src/app/core/jsonsfordev/manage-training-programs';
import { moduleJSON as json18 } from 'src/app/core/jsonsfordev/manage-types-exam';
import { moduleJSON as json19 } from 'src/app/core/jsonsfordev/manage-user-roles';
import { moduleJSON as json20 } from 'src/app/core/jsonsfordev/manage-user';
import { moduleJSON as json21 } from 'src/app/core/jsonsfordev/manage-users';
import { moduleJSON as json22 } from 'src/app/core/jsonsfordev/state-details';
import { moduleJSON as json23 } from 'src/app/core/jsonsfordev/support-ticket';
import { moduleJSON as json24 } from 'src/app/core/jsonsfordev/ticket-details';
import { moduleJSON as json25 } from 'src/app/core/jsonsfordev/training-institute-details';
import { moduleJSON as json26 } from 'src/app/core/jsonsfordev/er-application-view-details';
import { moduleJSON as json27 } from 'src/app/core/jsonsfordev/schedule-accomodation-details';
import { moduleJSON as json28 } from 'src/app/core/jsonsfordev/manage-all-users';
import { moduleJSON as json29 } from 'src/app/core/jsonsfordev/manage-accommodations';
import { moduleJSON as json30 } from 'src/app/core/jsonsfordev/schedule-accommodations';
import { moduleJSON as json31 } from 'src/app/core/jsonsfordev/manage-grievance';
import { moduleJSON as json32 } from 'src/app/core/jsonsfordev/view-details';
import { moduleJSON as json33 } from 'src/app/core/jsonsfordev/manage-profile-request';
import { moduleJSON as json35 } from 'src/app/core/jsonsfordev/certificate-renewal';
import { moduleJSON as json34 } from 'src/app/core/jsonsfordev/manage-registry';
import { moduleJSON as json36 } from 'src/app/core/jsonsfordev/reciprocity-request';
import { moduleJSON as json37 } from 'src/app/core/jsonsfordev/manage-registry-details';
import { moduleJSON as json38 } from 'src/app/core/jsonsfordev/certificate-renewal-details';
import { moduleJSON as json39 } from 'src/app/core/jsonsfordev/reciprocity-request-details';
import { moduleJSON as json40 } from 'src/app/core/jsonsfordev/duplicate-certificate';
import { moduleJSON as json41 } from 'src/app/core/jsonsfordev/duplicate-certificate-details';
import { moduleJSON as json42 } from 'src/app/core/jsonsfordev/manage-all-users-superadmin';
import { moduleJSON as json43 } from 'src/app/core/jsonsfordev/scheduled-exams';
import { moduleJSON as json44 } from 'src/app/core/jsonsfordev/manage-applications-op';
import { moduleJSON as json45 } from 'src/app/core/jsonsfordev/manage-all-users-TP';
import { moduleJSON as json46 } from 'src/app/core/jsonsfordev/manage-accommodations-sc';
import { moduleJSON as json47 } from 'src/app/core/jsonsfordev/manage-pre-regisry';
import { moduleJSON as json48 } from 'src/app/core/jsonsfordev/candidate-details-sc';
import { moduleJSON as json49 } from 'src/app/core/jsonsfordev/reciprocity-request-sc';
import { moduleJSON as json50 } from 'src/app/core/jsonsfordev/problem-report';
import { moduleJSON as json51 } from 'src/app/core/jsonsfordev/employeer';
import { moduleJSON as json52 } from 'src/app/core/jsonsfordev/employer_details';
import { moduleJSON as json53 } from 'src/app/core/jsonsfordev/manage-employees';
import { moduleJSON as json54 } from 'src/app/core/jsonsfordev/manage-excused';
import { moduleJSON as json55 } from 'src/app/core/jsonsfordev/reciporating-scmae';
import { moduleJSON as json56 } from 'src/app/core/jsonsfordev/certificate-renewal-dc';
import { moduleJSON as json57} from 'src/app/core/jsonsfordev/CSAT-emaildetails';
import { moduleJSON as json58} from 'src/app/core/jsonsfordev/Roster-schedule-accomodation';
import { moduleJSON as json59} from 'src/app/core/jsonsfordev/payment-refund';
import { moduleJSON as json60} from 'src/app/core/jsonsfordev/view-misconduct';
import { moduleJSON as json61} from 'src/app/core/jsonsfordev/manage-stateclient';
import { moduleJSON as json62} from 'src/app/core/jsonsfordev/manage_reinstate.sc';
import { moduleJSON as json63} from 'src/app/core/jsonsfordev/candidate_stateClient_bulk_import';
import { moduleJSON as json64} from 'src/app/core/jsonsfordev/manage-roster-details';
import { moduleJSON as json65} from 'src/app/core/jsonsfordev/booklet_inventory';
import { moduleJSON as json66} from 'src/app/core/jsonsfordev/Regular-Certificate';
import { moduleJSON as json67} from 'src/app/core/jsonsfordev/manage-regular-details';
import { moduleJSON as json68} from 'src/app/core/jsonsfordev/pre-registration-candidate';
import { moduleJSON as json69} from 'src/app/core/jsonsfordev/all-access-superadmin';
export const CompleteJSON = [
  ...customModules.modules,
  ...json2.modules,
  // ...json3.modules,
  ...json4.modules,
  ...json5.modules,
  ...json6.modules,
  ...json7.modules,
  ...json8.modules,
  ...json9.modules,
  ...json10.modules,
  ...json11.modules,
  ...json13.modules,
  ...json14.modules,
  ...json15.modules,
  ...json16.modules,
  ...json17.modules,
  ...json18.modules,
  ...json19.modules,
  ...json20.modules,
  ...json21.modules,
  ...json22.modules,
  ...json23.modules,
  ...json24.modules,
  ...json25.modules,
  ...json26.modules,
  ...json27.modules,
  ...json28.modules,
  ...json29.modules,
  ...json30.modules,
  ...json31.modules,
  ...json32.modules,
  ...json33.modules,
  ...json35.modules,
  ...json34.modules,
  ...json36.modules,
  ...json37.modules,
  ...json38.modules,
  ...json39.modules,
  ...json40.modules,
  ...json41.modules,
  ...json42.modules,
  ...json43.modules,
  ...json44.modules,
  ...json45.modules,
  ...json46.modules,
  ...json47.modules,
  ...json48.modules,
  ...json49.modules,
  ...json50.modules,
  ...json51.modules,
  ...json52.modules,
  ...json53.modules,
  ...json54.modules,
  ...json55.modules, 
  ...json56.modules,
  ...json57.modules,
  ...json58.modules,
  ...json59.modules,
  ...json60.modules,
  ...json61.modules,
  ...json62.modules,
  ...json63.modules,
  ...json64.modules,
  ...json65.modules,
  ...json66.modules,
  ...json67.modules,
  ...json68.modules,
  ...json69.modules

  
];