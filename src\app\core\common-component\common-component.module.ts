import { NgModule, Type } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FlexLayoutModule } from "@angular/flex-layout";
import { PageLayoutModule } from "src/@exai/components/page-layout/page-layout.module";
import { MatTableModule } from "@angular/material/table";
import {
  MatPaginatorIntl,
  MatPaginatorModule,
} from "@angular/material/paginator";
import { IconModule } from "@visurel/iconify-angular";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatInputModule } from "@angular/material/input";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatRippleModule } from "@angular/material/core";

import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { MatMenuModule } from "@angular/material/menu";
import { MatSortModule } from "@angular/material/sort";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatSelectModule } from "@angular/material/select";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatSnackBarModule } from "@angular/material/snack-bar";
import { MatTabsModule } from "@angular/material/tabs";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { NgDynamicBreadcrumbModule } from "ng-dynamic-breadcrumb";
import { NgInitDirective, PopUpComponent } from "./pop-up/pop-up.component";
import { DynamicBreadCrumbsComponent } from "./dynamic-bread-crumbs/dynamic-bread-crumbs.component";
import {
  DynamicFormControl,
  DynamicFormControlModel,
  DynamicFormsCoreModule,
  DYNAMIC_FORM_CONTROL_MAP_FN,
} from "@ng-dynamic-forms/core";
import { DynamicFormsMaterialUIModule } from "@ng-dynamic-forms/ui-material";
import { SnackbarComponent } from "./snackbar/snackbar.component";
import { ExaiPaginator } from "./table/custom.paginator";
import { FormViewComponent } from "./form-view/form-view.component";
import { FormBuilderModule } from "./examroom-formbuilder/form-builder.module";
import { DynamicPopupComponent } from "./dynamic-popup/dynamic-popup.component";
import { ConfirmationPopupComponent } from "./confirmation-popup/confirmation-popup.component";
import { MatDialogModule } from "@angular/material/dialog";
import { NotesSidebarComponent } from "./notes-sidebar/notes-sidebar.component";
import { NgxEditorModule } from "ngx-editor";
import { CustomPopupForManageUsersComponent } from "./pop-up/custom-popup-for-manage-users/custom-popup-for-manage-users.component";
import { NgxMatIntlTelInputModule } from "ngx-mat-intl-tel-input";
import { AddTrainingInstitutePopupComponent } from "./pop-up/add-training-institute-popup/add-training-institute-popup.component";
import { MatStepperModule } from "@angular/material/stepper";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { DynamicFileUploadControlComponent } from "./examroom-formbuilder/dynamic-file-upload-control/dynamic-file-upload-control.component";
import { CustomPopupForAccItemComponent } from "./pop-up/custom-popup-for-acc-item/custom-popup-for-acc-item.component";
import { AddApplicationForCandidateComponent } from "./pop-up/add-application-for-candidate/add-application-for-candidate.component";
import { HTTP_INTERCEPTORS } from "@angular/common/http";

import { TokenInterceptorService } from "../token-interceptor.service";
// import { BufferInterceptor } from "../buffer-service.service";
import { MatExpansionModule} from '@angular/material/expansion';
import { AddCandidateComponent } from './pop-up/add-candidate/add-candidate.component';
import { RegisterModule } from "./pop-up/register/register.module";
import { FreeDragDirective } from "./free-drag.directive";
import { FilterPipe } from "../../../@exai/pipes/filter.pipe";
import { SafeHmtlPipe } from "./safe-hmtl.pipe";
import { ImgPopUpComponent } from "./dynamic-popup/img-pop-up/img-pop-up.component";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { DynamicPopupMesageComponent } from './pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component';
import { SafeResourceurlPipe } from './safe-resourceurl.pipe';
import { AddFormsComponent } from './pop-up/add-forms/add-forms.component';
import {TooltipModule} from 'ng2-tooltip-directive';
import {MatRadioModule} from '@angular/material/radio';



import { DynamicFormUploadComponent } from "./examroom-formbuilder/dynamic-form-upload/dynamic-form-upload.component";
import { DynamicFormLinkComponent } from "./examroom-formbuilder/dynamic-form-link/dynamic-form-link.component";
import { EmployerPopupComponent } from './pop-up/employer-popup/employer-popup.component';
import { EmployeesPopupComponent } from './pop-up/employees-popup/employees-popup.component';
import { TransactionListPopupComponent } from './pop-up/transaction-list-popup/transaction-list-popup.component';
import { PaymentRefundPopupComponent } from './pop-up/payment-refund-popup/payment-refund-popup.component';
import { VideoComponent } from "./pop-up/video/video.component";

import { SubscriberComponent } from "./stream-video/subscriber/subscriber.component";
import { PublisherComponent } from "./stream-video/publisher/publisher.component";
import { OMRVideoImage } from "./pop-up/omr-image-video/omr-image-video.component";
import { PdfViewerModule } from "ng2-pdf-viewer";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
//import { DynamicFormUploadComponent } from "./examroom-formbuilder/dynamic-form-upload/dynamic-form-upload.component";
//import { DynamicFormLinkComponent } from "./examroom-formbuilder/dynamic-form-link/dynamic-form-link.component";
import {TextFieldModule} from '@angular/cdk/text-field';
import { MatDividerModule } from '@angular/material/divider';
import { ReEvaluationDialogComponent } from './re-evaluation-dialog/re-evaluation-dialog.component';
import { QacAccomodationComponent } from './pop-up/qac-accomodation/qac-accomodation.component';
import { CustomAddPopupFormManageUserAccessComponent } from "src/app/client/custom-template/custom-add-popup-form-manage-user-access.component";
import { CustomTemplatesModule } from "src/app/client/custom-template/custom-add-popup-form-manage-user-access.module";

@NgModule({
  declarations: [
    PopUpComponent,
    NgInitDirective ,
    DynamicBreadCrumbsComponent,
    ConfirmationPopupComponent,
    FormViewComponent,
    DynamicPopupComponent,

    SnackbarComponent,
    NotesSidebarComponent,
    CustomPopupForManageUsersComponent,
    AddTrainingInstitutePopupComponent,
    CustomPopupForAccItemComponent,

    AddApplicationForCandidateComponent,
    AddCandidateComponent,
    FreeDragDirective,
    FilterPipe,
    ImgPopUpComponent,
    SafeHmtlPipe,
    DynamicPopupMesageComponent,
    SafeResourceurlPipe,
    AddFormsComponent,
    EmployerPopupComponent,
    EmployeesPopupComponent,
    TransactionListPopupComponent,
    PaymentRefundPopupComponent,
    VideoComponent,
    PublisherComponent,
    SubscriberComponent,
    OMRVideoImage,
    VideoComponent,
    ReEvaluationDialogComponent,
    QacAccomodationComponent
  ],
  imports: [
    NgxMatIntlTelInputModule,
    CommonModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatTableModule,
    MatPaginatorModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatRadioModule,
    MatSelectModule,
    MatCheckboxModule,
    MatAutocompleteModule,
    MatRippleModule,
    ReactiveFormsModule,
    MatMenuModule,
    TooltipModule,
    MatSortModule,
    MatFormFieldModule,
    MatSnackBarModule,
    FormsModule,
    MatTabsModule,
    MatSlideToggleModule,
    CustomTemplatesModule,
    NgDynamicBreadcrumbModule,
    DynamicFormsCoreModule.forRoot(),
    DynamicFormsMaterialUIModule,
    FormBuilderModule,
    MatDialogModule,
    NgxEditorModule,
    MatStepperModule,
    MatDatepickerModule,
    MatExpansionModule,
    RegisterModule,
    MatProgressBarModule,
  PdfViewerModule,
  MatProgressSpinnerModule,
    PdfViewerModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  exports: [
    DynamicBreadCrumbsComponent,
    SnackbarComponent,
    FormViewComponent,
    NotesSidebarComponent,
    FreeDragDirective,
    FilterPipe,
    NgxEditorModule,
    SafeHmtlPipe,
    SafeResourceurlPipe
  ],
  entryComponents: [ReEvaluationDialogComponent],
  providers: [
    { provide: MatPaginatorIntl, useClass: ExaiPaginator },
    {
      provide: DYNAMIC_FORM_CONTROL_MAP_FN,
      useValue: (
        model: DynamicFormControlModel
      ): Type<DynamicFormControl> | null => {
        switch (model.type) {
          case "FILE_UPLOAD":
            return DynamicFileUploadControlComponent;
            case "RATING":
            return DynamicFormUploadComponent;
             case "COLORPICKER":
            return DynamicFormLinkComponent;
        }
      },
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptorService,
      multi: true,
    },
    // { provide: HTTP_INTERCEPTORS, useClass: BufferInterceptor, multi: true },
  ],
})
export class CommonComponentModule {}
