export const environment = {
    production: true,
    name: "qa",
    baseUrl: "https://credentiaqatestapi.examroom.ai/",
    reportUrl:"https://reports.examroom.ai/",
    reportUrlapi:"https://reportapiv2.examroom.ai/",
    redirectUrl: "https://credentiaqatest.examroom.ai/",
    gistUrl: "https://credentiaqatest.examroom.ai/gis/validate-gisaccess",
    ScanUrl :'https://erlens.examroom.ai',
    NAERiredectorErrupt:"https://testapi.examroom.ai/",
    URLNotAllowedToken:'https://credentiaqatest.examroom.ai/api/EventOffer/GetEventOfferByEmail',
    SkillsAccessKey :'CNA.QATester',
    SkillsSecrectKey:"8cu75",
    RedirecttoSkillsExams:'https://skillsreviewqa.examroom.ai/',
    GisApiUrl:'https://credentiauat.examroom.ai/gisapis/api/',
   /// Secret and Api key for AI scanner upload////
   PayPalConfigurationId:"AURSiJyrCRnfnU6pVeuu4IQhELaQVOTGC5JALwdCYx2AllFyOB1inK36PY8QY7rxfAiDUuKXtbJHxi3v",
  
  // start 
  apiUrl: "https://webrtcqaapi.examroom.ai/",
  streamApiKey: "47095304",
  
  
  
   // end 
  
   username:"cnareviewer",
   password:"UfyHdq82GkKIBl2",
    AISecretKet:"D50F173D-0F28-410C-ACCB-186360190491",
    AIAPIKey:"2020439",
    config: {
      iceServers: [{
        urls: ["stun:ec2-13-59-174-201.us-east-2.compute.amazonaws.com"]
      },
      {
        username: "brucewayne",
        credential: "1234567890",
        urls: [
          "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=udp",
          "turn:ec2-13-59-174-201.us-east-2.compute.amazonaws.com:3478?transport=tcp",
        ]
      }
      ]
    }
  };