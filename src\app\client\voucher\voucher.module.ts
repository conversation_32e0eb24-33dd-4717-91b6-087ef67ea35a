import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { VoucherRoutingModule } from './voucher-routing.module';
import { BuyNewVoucherComponent } from './buy-new-voucher/buy-new-voucher.component';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { FlexLayoutModule } from '@angular/flex-layout';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { VoucherEffects } from '../voucher/state/voucher.effect';
import { VOUCHER_STATE_NAME } from '../voucher/state/voucher.selector';
import { VoucherDetails } from '../voucher/state/voucher.reducer';
import { VoucherComponent } from '../voucher/voucher.component';
import { TableModule } from '../../core/common-component/table/table.module';
import { VoucherListComponent } from './voucher-list/voucher-list.component';
import { AssignVoucherComponent } from './assign-voucher/assign-voucher.component';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { SingleEntryTableModule } from 'src/app/core/common-component/single-entry-table/single-entry-table.module';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
;


@NgModule({
  declarations: [
    BuyNewVoucherComponent,
    VoucherComponent,
    VoucherListComponent,
    AssignVoucherComponent,
    
  ],
  imports: [
    MatIconModule,
    CommonModule,
    MatInputModule,
    VoucherRoutingModule,
    NgDynamicBreadcrumbModule,
    FlexLayoutModule,
    PageLayoutModule,
    MatCardModule,
    MatFormFieldModule,
    MatOptionModule,
    MatSelectModule,
    MatButtonModule,
    TableModule,
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,

    SingleEntryTableModule,
    MatProgressSpinnerModule,
    MatAutocompleteModule,
    StoreModule.forFeature(VOUCHER_STATE_NAME, VoucherDetails),
    EffectsModule.forFeature([VoucherEffects]),
  ]
})
export class VoucherModule { }
