import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { exhaustMap, map, switchMap } from "rxjs/operators";
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { getcertificatelink, getDisNotesbyId, getFormsFilled, gotcertificatelink, gotFormsFilled, gotsavedNotes, imageuploaded, imageuploadinput, revokeMade, revokeRegistration, savedDisNotes, saveDisNotes, updatedisNotes, updateNotes } from "./cert.action";


@Injectable({
  providedIn: 'root',
})

export class CertEffects {
    public url:any;
    personDetail:any;
    constructor(
        public  http:HttpClient,
        public action$:Actions,
        private router:Router,
        private sanckServices: SnackbarService,
        private global:GlobalUserService
      ) {
       }

       

;



    // public certDetails$=createEffect(()=>{
    //   return this.action$.pipe(
    //     ofType(getcertificatelink),
    //     switchMap((action)=>{
    //         return this.http.get(`${environment.baseUrl}registry/api/certificate/get-url?certPath=${action.certpath}&certNo=${action.certNumber}`)
    //       .pipe(map((data:any)=>{
    //           return gotcertificatelink({certurl:data});
    //       }));
    //   }))
    // });

    public registryImageAPIcall$=createEffect(()=>{
      return this.action$.pipe(
        ofType(getcertificatelink),
        switchMap((action)=>{
          let header = new HttpHeaders();
          header= header.append('content-type', 'application/json');
            return this.http.post(`${environment.baseUrl}registry/api/Certificate/view-cetificate?registryId=${action.id}`,{},{responseType:'text'})
          .pipe(map((data:any)=>{
              
              return  gotcertificatelink ({certurl:data});
            }));  
          }))
        });


    public saveDis$=createEffect(()=>{
      return this.action$.pipe(
        ofType(saveDisNotes),
        switchMap((action)=>{
            return this.http.post(`${environment.baseUrl}client/api/form/AddNote`,action.notesData)
          .pipe(map((data:any)=>{
              return savedDisNotes({addedDataId:data});
          }));
      }))
    });

    public getDisNotes$=createEffect(()=>{
      return this.action$.pipe(
        ofType(getDisNotesbyId),
        switchMap((action)=>{
            return this.http.get(`${environment.baseUrl}client/api/form/getnotes?certificateId=${action.id}&noteTypeId=${action.noteTypeId}`)
          .pipe(map((data:any)=>{
              return gotsavedNotes({notesDetail:data});
          }));
      }))
    });

    public updateDis$=createEffect(()=>{
      return this.action$.pipe(
        ofType(updateNotes),
        switchMap((action)=>{
            return this.http.put(`${environment.baseUrl}client/api/form/updatenote`,action.UpdatednotesData)
          .pipe(map((data:any)=>{
              return updatedisNotes({viewnotes:data});
          }));
      }))
    });

    public revokeReg$=createEffect(()=>{
      return this.action$.pipe(
        ofType(revokeRegistration),
        switchMap((action)=>{
            return this.http.put(`${environment.baseUrl}registry/api/Certificate/update`,action.revoke)
          .pipe(map((data:any)=>{
              return revokeMade({revoked:data});
          }));
      }))
    });

    public fetchimageDetails$=createEffect(()=>{
      return this.action$.pipe(
        ofType(imageuploadinput),
        switchMap((action)=>{
            return this.http.post(`${environment.baseUrl}formmsvc/api/File/upload`,action.details)
          .pipe(map((data:any)=>{
              return imageuploaded({fileDetails:data});
          }));
      }))
    });

    public getFormsFilled$=createEffect(()=>{
      return this.action$.pipe(
        ofType(getFormsFilled),
        switchMap((action)=>{
          //https://devaccountapi.examroom.ai/client/api/form/personform?candidateId=417&formTypeId=9
            return this.http.get(`${environment.baseUrl}client/api/form/personform?candidateId=${action.personTenantRoleId}&formTypeId=${action.formTypeId}`)
          .pipe(map((data:any)=>{
              return gotFormsFilled({forms:data});
          }));
      }))
    });
    
  }