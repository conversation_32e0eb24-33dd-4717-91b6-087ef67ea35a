import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, switchMap, tap, toArray } from 'rxjs/operators';
import { Category } from './interfaces/category';
import { TicketForm } from './interfaces/ticketForm';
import { TicketDetail } from './interfaces/ticket-detail';
import { environment } from 'src/environments/environment';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { TicketExam } from './interfaces/ticket-exam';
import { recentLinkClickEventSelector } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { Store } from '@ngrx/store';

@Injectable({
  providedIn: 'root'
})
export class HelpService {

  constructor(
    private http: HttpClient,
    private globalUserService: GlobalUserService,
    private store:Store
  ) { }

  baseFormExamUrl: string = environment.baseUrl + 'client/api/Candidate/'
  baseCertificatesUrl: string = environment.baseUrl + 'registry/api/certificate/get-all'

  customerService: string = 'customerservice/'
  baseCategoriesUrl: string = `${environment.baseUrl}${this.customerService}api/`
  basePreviousTicketsUrl: string = `${environment.baseUrl}${this.customerService}api/tickets/`

  userId = null
  roleId = null
  candidateId = null
  personTenantRoleId = null
  createdBy = null
  assignedToRoleId = null;
  selectedTicketId = new BehaviorSubject(null)
  selectedCategoryOutput = new Subject();


  selectedCategory = new BehaviorSubject(null)
  categories = new BehaviorSubject(null)

  selectedTicket = new BehaviorSubject(null)
  tickets = new BehaviorSubject(null)
  tickets$: Observable<any[]> = this.tickets.asObservable()

  previousTicketsCategories = new BehaviorSubject([])
  previousTicketDetail = new BehaviorSubject([])
  previousTicketDetail$: Observable<any> = this.previousTicketDetail.asObservable()

  fetchCategories() {
    return this.http.get<Category[]>(this.baseCategoriesUrl + `tickets/categories`).pipe(
      tap(data => {
        let result = []
        data.forEach(el => {
          el.dataDetail = JSON.parse(el.dataDetail)
          result.push(el)
        })
        this.categories.next(result)
      })
    )
  }

  getcatorgies(){
     var url = `${environment.baseUrl}customerservice/api/tickets/categories`
     return this.http.get(url)
  }

  getTickets() {
    this.store.select(recentLinkClickEventSelector).subscribe((data) => {
      if (data) {
        this.candidateId = data.element.personTenantRoleId;
      }
    })
    return this.selectedCategory.pipe(
      switchMap((currentCategory: Category) => {
        let rightPartUrl
        let leftPartUrl
        if (currentCategory.name === 'Application') {
          leftPartUrl = this.baseFormExamUrl
          // rightPartUrl = `${this.globalUserService.userDetails.value.roles[0].personTenantRoleId}/forms-filled`
          rightPartUrl = `${this.candidateId}/forms-filled`
        } else if (currentCategory.name === "Registering for Exam") {
          leftPartUrl = this.baseFormExamUrl
          // rightPartUrl = `${this.candidateId}/registered-exams`
          rightPartUrl = `${this.candidateId}/registered-exams`
        } 
     

        return this.http.get<TicketForm[]  | TicketExam[]>(leftPartUrl + rightPartUrl).pipe(
          tap(data => {
           this.tickets.next(data)
          })
        )
      })
    )
  }

  getTicket(id: string) {
    return this.tickets$.pipe(
      map((data) => {
        let result = []
        result = data.filter(el => {
          if (el.code == id) {
            return true
          } else if (el.id == id) {
            return true
          } else if (el.Id == id) {
            return true
          }
        })
        this.selectedTicket.next(result)
        return result
      })
    )
  }

  raiseTicket(formData) {
    return this.http.post(this.basePreviousTicketsUrl, formData)
  }

  getPreviousTickets() {
    return this.http.get(this.basePreviousTicketsUrl).pipe(
      map((data: any) => {
        let result = []
        data.forEach(el => {
          result.push({
            subject: el.subject,
            id: el.id
          })
        })
        return result
      })
    )
  }

  getPreviousTicketDetail(id: number): Observable<TicketDetail[]> {
    return this.http.get<TicketDetail>(this.basePreviousTicketsUrl + `${id}/detail`).pipe(toArray())
  }
}
