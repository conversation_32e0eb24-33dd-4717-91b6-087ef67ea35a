import { Component, Inject, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { GivanceViewService, GrievanceAction } from 'src/app/core/grivance-view';
import { SnackbarService } from 'src/app/core/snackbar.service';

export function noWhitespaceValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': 'value is only whitespace' };
  };
}

@Component({
  selector: 'app-qac-accomodation',
  templateUrl: './qac-accomodation.component.html',
  styleUrls: ['./qac-accomodation.component.scss']
})
export class QacAccomodationComponent implements OnInit {

  accomodationList : GrievanceAction[];
  formGroup:FormGroup;
  

  constructor(private dialogRef: MatDialogRef<QacAccomodationComponent>,
    private givanceViewService:GivanceViewService,
    private global:GlobalUserService,
    private snackbarService:SnackbarService,
    private fb:FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data,
  ) {
    this.formGroup = this.fb.group({
      accomodationtype:[ null,Validators.required],
      response:[ this.data?.id == 1?this.data.items.accomodationItems[0]:'',[Validators.required,noWhitespaceValidator()]]
    });

    
  }

  ngOnInit() {
    this.givanceViewService.getGrievanceActionsList("AC",this.global.personEventId.personFormId).subscribe(data => {
      this.accomodationList = data;
    },
    error => {
      this.snackbarService.callSnackbaronError(error)
    });

    if(this.global.personEventId.statusId != 2){
      this.formGroup.disable();
    }
    
    if(this.global.personEventId.statusId != 2){
      this.formGroup.disable();
    }
    
    this.givanceViewService.getAccomodationQac(this.global.personEventId.personFormId).subscribe((data : any) => {
      if(data){
        const grievanceActionIds = data.grievanceAction.map((action:any) => action.grievanceActionId);
        this.formGroup.patchValue({
          accomodationtype:grievanceActionIds,
          response:data.notes
        })
      }
    },
    error => {
      this.snackbarService.callSnackbaronError(error.message.error)
    });
  }

  close(){
    this.dialogRef.close();
  }

  save(){
    this.givanceViewService.addAccomodationQac({
      personFormId: this.global.personEventId.personFormId,
      grievanceActionId: this.formGroup.get('accomodationtype').value,
      notes: this.formGroup.get('response').value,
      createdBy: this.global.userDetails.value.personTenantRoleId,
      modifiedBy: this.global.userDetails.value.personTenantRoleId
    }).subscribe(data => {
      if(data.success) {
        this.snackbarService.callSnackbaronSuccess(data.message);
        this.dialogRef.close();
      }
    }, 
    err => {
      this.snackbarService.callSnackbaronError(err);
    });
  }
}
