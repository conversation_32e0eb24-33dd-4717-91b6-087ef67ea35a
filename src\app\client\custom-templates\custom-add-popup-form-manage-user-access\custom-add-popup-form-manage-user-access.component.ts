import { Component, OnInit, Input, Output, EventEmitter } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from "@angular/forms";
import { HttpClient } from "@angular/common/http";
import { MatSnackBar } from "@angular/material/snack-bar";
import { GlobalUserService } from "src/app/core/global-user.service";
import { Observable, of } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
  startWith,
  map,
  catchError,
} from "rxjs/operators";
import { MatDialogRef } from "@angular/material/dialog";
import {
  button,
  customPopup,
  submitEvent,
} from "src/app/core/common-component/pop-up/pop-up.types";
import { environment } from "src/environments/environment";
import lodash from "lodash";
import { Action, Store } from "@ngrx/store";
import { SnackbarService } from "src/app/core/snackbar.service";
import { HttpService } from "src/app/core/http.service";
import { creationSuccessfull } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import {
  requestDetails,
  requestParam,
  Roles,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { URL } from "src/app/core/url";
import { forkJoin } from "rxjs";

@Component({
  selector: "app-custom-add-popup-form-manage-user-access",
  templateUrl: "./custom-add-popup-form-manage-user-access.component.html",
  styleUrls: ["./custom-add-popup-form-manage-user-access.component.scss"],
})
export class CustomAddPopupFormManageUserAccessComponent
  implements OnInit, customPopup
{
  @Input() data: any;
  @Input() isEdit: boolean = false;
  @Output() formSubmit: EventEmitter<any> = new EventEmitter();
  @Output() formCancel: EventEmitter<any> = new EventEmitter();

  @Input() preResponse?: any = null;
  @Input() buttons?: Array<button>;
  @Input() dialogRef: MatDialogRef<any>;
  @Output() closeOutputEvent: EventEmitter<submitEvent> =
    new EventEmitter<submitEvent>();

  roles: any[] = [];
  users: any[] = [];
  states: any[] = [];
  isLoading: boolean = false;
  // userData: any;
  hide: boolean = false;

  Array = Array;
  Roles = Roles;
  userData: any;
  form: FormGroup;
  showAddress: boolean = false;
  rolesId: boolean = false;
  userRoles: Array<any> = [];
  isManageApplications;
  statesOrTrainingIns: Array<any> = [];
  CodeDetails: {
    tenantCode: string;
    testCenterId: number;
    testcenterGuid: string;
    testcenterName: string;
  };
  testCenterId: Array<any> = [];
  stateActive: boolean = false;
  showTrainingInstitute: boolean = false;
  trainingInstitutes: Array<any> = [];
  selectedTrainingInstitutes: Array<any> = []; // Array to hold selected training institutes for display
  trainingInstituteDisplayText: string = ""; // Comma-separated display text
  filteredTrainingInstitutes: Observable<any[]>;
  trainingInstituteControl = new FormControl();
  loadingTrainingInstitute: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private globalUserService: GlobalUserService,
    private services: SnackbarService,
    private store: Store,
    private https: HttpService
  ) {}

  ngOnInit(): void {
    // Initialize form first
    this.initializeForm();

    this.globalUserService.userDetails.subscribe((data) => {
      if (data) {
        this.userData = data;
        // Load roles and states data
        this.loadRolesAndStates();
      }
    });
  }

  initializeForm() {
    this.form = this.formBuilder.group({
      id: [
        this.preResponse ? this.preResponse.personId || this.preResponse.id : 0,
      ],
      firstName: [
        this.preResponse ? this.preResponse.firstName : "",
        // Removed validators since field is read-only
      ],
      middleName: [this.preResponse ? this.preResponse.middleName : ""],
      lastName: [
        this.preResponse ? this.preResponse.lastName : "",
        // Removed validators since field is read-only
      ],
      email: [
        this.preResponse ? this.preResponse.emailId : "",
        // Removed validators since field is read-only
      ],
      role: [[], [Validators.required]],
      tenantId: [
        {
          value: this.preResponse
            ? this.preResponse.stateId || this.preResponse.tenantId
            : "",
          disabled: true, // Make state field non-editable
        },
      ],
      trainingInstitute: [
        [], // Changed to array to support multiple training institutes
        // Validation will be added dynamically based on selected roles
      ],
      ClientStateCode: [""],
    });

    // Populate role data if available
    if (this.preResponse && this.preResponse.userRoles) {
      const roleIds = this.preResponse.userRoles.map(
        (role: any) => role.roleId
      );
      this.form.patchValue({ role: roleIds });

      // Populate training institutes if training institute role is selected
      if (roleIds.includes(15)) {
        // Training Institute role ID
        this.showTrainingInstitute = true;
        this.checkTrainingInstituteRole(roleIds);
      }
    }
  }

  loadRolesAndStates() {
    forkJoin([
      // Try the general userroles endpoint first (should return all roles)
      this.http.get<any>(URL.BASE_URL + `Account/userroles`),
      this.http.get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
    ]).subscribe(
      (responses: Array<any>) => {
        this.userRoles = responses[0];
        this.statesOrTrainingIns = responses[1];

        // After loading data, populate the form if we have preResponse data
        if (this.preResponse) {
          this.populateFormWithData();
        }
      },
      (error) => {
        console.warn(
          "Failed to load from userroles endpoint, falling back to manageuserroles:",
          error
        );
        // Fallback to the original API if the new one fails
        this.loadRolesAndStatesFallback();
      }
    );

    // Set up form change listeners
    if (this.preResponse) {
      this.form.controls.role.valueChanges.subscribe(
        (selectedRoles: number[]) => {
          this.fetchStateORTrnIns();
        }
      );
    }
  }

  loadRolesAndStatesFallback() {
    forkJoin([
      this.http.get<any>(
        URL.BASE_URL +
          `Account/manageuserroles?personTenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
      this.http.get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      ),
    ]).subscribe((responses: Array<any>) => {
      this.userRoles = responses[0];
      this.statesOrTrainingIns = responses[1];

      // After loading data, populate the form if we have preResponse data
      if (this.preResponse) {
        this.populateFormWithData();
      }
    });
  }

  populateFormWithData() {
    if (!this.preResponse) return;

    // Extract role IDs from userRoles array
    const roleIds = this.preResponse.userRoles
      ? this.preResponse.userRoles.map((role: any) => role.roleId)
      : [];

    // Find the state ID - could be stateId or tenantId
    const stateId = this.preResponse.stateId || this.preResponse.tenantId;

    // Update form with the correct data
    this.form.patchValue({
      id: this.preResponse.personId || this.preResponse.id,
      firstName: this.preResponse.firstName || "",
      middleName: this.preResponse.middleName || "",
      lastName: this.preResponse.lastName || "",
      email: this.preResponse.emailId || "",
      role: roleIds,
      tenantId: stateId,
    });

    // Check if user's roles exist in available roles
    const availableRoleIds = this.userRoles.map((role) => role.id);
    const missingRoles = roleIds.filter(
      (roleId) => !availableRoleIds.includes(roleId)
    );
    const validRoles = roleIds.filter((roleId) =>
      availableRoleIds.includes(roleId)
    );

    if (missingRoles.length > 0) {
      console.warn(
        "⚠️ Some user roles are not available in the dropdown:",
        missingRoles
      );
      console.warn("Available role IDs:", availableRoleIds);
      console.warn("User role IDs:", roleIds);
    }

    // Only set roles that are actually available in the dropdown
    const rolesToSet = validRoles.length > 0 ? validRoles : roleIds;

    // Force change detection for role dropdown
    setTimeout(() => {
      this.form.get("role")?.setValue(rolesToSet);

      const selectedOptions = this.userRoles.filter((role) =>
        rolesToSet.includes(role.id)
      );

      // Check if Training Institute role is selected and show/hide field accordingly
      this.checkTrainingInstituteRole(rolesToSet);

      // If training institute field should be shown, try to populate it with existing data
      if (this.showTrainingInstitute) {
        this.populateTrainingInstituteField();
      }
    }, 100);
  }

  checkTrainingInstituteRole(selectedRoles: number[]) {
    // Training Institute role ID is 15 (from global service)
    const trainingInstituteRoleId = 15;
    this.showTrainingInstitute = selectedRoles.includes(
      trainingInstituteRoleId
    );

    // Update validation based on training institute role selection
    const trainingInstituteControl = this.form.get("trainingInstitute");
    if (this.showTrainingInstitute) {
      // Make training institute mandatory when role is selected
      trainingInstituteControl?.setValidators([
        Validators.required,
        this.arrayNotEmptyValidator,
      ]);
      this.setupTrainingInstituteAutocomplete();
    } else {
      // Remove validation when role is not selected
      trainingInstituteControl?.clearValidators();
      trainingInstituteControl?.setValue([]); // Clear the array
    }
    trainingInstituteControl?.updateValueAndValidity();
  }

  // Custom validator to ensure array is not empty
  arrayNotEmptyValidator(control: any) {
    const value = control.value;
    if (Array.isArray(value) && value.length === 0) {
      return { required: true };
    }
    return null;
  }

  populateTrainingInstituteField() {
    if (!this.preResponse) return;

    // Handle multiple training institute IDs from userRoles
    const trainingInstituteIds = this.getTrainingInstituteIdsFromUserRoles();

    // Only populate if there are training institute IDs
    if (!trainingInstituteIds || trainingInstituteIds.length === 0) {
      console.log("No training institute IDs found, leaving field blank");
      return;
    }

    // Show loading spinner
    this.loadingTrainingInstitute = true;

    // Get the state ID for the search
    const stateId =
      this.form.get("tenantId")?.value || this.preResponse?.stateId || 4;

    // Fetch details for all training institutes
    this.fetchMultipleTrainingInstitutes(trainingInstituteIds, stateId);
  }

  getTrainingInstituteIdsFromUserRoles(): number[] {
    if (!this.preResponse?.userRoles) return [];

    // Find all training institute roles (roleId = 15) and extract their tenantIds
    const trainingInstituteRoles = this.preResponse.userRoles.filter(
      (role: any) => role.roleId === 15
    );

    return trainingInstituteRoles
      .map((role: any) => role.tenantId)
      .filter((id: number) => id);
  }

  fetchMultipleTrainingInstitutes(
    trainingInstituteIds: number[],
    stateId: number
  ) {
    const fetchPromises = trainingInstituteIds.map((id) =>
      this.fetchSingleTrainingInstituteData(id, stateId)
    );

    Promise.all(fetchPromises)
      .then((results) => {
        const validInstitutes = results.filter(
          (institute) => institute !== null
        );

        if (validInstitutes.length > 0) {
          // Set the form value to array of IDs
          this.form.patchValue({
            trainingInstitute: validInstitutes.map((inst) => inst.id),
          });

          // Set the selected training institutes for display
          this.selectedTrainingInstitutes = validInstitutes;

          // Update the display text
          this.updateTrainingInstituteDisplayText();
        }

        this.loadingTrainingInstitute = false;
      })
      .catch(() => {
        this.loadingTrainingInstitute = false;
      });
  }

  async fetchSingleTrainingInstituteData(
    trainingInstituteId: number,
    stateId: number
  ): Promise<any> {
    try {
      // Try direct lookup first
      const directUrl = `${URL.BASE_URL}traininginstitute/${trainingInstituteId}`;
      const institute = await this.http.get<any>(directUrl).toPromise();

      if (institute && (institute.id || institute.trainingInstituteId)) {
        return {
          id: institute.id || institute.trainingInstituteId,
          trainingInstituteId: institute.id || institute.trainingInstituteId,
          name: institute.title || institute.name || institute.organizationName,
          title:
            institute.title || institute.name || institute.organizationName,
          stateId: institute.stateId,
          stateName: institute.stateName,
        };
      }
    } catch (error) {
      // Fallback to search method
      try {
        const searchUrl = `${URL.BASE_URL}traininginstitute/search-trainingprogram?stateId=${stateId}&searchText=`;
        const institutes = await this.http.get<any[]>(searchUrl).toPromise();
        const found = institutes?.find(
          (inst) => inst.id === trainingInstituteId
        );

        if (found) {
          return {
            id: found.id,
            trainingInstituteId: found.id,
            name: found.title,
            title: found.title,
            stateId: found.stateId,
            stateName: found.stateName,
          };
        }
      } catch (searchError) {
        console.error(
          "Failed to fetch training institute:",
          trainingInstituteId,
          searchError
        );
      }
    }

    return null;
  }

  fetchTrainingInstituteById(trainingInstituteId: number, stateId: number) {
    // First try the direct endpoint that we know works
    const directUrl = `${URL.BASE_URL}traininginstitute/${trainingInstituteId}`;

    this.http.get<any>(directUrl).subscribe(
      (institute: any) => {
        if (institute && (institute.id || institute.trainingInstituteId)) {
          const trainingInstituteData = {
            id: institute.id || institute.trainingInstituteId,
            trainingInstituteId: institute.id || institute.trainingInstituteId,
            name:
              institute.title || institute.name || institute.organizationName,
            title:
              institute.title || institute.name || institute.organizationName,
            stateId: institute.stateId,
            stateName: institute.stateName,
          };

          this.trainingInstituteControl.setValue(trainingInstituteData);
          this.form.patchValue({
            trainingInstitute: trainingInstituteData.id,
          });
          this.loadingTrainingInstitute = false;
        } else {
          // Fallback to search method if direct lookup fails
          this.fetchTrainingInstituteBySearch(trainingInstituteId, stateId);
        }
      },
      () => {
        // Fallback to search method if direct endpoint fails
        this.fetchTrainingInstituteBySearch(trainingInstituteId, stateId);
      }
    );
  }

  fetchTrainingInstituteBySearch(trainingInstituteId: number, stateId: number) {
    // Fallback: Try to find training institute using search endpoint
    const searchUrl = `${URL.BASE_URL}traininginstitute/search-trainingprogram?stateId=${stateId}&searchText=`;

    this.http.get<any[]>(searchUrl).subscribe(
      (trainingInstitutes: any[]) => {
        const selectedInstitute = trainingInstitutes.find(
          (institute) => institute.id === trainingInstituteId
        );

        if (selectedInstitute) {
          const trainingInstituteData = {
            id: selectedInstitute.id,
            trainingInstituteId: selectedInstitute.id,
            name: selectedInstitute.title,
            title: selectedInstitute.title,
            stateId: selectedInstitute.stateId,
            stateName: selectedInstitute.stateName,
          };

          this.trainingInstituteControl.setValue(trainingInstituteData);
          this.form.patchValue({
            trainingInstitute: selectedInstitute.id,
          });
          this.loadingTrainingInstitute = false;
        } else {
          // Only search other states if not found in current state
          this.searchTrainingInstituteInAllStates(trainingInstituteId);
        }
      },
      () => {
        // Final fallback
        this.populateTrainingInstituteFieldFallback(trainingInstituteId);
      }
    );
  }

  searchTrainingInstituteInAllStates(trainingInstituteId: number) {
    const statesToSearch = this.statesOrTrainingIns || [];

    if (statesToSearch.length === 0) {
      this.populateTrainingInstituteFieldFallback(trainingInstituteId);
      return;
    }

    // Limit search to first 3 states to avoid too many API calls
    const limitedStates = statesToSearch.slice(0, 3);
    let foundInstitute = false;

    // Use Promise.race to return as soon as we find the institute
    const searchPromises = limitedStates.map((state) => {
      const searchUrl = `${URL.BASE_URL}traininginstitute/search-trainingprogram?stateId=${state.id}&searchText=`;

      return this.http
        .get<any[]>(searchUrl)
        .toPromise()
        .then((institutes: any[]) => {
          const found = institutes.find(
            (inst) => inst.id === trainingInstituteId
          );
          if (found && !foundInstitute) {
            foundInstitute = true;

            const trainingInstituteData = {
              id: found.id,
              trainingInstituteId: found.id,
              name: found.title,
              title: found.title,
              stateId: found.stateId,
              stateName: found.stateName,
            };

            this.trainingInstituteControl.setValue(trainingInstituteData);
            this.form.patchValue({
              trainingInstitute: found.id,
            });
            this.loadingTrainingInstitute = false;
            return found;
          }
          return null;
        })
        .catch(() => null);
    });

    // Wait for all searches but timeout after 5 seconds
    Promise.race([
      Promise.all(searchPromises),
      new Promise((resolve) => setTimeout(resolve, 5000)),
    ]).then(() => {
      if (!foundInstitute) {
        this.populateTrainingInstituteFieldFallback(trainingInstituteId);
      }
    });
  }

  populateTrainingInstituteFieldFallback(trainingInstituteId: number) {
    // Look for training institute role in userRoles to get more context
    const trainingInstituteRole = this.preResponse?.userRoles?.find(
      (role: any) => role.roleId === 15 && role.tenantId === trainingInstituteId
    );

    let trainingInstituteName =
      this.preResponse?.trainingInstituteName ||
      this.preResponse?.trainingInstitute?.name ||
      this.preResponse?.trainingInstitute?.title ||
      this.preResponse?.trainingInstituteTitle;

    // If no name found but we have the role, create a meaningful name
    if (!trainingInstituteName && trainingInstituteRole) {
      trainingInstituteName = `Selected Training Institute (ID: ${trainingInstituteId})`;
      // Try to fetch the organization/tenant name if possible
      this.fetchTrainingInstituteNameByTenantId(trainingInstituteId);
    } else if (!trainingInstituteName) {
      trainingInstituteName = `Training Institute (ID: ${trainingInstituteId})`;
    }

    const trainingInstituteData = {
      id: trainingInstituteId,
      trainingInstituteId: trainingInstituteId,
      name: trainingInstituteName,
      title: trainingInstituteName,
    };

    this.trainingInstituteControl.setValue(trainingInstituteData);
    this.form.patchValue({
      trainingInstitute: trainingInstituteId,
    });
    this.loadingTrainingInstitute = false;
  }

  fetchTrainingInstituteNameByTenantId(tenantId: number) {
    // Try the working endpoint that we discovered
    const endpoint = `${URL.BASE_URL}traininginstitute/${tenantId}`;

    this.http.get<any>(endpoint).subscribe(
      (data: any) => {
        const name = data?.title || data?.name || data?.organizationName;

        if (name) {
          const updatedTrainingInstituteData = {
            id: tenantId,
            trainingInstituteId: tenantId,
            name: name,
            title: name,
          };

          this.trainingInstituteControl.setValue(updatedTrainingInstituteData);
        }
        this.loadingTrainingInstitute = false;
      },
      () => {
        // Silently fail - keep the fallback name
        this.loadingTrainingInstitute = false;
      }
    );
  }

  setupTrainingInstituteAutocomplete() {
    this.filteredTrainingInstitutes =
      this.trainingInstituteControl.valueChanges.pipe(
        startWith(""),
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((value) => {
          if (typeof value === "string" && value.length >= 2) {
            return this.searchTrainingInstitutes(value).pipe(
              map((institutes) => {
                // Filter out already selected training institutes
                const selectedIds = this.selectedTrainingInstitutes.map(
                  (inst) => inst.id || inst.trainingInstituteId
                );
                return institutes.filter(
                  (inst) =>
                    !selectedIds.includes(inst.id || inst.trainingInstituteId)
                );
              })
            );
          } else {
            return of([]);
          }
        })
      );
  }

  searchTrainingInstitutes(searchText: string): Observable<any[]> {
    const stateId =
      this.form.get("tenantId")?.value || this.preResponse?.stateId || 4;

    return this.https.searchTrainingInstitutes(stateId, searchText).pipe(
      map((response: any) => {
        if (!response || response === null || response === undefined) {
          return [];
        }

        if (Array.isArray(response) && response.length === 0) {
          // Show user-friendly message when no training institutes found
          this.services.callSnackbaronError(
            `No training institutes found matching "${searchText}"`
          );
          return [];
        }

        if (Array.isArray(response)) {
          return response;
        }

        return [];
      }),
      catchError((error) => {
        // Show specific error message for training institute search failures
        console.error("Training institute search error:", error);
        this.services.callSnackbaronError(
          `Training institute "${searchText}" does not exist or could not be found`
        );
        return of([]);
      })
    );
  }

  onTrainingInstituteSelected(selectedInstitute: any) {
    const currentValues = this.form.get("trainingInstitute")?.value || [];
    const instituteId =
      selectedInstitute.id || selectedInstitute.trainingInstituteId;

    // Add to array if not already present
    if (!currentValues.includes(instituteId)) {
      const updatedValues = [...currentValues, instituteId];
      this.form.patchValue({
        trainingInstitute: updatedValues,
      });

      // Add to selected training institutes for display
      this.selectedTrainingInstitutes = [
        ...this.selectedTrainingInstitutes,
        selectedInstitute,
      ];

      // Update the display text with comma-separated names
      this.updateTrainingInstituteDisplayText();
    }

    // Clear the search input
    this.trainingInstituteControl.setValue("");
  }

  removeTrainingInstitute(instituteToRemove: any) {
    const currentValues = this.form.get("trainingInstitute")?.value || [];
    const instituteId =
      instituteToRemove.id || instituteToRemove.trainingInstituteId;

    // Remove from form array
    const updatedValues = currentValues.filter(
      (id: number) => id !== instituteId
    );
    this.form.patchValue({
      trainingInstitute: updatedValues,
    });

    // Remove from selected training institutes display array
    this.selectedTrainingInstitutes = this.selectedTrainingInstitutes.filter(
      (inst: any) => (inst.id || inst.trainingInstituteId) !== instituteId
    );

    // Update the display text
    this.updateTrainingInstituteDisplayText();
  }

  updateTrainingInstituteDisplayText() {
    // Create comma-separated display text
    const displayText = this.selectedTrainingInstitutes
      .map((inst) => inst.name || inst.title)
      .join(", ");

    // Update a separate display control or property
    this.trainingInstituteDisplayText = displayText;
  }

  getTrainingInstituteDisplayText(): string {
    return this.selectedTrainingInstitutes
      .map((inst) => inst.name || inst.title)
      .join(", ");
  }

  clearAllTrainingInstitutes() {
    this.selectedTrainingInstitutes = [];
    this.form.patchValue({ trainingInstitute: [] });
    this.updateTrainingInstituteDisplayText();
  }

  displayTrainingInstitute(institute: any): string {
    if (!institute) return "";
    return institute.title || institute.name || "";
  }

  fetchStateORTrnIns(x: number = Roles.TrainingInstitue) {
    this.http
      .get<any>(
        URL.BASE_URL +
          `state/states/persontenantroleid?persontenantRoleId=${this.globalUserService.userDetails.value.personTenantRoleId}&showAll=true`
      )
      .subscribe((response: any) => {
        this.statesOrTrainingIns = response;
      });
  }

  state(event) {
    if (this.preResponse == null) {
      let states = this.statesOrTrainingIns.filter((x) => x.id === event.value);
      this.rolesId == true
        ? this.http
            .post(
              `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
              {}
            )
            .subscribe((data: Array<object>) => {
              if (data.length > 0) {
                this.testCenterId = data;
              }
            })
        : null;
    } else {
      setTimeout(() => {
        let states = this.statesOrTrainingIns.filter(
          (x) => x.id === event.value
        );
        this.rolesId == true
          ? this.http
              .post(
                `${environment.baseUrl}client/api/exam/GetTestCentresForCredentia?stateCode=${states[0].stateCode}`,
                {}
              )
              .subscribe((data: Array<object>) => {
                if (data.length > 0) {
                  this.testCenterId = data;
                }
              })
          : null;
      }, 1500);
    }
  }

  selectedRole(event: Event | any) {
    const selectedRoles: number[] = event?.value || [];
    const has = (ids: number[]) => selectedRoles.some((id) => ids.includes(id));
    const hasRole = (id: number) => selectedRoles.includes(id);

    // Check if Training Institute role is selected
    this.checkTrainingInstituteRole(selectedRoles);

    // If training institute field should be shown and we have existing data, populate it
    if (this.showTrainingInstitute && this.preResponse) {
      setTimeout(() => {
        this.populateTrainingInstituteField();
      }, 100);
    }
    if (has([17, 13, 8, 28])) {
      this.showAddress = false;
      this.stateActive = false;

      // Preserve the existing tenantId (state) when updating other fields
      const currentTenantId = this.form.get("tenantId")?.value;
      const preserveTenantId = this.preResponse
        ? currentTenantId ||
          this.preResponse.stateId ||
          this.preResponse.tenantId
        : this.globalUserService.userDetails.value.clientORtenantId;

      this.form.patchValue({
        address: "null",
        License: "null",
        cantest: true,
        zipcode: "null",
        city: "null",
        tenantId: preserveTenantId,
      });
    } else {
      this.stateActive = true;
      this.rolesId = hasRole(4);
      this.form.patchValue(
        hasRole(4)
          ? {
              address: "",
              License: "null",
              cantest: true,
              zipcode: null,
              city: "",
            }
          : hasRole(22)
          ? {
              address: "",
              License: "",
              cantest: false,
              zipcode: null,
              city: "",
            }
          : {
              address: "null",
              License: "null",
              cantest: true,
              zipcode: "null",
              city: "null",
            }
      );
    }
    this.showAddress = has([22, 4]);
  }

  testcenterCode(code) {
    this.CodeDetails = code;
  }

  submit(requestDetails: requestDetails, index: number) {
    if (this.preResponse == null) {
      requestDetails?.requestParams?.forEach((x: requestParam) => {
        if (x.extractedFromGlobal) {
          x.paramValue =
            this.globalUserService.userDetails.value[
              x.elementPropertyToBeExtracted
            ];
        }
      });

      var formValueClone = lodash.cloneDeep(this.form.value);
      formValueClone.role = formValueClone.role;
      const selectedRoles: number[] = this.form.value.role || [];
      const hasRole = (id: number) => selectedRoles.includes(id);
      formValueClone = {
        ...formValueClone,
        testcenterGuid: this.CodeDetails?.testcenterGuid?.trim() || "",
        tenantCode: this.CodeDetails?.tenantCode?.trim() || "",
        testcenterName: this.CodeDetails?.testcenterName?.trim() || "",
        ClientStateCode:
          formValueClone.tenantId && !hasRole(Roles.TrainingInstitue)
            ? !hasRole(Roles.Employer)
              ? this.statesOrTrainingIns.find(
                  (x: any) => x.id === formValueClone.tenantId
                )?.stateCode
              : this.globalUserService.userDetails.value.stateCode
            : this.globalUserService.userDetails.value.stateCode,
      };

      this.closeOutputEvent.emit({
        response: formValueClone,
        requestDetails,
        index,
      });

      setTimeout(() => {
        this.store.dispatch<Action>(creationSuccessfull());
      }, 1500);
    } else {
      // Prepare request body for the new API endpoint
      const trainingInstituteIds = this.form.value.trainingInstitute || [];

      const requestBody = {
        roleId: this.form.value.role || [],
        clientStateCode: this.globalUserService.userDetails.value.stateCode
          ? this.globalUserService.userDetails.value.stateCode
          : this.statesOrTrainingIns.find(
              (x: any) => x.id === this.preResponse.stateId
            )?.stateCode || "",
        trainingInstituteId: Array.isArray(trainingInstituteIds)
          ? trainingInstituteIds.filter((id) => id && id !== 0) // Filter out invalid IDs
          : trainingInstituteIds
          ? [trainingInstituteIds]
          : [], // Convert single value to array
      };

      // Get user email for the endpoint URL
      const emailId = this.preResponse.emailId || "";

      this.https.manageUserRoleUpdate(emailId, requestBody).subscribe(
        (response: any) => {
          if (response && response.success) {
            this.dialogRef.close();
            this.services.callSnackbaronSuccess(
              response.message || "Update Successful"
            );
          } else {
            this.services.callSnackbaronError(
              response.message || "Update failed"
            );
          }
          this.store.dispatch<Action>(creationSuccessfull());
        },
        (error: any) => {
          console.error("API Error:", error);

          // Provide specific error messages for common issues
          let errorMessage = "An error occurred while updating user access";

          if (error?.error?.message) {
            const apiError = error.error.message.toLowerCase();
            if (apiError.includes("user does not exist")) {
              errorMessage =
                "The user you are trying to edit no longer exists in the system. The user may have been deleted or deactivated.";
            } else if (
              apiError.includes("training institute") ||
              apiError.includes("traininginstitute")
            ) {
              errorMessage =
                "The selected training institute is invalid or does not exist. Please select a valid training institute.";
            } else if (apiError.includes("inner exception")) {
              errorMessage =
                "There was a validation error with the submitted data. Please check all fields and try again.";
            } else {
              errorMessage = error.error.message;
            }
          } else if (error?.message) {
            const errorMsg = error.message.toLowerCase();
            if (errorMsg.includes("user does not exist")) {
              errorMessage =
                "The user you are trying to edit no longer exists in the system. The user may have been deleted or deactivated.";
            } else {
              errorMessage = error.message;
            }
          } else if (error?.status === 500 && error?.statusText) {
            // Handle cases where the error message might be in the response body as plain text
            if (
              error.error &&
              typeof error.error === "string" &&
              error.error.toLowerCase().includes("user does not exist")
            ) {
              errorMessage =
                "The user you are trying to edit no longer exists in the system. The user may have been deleted or deactivated.";
            }
          }

          this.services.callSnackbaronError(errorMessage);
        }
      );
    }
  }

  // Helper method to mark all controls as touched
  markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
