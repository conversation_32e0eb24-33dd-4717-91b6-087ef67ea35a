.dashboard-container {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  .dashboard-content {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  
  .dashboard-component-chart-holder{
    width: 629px !important;
    height: 619px !important;
  }
  
  .selectedView {
    color: #ee9400;
    border: 1px solid #ee9400;
  }
  .NonselectedView{
    color: black;
  }
  
  .SelectedName{
    font-size: 1.85rem;
      margin-top: 45px
  }
  
  .color {
    color: white;
  }
  
  .scrollWrapper {
    width: 100%;
    overflow: auto;
  }
  
  .content-box {
    padding: 1rem;
    border: 1px solid rgb(226, 222, 222);
    background: #1c75bc;
    box-sizing: content-box;
    display: inline-block;
    border-radius: 4px;
  }
  
  .selectedView {
    color: #ee9400;
    border: 1px solid #ee9400;
  }
  .NonselectedView{
    color: black;
  }
  
  .report-img {
    overflow-x: auto !important;
    overflow-y: hidden;
    height: 1000px;
  }
  
  .custom{
    width:50%;
    background-color: white;
  }
  
  .expand {
    height: 80vh;
  }
  
  .expand1 {
    height: 100vh;
    padding-bottom: 10%;
  }
  
  .color-1{
    background-color: #eaedf2ce;
  }
  
  .mat-icon{
    height : 20px;
    width: 20px;
  }
  
  
  .close{
    padding-left: 98%;
    color: #0076C1;
  }
  
  .customButtom{
    margin: 20px;
  }
  
  .addReport{
    margin-top: 20px;
    display: flex;
    width: 100%;
    justify-content: center;
  }
  
  .addReport1{
    display: flex;
    width: 100%;
    justify-content: center;
    color: #0076C1;
  }
  
  .customadd{
    padding:20px;
    display: flex;
    justify-content: center;
    color: #0076C1;
  }
  
  .customInput{
    width:50%;
    margin:10px;
    padding:10px
  }
  
  .customInput1{
    padding:15px
  }
  .customButton{
    padding:20px;
    background-color: #0076C1;
  }
  
  .search {
    height:fit-content;
  }
  
  .mat-icon {
    height: 15px;
    width: 15px;
  }
  
  .cardBorder {
    border-radius: var(--border-radius);
  }
  
  .color {
    color: var(--status-color);
  }
  .mat-icon {
    height: 16px;
    width: 16px;
    font-size: medium;
  }
  .SelectedName{
    font-size: 1.85rem;
      margin-top: 45px
  }
  
  