import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, State, statuses, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";
export const moduleJSON: consoleLoaderJSON = {
    modules: [

        {
            type: moduleTypes.Dynamic,
            name: "Payment Refund",
            moduleID: "payment-refund",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageApplicationIcon",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ["tab-col"],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter: true,
                                    showAddButton: [false,false,false,false,false],
                                    showMatTabs: true,
                                    showColumnWiseFilter: true,
                                    ShowDownloadPayment: true,
                                    matTabs: [
                                      { id: 0, name: "Credit/ Debit card details" },
                                      { id: 1, name: "Paypal transaction details" },
                                    ],
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                     isPaginationServerSide: [true,true],
                                     isSearchServerSide: [true,true],
                                },

                                displayedColumns: [
                                  [
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "checkbox",
                                      name: "Checkbox",
                                      cellType: "text",
                                      headerType: "checkbox",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-4"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "transId",
                                      name: "Transaction ID",
                                      cellType: "link",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      linkMetaData: {
                                        eventDataUseful: false,
                                        navigateToLabel: "Payment_Refund",
                                      },
                                      cellCssClasses: ["link-animation"],
                                      filterable: true,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "invoiceDate",
                                      name: "Transaction Date",
                                      cellType: "Date",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: true
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "transStatus",
                                      name: "Transaction Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-85"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "invoiceStatus",
                                      name: "Invoice Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-7"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "candidateId",
                                        name: "Candidate ID",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: ["width-6"],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                      },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: ["candidateFirstName","candidateLastName","candidateMiddleName"],
                                      name: "Candidate Name",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "cardId",
                                      name: "Payment Method ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "examType",
                                      name: "Exam Type",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "authAmount",
                                      name: "Payment Amount",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "settleDateUTC",
                                      name: "Settlement Date",
                                      cellType: "date",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "settleAmount",
                                      name: "Settlement Amount",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundTransactionId",
                                      name: "Refund Transaction ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundTotalAmount",
                                      name: "Refund Amount",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundRemark",
                                      name: "Refund Remark",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundStatus",
                                      name: "Refund Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundInitiatedEmpID",
                                      name: "Refund Initiated Emp ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundDate",
                                      name: "Refund Date",
                                      cellType: "date",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "action",
                                      name: "Action",
                                      cellType: "action",
                                      actions: [
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2102699,
                                          }
                                        ],
                                          
                                        },
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2146829,
                                          }
                                        ],
                                          
                                        },
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2133825,
                                          }
                                        ],
                                          
                                        }
                                    
                                      ],
                                      linkMetaData: {
                                        navigateToType: moduleTypes.Offspring,
                                        navigateTo: "",
                                        eventDataUseful: false,
                                        navigateToLabel: "",
                                      },
                                      headerType: "normal",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    },
                                  ],
                                  [
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "checkbox",
                                      name: "Checkbox",
                                      cellType: "text",
                                      headerType: "checkbox",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-4"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "orderId",
                                      name: "Order ID",
                                      cellType: "link",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      linkMetaData: {
                                        eventDataUseful: false,
                                        navigateToLabel: "Payment_Refund",
                                      },
                                      cellCssClasses: ["link-animation"],
                                      filterable: true,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "invoiceDate",
                                      name: "Order Date",
                                      cellType: "Date",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: true
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "orderStatus",
                                      name: "Order Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-85"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "invoiceStatus",
                                      name: "Invoice Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-7"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                        visible: true,
                                        id: "candidateId",
                                        name: "Candidate ID",
                                        cellType: "text",
                                        headerType: "sort",
                                        headerAlign: "center",
                                        cellAlign: "center",
                                        headerCssClasses: ["width-6"],
                                        cellCssClasses: [],
                                        filterable: true,
                                        isFilter: false
                                      },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: ["candidateFirstName","candidateLastName","candidateMiddleName"],
                                      name: "Candidate Name",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "captureId",
                                      name: "Capture ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "examType",
                                      name: "Exam Type",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "amount",
                                      name: "Payment Amount",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                      filterable: false,
                                      isFilter: false
                                    },
                                 
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundId",
                                      name: "Refund Transaction ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundTotalAmount",
                                      name: "Refund Amount",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundRemark",
                                      name: "Refund Remark",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundStatus",
                                      name: "Refund Status",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundInitiatedEmpID",
                                      name: "Refund Initiated Emp ID",
                                      cellType: "text",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-5"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "refundDate",
                                      name: "Refund Date",
                                      cellType: "date",
                                      headerType: "sort",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-8"],
                                      cellCssClasses: [],
                                    },
                                    <tableTypes.column>{
                                      visible: true,
                                      id: "action",
                                      name: "Action",
                                      cellType: "action",
                                      actions: [
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2133825,
                                          }
                                        ],
                                          
                                        },
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2102699,
                                          }
                                        ],
                                          
                                        },
                                        <tableTypes.action>{
                                          icon: "payment",
                                          type: tableTypes.actionTypes.Refund,
                                          tooltip: "Refund Payment",
                                          color: "#7d7d7d",
                                          visibilityRule: [{
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'isRefund',
                                            compareWithOperator: comparisionOperators.NOTEQUAL,
                                            shouldHaveValue: false,
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'roleId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue: Roles.OperationStaff
                                          },
                                          {
                                            operator: tableTypes.Operators.AND,
                                            elementProperty: 'personId',
                                            extractedFromGlobal: true,
                                            compareWithOperator: comparisionOperators.EQUAL,
                                            shouldHaveValue:2146829,
                                          }
                                        ],
                                          
                                        }
                                        
                                        
                                      ],
                                      linkMetaData: {
                                        navigateToType: moduleTypes.Offspring,
                                        navigateTo: "",
                                        eventDataUseful: false,
                                        navigateToLabel: "",
                                      },
                                      headerType: "normal",
                                      headerAlign: "center",
                                      cellAlign: "center",
                                      headerCssClasses: ["width-6"],
                                      cellCssClasses: [],
                                    }
                                    
                                  ],
                                ],
                                requestDetails: [
                                  {
                                    fetchUrl: ["client", "api", "payment", "authorizenet", "transactions"],
                                    requestBody: null,
                                    requestParams: [
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                      },
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                      },
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "stateId",
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'stateId'
                                      },
                                    ],
                                    method: "GET",
                                  },
                                  {
                                    fetchUrl: ["client", "api", "payment", "paypal", "orders"],
                                    requestBody: null,
                                    requestParams: [
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageNo",
                                        paramValue: 1,
                                      },
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "pageSize",
                                        paramValue: 8,
                                      },
                                      {
                                        paramType: paramTypes.Optional,
                                        paramName: "stateId",
                                        paramValue: null,
                                        extractedFromGlobal: true,
                                        elementPropertyToBeExtracted: 'stateId'
                                      },
                                    ],
                                    method: "GET",
                                  },
                                ],
                            }
                        },
                    ],
                },
            ],
        }
    ]
}