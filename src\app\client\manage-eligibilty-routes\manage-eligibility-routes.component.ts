import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { cellClickEvent } from 'src/app/core/common-component/table/dynamic-table-types';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types'
import { ManageEligibilityRoutesService } from './manage-eligibilty-routes.service';
import { Router } from '@angular/router';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { EligibilityRouteStore, GetAccomodationFormResponse, GetApplicationFormResponse, GetElapsedTimeResponse, GetEligibilityRouteResponse, GetStateResponse, PostEligibilityRoute, statusBody } from './store/eligibility.model';
import { LanguageService } from 'src/app/core/language.service';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { Action, select, Store } from '@ngrx/store';
import * as actions from './store/action';
import { ELIGIBILITY_ROUTE_STATE } from './store/store';
import { take } from 'rxjs/operators';
import lodash from 'lodash';
import { changeERStatus } from './store/action';
import { HttpErrorResponse } from '@angular/common/http';
import { SnackbarService } from 'src/app/core/snackbar.service';

@Component({
  selector: 'exai-manage-eligibility-routes',
  templateUrl: './manage-eligibility-routes.component.html',
  styleUrls: ['./manage-eligibility-routes.component.scss']
})
export class ManageEligibilityRoutesComponent implements OnInit, OnDestroy {
  dataSource: MatTableDataSource<GetEligibilityRouteResponse> = new MatTableDataSource([]);
  tableOptions = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showColumnFilter: true,
    showAddButton: [true],
    addButtonTexts: ['Add Eligibility Route'],
    showMatTabs: false,
    matTabs: [],
    showSearch: true,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: true,
  }
  displayedColumns = [[
    <tableTypes.column>{
      visible: true,
      id: "checkbox",
      name: "Checkbox",
      cellType: "text",
      headerType: "checkbox",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-4"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "eligibilityName",
      name: "Eligibility Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "stateName",
      name: "State name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/6"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "lastUpdatedDate",
      name: "Created Date",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/6"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "active",
      name: "Status",
      cellType: "toggle",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      showOnlyData:true,
      headerCssClasses: ['w-12'],
      cellCssClasses: [],
      statusUpdateDetails: {
        fetchUrl: ["client", "api", "eligibility", "updatebulkstatus"],
        requestBody: null,
        requestParams: null,
        method: "PUT",
      },
    },
    <tableTypes.column>{
      visible: true,
      id: "action",
      name: "Actions",
      cellType: "action",
   
      actions: [
        { tooltip: 'View', color: '#7d7d7d', icon: 'visibility' },
        { tooltip: 'Edit', color: '#7d7d7d', icon: 'edit' ,showOnlyData :true,},
      ],
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["width-6"],
      cellCssClasses: ["cursor-pointer"],
     
    },
    



  ]];
  eligibleRoutes: Array<GetEligibilityRouteResponse> = [];
  eligibility$: Observable<EligibilityRouteStore>;
  subscription: Subscription;
  tableData: MatTableDataSource<any> = null;


  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();

  constructor(private service: ManageEligibilityRoutesService, private router: Router,
    private dialog: MatDialog, private formBuilder: FormBuilder, private _snackBar: SnackbarService,
    public language: LanguageService, private store: Store<{ ELIGIBILITY_ROUTE_STATE: EligibilityRouteStore }>) {
    this.eligibility$ = this.store.pipe(select(ELIGIBILITY_ROUTE_STATE));
  }

  ngOnInit(): void {
    this.store.dispatch<Action>(actions.loadRoutes());
    this.subscription = this.eligibility$.subscribe((data: EligibilityRouteStore) => {
      data.eligibilityRoutes == null ? this.store.dispatch<Action>(actions.loadRoutes()) : (this.dataSource.data = data.eligibilityRoutes, this.eligibleRoutes = data.eligibilityRoutes);
    });
  }


  addAction(event: boolean): void {
    if (!event) return;
    this.router.navigateByUrl('manage-eligiblity/view');
  }

  actions(event: any): void {
    if ('action' in event && 'tooltip' in event.action && event.action.tooltip == 'View' && 'element' in event) {
      this.service.viewEligibilityRoute(event.element.id).subscribe((data: PostEligibilityRoute) => {
        if (data) {
          this.service.selectedInformation = data;
          this.router.navigateByUrl('manage-eligiblity/view');
        }
      }, (error: HttpErrorResponse) => {
        typeof error.message == 'object' ? this._snackBar.callSnackbaronError(JSON.stringify(error.message)) : this._snackBar.callSnackbaronError(error.message)
      });
    } else if ('action' in event && 'tooltip' in event.action && event.action.tooltip == 'Delete') {
      this.service.selectedInformation = 'element' in event ? event.element : null;
    }
    // else if ('action' in event && 'tooltip' in event.action && event.action.tooltip =="Edit"){
     
    // }
  }

  print($event: any) {
  }

  toggleHandler($event: cellClickEvent) {
    var tableDataClone = lodash.cloneDeep(this.dataSource.data);
    tableDataClone[$event.rowIndex][$event.column.id as string] = !$event.element[$event.column.id as string];
    this.dataSource = new MatTableDataSource<any>(tableDataClone);
    this.store.dispatch(changeERStatus({
      statusBody:
      {
        tenantIds: [$event.element.id],
        isActive: !$event.element.active
      }
    }));
  }


  ngOnDestroy(): void {
    this.subscription ? this.subscription.unsubscribe() : null;
  }
}
