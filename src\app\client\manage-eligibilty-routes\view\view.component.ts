import { StepperSelectionEvent } from '@angular/cdk/stepper';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ManageEligibilityRoutesService } from '../manage-eligibilty-routes.service';

@Component({
  selector: 'exai-view',
  templateUrl: './view.component.html',
  styleUrls: ['./view.component.scss']
})
export class ViewComponent implements OnInit, OnDestroy {
  selectedInformation: any;
  selectedIndex: number = 0;
  editable: boolean = true;
  addRouteForm: FormGroup;
  examDetailsForm: FormGroup;

  constructor(private _service: ManageEligibilityRoutesService, private router: Router) {
    !this._service.selectedInformation ? null : this.selectedInformation = this._service.selectedInformation;
  }

  ngOnInit(): void {
  }

  tabChange(event: StepperSelectionEvent): void {
    this.selectedIndex = event.selectedIndex
    /* try {
      if (event.selectedIndex == 0) {
        this._service.information.next('check');
        setTimeout(() => {
          this.examDetailsForm = this._service.examDetailsForm;
          this.examDetailsForm.valid ? this.selectedIndex = event.selectedIndex : null;
        }, 800)
      } else if (event.selectedIndex == 1) {
        this._service.information.next('check');
        setTimeout(() => {
          this.addRouteForm = this._service.addRouteForm;
          this.addRouteForm.valid ? this.selectedIndex = event.selectedIndex : null;
        }, 800)
      }
    } catch (e) { } */
  }

  nextStep(event: boolean, index: number): void {
    if (!event) return;
    this.selectedIndex = index;
  }

  ngOnDestroy(): void {
    this._service.selectedInformation = null;
  }

}
