import {
  HttpClient,
  HttpErrorResponse,
  HttpHeaders,
} from "@angular/common/http";
import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA } from "@angular/material/dialog";
import { MatTableDataSource } from "@angular/material/table";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";
import { ActivatedRoute, ParamMap, Params, Router } from "@angular/router";
import { Store } from "@ngrx/store";
import { Param } from "@thoughtspot/visual-embed-sdk/lib/src/types";
import { NgDynamicBreadcrumbService } from "ng-dynamic-breadcrumb";
import { BehaviorSubject, Observable, forkJoin } from "rxjs";
import { debounceTime } from "rxjs/operators";
import { HttpService } from "src/app/core/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { getLoading } from "../../state/shared/shared.selectors";

@Component({
  selector: "app-scan",
  templateUrl: "./Evaluator.component.html",
  styleUrls: ["./Evaluator.component.scss"],
})
export class EvaluatorScanComponent implements OnInit {
  urlSafe: SafeResourceUrl;
  ExamTypes;
  body: Object;
  loadingObs: Observable<boolean>;
  resourceurl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  public dataSource: MatTableDataSource<any>;
  SucessfullyStatus: boolean = false;
  $resourceurl: Observable<string> = this.resourceurl.asObservable();
  greivance: number | any;
  presonTenatrole: number | any;
  bookletId: string;
  personEventId:number;
  personId:number;
  PersonTenanatRole:number
  appointmentId:string
  Eventype: number | any;
  Id: number | any;
  isLoading = false;
  receivedMessage: {
    tag: string;
    content: {
      image: string;
      croppedImage: string;
      pdf: string;
      videoBlobUrl: string;
    };
  };
  binaryImageString: any;
  constructor(
    private sanitizer: DomSanitizer,
    private http: HttpService,
    private ngDynamicBreadcrumbService: NgDynamicBreadcrumbService,
    private https: HttpClient,
    private activate: ActivatedRoute,
    private router: Router,
    private snackbar: SnackbarService,
    private store: Store
  ) {
    this.activate.paramMap.subscribe((params: Params) => {
      console.log(params)
      this.greivance = Number(params.params.grievance);
      this.Id = params.params.id;
      this.Eventype = params.params.eventExamType;
      this.presonTenatrole = params.params.personTenantRoleId;
      this.bookletId = params.params.bookletId;
      this.PersonTenanatRole = Number(params.params.PersonTenantRoleId)
      this.personEventId = Number(params.params.personEventId)
      this.personId = Number(params.params.personId)
      this.appointmentId = (params.params.appointmentId)
    });

    window.addEventListener("message", (event) => {
      this.receivedMessage = event.data;
      if (this.receivedMessage.tag == "er-doc") {
        if (this.receivedMessage) {
            this.upload();
        }
      }
    });
  }

  randomFileName() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, "0");
    const day = String(currentDate.getDate()).padStart(2, "0");
    const hours = String(currentDate.getHours()).padStart(2, "0");
    const minutes = String(currentDate.getMinutes()).padStart(2, "0");
    const seconds = String(currentDate.getSeconds()).padStart(2, "0");
    // Generate a random name for the PDF file with formatted date
    return `Credentia_${year}-${month}-${day}_${hours}-${minutes}-${seconds}_${Math.random()
      .toString(36)
      .substring(4)}.jpg`;
  }
  ngOnInit(): void {
    const breadcrumb = [
      {
        label: "Home",
        url: "/dashboard",
      },
      {
        label: "Roast",
        url: "/Scan",
      },
      {
        label: "Scan",
        url: "",
      },
    ];
    this.ngDynamicBreadcrumbService.updateBreadcrumb(breadcrumb);
    this.loadingObs = this.store.select(getLoading).pipe(debounceTime(50));
    this.greivance == 1
      ? this.resourceurl.next(`${environment.ScanUrl}?type=omr&docfiletype=`)
      : this.greivance == 3
      ? this.resourceurl.next(
          `${environment.ScanUrl}?type=video&docfiletype=pdf`
        )
      : this.resourceurl.next(
          `${environment.ScanUrl}?type=doc&docfiletype=pdf`
        );
  }

  upload() {
    let binaryImageString;
    let croppedImageString;
    let file
    this.isLoading = true;
    if (this.greivance == 1) {
      if(this.receivedMessage.content.pdf) {
        const base64String = this.receivedMessage.content.pdf.split(",")[1];
        const byteArray = new Uint8Array(
          atob(base64String)
            .split("")
            .map((char) => char.charCodeAt(0))
        );
        file = new File([byteArray], `${this.Eventype}.pdf`, {
          type: "application/pdf",
        });
      }
      

      if (this.receivedMessage.content.image) {
        const baseImage64String =
          this.receivedMessage.content.image.split(",")[1];
        // Convert base64 string to binary data
        this.binaryImageString = new Uint8Array(
          atob(baseImage64String)
            .split("")
            .map((char) => char.charCodeAt(0))
        );
      }

      if (this.receivedMessage.content.image) {
        const baseImage64String =
          this.receivedMessage.content.image.split(",")[1];
        // Convert base64 string to binary data
        binaryImageString = new Uint8Array(
          atob(baseImage64String)
            .split("")
            .map((char) => char.charCodeAt(0))
        );
      }

      if(this.receivedMessage.content.croppedImage){
        const baseImage64String =this.receivedMessage.content.croppedImage.split(",")[1];
        // Convert base64 string to binary data
        croppedImageString = new Uint8Array(
          atob(baseImage64String)
            .split("")
            .map((char) => char.charCodeAt(0))
        );
      }


      // Create a File object from the Blob
      let Imagefile;
      let CroppedImageFile;
      if(binaryImageString){
        Imagefile = new File([binaryImageString], `${this.Eventype}.jpeg`, {  type: 'image/jpeg', });
      }
      if(croppedImageString){
        CroppedImageFile = new File([croppedImageString], `${this.Eventype}.jpeg`, {  type: 'image/jpeg', });
      }
      let CandidateDetail:any = {
        PersonId: this.personId,
        PersonEventID: this.personEventId,
        PersonTenantRoleId: this.PersonTenanatRole,
        AppointmentId: this.appointmentId
      };
      
      const formData = new FormData();
      formData.append("File", file);
      formData.append("CroppedImageFile", CroppedImageFile);
      formData.append("ImageFile", Imagefile);
      formData.append("SubFolderName", "NAEDocuments");
      formData.append("type", this.greivance);
      
      // Option 1: If your backend expects flat keys
      formData.append("CandidateDetail.PersonId", CandidateDetail.PersonId);
      formData.append("CandidateDetail.PersonEventID", CandidateDetail.PersonEventID);
      formData.append("CandidateDetail.PersonTenantRoleId", CandidateDetail.PersonTenantRoleId);
      formData.append("CandidateDetail.AppointmentId", CandidateDetail.AppointmentId);

      this.http
        .getUploadORM(this.Id, this.presonTenatrole, formData, this.bookletId)
        .subscribe(
          (data: boolean) => {
            if (data == true) {
              this.isLoading = false;
              this.snackbar.callSnackbaronSuccess('Uploaded Successfully');
              setTimeout(()=>{
                let scanURL = sessionStorage.getItem('scanCurrentURL')
                window.location.href = scanURL;
              },5000)
           
            }else {
              this.isLoading = false;
              setTimeout(()=> {
                let scanURL = sessionStorage.getItem('scanCurrentURL')
                window.location.href = scanURL;
              },5000)
              this.snackbar.callSnackbaronError('Please Contact Credentia Support');
            }
          },
          (err: HttpErrorResponse | any) => {
            this.isLoading = false;
            this.snackbar.callSnackbaronError(`${err.message.message.error}`);
          }
        );
    }
  }
}
