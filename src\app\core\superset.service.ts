



import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, of } from 'rxjs';
import { catchError, switchMap, tap, map } from 'rxjs/operators';
import { embedDashboard } from '@superset-ui/embedded-sdk';
import { environment } from 'src/environments/environment';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

export interface Dashboard {
  id: number;
  dashboard_title: string;
  url: string;
}

export interface EmbeddedDashboardInfo {
  uuid: string;
  dashboard_id: string;
}

@Injectable({
  providedIn: 'root'
})
export class SupersetService {
  private supersetUrl = 'https://workbench.examroom.ai';
  private supersetApiUrl = `${this.supersetUrl}/api/v1`;
  private securityApiUrl = `${this.supersetApiUrl}/security`;
  private dashboardApiUrl = `${this.supersetApiUrl}/dashboard`;
  
  // Store current dashboard UUID
  private currentDashboardUuid = new BehaviorSubject<string>('');
  currentDashboardUuid$ = this.currentDashboardUuid.asObservable();

  private isAuthenticated = false;
  private lastAuthTime = 0;
  private AUTH_REFRESH_INTERVAL = 4 * 60 * 1000; // 4 minutes

  // Authentication credentials
  private credentials = {
    username: "admin",
    password: "Examroom15$$AIML"
  };

  // Store the access token
  private accessToken = '';

  constructor(private http: HttpClient,private sanitizer:DomSanitizer) { }

  // Login and get access token
  login(): Observable<string> {
    const body = {
      "password": this.credentials.password,
      "provider": "db",
      "refresh": true,
      "username": this.credentials.username
    };
    
    const headers = new HttpHeaders({
      "Content-Type": "application/json"
    });
    
    return this.http.post<any>(`${this.securityApiUrl}/login`, body, { headers }).pipe(
      catchError(error => {
        console.error('Login error:', error);
        return throwError(() => error);
      }),
      map(response => {
        this.accessToken = response.access_token;
        return this.accessToken;
      })
    );
  }

  // Get all dashboards
  getDashboards() {
    const token = sessionStorage.getItem('token')
   return this.http.post(`${environment.superset}api/v1/auth/token`,{token:token})
  }

  // Get embedded dashboard UUID by dashboard ID
  getEmbeddedDashboardUuid(dashboardId: number): Observable<string> {
    return this.ensureAuthenticated().pipe(
      switchMap(token => {
        const headers = this.getAuthHeaders(token);
        
        return this.http.get<any>(`${this.dashboardApiUrl}/${dashboardId}/embedded`, { headers }).pipe(
          map(response => {
            const uuid = response.result.uuid;
            this.currentDashboardUuid.next(uuid); // Update current dashboard UUID
            return uuid;
          }),
          catchError(error => {
            console.error('Get embedded dashboard error:', error);
            return throwError(() => error);
          })
        );
      })
    );
  }

  // Get guest token for embedding
  getGuestToken(uuid: string): Observable<any> {
    return this.ensureAuthenticated().pipe(
      switchMap(token => {
        const headers = this.getAuthHeaders(token);
        const body = {
          "resources": [
            {
              "type": "dashboard",
              "id": uuid,
            }
          ],
          "rls": [],
          "user": {
            "username": "report-viewer",
            "first_name": "report-viewer",
            "last_name": "report-viewer",
          }
        };
        
        return this.http.post<any>(`${this.securityApiUrl}/guest_token/`, body, { headers }).pipe(
          catchError(error => {
            console.error('Get guest token error:', error);
            return throwError(() => error);
          })
        );
      })
    );
  }

  
  // Embed dashboard with given UUID
  embedDashboard(uuid: string): Observable<void> {
    return new Observable<void>((observer) => {
      this.getGuestToken(uuid).subscribe({
        next: (tokenResponse) => {
          debugger
          try {
          
            const dashboardPromise = embedDashboard({
              id: uuid,
              supersetDomain: this.supersetUrl,
              mountPoint: document.getElementById('superset_embedding_div_class')!,
              fetchGuestToken: () => Promise.resolve(tokenResponse.token),
              dashboardUiConfig: {
                hideTitle: true,
                hideChartControls: false,
                hideTab: false,
                filters: {
                  visible: false,
                  expanded: false
                },
                urlParams: {
                  standalone: "1",
                  show_filters: "0",
                  show_native_filters: "0"
                }
              },
            });
            
            // Set up resize handling
            dashboardPromise.then(dashboard => {
              const resizeObserver = new ResizeObserver(() => {
                const container:any = document.getElementById('superset_embedding_div_class');
                if (container) {
                 container.children[0].width = "100%"
                 container.children[0].height = "500px"
                }
              });
              
              const container = document.getElementById('superset_embedding_div_class');
              if (container) {
                resizeObserver.observe(container);
              }
            });
            
            observer.next(void 0);
            observer.complete();
         
          } catch (err) {
            observer.error(err);
          }
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  // Helper method to ensure we have a valid token
  private ensureAuthenticated(): Observable<string> {
    if (this.accessToken) {
      return new Observable<string>(observer => {
        observer.next(this.accessToken);
        observer.complete();
      });
    } else {
      return this.login();
    }
  }

  // Helper to create auth headers
  private getAuthHeaders(token: string): HttpHeaders {
    return new HttpHeaders({
      "Content-Type": "application/json",
      "Authorization": `Bearer ${token}`
    });
  }

  generateIframeFriendlyUrl(dashboard: Dashboard): SafeResourceUrl {
    // This uses a custom endpoint that handles authentication for iframe
    const iframeUrl = `${environment.superset}iframe/dashboard/${dashboard.id}?token=${sessionStorage.getItem('token')}`;
    console.log('🌐 Using iframe-friendly endpoint:', iframeUrl);
    return this.sanitizer.bypassSecurityTrustResourceUrl(iframeUrl);
  }

  generateBypassEmbedUrl(dashboardId: number): SafeResourceUrl {
    const targetUrl = `/superset/dashboard/dashboard-${dashboardId}/?standalone=true&embedded=true`;
    const bypassUrl = `${environment.superset}/bypasslogin?token=${sessionStorage.getItem('token')}&target=${encodeURIComponent(targetUrl)}`;
    
    console.log('🚪 Using bypass URL (fallback):', bypassUrl);
    return this.sanitizer.bypassSecurityTrustResourceUrl(bypassUrl);
  }

  private ensureFreshAuthentication(): Observable<boolean> {
    const now = Date.now();
    const timeSinceLastAuth = now - this.lastAuthTime;
    
    // If authenticated recently (within 4 minutes), skip refresh
    if (this.isAuthenticated && timeSinceLastAuth < this.AUTH_REFRESH_INTERVAL) {
      console.log('✅ Using existing fresh authentication');
      return of(true);
    }
    
    // Otherwise, refresh authentication
    console.log('🔄 Refreshing authentication for iframe...');
    return this.getDashboards().pipe(
      map((response:{success}) => response.success),
      catchError(() => of(false))
    );
  }

  // SOLUTION 1: Generate embed URL with fresh authentication (MAIN METHOD)
  generateEmbedUrlWithFreshAuth(dashboard: Dashboard) {
    const targetUrl = `/superset/dashboard/${dashboard.id}/?standalone=true&embedded=true`;
    const newTabUrl = `${environment.superset}/bypasslogin?token=${sessionStorage.getItem('token')}&target=${encodeURIComponent(targetUrl)}`;
    window.open(newTabUrl, '_blank');
  }

  
}

