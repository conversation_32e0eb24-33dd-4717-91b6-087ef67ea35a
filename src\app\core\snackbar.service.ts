import { Injectable } from "@angular/core";
import { MatSnackBar } from "@angular/material/snack-bar";
import { SnackbarComponent } from "./common-component/snackbar/snackbar.component";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";

@Injectable({
  providedIn: "root",
})
export class SnackbarService {
  EmailVerified={email:''}
  constructor(private _snackbar: MatSnackBar,private http:HttpClient) {}

  callSnackbaronSuccess(msg) {
    this.openSnackBar(msg, "success-snackbar");
  }
  callSnackbaronError(msg) {
    this.openSnackBar(msg, "error-snackbar");
  }
  callSnackbaronWarning(msg) {
    this.openSnackBar(msg, "warning-snackbar");
  }
  openSnackBar(message: string | Array<SnackBarResult>, panelClass: string) {
    this._snackbar.openFromComponent(SnackbarComponent, {
      data: message,
      panelClass: panelClass,
      duration: 60000,
    });

    
  }

  getSystemFileName(fileName:string){
    var url =`${environment.baseUrl}formmsvc/api/File/url?systemFileName=${fileName}`
    return this.http.get(url)
  }
  




}
export interface ISnackBarResult{
  message:string;
  resultFlag:boolean;
}

export class SnackBarResult{
  message:string;
  resultFlag:boolean;
}