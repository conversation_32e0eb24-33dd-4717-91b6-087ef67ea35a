import { state } from "@angular/animations";
//import { State } from "@ngrx/store";

import {
  consoleLoaderJSON,
  moduleTypes,
  State,
  nestingTypes,
  paramTypes,
  Roles,
  
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";
export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Uploaded Answer Sheet",
      moduleID: "manage_nae",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "ticket",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showAddButton: [false],
                  showSlectdropstatedataattributes:"statecliendrop",
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "",
                      title: [""],
                      id: "",
                      buttons: [
                        {
                          buttonText: "Submit",
                          requestDetails: {
                            fetchUrl: ["client", "api", "user"],
                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                  showMatTabs: false,
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  showStateSelectionDropdown: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "personId",
                      name: "Candidate Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "firstName",
                      name: "First Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: 'lastName',
                      name: "Last Name",
                      cellType: "text",
                      headerDisable: true,
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,



                      visibilityRule: [
                        {
                        operator: tableTypes.Operators.OR,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.North_Carolina,
                      },
                      {
                        operator: tableTypes.Operators.OR,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.Virginia,
                      },
                      {
                        operator: tableTypes.Operators.OR,
                        elementProperty: 'stateId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:State.Washington,
                      },
                      {
                        operator: tableTypes.Operators.AND,
                        elementProperty: 'roleId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:Roles.StateClient,
                      },
                      {
                        operator: tableTypes.Operators.OR,
                        elementProperty: 'roleId',
                        extractedFromGlobal: true,
                        compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                        shouldHaveValue:Roles.OperationStaff,
                      }

                    ],


                      
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "dateTimeUTC",
                      name: "Exam Date",
                      cellType: "date",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-1/12"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examName",
                      name: "Exam Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-1/12"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examModeId",
                      name: "Exam Type",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                        visible: true,
                        id: "clientState",
                        name: "State",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        filterable: false,
                        isFilter: false,
  
                      },
  

                  
                   
                    <tableTypes.column>{
                      visible: true,
                      id: "eventId",
                      name: "Event Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "appointmentId",
                      name: "Appointment Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,

                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "bookletNumber",
                      name: "Booklet Id",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false,

                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "scoreReceived",
                      name: "Scored",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                      statusesboolean:true

                    },
                 


                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          type: tableTypes.actionTypes.omrsheetview,
                          icon: 'downpicture_as_pdfload',
                          tooltip: 'Pdf',
                          color: '#7d7d7d',
                        
                        },
                        <tableTypes.action>{
                          type: tableTypes.actionTypes.omrsheetview,
                          icon: 'image',
                          tooltip: 'Image',
                          color: '#7d7d7d',
                         
                        },
                        <tableTypes.action>{
                          type: tableTypes.actionTypes.omrsheetview,
                          icon: 'featured_video',
                          tooltip: 'Shed Video',
                          color: '#7d7d7d',
                          visibilityRule: [{
                            operator: tableTypes.Operators.AND,
                            elementProperty: 'bookletNumber',
                            compareWithOperator: comparisionOperators.NOTEQUAL,
                            shouldHaveValue:null ,
                          }]
                        },
                        <tableTypes.action>{
                          type: tableTypes.actionTypes.omrsheetview,
                          icon: 'credit_score',
                          tooltip: 'Score',
                          color: '#7d7d7d',
                          visibilityRule: [{
                            operator: tableTypes.Operators.AND,
                            elementProperty: 'bookletNumber',
                            compareWithOperator: comparisionOperators.NOTEQUAL,
                            shouldHaveValue:null ,
                          },
                          {
                            operator: tableTypes.Operators.AND,
                            elementProperty: 'scoreReceived',
                            compareWithOperator: comparisionOperators.NOTEQUAL,
                            shouldHaveValue:false,
                          },
                        ]

                        },
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Offspring,
                        navigateTo: "training-institute-details",
                        eventDataUseful: false,
                        navigateToLabel: "Training Program Details",
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },
                    // <tableTypes.column>{
                    //   visible: true,
                    //   id: "action",
                    //   name: "Action",
                    //   cellType: "link",
                    //   linkMetaData: {
                    //     navigateToType: moduleTypes.Offspring,
                    //     navigateTo: "training-institute-details",
                    //     eventDataUseful: false,
                    //     navigateToLabel: "Training Institute",
                    //   },
                    //   headerType: "normal",
                    //   headerAlign: "center",
                    //   cellAlign: "center",
                    //   headerCssClasses: [],
                    //   cellCssClasses: [],
                    // },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "operationstaff", "candidate-completed-exam"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                      
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personRoleId",
                        paramValue: 14,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "stateId",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: 'stateId'
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "IsScoreReceived",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: 'IsScoreReceived'
                      }
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
