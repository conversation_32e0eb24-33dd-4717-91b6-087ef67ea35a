

<div  gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr" gdGap="16px" exaiContainer>
    <div class="text-sm" gdColumn="1/ 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 /-1">
        <b> Evaluator Scan</b>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'" [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
    </div>
</div>
<ng-container *ngIf="!isLoading">
    <div  class="shadow-none justify-start overflow-auto  h-full" >
                  <div class=" " >
                  <iframe [src]="($resourceurl | async) | safeResourceurl" class="w-full eligibility1"  allow="camera; microphone;"></iframe>
                  </div>
           </div>
</ng-container>

<div *ngIf="isLoading" class="loader">
    <mat-progress-spinner class="calenderSpinner flex justify-center" color="primary"
        diameter="60" mode="indeterminate" >
    </mat-progress-spinner>
</div>