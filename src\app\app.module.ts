import { BrowserModule } from "@angular/platform-browser";
import { NgModule } from "@angular/core";

import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { exaiModule } from "../@exai/exai.module";
import { HttpClientModule, HTTP_INTERCEPTORS } from "@angular/common/http";
import { CustomLayoutModule } from "./client/custom-layout/custom-layout.module";
import { LocationStrategy, PathLocationStrategy } from "@angular/common";
import { EffectsModule } from "@ngrx/effects";
import { StoreRouterConnectingModule } from "@ngrx/router-store";
import { StoreModule } from "@ngrx/store";
import { StoreDevtoolsModule } from "@ngrx/store-devtools";
import { environment } from "src/environments/environment";
import { appReducer } from "./client/state/client.state";
import { CustomSerializer } from "./client/state/router/custom-serializer";
import { SharedEffects } from "./client/state/shared/shared.effects";
// import { BufferInterceptor } from "./core/buffer-service.service";
import { SharedModule } from "./shared.module";
import { TokenInterceptorService } from "./core/token-interceptor.service";
import { LoggerInterceptor } from './logs/logger.interceptor';
import { loggerReducer } from './logs/logger.reducers';
import {pageSizeReducer} from './client/state/pageSize/pageSize.reducers';
import {stateFilterReducer} from './client/state/stateFilter/stateFilter.reducers';

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    exaiModule,
    CustomLayoutModule,
    SharedModule,
    EffectsModule.forRoot([SharedEffects]),
    StoreModule.forRoot(appReducer),
    StoreDevtoolsModule.instrument({
      logOnly: environment.production,
    }),
    StoreRouterConnectingModule.forRoot({
      serializer: CustomSerializer,
    }),
    StoreModule.forFeature("httpLogger", loggerReducer),
    StoreModule.forFeature("pageSize", pageSizeReducer),
    StoreModule.forFeature("stateFilter", stateFilterReducer),
  ],
  providers: [
    { provide: LocationStrategy, useClass: PathLocationStrategy },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptorService,
      multi: true,
    },
    { provide: HTTP_INTERCEPTORS, useClass: LoggerInterceptor, multi: true },
    // { provide: HTTP_INTERCEPTORS, useClass: BufferInterceptor, multi: true },
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
