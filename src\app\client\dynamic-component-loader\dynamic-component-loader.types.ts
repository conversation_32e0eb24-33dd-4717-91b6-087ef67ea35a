import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from 'src/app/core/common-component/single-entry-table/single-entry-table.types';
import { popupData } from "src/app/core/common-component/pop-up/pop-up.types";
export class consoleLoaderJSON {
  modules: Array<module>;
}
export class module {
  type: moduleTypes
  name: string;
  moduleID: string;
   // @Optional ; will be used only if "dropdown" of console is moduleTypes.custom
  children?:Array<any>
  // @Optional ; will be used only if "type" of console is moduleTypes.Dynamic
  nestingType?: nestingTypes
  rows?: Array<row>;
  columns?: Array<column>;
  routeRef?: string;
  iconRef?: string;
  class?: string;
  dataattributes?:string
  // reason for using tableTypes.actions as need to use 
  actions?: Array<tableTypes.action>;
  gdColumns?: string;
  gdColumnsltlg?: string;
  gdColumnsltmd?: string;
  gdColumnsltsm?: string;
  gdGap?: string;
  gdColumn?: string;
  gdColumnltlg?: string;
  gdColumnltmd?: string;
  gdColumnltsm?: string;
  fxLayout?: string;
  fxLayoutAlign?: string;
  fxFlex?: string;
  fxFlexltmd?: string;
  fxHidexs?: string;
  fxHidesm?: string;
  sequence?: number;
}

export interface row {
  columns?: Array<column>;
  widget?: widget;
  class?: string;
  gdColumns?: string;
  gdColumnsltlg?: string;
  gdColumnsltmd?: string;
  gdColumnsltsm?: string;
  gdGap?: string;
  gdColumn?: string;
  gdColumnltlg?: string;
  gdColumnltmd?: string;
  gdColumnltsm?: string;
  fxLayout?: string;
  fxLayoutAlign?: string;
  fxFlex?: string;
  fxFlexltmd?: string;
  fxHidexs?: string;
  fxHidesm?: string;
}

export interface column {
  rows?: Array<row>,
  widget?: widget;
  class?: string;
  gdColumns?: string;
  gdColumnsltlg?: string;
  gdColumnsltmd?: string;
  gdColumnsltsm?: string;
  gdGap?: string;
  gdColumn?: string;
  gdColumnltlg?: string;
  gdColumnltmd?: string;
  gdColumnltsm?: string;
  fxLayout?: string;
  fxLayoutAlign?: string;
  fxFlex?: string;
  fxFlexltmd?: string;
  fxHidexs?: string;
  fxHidesm?: string;
}

export interface widget {
  // the displayed columns need to contain those properties of the api response 
  type: widgetTypes;
  tableOptions?: tableTypes.tableOptions;
  displayedColumns?: Array<Array<tableTypes.column | singleEntryTableTypes.column>>;
  requestDetails?: Array<requestDetails>;
  backwardsDataDependency?: boolean;
  widgetHeader?: string;
  formOptions?: formOptions;
  grandParentReference?: Map<number, string> | string;
 
}

export interface formOptions {
  formDisabled: boolean,
  changeRequestAllowed?: boolean;
  approveRejectAllowed?: boolean;
  addORviewNotesAllowed?: boolean;
  widgetHeader?: string;
  accessData?: Access;
  hideForm?:boolean;
}

export interface Access {
  view?: boolean;
  add?: boolean;
  edit?: boolean;
  delete?: boolean;
  status?: boolean;
}

export enum moduleTypes {
  Dynamic = 'dynamic',
  Custom = 'custom',
  Offspring = 'offspring',
  Independent = 'independent',
  Way_Bill_Url ='way_bill_no'
}

export enum nestingTypes {
  ColumnsInRows = 'columns-in-rows',
  RowsInColumns = 'rows-in-columns'
}

export enum widgetTypes {
  DynamicTable = "dynamictables",
  // Dynamic Forms is supposed to contain three request details object, first one to fetch the form json, 
  // second one to fetch the userResponse and third one to assign the form to current user
  // third one is optional if passed only then the current form will be assigned the current user
  DynamicForms = "dynamicforms",
  DynamicSingleEntryTables = "singleentrytables",
  DynamicProcessLogs = "processlogs",
}

export enum popupTypes {
  AddPopup = 'addPopup',
  EditPopup = 'editPopup',
  FormViewPopup = 'formViewPopup',
  RichTextView = 'richTextView',
  ComponentView = 'componentView',
  EmulateCandidate = 'emulateCandidate',
  EmulateClient = 'emulateClient'
}
// here popupData will have a type union of all the popupdata types existing in the application
export interface genericPopupConfig {
  type: popupTypes,
  popupData: FormViewPopupData| RichTextViewPopupData | popupData | any;
  popupDataType: popupDataTypes
}

export enum popupDataTypes {
  FetchedFromApi = 'fetchFromApi',
  FetchedFromProperties = 'fetchedFromProperties',
}
export interface requestDetails {
  fetchUrl: Array<string>,
  requestParams: Array<requestParam>,
  requestBody: any;
  method: "GET" | "POST" | "PUT" | "DELETE";// more can be included here;
}

export type requestParam = {
  paramName: string,
  paramValue: string | number,
  paramType: paramTypes,
  // this string refers to the property of element (of linkClickEvent) whose value has to be put here
  // the below property will only be accessed if paramType is 'dependent'
  extractedFromElement?: boolean;
  extractedFromGlobal?: boolean;
  elementPropertyToBeExtracted?: string | null;
  // position will be accessed only if paramtype is required, and 
  position?: number;
}

export interface FormViewPopupData {
  formJSON: any;
  userResponse: any;
  element: any;
}
export interface RichTextViewPopupData {
  richText: string;
  element: any;
}
export enum paramTypes {
  Required = 'required',
  Optional = 'optional',
  RouteParam = 'routeParam'
}

export enum statuses {
  Drafted = 1,
  Pending = 2,
  Rejected = 3,
  ChangeRequest = 4,
  YetToReceive = 5,
  Saved = 6,
  Approved = 7,
  Appeal = 12,
  Misconductpend = 3,
  Misconductappr = 1,
  Misconductrej = 2,
  Misconductnote = 11,
  Completed = 10,
  Expired = 11
}

export enum Roles {
  Candidate = 1,
  ClientAdmin = 2,
  Faculty = 3,
  Proctor = 4,
  OnboardingAgent = 5,
  ShiftManager = 6,
  Facilitator = 7,
  SupportingStaff = 8,
  PanelMember = 9,
  Reviewer = 10,
  PlatformAdmin = 11,
  ExamroomSuperAdmin = 12,
  SuperAdmin = 13,
  StateClient = 14,
  TrainingInstitue = 15,
  ExamroomAdmin = 16,
  OperationStaff = 17,
  Sponsor = 18,
  Employer = 19,
  NAE=22,
  QAC=28
}

export enum EventStatuses {
  EventAssigned = 1,
  EventScheduled = 2,
  WaitingforOnboarding = 3,
  OnboardingAgentApproved = 4,
  Onboarding = 5,
  WaitingforProctor = 6,
  ProctorApproved = 7,
  Proctoring = 8,
  EventCompleted = 9,
  EventCancelled = 10,
  NoShow = 11,
  EventNotStarted = 12,
  EventStarted = 13,
  SystemTestStarted = 14,
  SystemTestPassed = 15,
  SystemTestFailed = 16,
  TokBoxTokenGenerated = 17,
  TokBoxStreamStarted = 18,
  Reset = 19,
  EventRescheduled = 20,
  OnboardingAgentNotAssigned = 21,
  OnboardingAgentAssigned = 22,
  OnboardingAgentRejected = 23,
  OnboardingAgentAccepted = 24,
  OnboardNowClicked = 25,
  OnboardingConnectedtosession = 26,
  OnboardingStarted = 27,
  SystemCheckApproved = 28,
  SystemCheckRejected = 29,
  SystemCheckSkipped = 30,
  PhotoIDApproved = 31,
  PhotoIDRejected = 32,
  PhotoIDSkipped = 33,
  FaceIDApproved = 34,
  FaceIDRejected = 35,
  FaceIDSkipped = 36,
  TypingDNAApproved = 37,
  TypingDNARejected = 38,
  TypingDNASkipped = 39,
  BiometricApproved = 40,
  BiometricRejected = 41,
  BiometricSkipped = 42,
  Approved360 = 43,
  Rejected360 = 44,
  Skipped360 = 45,
  RoomScanApproved = 46,
  RoomScanRejected = 47,
  RoomScanSkipped = 48,
  TransferredtoProctor = 49,
  OnboardingCompleted = 50,
  OnboardingSkipped = 51,
  ProctorNotAssigned = 52,
  ProctorAssigned = 53,
  ProctorRejected = 54,
  ProctorAccepted = 55,
  ProctorNowClicked = 56,
  ProctorConnectedtoSession = 57,
  ProctoringStarted = 58,
  ProctoringCompleted = 59,
  CandidateLostConnection = 60,
  CandidateLogout = 61,
  EventCancelledByCandidate = 62,
  EventCancelledByClient = 63,
  EventCancelledByOnBoardingAgent = 64,
  EventCancelledByProctor = 65,
  EventCancelledByShiftManager = 66,
}

export enum State{
  Georgia=10,
  Colorado=9,
  Virginia=14,
  Mississippi=6 ,
  South_Carolina=7,
  Pennsylvania=4,
  District_of_columbia=5,
  North_Carolina=12,
  Washington=15,
  KYC = 22463
}


export const ModuleIdName={
  Manage_Candidate:"candidate-management",
  Training_Institute:"manage-training-programs",
  Manage_Candidates:"candidate-details",
  Manage_Application:"training-institute-details"
}
