import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { SharedModule } from 'src/app/shared.module';

import { ScheduleRoutingModule } from './schedule-routing.module';
import { ScheduleComponent } from './schedule.component';
import { ConfirmationPopupComponent } from './confirmation-popup/confirmation-popup.component';
import { TestCenterComponent } from './test-center/test-center.component';
import { DatePickerComponent } from './date-picker/date-picker.component';
import moment from 'moment';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { StoreModule } from '@ngrx/store';
import { SCHEDULED_STATE_NAME } from './state/scheduled.selectors';
import { scheduledReducer } from './state/scheduled.reducers';
import { ScheduledEffects } from './state/scheduled.effects';
import { EffectsModule } from '@ngrx/effects';
import {  ReactiveFormsModule } from '@angular/forms';
import { PaymentComponent } from './payment/payment.component';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { FlexLayoutModule } from '@angular/flex-layout';
import { ViewCartComponent } from './view-cart/view-cart.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { RescheduledComponent } from './rescheduled/rescheduled.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { PaymentService } from './state/services/PaymentService';

@NgModule({
  declarations: [
    ScheduleComponent,
    ConfirmationPopupComponent,
    TestCenterComponent,
    DatePickerComponent,
    PaymentComponent,
    ViewCartComponent,
    RescheduledComponent,
  
  ],
  imports: [
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    ScheduleRoutingModule,
    MatMomentDateModule,
    FlexLayoutModule,
    PageLayoutModule,
    //PaymentTranscationDetailsComponent,
    StoreModule.forFeature(SCHEDULED_STATE_NAME, scheduledReducer),
    EffectsModule.forFeature([ScheduledEffects]),
    MatSidenavModule,
    NgDynamicBreadcrumbModule,
    MatExpansionModule,
    MatDialogModule
  ],
  providers:[{provide: MatDialogRef,
    useValue: {}},
    DatePipe, 
    
    // { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
    PaymentService
  ]
})
export class ScheduleModule { }
