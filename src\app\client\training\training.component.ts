import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { Action, Store } from '@ngrx/store';
import moment from 'moment';
import { BehaviorSubject, Observable } from 'rxjs';
import * as tableTypes from 'src/app/core/common-component/table/dynamic-table-types'
import { GlobalUserService } from 'src/app/core/global-user.service';
import { candidateStatus, courseDetails, geteligibityRoute } from './state/training-program.action';
import { erResponseDetails, trainingCandiateDetailsStatus, trainingDetailsStatus } from './state/training-program.selector';
import { TrainingProgramState } from './state/training-program.state';
import { TrainingService } from './training.service';
import { CourseDetails } from './training.types';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-training',
  templateUrl: './training.component.html',
  styleUrls: ['./training.component.scss']
})
export class TrainingComponent implements OnInit {
  candidateName: any;
  public dataSource: MatTableDataSource<CourseDetails>;
  public dataSourceCandidate: MatTableDataSource<CourseDetails>;
  courseDetails: Array<any> = [];
  candidateDetials: Array<any> = [];
  traningCandidateDetails: Array<any> = [];
  traningCandidateList: Array<any> = [];
  CourseStatus: number = null;
  disableVerification: boolean = false;
  candidateListName: Array<any> = [];
  today = new Date();

  hightlightedColumnIndexForCourseDate: number = null;
  hightlightedRowIndexForCourseDate: number = null;

  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();
  tableOptions = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false],
    showMatTabs: false,
    matTabs: [],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: false,
    showHeaderRow: false
  }

  displayedColumns = [[
    <tableTypes.column>{
      visible: true,
      id: "CreatedOn",
      name: "Course Completion Date",
      cellType: "onlydate",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["width-4"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "pending_records",
      name: "Pending Records",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["cursor-pointer"],
    },
    <tableTypes.column>{
      visible: true,
      id: "confirmed_records",
      name: "Confirmed Records",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["cursor-pointer"],
    },
    <tableTypes.column>{
      visible: true,
      id: "denied_records",
      name: "Denied Records",
      cellType: "number",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: [],
      cellCssClasses: ["cursor-pointer"],
    },
  ]]

  tableOptionsCandidate = {
    tableName: "",
    showPaginator: true,
    pageSizeOptions: [8, 20, 100],
    pageSize: 8,
    showAddButton: [false],
    showMatTabs: false,
    matTabs: [],
    showSearch: false,
    verticalScroll: true,
    horizontalScroll: true,
    showTableHeaderName: false,
    showHeaderRow: false
  }

  displayedColumnsCandidate = [[
    <tableTypes.column>{
      visible: true,
      id: "checkbox",
      name: "Checkbox",
      cellType: "text",
      headerType: "checkbox",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-4"],
      cellCssClasses: [],
    },
    <tableTypes.column>{
      visible: true,
      id: "firstName",
      name: "Candidate Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["width-5"],
      cellCssClasses: ["cursor-pointer"],
    },
    <tableTypes.column>{
      visible: true,
      id: "trainingProgramName",
      name: "Training Program Name",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["width-4"],
      cellCssClasses: ["cursor-pointer"],
    },
    <tableTypes.column>{
      visible: true,
      id: "completedDate",
      name: "Completed Date",
      cellType: "onlydate",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ['width-5'],
      cellCssClasses: ["cursor-pointer"],
    },
    <tableTypes.column>{
      visible: true,
      id: "city",
      name: "City",
      cellType: "text",
      headerType: "normal",
      headerAlign: "center",
      cellAlign: "center",
      headerCssClasses: ["w-1/6"],
      cellCssClasses: ["cursor-pointer"],
    },
  ]]

  candidateRecordStatus: string;
  public range: FormGroup;
  startDate: Date | string;
  EndDate: Date | string;
  erItems: Array<any> = [];
  userDetails: any;
  erId: number;
  index = null;
  mainindex = null;

  constructor(public store: Store<TrainingProgramState>, public trainingService: TrainingService,
    public global: GlobalUserService,   private elementRef:ElementRef) {
  }


  public ngOnInit(): void {
    this.range = new FormGroup({
      start: new FormControl(),
      end: new FormControl()
    });
    this.getCandidateDetials();
    this.global.userDetails.subscribe((user) => {
      if (user != null) {
        this.userDetails = user;
        this.store.dispatch(candidateStatus({ personDetail: user }));
        this.store.dispatch(geteligibityRoute({ tenantId: user }))
      }
    })
    this.store.select(trainingDetailsStatus).subscribe((data: any) => {
      try {
        data.status.forEach((x: any) => {
        });
        this.traningCandidateDetails = data.status;
        this.getCourse();
      }
      catch (e) {

      }
    })
    this.store.select(trainingCandiateDetailsStatus).subscribe((details: any) => {
      try {
        this.traningCandidateList = details.listDetail;
        this.getCandidateDetials();
      }
      catch (e) {

      }
    })

    this.store.select(erResponseDetails).subscribe(er => {
      this.erItems = [];
      if (er != null) {
        er.forEach(element => {
          this.erItems.push({
            erName: element.eligibilityName,
            erId: element.id
          })
        });
      }
    })

    // this.test()
  }

  public OnStartDate(date: Date) {
    this.startDate = date;
  }

  public OnEndDate(date: Date) {
    this.disableVerification = false;
    this.dataSourceCandidate = new MatTableDataSource(null);
    this.EndDate = date;
    var datePipe = new DatePipe("en-US");
    this.EndDate = datePipe.transform(this.EndDate, 'yyyy-MM-dd');
    this.startDate = datePipe.transform(this.startDate, 'yyyy-MM-dd');
    this.global.userDetails.subscribe((user) => {
      if (user != null) {
        this.store.dispatch(candidateStatus({ start: this.startDate, end: this.EndDate, personDetail: user }));
      }
    })
  }

  public dateSelected(event) {
    this.candidateRecordStatus = event.column.id;
    if (this.mainindex != null) {
      this.dataSource.data[this.mainindex]["cellCssClasses"] = []
      this.dataSource.data[this.mainindex].cellCssClasses = [];
    }
    this.mainindex = event.rowIndex;
    this.dataSource.data.forEach((x: CourseDetails) => {
      x["cellCssClasses"] = [];
      x.cellCssClasses.splice(x.cellCssClasses.indexOf('onClickBlack'));
    })
    this.dataSource.data.forEach((x: CourseDetails, i: number) => {
      if (i != event.rowIndex) {
        x.cellCssClasses.push('onClickBlack', 'cursor-pointer');
      }
    })
    this.dataSource.data[event.rowIndex].cellCssClasses.push('link-animation', 'cursor-pointer',);

    this.displayedColumns[0].forEach((x) => {
      x["cellCssClasses"] = [];
      x.cellCssClasses.splice(x.cellCssClasses.indexOf("link-animation"));
      x.cellCssClasses.push('cursor-pointer')
    })
    this.displayedColumns[0][0].cellCssClasses.push("link-animation")

    this.displayedColumns[0].forEach(element => {
      event.element["cellCssClasses"] = []
      if (element.id == event.column.id) {
        element.cellCssClasses.push("link-animation", 'cursor-pointer')
      }
    });

    if (event.column.id == "pending_records") {
      this.CourseStatus = 2;
    }
    else if (event.column.id == "confirmed_records") {
      this.CourseStatus = 7;
    }
    else if (event.column.id == "CreatedOn") {
      this.CourseStatus = 0;
    }
    else {
      this.CourseStatus = 3;
    }
    this.global.userDetails.subscribe(user => {
      if (user != null) {
        var datePipe = new DatePipe("en-US");
        var test = datePipe.transform(event.element.CreatedOn, 'yyyy-MM-dd','+0000');
        this.store.dispatch<Action>(courseDetails({ date: test, status: this.CourseStatus, personDetail: user }))
        this.trainingService.createdOnDate = event.element.CreatedOn;
        this.trainingService.currentStatus = this.CourseStatus;
        this.disableVerification = false;
      }
    })
  }
  public getCandidateDetials(): void {
    this.index = null
    this.candidateDetials = [];
    this.traningCandidateList.forEach(list => {
      var datePipe = new DatePipe("en-US");
      var test = datePipe.transform(list.completedDate, 'yyyy-MM-dd', '+0000');
      this.candidateDetials.push({
        firstName: `${list.firstName} ${list.lastName}`,
        personFormReviewId: list.personFormReviewId,
        completedDate: list.completedDate,
        city: list.city,
        trainingProgramName: list.trainingProgramName
      })
    });
    this.dataSourceCandidate = new MatTableDataSource(this.candidateDetials);
    this.tabIndexStream.next(0);
  }

  public getCourse(): void {
    this.courseDetails = [];
    this.traningCandidateDetails.forEach(element => {
      var datePipe = new DatePipe("en-US");
      var modifiedDate = datePipe.transform(element.createdOn, 'yyyy-MM-dd', '+0000');
      this.courseDetails.push({
        CreatedOn: element.createdOn,
        pending_records: element.pending,
        confirmed_records: element.approved,
        denied_records: element.denied,
      })
    });
    this.mainindex = null;
    this.dataSource = new MatTableDataSource(this.courseDetails);
    this.dataSourceCandidate = new MatTableDataSource(null);
    this.tabIndexStream.next(0);
  }

  public candidateSelected(event): void {
    this.tabIndexStream.next(0);
    if (this.index != null) {
      this.dataSourceCandidate.data[this.index]["cellCssClasses"] = []
      this.dataSourceCandidate.data[this.index].cellCssClasses = []
    }
    this.index = event.rowIndex;
    this.dataSourceCandidate.data[event.rowIndex].cellCssClasses = ["link-animation"]
    if (this.candidateRecordStatus === 'pending_records') {
      this.disableVerification = false;
      this.candidateName = event.element;
      this.candidateListName = [];
      this.disableVerification = true;
    }
  }

  public checkBoxClicked(event): void {
    if (this.index != null) {
      this.dataSourceCandidate.data[this.index]["cellCssClasses"] = []
      this.dataSourceCandidate.data[this.index].cellCssClasses = []
    }
    this.candidateListName = [];
    if (this.candidateRecordStatus === 'pending_records') {
      this.disableVerification = false;
      this.candidateListName = event.selection;
      if (this.candidateListName.length > 0) {
        this.disableVerification = true;
      }
    }
  }

  private test(): void{
    var s = document.createElement("script");
    s.id = "zsiqchat";
    s.type = "text/javascript";
    s.src = `${environment.redirectUrl}/client/assets/static data/zoho.js`;
    this.elementRef.nativeElement.appendChild(s);
  }
}
