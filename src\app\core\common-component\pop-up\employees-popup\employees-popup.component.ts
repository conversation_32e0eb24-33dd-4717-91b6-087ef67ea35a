import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Action, Store } from '@ngrx/store';
import { creationSuccessfull } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { environment } from 'src/environments/environment';
import { button } from '../pop-up.types';
import { URL } from 'src/app/core/url';
import { DatePipe } from '@angular/common';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import moment from 'moment';

@Component({
  selector: 'app-employees-popup',
  templateUrl: './employees-popup.component.html',
  styleUrls: ['./employees-popup.component.scss']
})
export class EmployeesPopupComponent implements OnInit {
  @Input() preResponse?: any = null;
  expanded = -1;
  adminDetailsForm: FormGroup
  availableDates:Array<{testCenters:Array<any>}>=[]
  EmployeesDetails
  searchvalue: FormGroup
  @Input() dialogRef: MatDialogRef<any>;
  @Input() buttons?: Array<button>;
  formattedDate:string
  isSelectDate:boolean =false
  testsiteId: any;
  TescenterId:string
  displayPaymentMethods:boolean = false
  ExamId:string
  testCenters:Array<{siteSlot:Array<{slotime:string,siteslotid:number,examdateUTC:string,examdate:string}>}>=[]
  allUniqueDates: any[] = [];
  selectedDateTestCenters=[]
  EventData:Array<object>=[]
  examDateTime:string
  selectedSlots:any;
  step:number
  EVentDetails:any
  ExamType:Array<{ExamCode:string}>
  selectedTestCenterSoltDate:string
EventDateUTC
  userData = [{ id: 1, firstName: "shreya", status: "active", emailId: "<EMAIL>", Address: "bangalore", Registry_Number: "5688388" }]
  constructor(private formBuilder: FormBuilder, private http: HttpClient,private global:GlobalUserService,private snackbar:SnackbarService,private store:Store, @Inject(MAT_DIALOG_DATA) public data: any,private datePipe:DatePipe) {
    this.formattedDate = this.datePipe.transform(data.ExamDetails.examDateTime, 'yyyy-MM-dd hh:mm a','+0000');
    this.ExamId = data.ExamDetails.examId
    this.TescenterId = data.ExamDetails.testCenterDetails.testCenterId
       this.ExamType = JSON.parse(data.ExamDetails.examAttemptRule).filter((x)=>x.ExamId === this.ExamId)

   }

  ngOnInit(): void {
    this.setForm()
    if(this.data.id ==='transfer_details'){
      this.adminDetailsForm.controls['name'].disable();
      this.adminDetailsForm.controls['startDate'].disable();
      this.adminDetailsForm.controls['registerNumber'].disable();
    }

   
  }

  setForm() {
    this.searchvalue = this.formBuilder.group({
      serach: new FormControl("", [Validators.required])
    })
    if(this.preResponse == null){
      this.adminDetailsForm = this.formBuilder.group({
        name: [
          this.EmployeesDetails ? `${this.EmployeesDetails.FirstName} ${this.EmployeesDetails.MiddleName !=null?this.EmployeesDetails.MiddleName:""} ${this.EmployeesDetails.LastName}` :this.ExamId?this.ExamId: "",
          [Validators.required],
        ],
        registerNumber: [
          this.EmployeesDetails ? this.EmployeesDetails.CertNumber :this.TescenterId?this.TescenterId: "",
          [Validators.required],
  
        ],
        Address: [
          this.EmployeesDetails ? this.EmployeesDetails.Address : "",
          // [Validators.required],
        ],
        status: [
          this.EmployeesDetails ? this.EmployeesDetails.RegistryStatus : "",
          [Validators.required],
        ],
        startDate: [
          this.EmployeesDetails ? this.EmployeesDetails.startDate :this.formattedDate?this.formattedDate: "",
          [Validators.required],
        ],
        endDate: [
          this.EmployeesDetails ? this.EmployeesDetails.endDate : "",
          // [Validators.required],
        ],
  
      });
      if (this.EmployeesDetails != null) {
        this.adminDetailsForm.controls['name'].disable();
        this.adminDetailsForm.controls['registerNumber'].disable();
        this.adminDetailsForm.controls['Address'].disable();
        this.adminDetailsForm.controls['status'].disable();
      }
    }else{
      this.adminDetailsForm = this.formBuilder.group({
        name: [
          this.preResponse ? (`${this.preResponse.firstName} ${this.preResponse.lastName} ${this.preResponse.middleName}`) : "",
          [Validators.required],
        ],
        registerNumber: [
          this.preResponse ? this.preResponse.certNumber : "",
          [Validators.required],
  
        ],
        Address: [
          this.preResponse ? this.preResponse.address : "",
          // [Validators.required],
        ],
        status: [
          this.preResponse ? this.preResponse.certStatus : "",
          [Validators.required],
        ],
        startDate: [
          this.preResponse ? this.preResponse.employmentStartDate : "",
          [Validators.required],
        ],
        endDate: [
          this.preResponse ? this.preResponse.employmentEndDate : "",
          // [Validators.required],
        ],
  
      });
      if (this.preResponse != null) {
        this.adminDetailsForm.controls['name'].disable();
        this.adminDetailsForm.controls['registerNumber'].disable();
        this.adminDetailsForm.controls['Address'].disable();
        this.adminDetailsForm.controls['status'].disable();
      }

     
    }
   

  }

  searchRegistry() {
    this.http.get(URL.BASE_URL + `registry/get-recentCertificate?certNumber=${this.searchvalue.value.serach}&state=${this.global.userDetails.getValue().stateId}`).subscribe((data: any) => {
      if (data && this.preResponse ==null) {
        this.EmployeesDetails = data
        this.setForm()
      }
    })

  }

  SelectedStateEventType(eventDetails){
    this.EVentDetails = eventDetails
    this.getTSMSlots()

  }

  onDateChange(event: MatDatepickerInputEvent<Date>){
    this.EventDateUTC = event.value

    this.http.get<any>(
      environment.baseUrl +
      `client/api/state/${this.global.userDetails.value.clientORtenantId}`
    ).subscribe((data)=>{
      if(data){
         let Selectedstate = data.filter((x)=>x.stateName ===this.global.personEventId.stateName)
         this.TescenterId !='' && this.TescenterId !=null && this.TescenterId !=undefined && event.value ? this.http.post(`${environment.baseUrl}client/api/credentia/gis-events`,{facilityCode:this.TescenterId,eventDateTimeUTC:moment(event.value).format('YYYY-MM-DD'),examType: this.ExamType[0].ExamCode, state:Selectedstate[0].stateCode}).subscribe((data:Array<object>)=>{
          if(data){
           this.EventData = data
          }
       }):null
      }
     
    })
   
  }

  AddEmployees(){
    if(this.preResponse ==null){
      let body = {
        employerId:this.global.userDetails.value.clientORtenantId,
        personId:  this.EmployeesDetails.PersonId,
        employeeId: 0,
        employementStartDate:this.adminDetailsForm.value.startDate,
        employementEndDate:this.adminDetailsForm.value.endDate,
        certNumber: this.adminDetailsForm.getRawValue().registerNumber
    }
    this.http.post(environment.baseUrl + `client/api/api/Employer/employee/add`,body).subscribe((data:any)=>{
        if(data){
         
          this.preResponse ? this.snackbar.callSnackbaronSuccess("Updated SuccessFully") : this.snackbar.callSnackbaronSuccess("Created SuccessFully")
          this.dialogRef.close()
        }
        this.store.dispatch<Action>(creationSuccessfull())
    },
  
    (error:any)=>{
          this.snackbar.callSnackbaronError(`${error.message.message.error}`)
    })
    }else{
        let body = {
        employeeId:this.preResponse.personTenantRoleId,
        employementStartDate:this.adminDetailsForm.value.startDate,
        employementEndDate:this.adminDetailsForm.value.endDate,
    }
    this.http.put(environment.baseUrl + `client/api/api/Employer/employee/update`,body).subscribe((data:any)=>{
        if(data){
        
          this.preResponse ? this.snackbar.callSnackbaronSuccess("Updated SuccessFully") : this.snackbar.callSnackbaronSuccess("Created SuccessFully")
          this.dialogRef.close()
        }
        this.store.dispatch<Action>(creationSuccessfull()) ;
    },(error:any)=>{
          this.snackbar.callSnackbaronError(`${error.message.message.error}`)
    })
    }
    
  }

  setAllUniqueDates(data: any[]) {
    data.forEach(element => {
      element.setHours(0, 0, 0, 0)
    });
    //data.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    return Array.from(new Set(data))
  }

  createDateArray(data) {
    let array = [];
    data.forEach(elem1 => {
      let elem = elem1.slots !=null?elem1.slots:elem1.siteSlot
      elem.forEach(elem2 => {
        let date = new Date(Date.parse(elem2?.slotDate !=null?elem2.slotDate:elem2.examdate));
        array.push(date)
      });
    });
    return array;
  }

  setTestCentersForSelectedDate(slotDate: string) {
    var datePipe = new DatePipe("en-US");
    let slotDates = datePipe.transform(slotDate, 'MM/dd/yyyy', '+0000')
    let date = new Date(Date.parse(slotDates));
    const slot = this.allUniqueDates.find(x => x.toDateString() === date.toDateString());
    if (slot) {

      this.selectedDateTestCenters = [];

      for (let i = 0; i < this.testCenters.length; i++) {
        let testcenter = this.testCenters[i]?.siteSlot

        for (let j = 0; j < testcenter.length; j++) {
          let slotdate = testcenter[j].examdate


          let comparedDate = new Date(Date.parse(slotdate)).setHours(0, 0, 0, 0);

          if (comparedDate == slot.getTime()) {
           this.selectedDateTestCenters.push(this.testCenters[i])
          

            break;

          }

        }

      }

    }

  }

  event(i: any,item:any) {
    this.testsiteId = i;
    this.selectedSlots=item;
    this.displayPaymentMethods = true
    this.examDateTime=item.slotDateUtc !=null?item.slotDateUtc:item.examdateUTC;


  }

  setStep(index: number) {
    this.expanded = index;
  }

  selectedDateTime(slot) {
    this.examDateTime = slot.slotDateUtc?slot.slotDateUtc: slot.examdate?slot.examdate:slot.day
    // this.avaiableslots = slot.;
    this.selectedTestCenterSoltDate=slot.strSlotDate?slot.strSlotDate: slot.examdate?slot.examdate:slot.day
  }

  selectedDate(event) {
    this.step =event.slotDate?event.slotDate: event.day?event.day:event.examdate
    
  }
  isActive(item: any) {
    return this.selectedSlots === item;
  };
  getTSMSlots(){
    this.http.post(`${environment.baseUrl}schedulemsvc/api/test-center/SlotsForClientEventCode`,{startdate:this.EventDateUTC,enddate:this.EventDateUTC,duration:30,examCode:this.EVentDetails.examType,clientEventCode:this.EVentDetails.eventId,stateCode:this.EVentDetails.state}).subscribe((data:Array<any>)=>{
       if(data){
        let tempDatesSlots = [];
         this.availableDates = data
         this.availableDates.forEach((item) => item.testCenters.forEach(x => {
          tempDatesSlots.push(x);
        }))
        this.testCenters = tempDatesSlots;
        if (this.availableDates.length > 0) {
          this.isSelectDate = true
          this.allUniqueDates = this.setAllUniqueDates(this.createDateArray(tempDatesSlots))

            var time: any = tempDatesSlots.filter((ele: any) => ele.siteSlot.length > 0);
            time.length > 0 ?
            (( this.setTestCentersForSelectedDate(time[0].siteSlot[0].examdate)), this.selectedDate(time[0].siteSlot[0]),this.selectedDateTime(time[0].siteSlot[0]),this.setStep(0)) : null;
          


        } 
        
       }
    })
  }

  retry_Schedule_Without_payment(){
   this.http.put(`${environment.baseUrl}client/api/exam/UpdatePersonevent?personeventId=${this.data.ExamDetails.id}`,{dateTimeUTC:this.examDateTime,slotTime:this.selectedSlots.slotime,persontenantRoleId:this.data.ExamDetails.personTenantRoleId,examType:this.data.ExamDetails.examName == 'Nurse Aide Skills Exam'?4:this.data.ExamDetails.examName == 'Nurse Aide Written Exam'?1:this.data.ExamDetails.examName == 'Nurse Aide Oral English Exam'?2:this.data.ExamDetails.examName == 'Nurse Aide Oral Spanish Exam'?3:5,clientEventId:this.EVentDetails.eventId,testCenterId:this.testsiteId?.testCenterId?.toString(),testCenterName:this.testsiteId.testCenter,testCenterAddress:this.testsiteId.address,testCenterCity:this.testsiteId.city,testSiteId:this.TescenterId,testCenterState:this.global.personEventId.stateName,testCenterPostalCode:this.testsiteId.zipCode
  }).subscribe((data)=>{
     if(data){
       this.snackbar.callSnackbaronSuccess('Transfer Candidate SucessFully')
       this.dialogRef.close()
       this.store.dispatch<Action>(creationSuccessfull())
     }
  },(err:HttpErrorResponse | any)=>{
    this.snackbar.callSnackbaronError(`${err.message.error.text}`)
  })
  }

}
