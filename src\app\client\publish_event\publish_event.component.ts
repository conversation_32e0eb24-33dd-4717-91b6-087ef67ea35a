import { Component, OnInit } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { environment } from 'src/environments/environment';
import { getUserDetails } from '../state/shared/shared.actions';
import { Action, Store } from '@ngrx/store';
import { get_userDetails } from '../state/shared/shared.selectors';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { HttpService } from 'src/app/core/http.service';
import { Roles } from '../dynamic-component-loader/dynamic-component-loader.types';
import { Role } from 'src/app/core/common-component/examroom-formbuilder/form-builder.types';
import { SnackbarService } from 'src/app/core/snackbar.service';
@Component({
  selector: 'app-publish_event',
  templateUrl: './publish_event.component.html',
  styleUrls: ['./publish_event.component.scss']
})
export class PublishComponent implements OnInit {
  Value
  Gisturl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  $Gisturl: Observable<string> = this.Gisturl.asObservable();
  candidateDetails

  constructor(private global: GlobalUserService, private store: Store, private http: HttpService, private router: Router,private route:ActivatedRoute,private snackbar:SnackbarService) {
         this.route.paramMap.subscribe((params:Params)=>{
            this.candidateDetails = params.params
         })
  }

  ngOnInit(): void {
setTimeout(()=>{
  this.store.dispatch<Action>(getUserDetails({ personTenantRoleId: +this.global.PublishEmail }));
  this.store.select(get_userDetails).subscribe((data: any) => {
    if ((this.global.userDetails.getValue().roleId == Role.OperationStaff || this.global.userDetails.getValue().roleId == Role.Training || this.global.userDetails.getValue().roleId == Role.QAC) && Number(this.candidateDetails.NAE_Id) === 0) {
      const token = sessionStorage.getItem("token")
    window.open(`${environment.gistUrl}?token=${token}`, "_blank")
    this.router.navigateByUrl('/dashboard')
   }else{
    let body = {
      text: `ExamRoom_CandidateId=${this.candidateDetails.vmId}&ExamRoom_ExamId=${this.candidateDetails.personId}&VMasterId=${this.candidateDetails.eventId}&NAE_Id=${this.candidateDetails.NAE_Id}&EventId=${this.candidateDetails.personId}&SkillsAccessKey=${environment.SkillsAccessKey}&SkillsSecrectKey=${environment.SkillsSecrectKey}&callScoreAPI=${this.candidateDetails.callScoreAPI}`,
    };
    this.http.ErruptCodeNAE(body).subscribe((data) => {
      if (data) {
        let url = `${environment.RedirecttoSkillsExams}#/pages?id=${data}`;
          this.Gisturl.next(url)
      }
    },(err:HttpErrorResponse | any)=>{
      this.snackbar.callSnackbaronError(`${err.message.message.error}`)
  });
   }


  },)
},2000)
 
    window.addEventListener('message', function(event) {
      if (event.data === true) {
        // window.parent.postMessage('Success', '*');
        let scanURL = sessionStorage.getItem('scanCurrentURL')
        window.location.href = scanURL;
      }
 });
  }


}

export interface GistResponse {
  gisAccessToken: string
  message: string
  result: number
}
