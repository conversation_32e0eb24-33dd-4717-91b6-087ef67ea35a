import { SharedState } from './shared.state';
import { createFeatureSelector, createSelector } from '@ngrx/store';
export const SHARED_STATE_NAME = 'shared';

const getSharedState = createFeatureSelector<SharedState>(SHARED_STATE_NAME);

export const getLoading = createSelector(getSharedState, (state) => {
  return state.showLoading;
});

export const getErrorMessage = createSelector(getSharedState, (state) => {
  return state.errorMessage;
});

export const selectConsoleLoaderJSON = createSelector(getSharedState, (state) => {
  return state.consoleLoaderJSON;
});
export const selectUser = createSelector(getSharedState, (state) => {
  return state.getuserdata;
});

export const selectDatarefresh = createSelector(getSharedState, (state) => {
  return state.refreshData;
})

export const updateAbsent= createSelector(getSharedState,(state)=>{
  return state.response
})

export const bookletOrderStatus= createSelector(getSharedState,(state)=>{
  return state.BookletOrderStatusInformation
})



export const selectAccessData = createSelector(getSharedState, (state) => {
  return state.accessData;
});

export const get_userDetails = createSelector(getSharedState, (state) => {
  return state.userdetails
})

export const get_decodeInfo = createSelector(getSharedState, (state) => {
  return state.decodeInfo
})

export const get_reportJSON = createSelector(getSharedState, (state) => {
  return state.reportJSON
})

