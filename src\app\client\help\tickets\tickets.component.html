<div class="shadow-none card cardBorder justify-start dashboard px-2 pt-2 h-full" gdColumn="2/ -1" gdColumn.lt-md="1 " gdColumn.lt-sm="1">
  <div class="eligibility-desc mb-3">
      <div class="eligibility1 overflow-auto">
      <div class="exam">
        <h2 class="px-2 text-xs font-bold fontColor1">
          {{ selectedCategory.name }}
        </h2>
      <!-- ===========     CARD START -->
      <ul>
          <li *ngIf="selectedCategory.name=== 'Log In/Reset Password'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
          ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Payment Options'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
          ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Scheduling Error'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Exam Rescheduling'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Scores'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Credentia System Issue'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>

          <li *ngIf="selectedCategory.name=== 'Registry Certificate'">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Application'&& TicketList.length == 0">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <li *ngIf="selectedCategory.name=== 'Registering for Exam' && TicketList.length == 0">
            <exai-card 
            [selectedCategoryName]="selectedCategory.name"
            [getHelp]="false"
            ></exai-card>
          </li>
          <ng-container *ngIf= "selectedCategoryId.includes(selectedCategory.id)  "> 
            <li *ngFor="let ticket of tickets$ | async"  class="card cardBorder m-2 shadow-none">
                <exai-card *ngIf="selectedCategory.name=== 'Application' && TicketList.length > 0"
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                [name]="ticket.formName"
                [eligibilityRouteName]="ticket.eligibilityRouteName"
                [ticketRaised]="ticket.ticketRaised"
                [id]="ticket.code"
                [applicationId]="ticket.personFormId"
                [createdDate]="ticket.submittedDateTime"
                [status]="ticket.status"
                [changedDate]="ticket.statusSinceDate"></exai-card>

               <exai-card *ngIf="selectedCategory.name=== 'Registering for Exam'&& TicketList.length > 0"
                [selectedCategoryName]="selectedCategory.name"
                [getHelp]="false"
                [name]="ticket.examName"
                [eligibilityRouteName]="ticket.eligibilityRouteName"
                [ticketRaised]="ticket.ticketRaised"
                [id]="ticket.id"
                [applicationId]="ticket.examId"
                [createdDate]="ticket.examDateTime"
                [status]="ticket.examStatus"
                [changedDate]="ticket.statusSinceDate"></exai-card>
            </li>

          
                            </ng-container>
                           
      </ul>
      <!-- =========== CARD END -->
    </div>
</div>
</div>
