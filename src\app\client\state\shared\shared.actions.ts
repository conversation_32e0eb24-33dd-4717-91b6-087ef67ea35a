import { createAction, props } from "@ngrx/store";
import { AbsentResponse, Access, getUsers, ReportJSON, userDetails,BooketOrderStatusJson } from "./shared.state";
import { consoleLoaderJSON } from "../../dynamic-component-loader/dynamic-component-loader.types";
import { CartItem } from "../../schedule/state/models/cartItem";
export const SET_LOADING_ACTION = "[shared state] set loading spinner";
export const SET_ERROR_MESSAGE = "[shared state] set error message";
export const SET_USER_DETAILS = "[user details] user details set";

export const setLoadingSpinner = createAction(
  SET_LOADING_ACTION,
  props<{ status: boolean }>()
);

export const setErrorMessage = createAction(
  SET_ERROR_MESSAGE,
  props<{ message: any }>()
);

export const setUserDetails = createAction(
  SET_USER_DETAILS,
  props<{ userDetails: userDetails | null }>()
);

export const fetchConsoleLoderJSON = createAction(
  "[CONSOLE LOADER JSON] Fetching Console Loader JSON"
);

export const fetchedConsoleLoaderJSON = createAction(
  "[CONSOLE LOADER JSON] Fetched Console Load",
  props<{ consoleLoaderJSON: consoleLoaderJSON }>()
);

export const purgeConsoleLoaderJSON = createAction(
  "[CONSOLE LOADER JSON] Purging Console Loader JSON from store to decrease memory consumption"
);

export const fetchAccountAccess = createAction(
  "[ACCOUNT ACCESS] Fetching Account Access",
  props<{ personTenantRoleId: number; consoleId: number,stateId:number }>()
);

export const fetchedAccountAccess = createAction(
  "[ACCOUNT ACCESS] Fetched Account Access",
  props<{ accessData: Array<Access> }>()
);

export const setUserDetailsFromToken = createAction(
  "[UserDetails] setUserDetailsFromToken",
  props<{ token?: string, email?: string, personTenantroleId?:number }>()
);

export const setReportToken = createAction(
  "[Report Token] set Report Token in session",
  props<{ json: ReportJSON }>()
);

export const Refreshtable = createAction('[CREATE] Refresh Data');

export const getUserDetails = createAction(
  "[UserDetails] getUserDetails",
  props<{ personTenantRoleId?: number,emailId?:string }>()
);

export const gotUserDetails = createAction(
  "[UserDetails] gotUserDetails",
  props<{ userDetails: userDetails }>()
);

export const UpdateAbsentDetails=createAction('[GET Absent] getAbsentDetails',props<{body:AbsentResponse}>());

export const GotAbsentDetails=createAction('[Got Absent Details] GotAbsentDetails',props<{response:any}>());

export const getuser = createAction(
  "[user] getUser",
  props<{ personTentantId: number }>()
);

export const gotUser = createAction(
  "[userDetails] gotUser",
  props<{ getuserDetails: getUsers }>()
);

export const reportSpinBoardList = createAction(
  "[Reportspinboard] report api call",
  props<{ username: string,org_id:number,dependent_object_version:string,include_auto_created_objects:boolean,include_dependent_objects:boolean,include_details:boolean,include_headers:boolean,include_hidden_objects:boolean,include_incomplete_objects:boolean,include_visualization_headers:boolean,record_offset:number,record_size:number,include_stats:boolean}>()
);

export const gotReportSpinBoardList = createAction(
  "[GOT Reportspinboard] report api call response",
  props<{ json: ReportJSON }>()
);

export const getOrderStatus = createAction('[Get Booklet OrderStatus]',props<{body?:{},text?:string}>());
export const gotOrderStatus = createAction('[Got Booklets OrderStatues]',props<{response: any}>());

export const clearUserData = createAction('[clearUserData] clear user Response state');
export { BooketOrderStatusJson };

