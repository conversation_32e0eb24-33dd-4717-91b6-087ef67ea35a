import { Location } from '@angular/common';
import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormModel, DynamicFormService } from '@ng-dynamic-forms/core';
import { Observable, Subscription } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { FileUploadService } from '../dialogs/services/file-upload.service';
import { HelpService } from '../help.service';
import { Category } from '../interfaces/category';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'exai-support-ticket-support',
  templateUrl: './support-ticket.component.html',
  styleUrls: ['./support-ticket.component.scss']
})
export class SupportTicketComponent implements OnInit, OnD<PERSON>roy {

  constructor(
    private fb: FormBuilder,
    private helpService: HelpService,
    private fileUploadService: FileUploadService,
    private location: Location,
    private snackbar: SnackbarService,
    private dynamicFormService: DynamicFormService,
    public router:Router,
    private matDialog:MatDialog
  ) { }
  
  raiseTicketSub: Subscription
  fileName = '';
  categories$: Observable<Category[]>
  selectedCategory$: Observable<Category[]>
  setSelectedTicketSub: Subscription

  formGroup = this.fb.group({
    summary: [null, [Validators.required, Validators.maxLength(50)]],
    description: [null, Validators.required],
  })

  fileUploadModel: DynamicFormModel = [
    new DynamicFileUploadModel({
    required: false,
    id: 'attachments',
    name: 'attachments',
    label: 'Supporting Documents',
    accept: ['.pdf', '.doc', '.docx'],
    multiple: true,
    updateOn: DynamicFormHook.Change,
    showFileList: true,
    additional: {
      appearance: 'outline'
    }
    })
  ]
  fileUploadFormGroup: FormGroup = null;
  ngOnInit(): void {
    this.setSelectedTicketSub = this.helpService.getTicket(this.helpService.selectedTicketId.value).pipe(
      tap(data => {})
    ).subscribe()
    this.categories$ = this.helpService.categories
    this.selectedCategory$ = this.helpService.selectedCategory;
    this.fileUploadFormGroup = this.dynamicFormService.createFormGroup(this.fileUploadModel)
  }
  submit() {
    
    this.raiseTicketSub = this.fileUploadService.raiseTicket({ a: { ...this.formGroup.value }, b: this.fileUploadFormGroup.value["attachments"] ? this.fileUploadFormGroup.value['attachments'] : '' }).subscribe(
      data => {
        this.formGroup.reset()
        this.fileUploadFormGroup.reset();
        this.matDialog.closeAll();
        this.snackbar.callSnackbaronSuccess('Successfully Created Ticket.');
      }
    )
  }
  cancel() {
    this.matDialog.closeAll();
  }
  ngOnDestroy() {
    this.raiseTicketSub?.unsubscribe();
    this.setSelectedTicketSub.unsubscribe()
  } 
}
