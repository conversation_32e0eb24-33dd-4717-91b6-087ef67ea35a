<div class="toolbar w-full px-3 cardBorder1" fxLayout="row" fxLayoutAlign="start center" vexContainer>
  <button (click)="openSidenav()" [fxHide]="!mobileQuery" mat-icon-button type="button">
    <mat-icon [icIcon]="icMenu"></mat-icon>
  </button>
  <div class="header-padding">
    <img src="assets/img/Icons/Credentia-logo.svg" class="logo">
  </div>
  <!-- <a *ngIf="isVerticalLayout$ | async"
     [routerLink]="['/']"
     class="ltr:mr-4 rtl:ml-4 block"
     fxLayout="row"
     fxLayoutAlign="start center">
     <img src="assets/img/Icons/ER-logo.svg" class="logo" fxHide fxShow.gt-sm>
    <h1 [fxHide]="mobileQuery" class="title ltr:pl-4 rtl:pr-4 m-0 "></h1>
  </a> -->

  <!-- <button *ngIf="isHorizontalLayout$ | async"
          [fxHide]="mobileQuery"
          [matMenuTriggerFor]="addNewMenu"
          color="primary"
          mat-raised-button
          type="button">ADD NEW
  </button>

  <mat-menu #addNewMenu="matMenu" [overlapTrigger]="false" xPosition="after" yPosition="below">
    <button mat-menu-item>
      <mat-icon [icIcon]="icPersonAdd"></mat-icon>
      <span>Add Contact</span>
    </button>

    <button mat-menu-item>
      <mat-icon [icIcon]="icDoneAll"></mat-icon>
      <span>Add Task</span>
    </button>

    <button mat-menu-item>
      <mat-icon [icIcon]="icAssignmentTurnedIn"></mat-icon>
      <span>Add Project</span>
    </button>

    <button [matMenuTriggerFor]="documentMenu" mat-menu-item>
      <mat-icon [icIcon]="icBallot"></mat-icon>
      <span>Add Document</span>
    </button>
  </mat-menu>

  <mat-menu #documentMenu="matMenu">
    <button mat-menu-item>
      <mat-icon [icIcon]="icDescription"></mat-icon>
      <span>Add Quote</span>
    </button>

    <button mat-menu-item>
      <mat-icon [icIcon]="icAssignment"></mat-icon>
      <span>Add Invoice</span>
    </button>

    <button mat-menu-item>
      <mat-icon [icIcon]="icReceipt"></mat-icon>
      <span>Add Receipt</span>
    </button>
  </mat-menu>

  <div #megaMenuOriginRef class="ltr:ml-2 rtl:mr-2">
    <button *ngIf="isHorizontalLayout$ | async"
            [fxHide]="mobileQuery"
            (click)="openMegaMenu(megaMenuOriginRef)"
            color="primary"
            mat-button
            type="button">MEGA MENU
      <ic-icon [icon]="icArrowDropDown" class="ltr:-mr-1 rtl:-ml-1" inline="true"></ic-icon>
    </button>
  </div> -->


  <!-- <div *ngIf="(isVerticalLayout$ | async) && isNavbarInToolbar$ | async" [fxHide]="mobileQuery" class="px-gutter"
    fxFlex="none" fxLayout="row" fxLayoutAlign="start center"> -->
    <!-- <navigation-item *ngFor="let item of navigationItems" [item]="item"></navigation-item> -->
  <!-- </div> -->

  <!-- <span fxFlex></span>

  <div class="-mx-1 flex items-center">
    <div class="px-1">
      <button (click)="openSearch()" mat-icon-button type="button">
        <mat-icon [icIcon]="icSearch"></mat-icon>
      </button>
    </div>

    <div class="px-1">
      <toolbar-notifications></toolbar-notifications>
    </div> -->

    <!-- <div class="px-1">
      <button (click)="openQuickpanel()" mat-icon-button type="button">
        <mat-icon [icIcon]="icBookmarks"></mat-icon>
      </button>
    </div> -->

    <!-- <div class="px-1">
      <button [matMenuTriggerFor]="languageMenu" mat-icon-button type="button">
        <mat-icon [icIcon]="emojioneUS"></mat-icon>
      </button>
    </div>

    <div class="px-1">
      <exai-toolbar-user></exai-toolbar-user>
    </div>
    
  </div> -->
  <!-- <mat-menu #languageMenu="matMenu" overlapTrigger="false" xPosition="before" yPosition="below">
      <button mat-menu-item>
        <mat-icon [icIcon]="emojioneUS"></mat-icon>
        <span>English</span>
      </button>

      <button mat-menu-item>
        <mat-icon [icIcon]="emojioneDE"></mat-icon>
        <span>German</span>
      </button>
    </mat-menu> -->

  <span fxFlex></span>

  <div class="-mx-1 flex items-center">
    <!-- <div class="px-2">
      <button mat-icon-button type="button" >
        <mat-icon [ngClass]="{'toolbar-active':(HomeActive$ | async)}">home</mat-icon>
      </button>
    </div> -->
    <div class=" rounded px-2 py-1 font-medium text-xs console-color" fxFlex="none" *ngIf="roles.includes(this.global.userDetails.value?.roleId)">
        {{this.global.userDetails.value.roleName}} Console 
    </div>

    <div *ngIf="Roles.length > 0" class="ml-4" fxFlex="none">
      <button [matMenuTriggerFor]="belowMenu" matTooltip=
      "User Roles"  mat-icon-button type="button">
      <mat-icon>supervised_user_circle</mat-icon>
    </button>
  </div>

    <div class="px-1" *ngIf="showneedhelpbutton">
      <button  class="button button_life" mat-icon-button type="button" (click)="toggleButton()"  matTooltip=
      "Need Help?">
        <img src="data:image/svg+xml;base64,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">
      </button>
    </div>

    <div class="px-1">
      <button #originRef class="button" mat-icon-button type="button" (click)="openFullscreen()" *ngIf="this.screenShow == false">
        <span class="material-icons-two-tone">
          fullscreen
        </span>
      </button>
      <button #originRef class="button" mat-icon-button type="button" (click)="closeFullscreen()" *ngIf="this.screenShow == true">
        <span class="material-icons-two-tone">
          fullscreen_exit
        </span>
      </button>
    </div>
    <div *ngIf=disableCartIcon class="px-1">
      <button #originRef class="button" mat-icon-button type="button" (click)=cartItems()> 
        <span class="material-icons-outlined" matBadge="{{global.cart.length}}" class="material-icons-outlined">
          shopping_cart
        </span>
      </button>
    </div>
    <div class="px-1">
      <button #originRef class="button" mat-icon-button type="button"  >
        <span class="material-icons-outlined" matTooltip=
        "Coming Soon.">
          notifications_none
        </span>
      </button>
    </div>

    <!-- <div class="px-2">
      <button (click)="openQuickpanel()" mat-icon-button type="button">
        <mat-icon>shopping_cart</mat-icon>
      </button>
    </div> -->

    <!-- <div class="px-2">
      <button [matMenuTriggerFor]="languageMenu" mat-icon-button type="button">
        <mat-icon [icIcon]="emojioneUS"></mat-icon>
        <img src="assets/img/US.svg" class="p-2 pb-1">
      </button>
    </div> -->

    <div class="vl"></div>

    <div class="px-2">
      <exai-toolbar-user></exai-toolbar-user>
    </div>

    <mat-menu  #belowMenu="matMenu" fxLayoutAlign="flex-end center" overlapTrigger="false" xPosition="before" yPosition="below">
      <button (click)="SelectRole(role, token,this.global.stateId)" *ngFor="let role of Roles" mat-menu-item>
        <span>{{role.roleName}}</span>
        <hr>
      </button>
    </mat-menu>

  </div>


</div>

<exai-navigation *ngIf="(isVerticalLayout$ | async) && isNavbarBelowToolbar$ | async" [fxHide]="mobileQuery">
</exai-navigation>