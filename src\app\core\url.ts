import { environment } from 'src/environments/environment';
import { HttpHeaders } from "@angular/common/http";

export const httpHeader = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json'
    })
};

export const headerOptions = new HttpHeaders({
    'Content-Type': 'application/json'
});

export class URL {
    public static readonly BASE_URL_SHORTER: string = environment.baseUrl;
    public static readonly BASE_URL: string = environment.baseUrl + 'client/api/';
    public static readonly BASE_URL_SHORTER1: string;
    public static readonly BASE_URL_PAYMENT: string = environment.baseUrl + 'client/api/payment/authorizenet/';
    public static readonly BASE_URL_SPONSOR: string = environment.baseUrl + 'sponsor/api/';
    public static readonly ACCOUNT_BASE_URL: string = environment.baseUrl + 'login/';
    public static readonly CLIENT_ACCOUNT_BASE_URL: string = environment.baseUrl + 'client/api/eligibility/';
    public static readonly CANDIDATE_URL: string = environment.baseUrl + 'candidate/api/';
    public static readonly REDIRECT_URL: string = environment.redirectUrl;
    public static readonly GET_STATE: string = 'eligibility/state';
    public static readonly FORM: string = 'form';
    public static readonly GET_ELAPSED_TIME: string = 'eligibility/examelapsedtimerules';
    public static readonly ADD_ELIGIBILITY_ROUTE: string = 'eligibility/eligibilityroute';
    public static readonly GET_ELIGIBILITY_ROUTE: string = 'eligibility/eligibilityroutes';
    public static readonly VIEW_ELIGIBILITY_ROUTE: string = 'eligibility';
    public static readonly GET_REVIEWERS: string = 'reviewerrole';
    public static readonly GET_EXAM_TYPE: string = 'exam/examtype';
    public static readonly GET_EXAM_MODE: string = 'exam/exammode';
    public static readonly GET_PAID_BY: string = 'paymasterrole';
    public static readonly GET_TIME_PERIOD: string = 'eligibility/getperiod';
    public static readonly GET_TIME_RULE: string = 'eligibility/getruletype';
    public static readonly GET_ATTEMPT_RULES: string = 'client/attemptrules';
    public static readonly GET_EXAM_SUBMIT_OPTIONS: string = 'form/formtypesbyclient';
    public static readonly GET_EXISTING_RULES: string = 'eligibility';
    public static readonly GET_ELIGIBILITY_VALIDATION_ROUTE_CODE: string = 'eligibility/validatetenantcode';
    public static readonly GET_ELIGIBILITY_VALIDATION_ROUTE_NAME: string = 'eligibility/validatetenanttitle';
    public static readonly GET_CHANGE_STATUS :string =  "eligibility/updatebulkstatus";
    public static readonly GET_ALL_VOUCHER_CODES :string =  "Voucher/items";
    public static readonly ADD_VOUCHER_CODES :string =  "Voucher/259/add-item";
    public static readonly UPDATE_VOUCHER_CODES :string =  "Voucher/259/update-item";
}

