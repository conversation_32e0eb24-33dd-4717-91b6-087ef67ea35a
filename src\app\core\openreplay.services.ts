import { Injectable } from '@angular/core';
import {
  Http<PERSON>nterceptor,
  HttpRequest,
  HttpHandler,
  HttpResponse,
} from '@angular/common/http';
import Tracker from '@openreplay/tracker';
import OpenReplay from '@openreplay/tracker'
import { environment } from 'src/environments/environment';
import { GlobalUserService } from './global-user.service';
import { Roles } from '../client/dynamic-component-loader/dynamic-component-loader.types';


type ReqRespType = {
  request: HttpRequest<any>,
  response: HttpResponse<any>
}

@Injectable({
  providedIn: 'root'
})
export class ReplaySessionService {
  tracker: OpenReplay|null = null
  UrlNotAllowedToken =[environment.URLNotAllowedToken,`${environment.baseUrl}gisapis/api/Event/GetEventsByGISLoggedUser`]
  private startTime: number;

  constructor(private global:GlobalUserService) {

  
   }


   openReplay(UserId:string){
    this.global.userDetails.subscribe((data)=>{
       if(data){
        if(data.roleId === Roles.NAE){
            var initOpts = {
              projectKey: "Fyy83O5CfBlF29Q7IciW",
              ingestPoint: "https://openreplay.examroom.ai/ingest",
              defaultInputMode: 0,
              obscureTextNumbers: false,
              obscureTextEmails: true,
              capturePerformance: true,
                  __DISABLE_SECURE_MODE: true,
                  captureExceptions:true,
                  capturePageLoadTimings:true,
                  network:{capturePayload:true}
            };
            var startOpts = { userID: UserId};
            (function(A,s:any,a,y,e,r){
              r=window.OpenReplay=[e,r,y,[s-1, e]];
              s=document.createElement('script');s.src=A;s.async=!a;
              document.getElementsByTagName('head')[0].appendChild(s);
              r.start=function(v){r.push([0])};
              r.stop=function(v){r.push([1])};
              r.setUserID=function(id){r.push([2,id])};
              r.setUserAnonymousID=function(id){r.push([3,id])};
              r.setMetadata=function(k,v){r.push([4,k,v])};
              r.event=function(k,p,i){r.push([5,k,p,i])};
              r.issue=function(k,p){r.push([6,k,p])};
              r.isActive=function(){return false};
              r.getSessionToken=function(){};
            })("https://static.openreplay.com/9.0.0/openreplay-assist.js",1,0,initOpts,startOpts);;
        }else{
          const tracker = new Tracker({
            projectKey: "Fyy83O5CfBlF29Q7IciW",
            ingestPoint: "https://openreplay.examroom.ai/ingest",
            capturePerformance: true,
            __DISABLE_SECURE_MODE: true,
          });
          tracker.start()
          tracker.setUserID(UserId);
        }
       }
    })
  
   
 
  }

   


  startTimer() {
    this.startTime = Date.now();
  }

  endTimer(): number {
    const endTime = Date.now();
    const elapsedTimeMinutes = (endTime - this.startTime) / (1000 * 60); // Convert milliseconds to minutes
    return elapsedTimeMinutes;
  }


  sendEventToReplaySession(event: string, params: ReqRespType): void {
    const {request, response} = params

    this.tracker?.event(event + "[request]", {
      method: request.method,
      url: request.url,
      params: request.params
      
    })
    this.tracker?.event(event + "[response]", {
      body: response.body,
      status: response.status,
      headers: response.headers
    })
  }
}