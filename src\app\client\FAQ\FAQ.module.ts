import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { SecondaryToolbarModule } from 'src/@exai/components/secondary-toolbar/secondary-toolbar.module';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { ContainerModule } from 'src/@exai/directives/container/container.module';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { IconModule } from '@visurel/iconify-angular';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TokenInterceptorService } from 'src/app/core/token-interceptor.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatIntlTelInputModule } from 'ngx-mat-intl-tel-input';
import { MatCheckboxModule } from '@angular/material/checkbox';

import { FAQComponent } from './FAQ.component';
import { FAQRoutingModule } from './FAQ.routing.module';

@NgModule({
  declarations: [
    FAQComponent
  ],
  imports: [
    CommonModule,
    FAQRoutingModule,
    FlexLayoutModule,
    SecondaryToolbarModule,
    PageLayoutModule,
    ContainerModule,
    IconModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatInputModule,
    MatButtonModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatDialogModule,
    MatMenuModule,
    ReactiveFormsModule,
    MatTooltipModule,
    MatNativeDateModule,
    MatDatepickerModule,
    NgxMatIntlTelInputModule,
    FormsModule,
    ReactiveFormsModule,
    MatCheckboxModule,

  ],
  providers: [
    DatePipe,
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptorService, multi: true },
  ]
})
export class FAQModule { }
