import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  State,
  statuses,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";
import { FormStatusToNames, FormTypes } from "src/app/client/application/application.types";
import { StateLists } from "../common-component/examroom-formbuilder/form-builder.types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Offspring,
      name: "Candidate Details",
      moduleID: "candidate-details",
      nestingType: nestingTypes.RowsInColumns,
      actions: [
        {
          type: tableTypes.actionTypes.Custom,
        },
      ],
      iconRef: "",
      routeRef: "/loader",
      class: "",
      gdColumns: "1fr",
      gdColumnsltlg: "1fr",
      gdColumnsltmd: "1fr",
      gdColumnsltsm: "1fr",
      gdGap: "12px",
      columns: [
        {
          class: "flex-col-important",
          gdColumn: "1/1",
          gdColumnltlg: "1/1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "space-between stretch",
          fxFlex: "auto",
          rows: [
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicSingleEntryTables,
                displayedColumns: [
                  [
                    <singleEntryTableTypes.column>{
                      id: "personId",
                      name: "Candidate Id",
                      editable: false,

                    },
                    <singleEntryTableTypes.column>{
                      id: ["firstName", "middleName", "lastName"],
                      name: "Candidate Name",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: ["address", "city", "zipCode", "candidateStateName"],
                      name: "Address ",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "contactNumber",
                      name: "Contact Number",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "emailId",
                      name: "Email Id",
                      editable: true,
                    },
                    <singleEntryTableTypes.column>{
                      id: "dateofBirth",
                      name: "Date of Birth",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "ssn",
                      name: "Last 4 SSN",
                      editable: false,
                    },
                    <singleEntryTableTypes.column>{
                      id: "stateName",
                      name: "State Name",
                      editable: false,
                    },
                  ],
                ],
                //  the below hard coding needs to be removed
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "account", "getusers"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personTenantRoleId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: new Map([
                  [Roles.SuperAdmin, "manage-states"],
                  [Roles.OperationStaff, "candidate-management"],
                  [Roles.StateClient, "candidate-management"],
                  [Roles.SupportingStaff, "candidate-management"],
                ]),
              },
            },
            {
              class: "",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: [],
                  cellRowCssClasses: ["my-3 shadow"],
                  showPaginator: false,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showAddButton: [true, true, false],
                  addButtonTexts: ["Add Application", "Register For Exam", ""],
                  showMatTabs: true,
                  matTabs: [
                    { id: 0, name: "Application Details" },
                    { id: 1, name: "Exam Details" },
                    {
                      id: 2, name: "Registration Details", visibilityRule: [
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.NOTEQUAL,
                          shouldHaveValue: State.Georgia,
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.NOTEQUAL,
                          shouldHaveValue: Roles.StateClient,
                        }
                      ]
                    },
                    {
                      id: 3, name: 'Support Tickets', visibilityRule: [
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.SupportingStaff
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.OperationStaff
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.QAC
                        }
                      ]
                    },
                    {
                      id: 4, name: 'Emails Sent', visibilityRule: [
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.SupportingStaff
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.OperationStaff
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.QAC
                        }
                      ]
                    },
                    {
                      id: 4, name: 'Emails Sent', visibilityRule: [
                        {
                          operator: tableTypes.Operators.AND ,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.StateClient
                        },
                        {
                          operator: tableTypes.Operators.AND,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: StateLists.CO
                        },
                      ]
                    },
                    {
                      id: 6, name: "Employer History", visibilityRule: [
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.OperationStaff
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.StateClient
                        },
                        {
                          operator: tableTypes.Operators.AND,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: State.Pennsylvania
                        },
                      ]
                    },
                    {
                      id: 7, name: "Candidate History", visibilityRule: [

                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'roleId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: Roles.StateClient
                        },
                        {
                          operator: tableTypes.Operators.AND,
                          elementProperty: 'stateId',
                          extractedFromGlobal: true,
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: State.Pennsylvania
                        },
                      ]
                    }
                  ],
                  showSearch: false,
                  verticalScroll: true,
                  horizontalScroll: true,
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "addApplicationForCandidate",
                      title: ["Add Application for Candidate"],
                      id: "addApplicationForCandidate",
                      buttons: [],
                    },
                    {
                      isCustom: true,
                      customTemplateRef: "register",
                      title: [""],
                      id: "register",
                      buttons: [],
                    },
                  ],
                },
                displayedColumns: [
                  [ // application 
                    <tableTypes.column>{
                      visible: true,
                      id: "formName",
                      name: "Application Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "personFormId",
                      name: "Application ID",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "status",
                      name: "Status",
                      cellType: "status",
                      statuses: tableTypes.tableStatuses,
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: ["text-xs"],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "currentAssigneeRoleName",
                      name: "Currently Working",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.View,
                          tooltip: "View",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'hasFormResponse',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: false,
                            },

                          ],
                          requestDetails: [
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                `form`,
                                `formsbyformtypeid`,
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "formTypeId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "formTypeId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "eligibilityId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted:
                                    "eligibilityRouteId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "stateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "stateId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "clientId",
                                  paramValue: null,
                                  extractedFromGlobal: true,
                                  elementPropertyToBeExtracted: "clientORtenantId",
                                },
                              ],
                              method: "GET",
                            },
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                "form",
                                "personform",
                                "list",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },
                              ],
                              method: "GET",
                            },
                          ],
                          backwardsDataDependency: true,
                          grandParentReference: new Map([
                            [Roles.SuperAdmin, "manage-states"],
                            [Roles.OperationStaff, "candidate-management"],
                            [Roles.StateClient, "candidate-management"],
                            [Roles.SupportingStaff, "candidate-management"],
                          ]),
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Application",
                          color: "#7d7d7d",
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Certificate_Renewal,
                            },
                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.StateClient,
                            }
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1776203,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 686678,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 86,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1681866,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1746418,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },

                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 71,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2034255,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2034258,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:  2032061,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2034266,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1732889,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1732890,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Applications",
                          color: "#7d7d7d",
                          isDeleteShow: false,
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Demographic,
                            },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Renewal,
                            // },
                            // {
                            //   operator: tableTypes.Operators.OR,
                            //   elementProperty: 'formTypeId',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 1685519,
                            },
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },

                        <tableTypes.action>{

                          icon: "edit",

                          type: tableTypes.actionTypes.Link,

                          tooltip: "Edit",

                          color: "#7d7d7d",

                          visibilityRule: [

                            {

                              operator: tableTypes.Operators.AND,

                              elementProperty: "formTypeId",

                              compareWithOperator:

                                comparisionOperators.NOTEQUAL,

                              shouldHaveValue: FormTypes.Accomodation,

                            },

                            //   {

                            //   operator:tableTypes.Operators.AND,

                            //   elementProperty: 'status',

                            //   compareWithOperator: comparisionOperators.NOTEQUAL,

                            //   shouldHaveValue:'Approved'

                            // },

                            {

                              operator: tableTypes.Operators.AND,

                              elementProperty: "status",

                              compareWithOperator: comparisionOperators.EQUAL,

                              shouldHaveValue: "Drafted",

                            },

                          ],

                        },

                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[8],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:71,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        },
                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[8],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1769176,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        },
                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[8],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:2034258,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        },
                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:71,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        },
                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1769176,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        },
                        <tableTypes.action>{
                          icon: "timelapse",
                          type: tableTypes.actionTypes.Elapsed,
                          tooltip: "Extend Elapsed time",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormStatusToNames[1],
                            },
                           
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "elapseDateTime",
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: null,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:2034258,
                            },
                            
                            {
                                
                              
                                operator: tableTypes.Operators.AND,
                                elementProperty: 'roleId',
                                extractedFromGlobal: true,
                                compareWithOperator: comparisionOperators.EQUAL,
                                shouldHaveValue: Roles.OperationStaff
                              }
                           
                            
                       


                            
                          ],
                        }
                        


                      ],
                      headerCssClasses: ["width-6"],
                      cellCssClasses: ["cursor-pointer"],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "application",
                        navigateToLabel: "Application",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "formTypeId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "personTenantRoleId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "eligibilityRouteId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "stateId",
                            },
                            // the below one is the proper one , the one below this is only temporary
                            // {
                            //   paramType: paramTypes.RouteParam,
                            //   paramName: null,
                            //   paramValue: null,
                            //   extractedFromElement: true,
                            //   elementPropertyToBeExtracted: "personEventId",
                            // },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: 0,
                              extractedFromElement: false,
                              elementPropertyToBeExtracted: "personEventId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "personFormId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "code",
                            },
                          ],
                          method: "GET",
                        },
                      },
                    },
                  ],
                  [ // exam details
                    <tableTypes.column>{
                      visible: true,
                      id: "checkbox",
                      name: "checkbox",
                      cellType: "text",
                      headerType: "checkbox",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent", "w-4"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examName",
                      name: "Exam Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "mode",
                      name: "Mode",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "testCenterDetails",
                      name: "Test Center Details",
                      cellType: "json",
                      cellArrayProps: ["testCenterId", "testCenterName"],
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examDateTime",
                      name: "Exam Date/Time",
                      cellType: "dobtype",
                      dateCellProps: 'medium',
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "timeZoneAbbreviation",
                      name: "Timezone",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examStatus",
                      name: "Exam Status",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "schedule",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "reschedule",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'allowReschedule',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: true,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.StateClient
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: tableTypes.examstatus.Exam_Cancelled
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.SupportingStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: tableTypes.examstatus.ExamCompleted
                            }
                          
                          ],
                        },
                        <tableTypes.action>{
                          icon: "schedule",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "reschedule",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'allowReschedule',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: false,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.StateClient
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: tableTypes.examstatus.Exam_Cancelled
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.SupportingStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: tableTypes.examstatus.ExamCompleted
                            }
                          
                          ],
                        },
                        <tableTypes.action>{
                          icon: "alarm_add",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "Schedule",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: tableTypes.examstatus.scheduling_error,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.SupportingStaff
                            },
                            // { 
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'examStatus',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue:'Event Scheduled'
                            // }
                          ],
                        },
                        <tableTypes.action>{
                          icon: "cancel",
                          type: tableTypes.actionTypes.Delete,
                          tooltip: "cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'allowCancel',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: true,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.StateClient
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.SupportingStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: tableTypes.examstatus.Exam_Cancelled
                            },
                            // { 
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'examStatus',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue:'Event Scheduled'
                            // }
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                `operationstaff`,
                                "exam",
                                "cancel",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "examScheduleId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "id",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "candidateId",
                                },
                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.ComponentView,
                          tooltip: "Show Result",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: "allowShowResult",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: true,
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1681866,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1776203,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:84,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1746418,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1011183,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:71,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:2034255,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2034258,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:2032061,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:2034266,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1732889,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1732890,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "sync",
                          type: tableTypes.actionTypes.Refresh_Noshow_to_cancel,
                          tooltip: "Change status from No_show to cancel",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Noshow
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1685519,
                            },
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "exit_to_app",
                          type: tableTypes.actionTypes.Transfer_Candidate,
                          tooltip: "Transfer Exam",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.ExamSchedule
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator:tableTypes.Operators.AND,
                              elementProperty:"mode",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:'Test Center'

                            }
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "exit_to_app",
                          type: tableTypes.actionTypes.Transfer_Candidate,
                          tooltip: "Transfer Exam",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "examStatusId",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:tableTypes.examstatus.Event_Cancelled
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: Roles.OperationStaff
                            },
                            {
                              operator:tableTypes.Operators.AND,
                              elementProperty:"mode",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:'Test Center'

                            }
                           
                          ],
                        },
                        <tableTypes.action>{
                          icon: "payment",
                          type: tableTypes.actionTypes.Refund_payment,
                          tooltip: "Refund Payment",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:9,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2133825
                            },
                            // { 
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'examStatus',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue:'Event Scheduled'
                            // }
                          ],
                        },
                        <tableTypes.action>{
                          icon: "payment",
                          type: tableTypes.actionTypes.Refund_payment,
                          tooltip: "Refund Payment",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'examStatusId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue:1,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'personId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: 2133825
                            },
                            // { 
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'examStatus',
                            //   compareWithOperator: comparisionOperators.EQUAL,
                            //   shouldHaveValue:'Event Scheduled'
                            // }
                          ],
                        }

                        
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "scheduleExam",
                        eventDataUseful: false,
                        navigateToLabel: "Training Program Details",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "candidateId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "id",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "examId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "examName",
                            },
                            
                          ],
                        },
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [""],
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: "examCancelComments",
                      name: "Comments",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      visibilityRule: [

                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'examStatusId',
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: tableTypes.examstatus.Exam_Cancelled
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'examStatusId',
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: tableTypes.examstatus.Exam_Cancelled_by_Proctor
                        },


                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'examStatusId',
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: tableTypes.examstatus.ExamCompleted
                        },
                        {
                          operator: tableTypes.Operators.OR,
                          elementProperty: 'examStatusId',
                          compareWithOperator: comparisionOperators.EQUAL,
                          shouldHaveValue: tableTypes.examstatus.Noshow
                        },
                      ],
                      headerCssClasses: ["width-4"],
                      cellCssClasses: [],

                    },
                  ],
                  [ // registration details
                    <tableTypes.column>{
                      visible: true,
                      id: "checkbox",
                      name: "checkbox",
                      cellType: "text",
                      headerType: "checkbox",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-4"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "RegistryName",
                      name: "Registry Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },


                    <tableTypes.column>{
                      visible: true,
                      id: "CertNumber",
                      name: "Certification Number",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "EffectiveDate",
                      name: "Start Date",
                      cellType: "onlydate",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "ExpirationDate",
                      name: "End Date",
                      cellType: "onlydate",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: "RegistryStatus",
                      name: "Status",
                      cellType: "status",
                      statuses: tableTypes.tableStatuses,
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "View",
                          color: "#7d7d7d",
                        },
                      ],
                      headerCssClasses: ["w-12"],
                      cellCssClasses: [""],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "certificate",
                        eventDataUseful: false,
                        navigateToLabel: "certification",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [],
                          method: "GET",
                        },
                      },
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "renewalAction",
                      name: "Renewal",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "schedule",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "Renewal",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'StateId',
                              compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                              shouldHaveValue: State.South_Carolina
                            },

                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'StateId',
                              compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                              shouldHaveValue: State.Mississippi
                            },


                          ],
                        },
                      ],
                      headerCssClasses: ["w-12"],


                      cellCssClasses: [""],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "application",
                        navigateToLabel: "Application",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: FormTypes.Certificate_Renewal,
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "PersonTenantRoleId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "EligibilityRouteId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "StateId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "Id",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: 0,
                            }
                          ],
                          method: "GET",
                        },
                      },
                    },
                  ],
                  [ // support tickets
                    <tableTypes.column>{
                      visible: true,
                      id: "assignedTo",
                      name: "Assigned To",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "description",
                      name: "Description",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "statusName",
                      name: "Status",
                      cellType: "status",
                      statuses: tableTypes.tableStatuses,
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: ["text-xs"],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "subject",
                      name: "Subject",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "createdDate",
                      name: "Created Date",
                      cellType: "Date",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "View",
                          color: '#7d7d7d',
                        },
                      ],
                      linkMetaData: {
                        navigateToType: moduleTypes.Custom,
                        navigateTo: 'support-ticket/support-ticket-details',
                        eventDataUseful: false,
                        navigateToLabel: 'Ticket Details',
                      },
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: ["cursor-pointer"],
                    },
                  ],
                  [ // email
                    <tableTypes.column>{
                      visible: true,
                      id: "to",
                      name: "To",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "cc",
                      name: "Cc",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "bcc",
                      name: "Bcc",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "createdOn",
                      name: "Created On",
                      cellType: "Date",
                      dateCellProps: 'medium',
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "subject",
                      name: "Subject",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.RichTextView,
                          tooltip: "View",
                          color: "#7d7d7d",
                        },
                      ],
                      headerCssClasses: ["w-12"],
                      cellCssClasses: [""],
                    },
                  ],
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "formName",
                      name: "Application Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "personFormId",
                      name: "Application ID",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "action",
                      name: "Action",
                      cellType: "action",
                      headerType: "normal",
                      headerAlign: "center",
                      actions: [
                        <tableTypes.action>{
                          icon: "visibility",
                          type: tableTypes.actionTypes.View,
                          tooltip: "View",
                          color: "#7d7d7d",

                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'hasFormResponse',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: false,
                            },

                          ],
                          requestDetails: [
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                `form`,
                                `formsbyformtypeid`,
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "formTypeId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "formTypeId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "eligibilityId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted:
                                    "eligibilityRouteId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "stateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "stateId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "clientId",
                                  paramValue: null,
                                  extractedFromGlobal: true,
                                  elementPropertyToBeExtracted: "clientId",
                                },
                              ],
                              method: "GET",
                            },
                            {
                              fetchUrl: [
                                "client",
                                "api",
                                "form",
                                "personform",
                                "list",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },
                              ],
                              method: "GET",
                            },
                          ],
                          backwardsDataDependency: true,
                          grandParentReference: new Map([
                            [Roles.SuperAdmin, "manage-states"],
                            [Roles.OperationStaff, "candidate-management"],
                            [Roles.StateClient, "candidate-management"],
                            [Roles.SupportingStaff, "candidate-management"],
                          ]),
                        },
                        <tableTypes.action>{
                          icon: "delete",
                          type: tableTypes.actionTypes.DeleteApp,
                          tooltip: "Delete Application",
                          color: "#7d7d7d",
                          visibilityRule: [

                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Application,
                            },
                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Certificate_Renewal,
                            },
                            {
                              operator: tableTypes.Operators.OR,
                              elementProperty: 'formTypeId',
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: FormTypes.Certificate_Reciprocity,
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[1]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[2]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[3]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[4]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[8]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'status',
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormStatusToNames[9]
                            },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: 'roleId',
                              extractedFromGlobal: true,
                              compareWithOperator: comparisionOperators.NOTEQUAL,
                              shouldHaveValue: Roles.StateClient,
                            }
                          ],

                          requestDetails: [
                            {
                              fetchUrl: [
                                "candidate",
                                "api",
                                `form`,
                                "personform",
                              ],
                              requestBody: null,
                              requestParams: [
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "candidateId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personTenantRoleId",
                                },
                                {
                                  paramType: paramTypes.Optional,
                                  paramName: "personFormId",
                                  paramValue: null,
                                  extractedFromElement: true,
                                  elementPropertyToBeExtracted: "personFormId",
                                },

                              ],
                              method: "DELETE",
                            },
                          ],
                        },
                        <tableTypes.action>{
                          icon: "edit",
                          type: tableTypes.actionTypes.Link,
                          tooltip: "Edit",
                          color: "#7d7d7d",
                          visibilityRule: [
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "formTypeId",
                              compareWithOperator:
                                comparisionOperators.NOTEQUAL,
                              shouldHaveValue: FormTypes.Accomodation,
                            },
                            //   {
                            //   operator:tableTypes.Operators.AND,
                            //   elementProperty: 'status',
                            //   compareWithOperator: comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:'Approved'
                            // },
                            {
                              operator: tableTypes.Operators.AND,
                              elementProperty: "status",
                              compareWithOperator: comparisionOperators.EQUAL,
                              shouldHaveValue: "Drafted",
                            },
                          ],
                        },
                      ],
                      headerCssClasses: ["width-6"],
                      cellCssClasses: ["cursor-pointer"],
                      linkMetaData: {
                        navigateToType: moduleTypes.Independent,
                        navigateTo: "application",
                        navigateToLabel: "Application",
                        requestDetails: {
                          fetchUrl: [],
                          requestBody: null,
                          requestParams: [
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "formTypeId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "personTenantRoleId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted:
                                "eligibilityRouteId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "stateId",
                            },
                            // the below one is the proper one , the one below this is only temporary
                            // {
                            //   paramType: paramTypes.RouteParam,
                            //   paramName: null,
                            //   paramValue: null,
                            //   extractedFromElement: true,
                            //   elementPropertyToBeExtracted: "personEventId",
                            // },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: 0,
                              extractedFromElement: false,
                              elementPropertyToBeExtracted: "personEventId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "personFormId",
                            },
                            {
                              paramType: paramTypes.RouteParam,
                              paramName: null,
                              paramValue: null,
                              extractedFromElement: true,
                              elementPropertyToBeExtracted: "code",
                            },
                          ],
                          method: "GET",
                        },
                      },
                    },
                  ],
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "employerName",
                      name: "Employer Name",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "employmentStartDate",
                      name: "Start Date",
                      cellType: "date",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "employmentEndDate",
                      name: "End Date",
                      cellType: "date",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },

                  ],
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "changeValue",
                      name: "ChangeType",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "oldValue",
                      name: "Old Value",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "newValue",
                      name: "New Value",
                      cellType: "text",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "createdOn",
                      name: "Changed On",
                      cellType: "date",
                      headerType: "normal",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["bg-transparent"],
                      cellCssClasses: [],
                    },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "Candidate", "forms-filled"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                        position: 3,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "actionBy",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "Candidate",
                      "registered-exams",
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                        position: 3,
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: ['client', 'api', 'registry', 'get-all'],
                    requestBody: null,

                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 1000,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personId",
                        position: 3,
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: ["customerservice", "api", "tickets"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "candidateId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "customerservice",
                      "api",
                      "tickets",
                      "user-notifications",
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "personId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personId",
                        position: 4,
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: ["client", "api", "Candidate", "forms-filled"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                        position: 3,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "actionBy",
                        paramValue: null,
                        extractedFromGlobal: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "formTypeId",
                        paramValue: 11,
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "api",
                      "Employer",
                      "employment-history",
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personId",
                        position: 4,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNo",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                    ],
                    method: "GET",
                  },
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "Candidate",
                      "change-history",
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personId",
                        position: 3,
                      },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: new Map([
                  [Roles.SuperAdmin, "manage-states"],
                  [Roles.OperationStaff, "candidate-management"],
                  [Roles.StateClient, "candidate-management"],
                  [Roles.SupportingStaff, "candidate-management"],
                ]),
              },
            },
          ],
        },
        {
          class: "grid",
          gdColumn: "1 / 1",
          gdColumnltlg: "1 / 1",
          gdColumnltmd: "1/1",
          gdColumnltsm: "1",
          fxLayoutAlign: "start center",
          fxFlex: "flex-grow",
          rows: [
            {
              class: "appProcessLog w-full h-full items-initial",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start",
              fxFlex: "none",
              fxFlexltmd: "auto",

              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicProcessLogs,
                requestDetails: [
                  {
                    fetchUrl: ["formmsvc", "api", `form`, "candidate-logs"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "candidateId",
                        paramValue: null,
                        extractedFromElement: true,
                        elementPropertyToBeExtracted: "personTenantRoleId",
                      },
                    ],
                    method: "GET",
                  },
                ],
                backwardsDataDependency: true,
                grandParentReference: new Map([
                  [Roles.SuperAdmin, "manage-states"],
                  [Roles.OperationStaff, "candidate-management"],
                  [Roles.StateClient, "candidate-management"],
                  [Roles.SupportingStaff, "candidate-management"],
                ]),
                widgetHeader: "Application Process",
              },
            },
          ],
        },
      ],
    },
  ],
};
