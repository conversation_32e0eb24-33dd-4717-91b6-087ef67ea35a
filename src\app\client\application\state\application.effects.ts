import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { createEffect, Actions, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { switchMap, map, catchError, tap, mergeMap, concatMap } from 'rxjs/operators';
import { deletedUserResponse, deleteUserResponse, downloadAccTypeForm, downloadedAccTypeForm, getFormJson, getPersonFormLogs, getUserResponse, gotFormJson, gotPersonFormLogs, gotUserResponse, savedUserResponse, saveUserResponse } from './application.actions';
import { environment } from 'src/environments/environment';
import { setErrorMessage } from '../../state/shared/shared.actions';
import { Router } from '@angular/router';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import lodash from 'lodash';
import { FormTypes } from '../application.types';


@Injectable({
  providedIn: 'root',
})
export class ApplicationEffects {

  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    private globalUserService: GlobalUserService,
    private router: Router, private snackbar: SnackbarService
  ) { }

  effectivelyGetForms$ = createEffect(() => this.actions$.pipe(
    ofType(getFormJson),
    mergeMap((action) => {
      return this.httpClient
        .get<any>(
          environment.baseUrl +
          `client/api/form/formsbyformtypeid?formTypeId=${action.formTypeID}&` + (action.eligibilityID ? `eligibilityId=${action.eligibilityID}&` : ``) + `clientId=${this.globalUserService.userDetails.value.clientId?this.globalUserService.userDetails.value.clientId:this.globalUserService.userDetails.value.clientORtenantId}&stateId=${action.stateID}`)
        .pipe(
          map(form =>
            gotFormJson({
              formID: [form.id],
              formTypeID: action.formTypeID,
              stateID: action.stateID,
              eligibilityID: action.eligibilityID,
              formJSON: lodash.cloneDeep(JSON.parse(form.formJson)),
              isSubmitAllowed: action.isSubmitAllowed,
            })),
        );
    }),
  ));
  effectivelySaveResponse$ = createEffect(() => this.actions$.pipe(
    ofType(saveUserResponse),
    concatMap((action) => {
      return this.httpClient
        .post<any>(
          environment.baseUrl +
          `client/api/form/savepersonform`, action.userResponse
        )
        .pipe(
          map(savedResponseId =>
            savedUserResponse({
              personFormId: savedResponseId,
            })),
          tap(() => {
            if (action.userResponse.isSubmit) {
              this.router.navigate(['loader', 'candidate-details'])
            }else{
              this.router.navigate(['loader', 'candidate-details'])
            }
            if (action.formTypeID != FormTypes.Accomodation) {
              this.snackbar.callSnackbaronSuccess('Successfully Saved Response.');
            }
          }),
        );
    }),
  ));

  effectivelyGetPersonFormLogs$ = createEffect(() => this.actions$.pipe(
    ofType(getPersonFormLogs),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `candidate/api/form/personformlogs?` + (action.code && action.code != '0' ? `code=${action.code}` : `personFormId=${action.personFormId}`))
        .pipe(
          map(personFormLogs =>
            gotPersonFormLogs({
              personFormLogs: personFormLogs
            })),
        );
    }),
  ));

  effectivelyGetUserResponse$ = createEffect(() => this.actions$.pipe(
    ofType(getUserResponse),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `candidate/api/form/personform/list?` + (action.code && action.code != '0' ? `code=${action.code}` : `personFormId=${action.personFormId}`))
        .pipe(
          map(userResponse =>
            gotUserResponse({ userResponse: userResponse })
          ),
        );
    }),
  ))

  effectivelyDownloadFile$ = createEffect(() => this.actions$.pipe(
    ofType(downloadAccTypeForm),
    switchMap((action) => {
      return this.httpClient
        .get<any>(environment.baseUrl + `formmsvc/api/File/url?systemFileName=${action.sysFileName.split('|')[1]}`)
        .pipe(
          map(response =>
            downloadedAccTypeForm({ url: response.url })),
          tap((response) => {
            window.open(response.url);
          }),
        );
    }),
  ))

  effectivelyDeleteUserResponse$ = createEffect(() => this.actions$.pipe(
    ofType(deleteUserResponse),
    concatMap((action) => {
      return this.httpClient
        .delete<any>(environment.baseUrl + `client/api/form/personform?candidateId=${action.candidateId}&personFormId=${action.personFormId}`)
        .pipe(
          map(Response =>
            deletedUserResponse()
          ),
          tap(() => {
            if (action.formTypeID != FormTypes.Accomodation)
              this.snackbar.callSnackbaronSuccess('Successfully deleted');
            if (action.route)
              this.router.navigate(['loader', 'candidate-details']);
          }),
        );
    }),
  ))
}
