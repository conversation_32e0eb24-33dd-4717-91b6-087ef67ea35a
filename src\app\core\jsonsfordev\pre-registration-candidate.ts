import { state } from "@angular/animations";
import { FormTypes } from "src/app/client/application/application.types";
import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  Roles,
  State,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Pre Registration Candidates",
      moduleID: "Pre-RegisterCandidate",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "demographicManageIcon",
      routeRef: "/loader",
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  showMatTabs: false,
                  showAddButton: [false],
                  addButtonTexts: ["Add Candidate"],
                  showSearch: true,
                  verticalScroll: true,
                  ShowFilenameFilter:true,
                  horizontalScroll: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  showStateSelectionDropdown:true,
                  showSlectdropstatedataattributes:"preregistration",
                  addPopupData: [
                    {
                      isCustom: true,
                      customTemplateRef: "addCandidate",
                      title: ["Add Candidate"],
                      id: "addCandidate",
                      buttons: [
                        {
                          buttonText: "Submit",
                          requestDetails: {
                            fetchUrl: ["client", "api", "user"],
                            requestBody: null,
                            requestParams: null,
                            method: "POST",
                          },
                        },
                      ],
                    },
                  ],
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "checkbox",
                      name: "Checkbox",
                      cellType: "text",
                      headerType: "checkbox",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["w-4"],
                      cellCssClasses: [],
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "firstName",
                      name: "First Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "middleName",
                      name: "Middle Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "lastName",
                      name: "Last Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
               
                    <tableTypes.column>{
                      visible: true,
                      id: "emailId",
                      name: "Email Id",
                      cellType: "text",
                      headerDisable:true,
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: false
                    },
                    
                  
                    <tableTypes.column>{
                      visible: true,
                      id: "createdOn",
                      name: "Created On",
                      cellType: "date",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },

                    <tableTypes.column>{
                      visible: true,
                      id: "createdByEmailId",
                      name: "Created By",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: [],
                      cellCssClasses: [],
                    },

                    
                    <tableTypes.column>{
                      visible: true,
                      id: "statusName",
                      name: "Status Name",
                      cellType: "status",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ['db-table', "text-center"],
                      cellCssClasses: ["text-center"],
                      statuses: tableTypes.tableStatuses
                    },
                     
                    
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: ["client", "api", "traininginstitute", "uploaded-candidate"],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNo",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                       {
                        paramType: paramTypes.Required,
                        paramName: null,
                        paramValue: null,
                        elementPropertyToBeExtracted: "clientORtenantId",
                        extractedFromGlobal: true,
                        position: 4,
                      }
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
