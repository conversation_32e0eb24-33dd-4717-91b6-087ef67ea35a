import { Component, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";
import { GlobalUserService } from 'src/app/core/global-user.service';
import { assignTicket, attcahDoc, closeTicket, getcandidateDetails, getTicketDetails, getTicketProcessLog } from './state/support.action';
import { assignTickets$, closeTickets$, getCandidateDetails$, getTicketDetails$, getTicketProcessDetails$ } from './state/support.selector';
import { breadCrumbsSelector, recentLinkClickEventSelector } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { Router } from '@angular/router';
import { ConfirmationPopupComponent } from 'src/app/core/common-component/confirmation-popup/confirmation-popup.component';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { BreakpointObserver } from '@angular/cdk/layout';
import { moduleTypes, Roles } from '../dynamic-component-loader/dynamic-component-loader.types';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { URL } from 'src/app/core/url'
import { HttpClient } from '@angular/common/http';
import { FileViewPopupComponent } from 'src/app/core/common-component/examroom-formbuilder/file-upload-control/file-view-popup/file-view-popup.component';
import { FormGroup } from '@angular/forms';
import { DynamicFormModel, DynamicFileUploadModel, DynamicFormHook, DynamicFormService } from '@ng-dynamic-forms/core';
import { Attachment } from './state/support.state';
import { PopFromBreadCrumbUptoIndex, PushIntoBreadCrumb, setRecentLinkClickEvent } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { crumb } from 'src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types';

@Component({
  selector: 'app-support-ticket',
  templateUrl: './support-ticket.component.html',
  styleUrls: ['./support-ticket.component.scss']
})
export class SupportTicketComponent implements OnInit {
  Roles = Roles;
  data: any;
  ticketdata: any;
  personTenantRoleId: number;
  recentlinkClickEvent:any;
  ticketId: number;
  assignTicketDetails:any;
  processLogData: any;
  isTrasnferred:boolean=true;
  isCLosed:boolean=true;
  header: string = "Ticket Logs";
  isTicketClosed:boolean=false;
  isTicketTransferred:boolean=false;
  attachments:[];

  viewUrl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  viewUrl$: Observable<string> = this.viewUrl.asObservable();
  
  displayedColumnsticket = [
    <singleEntryTableTypes.column>{
      id: 'id',
      name: 'Ticket ID',
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "categoryName",
      name: "Category",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "createdDate",
      name: "Create Date",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "statusName",
      name: "Status",
      editable: false,
    },

  ]

  displayedColumns = [
    <singleEntryTableTypes.column>{
      id: 'firstName',
      name: 'Candidate Name',
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "emailId",
      name: "EmailId",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "address",
      name: "Address",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "phoneNumber",
      name: "Contact Number",
      editable: false,
    },
  ]
  
  ticketDetaislColumns = [
    <singleEntryTableTypes.column>{
      id: 'subject',
      name: 'Subject',
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "description",
      name: "Description",
      editable: false,
    },
    <singleEntryTableTypes.column>{
      id: "responses",
      name: "Responses",
      editable: false,
      cellArrayProps:[]
    },
  ]

  fileUploadModel: DynamicFormModel;
  fileUploadFormGroup: FormGroup = null;

  fileUploadFormGroupSupport: FormGroup = null;
  fileUploadModelSupport: DynamicFormModel;

  fileUploadFormGroupOperation: FormGroup = null;
  fileUploadModelOperation: DynamicFormModel;

  docDetails: Attachment;
  breadCrumbsArray: crumb[] = null;

  constructor(private store: Store,
    public globalUser: GlobalUserService,
    private router: Router,
    private breakpointObserver: BreakpointObserver,
    private dialog: MatDialog,
    private dynamicFormService:DynamicFormService
  ) { 
    
  }

  ngOnInit() {
    this.store.dispatch(PushIntoBreadCrumb({
      crumb: {
        label: 'Support Ticket Details',
        navigationMetaData: {
          moduleID: 'support-ticket-details',
          linkClickEvent: null,
          navigateToType: moduleTypes.Independent,
        }
      }
    }));
    this.store.select(breadCrumbsSelector).subscribe((x: any) => this.breadCrumbsArray = x );
    this.store.select(recentLinkClickEventSelector).subscribe((data) => {
      if (data) {
        this.recentlinkClickEvent=data;
        this.personTenantRoleId = data.element.personTenantRoleId;
        this.ticketId = data.element.id;
      }
    })

    if(!this.recentlinkClickEvent) {
      this.router.navigate(['loader','support-ticket']);
      
    }
    
    this.store.select(getTicketProcessDetails$).subscribe(x => {
      if (x) {
        this.processLogData = x;
      }
    })
    
    this.store.select(getTicketDetails$).subscribe(x => {
      if (x) {
        this.ticketdata = [x];
        if(this.ticketdata &&  this.ticketdata[0]!=undefined){
        this.attachments=this.ticketdata[0].attachments;
        this.attachments.forEach((doc:any)=>{

       
        if(doc.createdByRoleId==Roles.Candidate){
       

        this.fileUploadModel = [
          new DynamicFileUploadModel({
          required: false,
          id: 'attachments',
          name: 'attachments',
          label: 'Supporting Documents by Candidate',
          disabled: true,
          value: this.ticketdata[0].attachments.map((x:any)=>{ 
            if(x.createdByRoleId==Roles.Candidate)
             return x.fileName+ '|' + x.systemFileName
            return '' }),
          accept: ['.pdf', '.doc', '.docx'],
          multiple: true,
          updateOn: DynamicFormHook.Change,
          showFileList: true,
          additional: {
            appearance: 'outline'
          }
          })
        ]
        this.fileUploadFormGroup = this.dynamicFormService.createFormGroup(this.fileUploadModel);
      }
      else if(doc.createdByRoleId==Roles.OperationStaff){
        this.fileUploadModelOperation = [
          new DynamicFileUploadModel({
          required: false,
          id: 'attachments',
          name: 'attachments',
          label: 'Supporting Documents by OperationStaff',
          disabled: true,
          value: this.ticketdata[0].attachments.map((x:any)=>{ 
            if(x.createdByRoleId==Roles.OperationStaff)
             return x.fileName+ '|' + x.systemFileName
            return '' }),
          accept: ['.pdf', '.doc', '.docx'],
          multiple: true,
          updateOn: DynamicFormHook.Change,
          showFileList: true,
          additional: {
            appearance: 'outline'
          }
          })
        ]
        this.fileUploadFormGroupOperation = this.dynamicFormService.createFormGroup(this.fileUploadModelOperation);
      }
      else if(doc.createdByRoleId==Roles.SupportingStaff){
        this.fileUploadModelSupport = [
          new DynamicFileUploadModel({
          required: false,
          id: 'attachments',
          name: 'attachments',
          label: 'Supporting Documents by Supporting Staff',
          disabled: true,
          value: this.ticketdata[0].attachments.map((x:any)=>{ 
            if(x.createdByRoleId==Roles.SupportingStaff)
             return x.fileName+ '|' + x.systemFileName
            return '' }),
          accept: ['.pdf', '.doc', '.docx'],
          multiple: true,
          updateOn: DynamicFormHook.Change,
          showFileList: true,
          additional: {
            appearance: 'outline'
          }
          })
        ]
        this.fileUploadFormGroupSupport = this.dynamicFormService.createFormGroup(this.fileUploadModelSupport);
      }
    })
      }
      
      }
    })

    this.store.select(getCandidateDetails$).subscribe(x => {
      if (x) {
        this.data = [{
          firstName: `${x["firstName"]} ${x["middleName"]} ${x["lastName"]}`,
          emailId: x["emailId"],
          address: x["address"],
          phoneNumber: x["phoneNumber"]
        }]
      }
    })
    this.personTenantRoleId !=null && this.personTenantRoleId !=undefined? this.store.dispatch(getcandidateDetails({ personTenantRoleId: this.personTenantRoleId })):null
    this.ticketId !=null && this.ticketId !=undefined? this.store.dispatch(getTicketDetails({ ticketId: this.ticketId })):null
    setTimeout(() => {
      this.ticketId !=null && this.ticketId !=undefined? this.store.dispatch(getTicketProcessLog({ ticketId: this.ticketId })):null
    }, 1000);
 
  }
  navigateToParentUsingBreadCrumb() {
    // extracting the second last 
    let crumb = this.breadCrumbsArray[this.breadCrumbsArray.length - 2];
    if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
    else {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
        this.recentlinkClickEvent = crumb.navigationMetaData.linkClickEvent;
      }
      this.store.dispatch(setRecentLinkClickEvent({ event: this.recentlinkClickEvent }));
      this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
    }
    // this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
  }
  handleCrumbNavigation(crumb: crumb) {
    if (!(this.breadCrumbsArray.indexOf(crumb) == this.breadCrumbsArray.length - 1)) {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
      else {
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
          this.recentlinkClickEvent = crumb.navigationMetaData.linkClickEvent;
          this.store.dispatch(setRecentLinkClickEvent({ event: this.recentlinkClickEvent }));
        }
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Independent) {
          this.router.navigate([crumb.navigationMetaData.moduleID]);
        }
        else
          this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
      }
      this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) }));
    }
  }

  assignTickets(){
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    dialogConfig.disableClose = true
    this.breakpointObserver
      .observe(['(min-width : 1024px)'])
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = '40vw';
          dialogConfig.minHeight = 'fit-content';
          dialogConfig.maxWidth = '40vw';
        } else {
          dialogConfig.minWidth = '90vw';
          dialogConfig.minHeight = '90vh';
        }
      });
      dialogConfig.data = {
        message: "Are you sure you want to assign this ticket?",
        id:1,
       
      };
  
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((d) => {
    var data=d;
    const message=data.message
    var assignTo=this.globalUser.userDetails.value.roleId==Roles.OperationStaff?Roles.SupportingStaff:Roles.OperationStaff;
    if(data.confirmed==true){
      this.store.select(getTicketDetails$).subscribe((data) => {
        
        if (data) {
        const  ticketDetails =data["id"];
          this.assignTicketDetails={
          "ticketId":ticketDetails,
          "assigneeId":this.globalUser.userDetails.value.personTenantRoleId,
          "assignToRoleId": assignTo,
          "userId": this.globalUser.userDetails.value.personTenantRoleId,
          "statusId":ticketStatus.Open,//open status  
          "comment":message
        }
        var filename=d.doc[1].split("|")[0];
        var systemName=d.doc[1].split("|")[1];
        this.docDetails={
          ticketId: ticketDetails,
        fileName: filename,
        systemFileName: systemName,
        createdBy: this.globalUser.userDetails.value.personTenantRoleId
        }
        }
        
      })

      this.store.dispatch(assignTicket({ ticketDetails: this.assignTicketDetails }))
      this.store.select(assignTickets$).subscribe(data=>{
       if(data && data.valueOf() ===2){
         this.isTicketTransferred = true
         var filename=d.doc[1] !=null?d.doc[1].split("|")[0]:''
         var systemName=d.doc[1] !=null?d.doc[1].split("|")[1]:''
         this.docDetails={
           ticketId: this.assignTicketDetails.ticketId,
         fileName: filename,
         systemFileName: systemName,
         createdBy: this.globalUser.userDetails.value.personTenantRoleId
         }
         this.store.dispatch(attcahDoc({attachedDoc:this.docDetails}))
         //  this.router.navigate(['loader','support-ticket']);
         this.navigateToParentUsingBreadCrumb();
       }
      })
    }
    })

  }
  assigntoSupportingOrOpertionStaff(){

    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointObserver
      .observe(['(min-width : 1024px)'])
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = '40vw';
          dialogConfig.minHeight = 'fit-content';
          dialogConfig.maxWidth = '40vw';
        } else {
          dialogConfig.minWidth = '90vw';
          dialogConfig.minHeight = '90vh';
        }
      });
      dialogConfig.data = {
        message: "Are you sure you want to assign this ticket to yourself?",
        id:1
      };
  
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((d) => {
    var data=d
    const message=data.message
    if(data.confirmed==true){
      
      this.store.select(getTicketDetails$).subscribe((data) => {
        if (data) {
          const  ticketDetails =data["id"];
          this.assignTicketDetails={
          "ticketId":ticketDetails,
          "assignToRoleId": this.globalUser.userDetails.value.roleId,
          "assigneeId":this.globalUser.userDetails.value.personTenantRoleId,
          "userId": this.globalUser.userDetails.value.personTenantRoleId,
          "statusId":ticketStatus.Assigned,//assigned status
          "comment":message
          }
          var filename=d.doc[1].split("|")[0];
          var systemName=d.doc[1].split("|")[1];
          this.docDetails={
            ticketId: ticketDetails,
          fileName: filename,
          systemFileName: systemName,
          createdBy: this.globalUser.userDetails.value.personTenantRoleId
          }
        }
        
      })

      this.store.dispatch(assignTicket({ ticketDetails: this.assignTicketDetails }))
      this.store.select(assignTickets$).subscribe(data=>{
       if(data && data.valueOf() ===2){
        var filename=d.doc[1] !=null?d.doc[1].split("|")[0]:''
        var systemName=d.doc[1] !=null?d.doc[1].split("|")[1]:''
        this.docDetails={
          ticketId: this.assignTicketDetails.ticketId,
        fileName: filename,
        systemFileName: systemName,
        createdBy: this.globalUser.userDetails.value.personTenantRoleId
        }
         this.isTicketTransferred = true
         this.store.dispatch(attcahDoc({attachedDoc:this.docDetails}))
         this.router.navigate(['loader','support-ticket']);
         //this.navigateToParentUsingBreadCrumb();
       }
      })
    }
    })


  }
  closeTickets(){
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointObserver
      .observe(['(min-width : 1024px)'])
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = '40vw';
          dialogConfig.minHeight = 'fit-content';
          dialogConfig.maxWidth = '40vw';
        } else {
          dialogConfig.minWidth = '90vw';
          dialogConfig.minHeight = '90vh';
        }
      });
      dialogConfig.data = {
        message: "Are you sure you want to close this ticket?",
        id:1,
        selectticket:"Reply and Close Ticket"
      };
  
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((d) => {
    var data=d
    const message=data.message
    if(data.confirmed==true){
      this.store.select(getTicketDetails$).subscribe((data) => {
        if (data) {
        const  ticketDetails =data["id"];
          this.assignTicketDetails={
            "ticketId": ticketDetails,
            "userId": this.globalUser.userDetails.value.roleId,
            "response":message,
            "statusId":ticketStatus.Close,
            "modifiedBy": this.globalUser.userDetails.value.personTenantRoleId,
            "currentAssigneeId": this.globalUser.userDetails.value.personTenantRoleId,
            "ticketCategoryId":d.SelectedcategoriesId

          }
          var filename=d.doc[1].split("|")[0];
          var systemName=d.doc[1].split("|")[1];
          this.docDetails={
            ticketId: ticketDetails,
          fileName: filename,
          systemFileName: systemName,
          createdBy: this.globalUser.userDetails.value.personTenantRoleId
          }
        }
        // this.navigateToParentUsingBreadCrumb();
      })
      
      this.store.dispatch(closeTicket({ ticketDetails: this.assignTicketDetails }))
      this.store.select(closeTickets$ ).subscribe(data=>{
       if(data && data.valueOf() ===1) {
        this.isTicketClosed = true;
        var filename=d.doc[1] !=null?d.doc[1].split("|")[0]:''
        var systemName=d.doc[1] !=null?d.doc[1].split("|")[1]:''
        this.docDetails={
          ticketId: this.assignTicketDetails.ticketId,
        fileName: filename,
        systemFileName: systemName,
        createdBy: this.globalUser.userDetails.value.personTenantRoleId
        
        }
        this.store.dispatch(attcahDoc({attachedDoc:this.docDetails}))
        //this.router.navigate(['loader','support-ticket']);
         this.navigateToParentUsingBreadCrumb();
       }
      })
    }

    }) 
    
  }

  
  
    
}

export enum ticketStatus{
  Open=1,
  Close=2,
  Assigned=3,
}