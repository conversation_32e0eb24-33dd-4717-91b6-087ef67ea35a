
import {
    consoleLoaderJSON,
    moduleTypes,
    nestingTypes,
    paramTypes,
    Roles,
    widgetTypes,
  } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
  import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
  
  export const moduleJSON: consoleLoaderJSON = {
    modules: [
      {
        type: moduleTypes.Dynamic,
        name: "Manage Employees",
        moduleID: "manage-employees",
        nestingType: nestingTypes.ColumnsInRows,
        iconRef: "demographicManageIcon",
        routeRef: "/loader",
        rows: [
          {
            class: "",
            gdColumns: "1fr",
            gdColumnsltlg: "1fr",
            gdColumnsltmd: "1fr",
            gdColumnsltsm: "1fr",
            gdGap: "12px",
            columns: [
              {
                class: "w-full",
                gdColumn: "1 / 1",
                gdColumnltlg: "1 / 1",
                gdColumnltmd: "1 / 1",
                gdColumnltsm: "1",
                fxLayout: "",
                fxLayoutAlign: "start center",
                fxFlex: "none",
                fxFlexltmd: "auto",
                fxHidexs: "false",
                fxHidesm: "false",
                widget: {
                  type: widgetTypes.DynamicTable,
                  tableOptions: {
                    headerRowCssClasses: ["tab-col"],
                    showPaginator: true,
                    pageSizeOptions: [8, 20, 100],
                    pageSize: 8,
                    showColumnFilter: true,
                    ShowDownload:true,
                    showColumnWiseFilter: true,
                    showMatTabs: false,
                    showAddButton: [true],
                    addButtonTexts: ["Add Employees"],
                    showSearch: true,
                    verticalScroll: true,
                    horizontalScroll: true,
                    isPaginationServerSide: [true],
                    isSearchServerSide: [false],
                    showStateSelectionDropdown:false,
                    addPopupData: [
                      {
                        isCustom: true,
                        customTemplateRef: "add-employees",
                        title: ["Add Employees"],
                        id: "add-employees",
                        buttons: [
                          {
                            buttonText: "Submit",
                            requestDetails: {
                              fetchUrl: ["client", "api", "user"],
                              requestBody: null,
                              requestParams: null,
                              method: "POST",
                            },
                          },
                        ],
                      },
                    
                    ],
                  },
                  displayedColumns: [
                    [
                      <tableTypes.column>{
                        visible: true,
                        id: "checkbox",
                        name: "Checkbox",
                        cellType: "text",
                        headerType: "checkbox",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["w-4"],
                        cellCssClasses: [],
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: ["firstName", "middleName", "lastName"],
                        name: "Candidate Name",
                        cellType: "link",
                        linkMetaData: {
                          navigateToType:null,
                          navigateTo: "",
                          eventDataUseful: false,
                          navigateToLabel: "",
                        },
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                    
                      <tableTypes.column>{
                        visible: true,
                        id: "address",
                        name: "Address",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "certNumber",
                        name: "Cert Number",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        filterable: true,
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "certStatus",
                        name: "Cert Status ",
                        cellType: "text",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        filterable: true,
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "employmentStartDate",
                        name: "Employment StartDate ",
                        cellType: "date",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        isFilter: false
                      },
                      <tableTypes.column>{
                        visible: true,
                        id: "employmentEndDate",
                        name: "Employment EndDate ",
                        cellType: "date",
                        headerType: "sort",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: [],
                        cellCssClasses: [],
                        isFilter: false
                      },
                    
                      <tableTypes.column>{
                        visible: true,
                        id: "action",
                        name: "Action",
                        cellType: "action",
                        actions: [
                          <tableTypes.action>{
                            icon: "edit",
                            type: tableTypes.actionTypes.Edit,
                            tooltip: "Edit",
                            color: "#7d7d7d",
                            // visibilityRule: [{
                            //   operator: tableTypes.Operators.AND,
                            //   elementProperty: 'emailId',
                            //   compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                            //   shouldHaveValue:null,
                            // }],
                            requestDetails: [
                              {
                                fetchUrl: ["client", "api", `user`],
                                requestBody: null,
                                requestParams: [],
                                method: "PUT",
                              },
                            ],
                          },
                          <tableTypes.action>{
                            icon: "delete",
                            type: tableTypes.actionTypes.Employee_delete,
                            tooltip: "Delete",
                            color: "#7d7d7d",
                          },
                        ],
                        headerType: "normal",
                        headerAlign: "center",
                        cellAlign: "center",
                        headerCssClasses: ["width-6"],
                        cellCssClasses: [],
                      },    
                      
                    
                    ],
                  ],
                  requestDetails: [
                    {
                      fetchUrl: ["client", "api","api", "Employer", "employees"],
                      requestBody: null,
                      requestParams: [
                        {
                            paramType: paramTypes.Required,
                            paramName: null,
                            paramValue: null,
                            extractedFromGlobal: true,
                           elementPropertyToBeExtracted: "clientORtenantId",
                            position: 4,
                        },
                      ],
                      method: "GET",
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
    ],
  };