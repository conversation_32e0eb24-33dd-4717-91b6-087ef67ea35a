.viewBtns {
    right: 0px!important;
}

.viewNotes {
    color: var(--text-color2);
}

.custom1{
    margin-right: 33rem;
}
.re-evaluate {
    margin-right: 23px;
}
.textcancel{
    color: var(--text-delete) !important;;
}

.textsave{
    color: var(--text-color2) !important;
    ;
}

.matheader{
    height: 42px !important;
    background-color: #F9F9F9;
    font: Roboto;
    font-weight: 500;
    size: 18px;
    color: #a7a8ac !important;
    box-shadow: none;
}
.title{
    color: #a7a8ac !important;
}

.form-cards {
    display: flex;
    flex-direction: column;
    align-items: normal;
    justify-content: center;
    box-shadow: none;
    padding: 0.65rem;
    margin-top: -25px;
}

@media screen and (min-width: 1440px) {
    #gallery-1 {width:788px}
}
.button-disabled {
    opacity: 0.4;
    pointer-events: none;
}

.INF {
    font-size: 11px;
    position: absolute;
    /* font-size: 2rem; */
    margin-top: .16rem;
  
  }

  .mat-text-header{
    color: #11263c;
    font-size: 0.75rem;
  }
  
  .INFLABEL {
    font-size: 0.55rem;
  }

  .INFTEXT {
    font-size: 0.7rem;
    font-color: gray;
  }

.wwidth{
    width: 70%;
    margin-top: 8px;
}
