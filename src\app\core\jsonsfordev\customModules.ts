import { consoleLoaderJSON, moduleTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";


export const customModulesJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Custom,
      name: "Dashboard",
      moduleID: "dashboard",
      routeRef: '/dashboard',
      iconRef: "dashboardIcon",
    },
    {
      type: moduleTypes.Custom,
      name: "Schedule-Exam",
      moduleID: "schedule-exam",
      routeRef: "/scheduleExam",
      iconRef: "scheduleIcon",
    },
    {
      type: moduleTypes.Custom,
      name: "Manage Eligibilty Routes",
      moduleID: "manage-eligibilty-route",
      routeRef: '/manage-eligiblity',
      iconRef: "manageEligibilityIcon",
    },
    {
      type: moduleTypes.Custom,
      name: "Manage Voucher Codes",
      moduleID: "manage-voucher-code",
      routeRef: '/voucher-codes',
      iconRef: "manageVoucherCodes",
    },
    {
      type: moduleTypes.Custom,
      name: "Course Completions",
      moduleID: "training-program",
      routeRef: '/training',
      iconRef: "training",
    },  
    {
      type: moduleTypes.Custom,
      name: "Voucher",
      moduleID: "voucher",
      routeRef: '/voucher',
      iconRef: "gift_Card",
    }, 
    {
      type: moduleTypes.Custom,
      name: "Reports",
      moduleID: "reports-parent",
      routeRef: '/report',
      iconRef: "insert_chart_outlined",
    },
    {
      type: moduleTypes.Custom,
      name: "Work_bench_report",
      moduleID: "Super_Set",
      routeRef: '/superset',
      iconRef: "insert_chart_outlined",
    },


    
    {
      type: moduleTypes.Custom,
      name: "Events",
      moduleID: "publish_event",
      routeRef: '/SkillNAE/0/0/0/0/0',
      iconRef: "publish",
    },
      {
        type: moduleTypes.Custom,
        name: "Roster",
        moduleID: "Scan",
        routeRef: '/Skills',
        iconRef: "scanner",
      }
      // {
      //   type: moduleTypes.Custom,
      //   name: "Roster",
      //   moduleID: "scan-roaster",
      //   routeRef: '/Skills',
      //   iconRef: "scanner",
      // }
    // {
    //   type: moduleTypes.Custom,
    //   name: "Home",
    //   moduleID: "state-report",
    //   routeRef: '/report',
    //   sidenavParentReference:"reports-parent"
    // },
    // {
    //   type: moduleTypes.Custom,
    //   name: "Self-service",
    //   moduleID: "self-service",
    //   routeRef: '/self-service',
    //   sidenavParentReference:"reports-parent"
    // },
  ]
}