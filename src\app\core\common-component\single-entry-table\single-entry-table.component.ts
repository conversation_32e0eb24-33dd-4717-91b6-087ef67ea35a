import { Component, Input, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Action, Store } from '@ngrx/store';
import { Roles, State } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { BehaviorSubject, Observable } from 'rxjs';
import { Access, requestDetails } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { createGeneric, creationResultReset, updateAccount } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import * as singleEntryTableTypes from './single-entry-table.types';
import lodash from 'lodash';
import { recentLinkClickEventSelector, selectCreationResult, UpdatedEmailstatus } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { column } from './single-entry-table.types';
import { DatePipe } from '@angular/common';
import { clearUserData, getuser, Refreshtable } from 'src/app/client/state/shared/shared.actions';
import { selectUser } from 'src/app/client/state/shared/shared.selectors';
import { getUsers } from 'src/app/client/state/shared/shared.state';
import { GlobalUserService } from '../../global-user.service';
import { linkClickEvent } from '../table/dynamic-table-types';
import { DynamicPopupMesageComponent } from '../pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component';
import { LanguageService } from '../../language.service';
import { MatDialog } from '@angular/material/dialog';
import { HttpService } from '../../http.service';
import { SnackbarService } from '../../snackbar.service';
import { OMRVideoImage } from '../pop-up/omr-image-video/omr-image-video.component';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'exai-single-entry-table',
  templateUrl: './single-entry-table.component.html',
  styleUrls: ['./single-entry-table.component.scss']
})
export class SingleEntryTableComponent implements OnInit {
  tabdetails;
  description  = "";
  dataIndex: number
  @Input() displayedColumns: Array<singleEntryTableTypes.column>;
  @Input() requestDetails: Array<requestDetails>;
  @Input() data: any;
  @Input() accessData: Access;
  Omr:Array<object>=[]
  ExamName:string
  isEditing: BehaviorSubject<string | null> = new BehaviorSubject<string>(null);
  isEditing$: Observable<string | null> = this.isEditing.asObservable();
  editingIndex: BehaviorSubject<number | null> = new BehaviorSubject<number>(null);
  editingIndex$: Observable<number | null> = this.editingIndex.asObservable();
  editFormControl: FormControl;
  disableSave: boolean = true;
  cancelbutton: boolean = true
  personTenantRole: number;
  eligibilityRoute:any;
  NAE_Description:string
  recentLinkClickEvent: linkClickEvent;
  
  constructor(private store: Store, public global: GlobalUserService, private lngSrvc: LanguageService, public dialog: MatDialog, private http: HttpService, private services: SnackbarService,private https:HttpClient
  ) {}
  
  ngOnInit(): void {
    setTimeout(() => {
      this.store.select(selectCreationResult).subscribe((result: boolean) => {
        if (result) {
          this.isEditing.next(null);
          this.editingIndex.next(null);
          this.store.dispatch(creationResultReset());
        }
      })
    }, 1000);

    this.store.select(recentLinkClickEventSelector).subscribe(data => {
         this.NAE_Description = data.element.notes?data.element.notes:'hello'
     
      this.recentLinkClickEvent = data;

      this.tabdetails = data.column.linkMetaData.navigateToLabel


      this.personTenantRole = data.element.personTenantRoleId



    })
    this.tabdetails ==='View Application Detailss'? this.http.getEligibilityRoute(this.recentLinkClickEvent.element.eligibilityRouteId).subscribe((result:eligibility) => {
      this.eligibilityRoute = result;
    }):null;

    //     this.store.select(UpdatedEmailstatus).subscribe((data:boolean)=>{
    //       // 
    //       if(data){
    //         this.disableSave=!data;
    //         this.displayedColumns.filter(x=>)
    //       }
    //  })
    if ((this.global.userDetails.getValue().stateId == State.Virginia && this.global.userDetails.getValue().roleId == Roles.StateClient) || (this.global.userDetails.getValue().stateId == State.District_of_columbia && this.global.userDetails.getValue().roleId == Roles.StateClient) || (this.global.userDetails.getValue().stateId == State.Colorado && this.global.userDetails.getValue().roleId == Roles.StateClient) || (this.global.userDetails.getValue().stateId == State.South_Carolina && this.global.userDetails.getValue().roleId == Roles.StateClient)) {
      var ssnColumn = this.displayedColumns.find(col => col.name == 'Last 4 SSN')
      ssnColumn.name = "SSN"
    }

   this.tabdetails ==='View Grievance Details'? this.getAddNotes():null
  }


  getAddNotes(){
    this.http.getNotes(this.recentLinkClickEvent.element.personFormId ,this.recentLinkClickEvent.element.personTenantRoleId,10).subscribe(res => {
      this.description = res[0].body;
    })
  }

  columnText(data: any, id: any, cellArrayProps: Array<string>) {
    if (data) {
      //only uncomment if the bug is raised to change the date format for single entry table
      if (id === "dateofBirth" || id ==='completionDate') {

        var datePipe = new DatePipe("en-US");
        return datePipe.transform(data[id], 'MM-dd-yyyy','+0000');

      }
      if (id === "createdDate") {
        var datePipe = new DatePipe("en-US");
        return datePipe.transform(data[id], 'MM-dd-YYYY');
      }
      if (id === "id") {
        const val = typeof id == 'string' ? this.cellText(data[id], cellArrayProps) : id.map((x: string) => { return this.cellText(data[x], cellArrayProps) }).join(' ');
        if (val) return val;
        if (this.recentLinkClickEvent?.element?.personId) return this.recentLinkClickEvent?.element?.personId;
        if (this.recentLinkClickEvent?.element?.PersonId) return this.recentLinkClickEvent?.element?.PersonId;
        return '';
      }

      if ((id === "ssn" && this.global.userDetails.getValue().stateId == State.District_of_columbia && this.global.userDetails.getValue().roleId == Roles.StateClient) || (id === "ssn" && this.global.userDetails.getValue().stateId == State.Virginia && this.global.userDetails.getValue().roleId == Roles.StateClient) || (id === "ssn" && this.global.userDetails.getValue().stateId == State.Colorado && this.global.userDetails.getValue().roleId == Roles.StateClient) || (id === "ssn" && this.global.userDetails.getValue().stateId == State.South_Carolina && this.global.userDetails.getValue().roleId == Roles.StateClient)) {
        id = "fullSSN";
      }
      return typeof id == 'string' ? this.cellText(data[id], cellArrayProps) : id.map((x: string) => { return this.cellText(data[x], cellArrayProps) }).join(' ');
    }
  }
  cellText(cellData: any, cellArrayProps: Array<string>) {
    return Array.isArray(cellData) ? cellData.map((cellEle: any) => {
      let retValue = '';
      if (typeof cellEle == 'string') {
        retValue += cellEle['response']
      }
      else {
        //need to work-ashutosh
        if (cellEle.response) {
          retValue += cellEle.response
        }
        cellArrayProps.forEach((x: any) => {
          retValue += cellEle[x];
        })
      }
      return retValue;
    }).join(', ') : cellData;
  }
  edit(property: string, dataIndex: number) {
    this.dataIndex = dataIndex
    if (this.isEditing.value == null && this.recentLinkClickEvent.element.externalId != "Migrated") {
      this.editFormControl = new FormControl(this.data[dataIndex][property], Validators.required);
      this.isEditing.next(property);
      this.editingIndex.next(dataIndex);
      this.disableSave = true
    }
  }
  cancel(property: string, dataIndex: number) {
    this.isEditing.next(null);
  }
  save(property: string, dataIndex: number) {
    let EMAIL_REGEXP = /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i;
    let EmailValid = EMAIL_REGEXP.test(this.editFormControl.value)
    this.dialog.open(DynamicPopupMesageComponent, {
      data: {
        title: EmailValid == false && this.tabdetails == "Training Program Details" ? this.lngSrvc.curLangObj.value.editPhoneNumber : this.lngSrvc.curLangObj.value.editEmail,
        cancelButton: this.lngSrvc.curLangObj.value.no,
        OkButton: this.lngSrvc.curLangObj.value.yes,
      },
    })
      .afterClosed().subscribe((confirmed: any) => {

        if (confirmed == true || confirmed.confirmed == true) {

          if (this.editFormControl.valid && EmailValid == true) {




            // var dataClone = lodash.cloneDeep(this.data);
            // dataClone[dataIndex][property] = this.editFormControl.value;
            // var requestDetailsClone = lodash.cloneDeep(this.requestDetails[1]);
            // requestDetailsClone.requestBody = dataClone[dataIndex];

            // this.store.dispatch(createGeneric({
            //   requestDetails: requestDetailsClone
            // }));

            //added edit email
            this.store.dispatch(updateAccount({

              personTenantRoleId: this.data[this.dataIndex].personTenantRoleId,
              personId: this.data[this.dataIndex].personId,
              newEmailId: this.editFormControl.value,
            }));
            this.store.dispatch(clearUserData());
            setTimeout(() => {
              this.store.select(UpdatedEmailstatus).subscribe((data: boolean) => {

                if (data) {

                  this.disableSave = !data;
                  this.isEditing.next(null);
                  if (this.recentLinkClickEvent.column.linkMetaData.navigateTo != "candidate-details") {
                    let Id = this.recentLinkClickEvent.element.id ? this.recentLinkClickEvent.element.id : this.global.trainingId
                    this.http.getUserstatus(Id).subscribe((data: any) => {
                      if (data) {
                        this.data = data
                        this.store.dispatch(clearUserData());
                        this.store.dispatch<Action>(Refreshtable())
                      }
                    })
                  } else {
                    this.store.dispatch(getuser({ personTentantId: this.data[0].personTenantRoleId }))
                    setTimeout(() => {
                      this.store.select(selectUser).subscribe((user: getUsers) => {
                        if (user != null && user) {
                          this.data = user
                          this.store.dispatch(clearUserData());

                        }
                      })
                    }, 4000);
                  }

                  // for(let i=0;i<this.data.length;i++){
                  //    a.push(this.data[i].personTenantRoleId)

                  // }

                }

                // setTimeout(() => {


                //   this.store.select(selectUser).subscribe((user:getUsers)=>{
                //     if(user !=null && user){
                //     //  this.data=user
                //      this.store.dispatch(clearUserData());
                //     }
                //   })
                // }, 4000);
              })
            }, 3000);


            // this.data = dataClone;
          }
          else if (this.tabdetails == "Training Program Details") {
            let contactDetails = {

              personTenantRoleId: this.data[this.dataIndex].personTenantRoleId,
              emailId: this.data[this.dataIndex].emailId ? this.data[this.dataIndex].emailId : "",
              contactNumber: this.editFormControl.value
            }
            this.http.getContactUpdate(contactDetails).subscribe((data) => {
              if (data != null) {

                this.isEditing.next(null);
                // this.store.dispatch(getuser({personTentantId:this.data[this.dataIndex].personTenantRoleId}))

                // this.store.select(selectUser).subscribe((user:getUsers)=>{
                //   if(user !=null && user){
                //    this.data=user
                //    this.services.callSnackbaronSuccess("Updated Successfully")
                //   }
                // })
                this.http.getUserstatus(this.recentLinkClickEvent.element.id).subscribe((data: any) => {
                  if (data) {
                    this.data = data
                    this.services.callSnackbaronSuccess("Updated Successfully");

                  }
                })

              }
            }, (error) => {
              this.services.callSnackbaronError(`${error.message.message.error}`)
            })
          }
        }

      })
  }


  OMRVIEW(message:string){
    const dialogRef = this.dialog.open(OMRVideoImage, {
      data: {id:this.recentLinkClickEvent,message:message,measurescore:this.Omr,vdmasterId:"608826"},
      width: '750px',
      height: '600px',
    
    })
    dialogRef.afterClosed().subscribe(data => {


    })
  }

  getVideoImage(){
    if(this.recentLinkClickEvent.column.linkMetaData.navigateToLabel=="View Grievance Details"){
      this.http.getOMrVideos(this.recentLinkClickEvent.element.personEventId).subscribe((data:any)=>{
        if(data.arkivOMRSheets.length > 0){
         this.displayedColumns = [...this.displayedColumns,...[{id: '', name: 'Action', editable: false}]]
        this.Omr = data.arkivOMRSheets
        }
   })

    // this.store.dispatch<Action>(GetVMasterId({personTenanatRoleId:this.recentLinkClickEvent.element.personEventId}))
    //     this.store.select(updateVmasterId).subscribe((data)=>{
    //        if(data){
    //         this.ExamName = data[0].title
    //           this.getVMasterId(data[0].vMasterID.toString())
    //        }
    //     })
    }
   }
}


export interface eligibility{
  title:string
  description:string
}
