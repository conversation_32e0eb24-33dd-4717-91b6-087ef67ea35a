import { HttpClient } from "@angular/common/http";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { HttpService } from "src/app/core/http.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";
import { BookletInventoryService } from "./services/booklet-inventory.service";
import moment from "moment";
import { GlobalUserService } from "src/app/core/global-user.service";
import { Router } from "@angular/router";

@Component({
  selector: "app-skills-details",
  templateUrl: "./skills-details.component.html",
  styleUrls: ["./skills-details.component.scss"],
})
export class SkillsDetailsComponent implements OnInit {
  states: Array<any> = [];
  stateCode: string;
  TestcenterDetails;
  fomrs: Array<any> = [];
  ExamCode: string;
  ShippingInformationDetails: {};
  ExamDetails: Array<{ formCode: string; quantity: number; examCode: string }> =
    [];
  orderForm: FormGroup;
  ShipInformation: FormGroup;
  examID: number;
  formIdOptions: { [key: string]: string[] } = {};
  ExamName = [
    {
      examName: "Written Exam",
      id: 1,
      examCode: "NA-WR",
    },
    { examName: "Skill Exam", id: 2, examCode: "NA-PR" },
  ];
  testCenterId: Array<any> = [];
  today = new Date()

  constructor(
    private services: HttpService,
    private snackbar: SnackbarService,
    private http: HttpClient,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private bookletInventoryService: BookletInventoryService,
    private global: GlobalUserService,
    private router: Router,
    private Snackbar: SnackbarService
  ) {
    this.orderForm = this.fb.group({
      state: new FormControl("", Validators.required),
      testCenter: new FormControl("", Validators.required),
      requiredDate: new FormControl("", Validators.required),
      orderNotes: new FormControl(""),
      exams: this.fb.array([this.createExam()], Validators.required),
    });

    this.global.userDetails.subscribe((data) => {
      if (data) {
      } else {
        this.router.navigateByUrl("/loader/booklet_inventory");
      }
    });

    this.loadShipInformation();
  }

  ngOnInit(): void {
    this.services.getStates().subscribe(
      (data: any) => {
        if (data) {
          for (var index in data) {
            this.states.push({
              stateName: data[index].stateName,
              stateCode: data[index].stateCode,
              stateId: data[index].id,
            });
          }
        }
      },
      (error) => {
        if (error.error && typeof error.error == "string")
          this.snackbar.callSnackbaronError(error.error);
      }
    );

    this.today.setDate(this.today.getDate() + 8)

  }

  get exams(): FormArray {
    return this.orderForm.get("exams") as FormArray;
  }

  get eventDate(): FormGroup {
    return this.orderForm.get("eventDate") as FormGroup;
  }

  get disableExamSelection(): boolean {
    const examId = this.orderForm.value.exams;
    const tenantId = this.orderForm.value.state;
    const startDate = this.orderForm.value.requiredDate;
    if (examId && tenantId && startDate) return false;
    return true;
  }

  loadShipInformation() {
    this.ShipInformation = this.fb.group({
      contactName: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.contactPersonName : "",
        Validators.required
      ),
      contactPhoneNumber: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.phoneNumber : "",
        Validators.required
      ),
      addressLine1: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.address1 : "",
        Validators.required
      ),
      addressLine2: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.address2 : "",
        Validators.required
      ),
      contactEmail: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.contactEmailId : "",
        Validators.required
      ),
      city: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.city : "",
        Validators.required
      ),
      postalCode: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.zipCode : "",
        Validators.required
      ),
      country: new FormControl("US", Validators.required),
      state: new FormControl(
        this.TestcenterDetails ? this.TestcenterDetails.stateCode : "",
        Validators.required
      ),
      preferredCarrier: new FormControl("UPS", Validators.required),
      shippingNotes: new FormControl("", Validators.required),
    });
  }

  onSubmit() {
    this.orderForm.value.exams.filter((x) => {
      x.formId != "" && x.ExamCode != "" && x.quantity != ""
        ? this.ExamDetails.push({
            formCode: `Form ${x.formId}`,
            examCode: x.examName == 1 ? "NA-WR" : "NA-PR",
            quantity: x.quantity,
          })
        : null;
    });

    this.ShippingInformationDetails = {
      ...this.ShipInformation.value,
      requiredDate: this.orderForm.value.requiredDate,
      recipient: this.ShipInformation.value.contactName,
      phoneNumber: this.ShipInformation.value.contactPhoneNumber,
    };
    let ExamDetailsInformation = {
      jurisdiction: this.stateCode,
      personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
      testingSiteIdentifier: this.TestcenterDetails.testCenterId,
      orderNotes: this.orderForm.value.orderNotes,
      exams: this.ExamDetails,
      shippingInformation: this.ShippingInformationDetails,
    };

    this.http
      .post(
        `${environment.baseUrl}client/api/credentia/order-inventory`,
        ExamDetailsInformation
      )
      .subscribe(
        (data: { message: string }) => {
          if (data) {
            this.snackbar.callSnackbaronSuccess(`${data.message}`);
            this.router.navigateByUrl("/loader/booklet_inventory");
            this.orderForm.reset();
            this.ShipInformation.reset();
          }
        },
        (err) => {
          this.snackbar.callSnackbaronError(`${err.message.error.message}`);
        }
      );
  }

  changeCalendar(event: any) {
    // if (this.examID) {
    //   this.getExamFormDetails(this.examID);
    // }
  }

  onSelectionChange(event: any, index: number) {
    this.orderForm.value.exams = [];
    this.ExamCode = this.ExamName.filter(
      (x) => x.id === event.value
    )[0].examCode;
    this.getExamFormDetails(index, event.value);
  }

  getExamFormDetails(index: number, examId: number) {
    this.bookletInventoryService
      .getExamformDetails({
        examId: examId,
        tenantId: this.orderForm.value.state,
        eventDate: this.orderForm.value.requiredDate,
      })
      .subscribe(
        (result) => {
          if (result) {
            this.formIdOptions[examId] = result; // Update the formId options dictionary
            this.updateFormId(index, result);
          }
        },
        (error) => {
          if (error.error && typeof error.error == "string")
            this.snackbar.callSnackbaronError(error.error);
        }
      );
  }

  SelectedTestcenter(testcenterDetails) {
    this.TestcenterDetails = testcenterDetails;
    this.loadShipInformation();
  }

  examEvent(event) {
    this.stateCode = event.stateCode;
    this.http
      .get(
        `${environment.baseUrl}client/api/client/GetTestCenterDetailsByStateForCredentia?stateCode=${event.stateCode}`
      )
      .subscribe((data: Array<object>) => {
        if (data.length > 0) {
          this.testCenterId = data;
          this.ShipInformation.reset();
        }
      });
  }

  createExam(): FormGroup {
    return this.fb.group({
      examName: ["", Validators.required],
      formId: ["", Validators.required],
      quantity: ["", Validators.required],
    });
  }

  addExam(): void {
    this.exams.push(this.createExam());
    this.cdr.detectChanges();
  }

  examSelect(event: any) {
    return event.id;
  }

  removeExam(index: number) {
    this.exams.removeAt(index);
  }

  updateFormId(index: number, formIdList: string[]): void {
    const formGroup = this.exams.at(index) as FormGroup;
    formGroup.get("formId")?.setValue("");
  }

  cancel() {
    this.router.navigateByUrl("/loader/booklet_inventory");
  }
}
