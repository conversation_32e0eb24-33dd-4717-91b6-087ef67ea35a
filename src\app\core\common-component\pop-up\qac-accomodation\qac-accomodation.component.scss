.borderr{
    border-bottom: 1px solid var(--text-edit);
}

.status {
    color: var(--text-edit);
}

.ww{
    width: 25vw;
}

.textarea{
    width: 400px;
}


.status1{
    color: var(--text-tr);
}

::ng-deep .mat-dialog-container{
    padding: 0px;
}

.btn-11 {
    background-color: var(--text-color2);
    color: var(--button-color);
    border: 1px solid var(--text-color2);
    border-radius: 4px;
    padding: 0rem 1rem;
    font-weight: 500;
    line-height: 26px;
}

.btn-11:disabled{
    background-color: transparent;
}
