<div exaiContainer class="w-full pt-2 customPopUp">
    <form [formGroup]="form" class="w-full">
      <ng-container>
        <div fxLayout="row" class="flex justify-between pt-2">
          <mat-form-field appearance="outline" class="manageUsers mr-2">
            <mat-label>First Name</mat-label>
            <input type="text" formControlName="firstName" matInput readonly>
          </mat-form-field>
          <mat-form-field appearance="outline" class="manageUsers mr-2">
            <mat-label>Middle Name</mat-label>
            <input type="text" formControlName="middleName" matInput readonly>
          </mat-form-field>
          <mat-form-field appearance="outline" class="manageUsers">
            <mat-label>Last Name</mat-label>
            <input type="text" formControlName="lastName" matInput readonly>
          </mat-form-field>
        </div>
  
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>Email</mat-label>
          <input type="text" formControlName="email" matInput readonly>
        </mat-form-field>
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>User Role</mat-label>
          <mat-select (selectionChange)="selectedRole($event)" formControlName="role" multiple>
            <mat-option *ngFor="let role of userRoles" [value]="role.id">{{role.roleName}}</mat-option>
          </mat-select>
          <mat-error *ngIf="form.controls['role'].hasError('required')">
            User Role is required
          </mat-error>
        </mat-form-field>
  
        <mat-form-field appearance="outline" class="w-full">
          <mat-label>State</mat-label>
          <mat-select formControlName="tenantId" (selectionChange)="state($event)">
            <mat-option *ngFor="let state of statesOrTrainingIns" [value]="state.id">{{state.stateName ? state.stateName :
              state.name}}</mat-option>
          </mat-select>
        </mat-form-field>
  
        <div *ngIf="showTrainingInstitute" class="w-full">
          <!-- Display selected training institutes -->
          <mat-form-field *ngIf="selectedTrainingInstitutes.length > 0" appearance="outline" class="w-full">
            <mat-label>Selected Training Institutes</mat-label>
            <input matInput [value]="getTrainingInstituteDisplayText()" readonly
                   placeholder="Selected training institutes will appear here">
            <button matSuffix mat-icon-button type="button"
                    (click)="clearAllTrainingInstitutes()"
                    title="Clear all training institutes">
              ×
            </button>
          </mat-form-field>
  
          <!-- Search and add new training institutes -->
          <mat-form-field appearance="outline" class="w-full">
            <mat-label>Add Training Institute</mat-label>
            <input type="text" matInput [formControl]="trainingInstituteControl" [matAutocomplete]="trainingInstituteAuto"
              placeholder="Type to search and add training institutes..." [disabled]="loadingTrainingInstitute">
            <mat-spinner *ngIf="loadingTrainingInstitute" matSuffix diameter="20" strokeWidth="3"></mat-spinner>
            <mat-autocomplete #trainingInstituteAuto="matAutocomplete" [displayWith]="displayTrainingInstitute"
              (optionSelected)="onTrainingInstituteSelected($event.option.value)">
              <mat-option *ngFor="let institute of filteredTrainingInstitutes | async" [value]="institute">
                {{institute.name || institute.title}}
              </mat-option>
              <mat-option *ngIf="(filteredTrainingInstitutes | async)?.length === 0" disabled>
                No training institutes found
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
  
          <!-- Individual training institute tags with remove buttons -->
          <div *ngIf="selectedTrainingInstitutes.length > 0" class="flex flex-wrap gap-2 mt-2">
            <div *ngFor="let institute of selectedTrainingInstitutes"
                 class="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
              <span>{{institute.name || institute.title}}</span>
              <button type="button"
                      (click)="removeTrainingInstitute(institute)"
                      class="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                      title="Remove this training institute">
                ×
              </button>
            </div>
          </div>
  
          <!-- Validation error -->
          <mat-error *ngIf="form.controls['trainingInstitute'].hasError('required')" class="mt-1">
            Training Institute is required
          </mat-error>
        </div>
  
      </ng-container>
      <div #ButtonDiv class="flex justify-end pb-2">
        <button class="btn-2 t-xs" type="submit" [disabled]="form.invalid" primary mat-button
          *ngFor="let button of buttons; let btnIndex = index" (click)="submit(button.requestDetails,btnIndex)">
          <span>{{button.buttonText}}</span>
        </button>
      </div>
    </form>
  </div>
  
  