import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { exhaustMap, map, switchMap } from "rxjs/operators";
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { assignVoucher, candidateCompleteDetails, candidateCompleteDetailssponsor, candidateCompleteList, candidateCompleteListSponsor, candidateDetails, candidateList, downloadLink, examVoucherDetails, fetchvoucherCartDetails, fetchvouchercartDetails, getExamDetailtoDownload, getinduvialExamDetailtoDownload, getStateId, getVoucherDetails, getVoucherlist, gotSucessCart, induvialdownloadLink, passDataToCart, selectedExamVoucherList, selectedVoucherList, successMessageAssignVoucher,  tempselectedExamVoucherList, tempselectedVoucherList, trainingStateId, VoucherDetails} from "./voucher.action";
import { environment } from "src/environments/environment";
import * as FS from 'file-saver';
import { VoucherService } from "../voucher.service";
@Injectable({
    providedIn: 'root',
  })


  
  export class VoucherEffects {
    public url:any;
    personDetail:any;
    constructor(
        public  http:HttpClient,
        public action$:Actions,
        private router:Router,
        private sanckServices: SnackbarService,
        private global:GlobalUserService,
        private voucherService:VoucherService
      ) {
       }

      

      public ExamDetails$=createEffect(()=>{
        return this.action$.pipe(
          ofType(examVoucherDetails),
          switchMap((action)=>{
            if(action.personDetail.roleId==15){
              return this.http.get(`${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`)
            .pipe(map((data:any)=>{
                return getVoucherlist({details:data});
            }));
            }
            else{
              return this.http.get(`${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`)
              .pipe(map((data:any)=>{
                  return getVoucherlist({details:data});
              }));
            }
            
        }))
      });

      public VoucherDetails$=createEffect(()=>{
        return this.action$.pipe(
          ofType(VoucherDetails),
          switchMap((action)=>{
            if(action.personDetail.roleId==15){
              return this.http.get(`${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`)
            .pipe(map((data:any)=>{
                return getVoucherDetails({voucherDetails:data});
            }));
            }
            else{
              return this.http.get(`${environment.baseUrl}sponsor/api/Voucher/item-list?stateId=${action.personDetail.stateId}&personId=${action.personDetail.personId}&personRoleId=${action.personDetail.roleId}`)
              .pipe(map((data:any)=>{
                  return getVoucherDetails({voucherDetails:data});
              }));
            }
        }))
      });
      
      public listOfVoucher$=createEffect(()=>{
        return this.action$.pipe(
          ofType(selectedVoucherList),
          switchMap((action)=>{
            return this.http.post(`${environment.baseUrl}sponsor/api/Voucher/get-details`,action.examTypeId)
            .pipe(map((data:any)=>{
                return selectedExamVoucherList({listOfVoucher:data});
            }));
        }))
      });

      public candidateList$=createEffect(()=>{
        return this.action$.pipe(
          ofType(candidateDetails),
          switchMap((action)=>{
            try{
              if(action.personTenantRoleId!=undefined){
            return this.http.get(`${environment.baseUrl}client/api/account/getusers?personRoleId=1&personTenantRoleId=${action.personTenantRoleId}&showall=false&showOnlyPrimary=false`)
            .pipe(map((data:any)=>{
                return candidateList({candidateList:data});
            }));
          }
          }
          catch(e){}
            
        }))
      })
      
      public candidateCompleteList$=createEffect(()=>{
        return this.action$.pipe(
          ofType(candidateCompleteList),
          switchMap((action)=>{
            try{
              let url : string = `${environment.baseUrl}client/api/traininginstitute/candidates?`;
            let res:Array<number>  = action.tenantId;
            res.forEach((value)=>{
            url += '&Id='+value;
            })
            return this.http.get(url)
            .pipe(map((data:any)=>{
                return candidateCompleteDetails({candidateCompleteList:data});
            }));
          }
          catch(e){}
            
        }))
      })


      public getVoucherId$=createEffect(()=>{
        return this.action$.pipe(
          ofType(assignVoucher),
          switchMap((action)=>{
            try{
            return this.http.put(`${environment.baseUrl}sponsor/api/Voucher/assign-to-person?voucherId=${action.voucherId}&personId=${action.tenantroleId}`,{})
            .pipe(map((data:any)=>{
              this.sanckServices.callSnackbaronSuccess(
                "Assigned successfully"
              );
                return successMessageAssignVoucher({sucessmessage:data});
            }));
          }
          catch(e){}
            
        }))
      })

      public cartDetails$=createEffect(()=>{
        return this.action$.pipe(
          ofType(passDataToCart),
          switchMap((action)=>{
            try{
              
            return this.http.post(`${environment.baseUrl}candidate/api/Exam/cart/vouchers`,action.cartDetails)
            .pipe(map((data:any)=>{
              this.sanckServices.callSnackbaronSuccess(
                "Added successfully"
              );
                return gotSucessCart({sucessCartmessage:data});
            }));
          }
          catch(e){}
            
        }))
      })
      
      public submissionForm$=createEffect(()=>{
        return this.action$.pipe(
          ofType(fetchvoucherCartDetails),
          switchMap((action)=>{
            return this.http.get(`${environment.baseUrl}candidate/api/Exam/cart/vouchers?personTenantRoleId=${action.personTentantRoleId}`)
            .pipe(map((data:any)=>{
                return fetchvouchercartDetails({voucherCartDeatils:data});
            }));
        }))
      });

      public getStateID$=createEffect(()=>{
        return this.action$.pipe(
          ofType(getStateId),
          switchMap((action)=>{
            return this.http.get(`${environment.baseUrl}client/api/traininginstitute/list?trainingInstituteId=${action.trainingInstituteID}&pageNumber=1&pageSize=1000`)
            .pipe(map((data:any)=>{
                return trainingStateId({gotTiId:data});
            }));
        }))
      });

      public getvoucherDownload$=createEffect(()=>{
        return this.action$.pipe(
          ofType(getExamDetailtoDownload),
          exhaustMap((action)=>{
            return this.http.post(`${environment.baseUrl}sponsor/api/Voucher/download-all`,action.voucherData,  { observe: 'body', responseType: 'blob' as 'json' })
            .pipe(map((data:any)=>{
              var blobResponse = data as Blob;
               FS.saveAs(blobResponse,this.voucherService.downloadFileName + '_' + new Date());
                return downloadLink({downloadlink:data});
            }));
        }))
      });

      
      public getindivualvoucherDownload$=createEffect(()=>{
        return this.action$.pipe(
          ofType(getinduvialExamDetailtoDownload),
          exhaustMap((action)=>{
            return this.http.post(`${environment.baseUrl}sponsor/api/Voucher/download`,action.induvialvoucherData,  { observe: 'body', responseType: 'blob' as 'json' })
            .pipe(map((data:any)=>{
              var blobResponse = data as Blob;
               FS.saveAs(blobResponse,  this.voucherService.VoucherFileName + '_' + new Date());
                return induvialdownloadLink({induvialdownloadlink:data});
            }));
        }))
      });


      public templistOfVoucher$=createEffect(()=>{
        return this.action$.pipe(
          ofType(tempselectedVoucherList),
          switchMap((action)=>{
            return this.http.post(`${environment.baseUrl}sponsor/api/Voucher/get-details`,action.tempexamTypeId)
            .pipe(map((data:any)=>{
                return tempselectedExamVoucherList({templistOfVoucher:data});
            }));
        }))
      });

      public candidateCompleteListsponsor$=createEffect(()=>{
        return this.action$.pipe(
          ofType(candidateCompleteListSponsor),
          switchMap((action)=>{
            try{
              return this.http.get(`${environment.baseUrl}client/api/state/${action.tenantId.stateId}/candidates`)
            .pipe(map((data:any)=>{
                return candidateCompleteDetailssponsor({candidateCompleteListsponsor:data});
            }));
          }
          catch(e){}
            
        }))
      })

    }

