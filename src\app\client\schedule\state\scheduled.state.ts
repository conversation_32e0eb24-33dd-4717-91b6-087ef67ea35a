import { ActiveForm } from "./models/activeForm";
import { Cart } from "./models/cart";
import { CartItem } from "./models/cartItem";
import { chargeResponse } from "./models/charge";
import { customerId } from "./models/customerId";
import { Exam } from "./models/Exam";
import { ExamName } from "./models/examName";
import { ExamType } from "./models/examType";
import { MakePaymentResponse } from "./models/makePayment";
import { MonthlySlot } from "./models/monthlySlot";
import {
  CreatePaymentCustomerIdResponse,
  CreatePaymentMethodRespnse,
  paymentMethod,
} from "./models/paymentMethods";
import { slot, Slot } from "./models/slot";
import { Timezone } from "./models/timezone.model";
import { VocherResponse, VocherUpdateResponse } from "./models/VocherList";

export interface ScheduledState {
  timeSlotsTestCenterStatus: "idle" | "loading" | "success" | "error";
  timeslotsTestCenter: slot[];
  timezones: Timezone[];
  timezone: Timezone;
  timeslots: Slot[];
  cart: Cart;
  monthlySlots: MonthlySlot[];
  examId: ExamType[];
  route: ExamName;
  registeredExams: Exam[];
  makepaymentresponse: MakePaymentResponse;
  cartItems: CartItem[];
  schedule: string;
  isCancelled: boolean;
  rescheduleResponse: number;
  isPayment: boolean;
  candidateId: number;
  scheduleResponse: any;
  voucherCart: any;
  deleteVoucherCart: any;
  paymentDetails: any;
  generateVoucher: any;
  VocherResponse: VocherResponse;
  VocherUpdateResponse: VocherUpdateResponse;
  editCartItems: any;
  VochersApply: number;
  trainingStateID: any;
  paymentMethods: paymentMethod[];
  chargeResponse: chargeResponse;
  customerIdObj: customerId,
  voucherDetails: any;
  createPaymentMethodResponse: CreatePaymentMethodRespnse;
  createPaymnetCustomerIdResponse: CreatePaymentCustomerIdResponse;
  voucherCode: string;
}

export const initScheduledState: ScheduledState = {
  timeSlotsTestCenterStatus: "idle",

  timeslotsTestCenter: null,
  timezones: [],
  timezone: null,
  timeslots: null,
  cart: null,
  monthlySlots: [],
  VocherUpdateResponse: null,
  examId: [],
  route: null,
  registeredExams: [],
  makepaymentresponse: null,
  cartItems: [],
  schedule: null,
  isCancelled: null,
  rescheduleResponse: null,
  scheduleResponse: null,
  isPayment: null,
  candidateId: null,
  voucherCart: null,
  deleteVoucherCart: null,
  paymentDetails: null,
  generateVoucher: null,
  editCartItems: null,
  trainingStateID: null,
  paymentMethods: [],
  chargeResponse: null,
  customerIdObj: null,
  createPaymentMethodResponse: null,
  voucherDetails: null,
  VocherResponse: null,
  createPaymnetCustomerIdResponse: null,
  VochersApply: null,
  voucherCode: null,
};
