import { FormTypes } from "src/app/client/application/application.types";
import { State, consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, statuses, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";


export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: " Certified By Test",
            moduleID: "regular-certificate",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "certificateRenewal",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ['tab-col'],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showAddButton: [false],
                                    showMatTabs: true,
                                    showSearch: true,
                                    showColumnFilter: true,
                                    showColumnWiseFilter: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                    ShowFilenameFilter:true,
                                    showSlectdropstatedataattributes:"reneregdrop",
                                    matTabs: [
                                        // { id: 1, name: "Pending" },
                                        // { id: 2, name: "Overdue" },
                                        // { id: 3, name: "Approved" },
                                        // { id: 4, name: "Rejected" },
                                        // { id: 5, name: "All" },
                                    ],
                                    showStateSelectionDropdown:true,
                                    isPaginationServerSide: [true],
                                    isSearchServerSide: [true]
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent", "w-4"],
                                            cellCssClasses: [],
                                          },
                                       
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "candidateId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["candidateFirstName", "candidateMiddleName", "candidateLastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "effectiveDate",
                                            name: "Submission Date",
                                            cellType: "Date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable:true
                                           
                                        },

                                        
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                            statuses: tableTypes.tableStatuses
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "registryStatus",
                                            name: "Status",
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                            statuses: tableTypes.tableStatuses
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center","width-6"],
                                            cellCssClasses: ["text-center"],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                },
                                                <tableTypes.action>{
                                                    icon: "download",
                                                    type: tableTypes.actionTypes.Certificdown,
                                                    tooltip: "Certificate Download",
                                                    color: "#7d7d7d",
                                                    visibilityRule:[
                                                        {
                                                            operator: tableTypes.Operators.AND,
                                                            elementProperty: 'stateId',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: State.Pennsylvania
                                                        },
                                                        {
                                                            operator: tableTypes.Operators.AND,
                                                            elementProperty: 'registryStatusId',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: 4
                                                        }
                                                    ]
                                                }
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "regular-certificate-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Regular Certificate Details",
                                            },

                                        }
                                    ]
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "operationstaff",
                                            "active-registry-exam",
                                          
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            }
                                        ],
                                        method: "GET",
                                    }
                                ]
                            }
                        }
                    ]
                }
            ]
        }
    ]
}