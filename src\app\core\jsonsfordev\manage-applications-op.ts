import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, statuses, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [

        {
            type: moduleTypes.Dynamic,
            name: "Manage Applications",
            moduleID: "manage-applications-ops",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageApplicationIcon",
            routeRef: "/loader",
            dataattributes:"manageapp",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ["tab-col"],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                  
                                    showColumnFilter: true,
                                    showColumnWiseFilter: true,
                                    showAddButton: [false,false,false,false,false,false,false,false],
                                    showMatTabs: true,
                                    showStateSelectionDropdown:true,
                                    showSlectdropstatedataattributes:"applicdrop",
                                    matTabs: [
                                        { id: 1, name: "Pending" },
                                        {
                                            id: 2, name: "Overdue",
                                            // visibilityRule: [{
                                            //     operator: tableTypes.Operators.AND,
                                            //     elementProperty: 'roleId',
                                            //     extractedFromGlobal: true,
                                            //     compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                                            //     shouldHaveValue: Roles.StateClient
                                            // }]
                                        },
                                        { id: 3, name: "Approved" },
                                        { id: 4, name: "Change Request" },
                                        { id: 5, name: "Rejected" },
                                        { id: 6, name: "Completed" },
                                        { id: 7, name: "Expired" },
                                        { id: 8, name: "All" },
                                    ],
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                   isPaginationServerSide: [true, true, true, true, true, true],
                                    isSearchServerSide: [true, true, true, true, true, true]
                                },

                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "Checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "width-5"],
                                            cellCssClasses: [],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "submissionDate",
                                            name: "Submission Date",
                                            cellType: "Date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-4"],
                                            cellCssClasses: [],
                                        
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['width-4'],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['width-4'],
                                            cellCssClasses: [],
                                        
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [],

                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Detailss",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "Checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ ""],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-4"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "submissionDate",
                                            name: "Submission Date",
                                            cellType: "Date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-4"],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],

                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Detailss",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-13"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],

                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-7"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-1/6"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-1/12"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-4"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-13"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName","middleName","lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],  filterable: true,
                                            isFilter: false

                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingInstituteName",
                                            name: "Training Program Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityRouteName",
                                            name: "Eligibility Route",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-5"],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "dob",
                                            name: "DOB",
                                            cellType: "dobtype",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "SSN",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [""],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["currentAssigneeUserName", "currentAssigneeRoleName"],
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],

                                        },


                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                    ],
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        // hardcode as of now
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Pending,
                                            },

                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        // hardcode as of now
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Pending,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "isCritical",
                                                paramValue: 'true',
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        // hardcode as of now
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Approved,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        // hardcode as of now
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.ChangeRequest,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        // hardcode as of now
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Rejected,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    // Completed Tab Configuration (id: 6, index: 5)
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Completed,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    // Expired Tab Configuration (id: 7, index: 6)
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Expired,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    // All Tab Configuration (id: 8, index: 7) - No statusId to show all
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformreview",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Application,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "isCritical",
                                                paramValue: 'false',
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ]
                            }
                        },
                    ],
                },
            ],
        }
    ]
}