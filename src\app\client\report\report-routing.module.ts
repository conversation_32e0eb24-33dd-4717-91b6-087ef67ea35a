import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ReportComponent } from './report.component';
import { SupersetDashboardComponent } from './superset/superset.component';

const routes: Routes = [
 {path: 'report', component: ReportComponent},
 {path:'superset',component:SupersetDashboardComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportRoutingModule { }
