<div exaiContainer class="w-full pt-2 customPopUp" *ngIf="this.globalUserService.userDetails.value.roleId !=13">
    <form [formGroup]="form" class="w-full">
        <ng-container>
            <mat-form-field appearance="outline" class="w-full">
                <mat-label>State</mat-label>
                <mat-select formControlName="state" >
                    <mat-option *ngFor="let state of states" [value]="state.id">{{state.stateName}} <span class="text-sm" [style.color]="'var(--text-color1)'"> | No. of Eligiblity
                            Routes:{{state.eligibilityRoutes}}</span>
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" class="w-full">
                <mat-label>Application Category</mat-label>
                <mat-select formControlName="applicationCategory" placeholder="Selected Application Category">
                    <mat-option *ngFor="let appCategory of applicationCategories" [value]="appCategory.id">
                        {{appCategory.name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field appearance="outline" class="w-full">
                <mat-label>Eligibility Route</mat-label>
                <mat-select formControlName="eligibilityRoutes">
                    <mat-option *ngFor="let er of eligibilityRoutes" [value]="er.id" [disabled]="er.isFormSubmissionAllowed == false">{{er.eligibilityName}} 
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field *ngIf="form.value.applicationCategory == FormTypes.Grievance && isGrievance" appearance="outline" class="w-full">
                <mat-label>Select Exam</mat-label>
                <mat-select formControlName="selectExam">
                    <div *ngFor="let re of registeredExams">
                    <mat-option  *ngIf="re.allowGrievance" [value]="re.id">
                        {{re.examName}}
                    </mat-option>
                    </div>
                </mat-select>
            </mat-form-field>
           
        </ng-container>
        <div #ButtonDiv class="flex justify-end pb-2">
            <button class="btn-3 t-xs mr-2" type="cancel" primary mat-button (click)="dialogRef.close()">
                <span>Cancel</span>
            </button>
            <button class="btn-1 t-xs" type="submit" [disabled]="form.invalid" primary mat-button (click)="submit()">
                <span>Create</span>
            </button>
        </div>
    </form>
</div>

<div exaiContainer class="w-full pt-2 customPopUp" *ngIf="this.globalUserService.userDetails.value.roleId ==13">
    <form [formGroup]="form" class="w-full">
        <ng-container>
            <mat-form-field  appearance="outline" class="w-full">
                <mat-label>State</mat-label>
                <input matInput placeholder="State"  formControlName="state">
              </mat-form-field>
              <mat-form-field  appearance="outline" class="w-full">
                <mat-label>State Code</mat-label>
                <input matInput placeholder="StateCode"  formControlName="stateCode">
              </mat-form-field>
              <mat-slide-toggle color="primary"
              formControlName="enable" (change)="isActive($event.checked)" 
              >
              Active
            </mat-slide-toggle>
            <mat-slide-toggle color="primary"
            formControlName="fullssn" (change)="isFullssn($event.checked)"
            >
            ShowFullSSN
          </mat-slide-toggle>
            <mat-slide-toggle color="primary"
            formControlName="isRegistry" class="" (change)="isRegistry($event.checked)"
            >
            Registry
          </mat-slide-toggle>
          <mat-slide-toggle color="primary"
          formControlName="isEmployer" class="" (change)="isEmployer($event.checked)"
          >
          IsEmployer
        </mat-slide-toggle>
           
      <mat-slide-toggle color="primary" *ngIf="this.form.value.isRegistry"
      formControlName="renewal" class="ml-4" (change)="sendRenewalReminder($event.checked)"
      >
      RenewalReminder
    </mat-slide-toggle>
    <mat-form-field class="mt-2" appearance="outline" >
        <mat-label>Select ticket Categories</mat-label>
        <mat-select multiple   formControlName="dropdown" tooltipClass="edupala-tooltip" 
        >
           <mat-option *ngFor="let ticket of tickets" [value]="ticket.id"  (click)="onselect(ticket)">
               {{ticket.name}}
            </mat-option>
         </mat-select>
       </mat-form-field>
          <div >
              <div *ngIf="state||this.form.value.isRegistry">
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>RegistryExpiryInYears</mat-label>
                    <input type="number" class="form-control" matInput 
                    formControlName="registryexpire"  [required]="true">
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>RenewalExpiryInYears</mat-label>
                    <input type="number" class="form-control" matInput
                    formControlName="renewalexpire" [required]="true" >
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>ReciprocityExpiryInYears</mat-label>
                    <input type="number" class="form-control" matInput
                    formControlName="reciporating" [required]="true"> 
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                    <mat-label>StartingNumber</mat-label>
                    <input type="text" class="form-control" matInput
                    formControlName="startingNumber">
                  
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>StartsWith</mat-label>
                  <input type="text" class="form-control" matInput
                  formControlName="startsWith" minlength="3" [required]="true">
                
              </mat-form-field>
             
              </div>
           
     
          
          <div *ngIf="this.form.value.renewal">
            <mat-form-field  *ngIf="this.form.value.isRegistry" appearance="outline" class="w-full" >
                <mat-label>Renewal Reminders In Days</mat-label>
                <input type="text" class="form-control" matInput
                formControlName="reminders"  >
            </mat-form-field>
          </div>
         
      
     
    <div *ngIf="this.form.value.renewal && this.form.value.isRegistry">
        <button class="btn-1 t-xs mb-3"   primary mat-button (click)="Add()" [disabled]="form.value.reminders == 0" >
            <span>Add</span>
        </button>
    </div>
        <div class="justify-start flex"  *ngIf="this.form.value.renewal && this.form.value.isRegistry" >
            <div *ngFor ="let item of Interval" class="flex ml-4">
                <p>{{item}}</p> <mat-icon class="cursor-pointer" (click)="close(item)" style="width: -4px;">close</mat-icon>
     
             </div>
           
        </div>
     


      </div>
        </ng-container><br>
        <div fxLayout="row" fxLayoutAlign="center center" *ngIf="!preResponse">

            <button mat-flat-button color="primary" class="loginBtn"
            (click)="submit()"  [disabled]="form.invalid">Create</button>
        </div>
        <div fxLayout="row" fxLayoutAlign="center center" *ngIf="preResponse">
       <button mat-flat-button color="primary"  [disabled]="form.invalid" class="loginBtn"  (click)="submit()"  >Update</button>
        </div>
    
      
      
    </form>
</div>

