import {
  Component,
  Inject,
  LOCALE_ID,
  Renderer2,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
} from "@angular/core";
import { ActivationEnd, Router } from "@angular/router";
import { Settings } from "luxon";
import { DOCUMENT } from "@angular/common";
import { Platform } from "@angular/cdk/platform";
import { NavigationService } from "../@exai/services/navigation.service";
import { SplashScreenService } from "../@exai/services/splash-screen.service";
import { LanguageService } from "./core/language.service";
import { IconService } from "./core/icon.service";
import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  row,
  module,
  column,
  Roles,
} from "./client/dynamic-component-loader/dynamic-component-loader.types";
import { Action, Store } from "@ngrx/store";
import {
  Access,
  decodedIdentityToken,
  SharedState,
} from "./client/state/shared/shared.state";
import {
  getErrorMessage,
  get_decodeInfo,
  get_reportJSON,
  get_userDetails,
  selectAccessData,
  selectConsoleLoaderJSON,
} from "./client/state/shared/shared.selectors";
import {
  fetchConsoleLoderJSON,
  fetchAccountAccess,
  setUserDetailsFromToken,
  getUserDetails,
  reportSpinBoardList,
  setReportToken,
} from "./client/state/shared/shared.actions";
import { GlobalUserService } from "./core/global-user.service";
import { Subscription } from "rxjs";
import { recheckCurModuleIndex } from "./client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions";
import { CompleteJSON } from "src/app/core/jsonsfordev/00exports-file00";
import { environment } from "src/environments/environment";
import lodash from "lodash";
import { SupportChatComponent } from "./client/support-chat/support-chat.component";
import { SnackbarService } from "./core/snackbar.service";
import Tracker from "@openreplay/tracker";
import { ReplaySessionService } from "./core/openreplay.services";
import moment from "moment";
import { productFruits } from "product-fruits";
import jwt_decode from "jwt-decode";
@Component({
  selector: "exai-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.scss"],
})
export class AppComponent implements OnInit {
  title = "exai";
  accessData: Array<Access>;
  routerEventSub: Subscription;
  personTenantRoleId: number;
  roleName = { tsmemail: "", roleName: "" };
  public static consoleLoaderJson: consoleLoaderJSON = {
    modules: [],
  };
  userName: string;
  email: string;
  harshDetails = {
    hmacHash: "",
    expirationDate: "",
    personId: 0,
    username: "",
  };
  @HostListener("window:beforeunload", ["$event"])
  async destroy($event: any) {
    const requestBody = {
      personTenantRoleId: this.global.userDetails.value.personTenantRoleId,
      token: SupportChatComponent.agentID,
      isOnline: false,
    };
    const headers = {
      type: "application/json",
    };
    navigator.sendBeacon(
      environment.baseUrl + `customerservice/api/chat/update-status`,
      new Blob([JSON.stringify(requestBody)], headers)
    );
  }

  @HostListener("contextmenu", ["$event"])
  onRightClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
  }
  constructor(
    private renderer: Renderer2,
    private platform: Platform,
    @Inject(DOCUMENT) private document: Document,
    @Inject(LOCALE_ID) private localeId: string,
    private navigationService: NavigationService,
    private splashScreenService: SplashScreenService,
    public lngSrvc: LanguageService,
    private iconService: IconService,
    private store: Store<SharedState>,
    private router: Router,
    private global: GlobalUserService,
    private snackbar: SnackbarService,
    private openreplayservices: ReplaySessionService
  ) {
    Settings.defaultLocale = this.localeId;
    if (this.platform.BLINK) {
      this.renderer.addClass(this.document.body, "is-blink");
    }

    this.store
      .select(selectConsoleLoaderJSON)
      .subscribe((x: consoleLoaderJSON) => {
        if (x && this.accessData && this.accessData.length > 0) {
          // In production the 'CompleteJSON' import will be from 00export-file00.prod.ts
          // hence will be completely empty, therefore all the ts files in
          // jsonfordev directory will not be part of typescript compilation
          AppComponent.consoleLoaderJson = environment.production
            ? lodash.cloneDeep(x)
            : { modules: [...CompleteJSON] };
          // the below is to validate whether the json copied is a valid or not
          // AppComponent.consoleLoaderJson = lodash.cloneDeep(x);
          AppComponent.consoleLoaderJson.modules =
            AppComponent.consoleLoaderJson.modules.filter((x) =>
              this.accessData.find((y) => y.moduleIdentifier == x.moduleID)
            );

          var navigationItems = [];
          for (let module of AppComponent.consoleLoaderJson.modules) {
            module.sequence = this.accessData.find(
              (y) => y.moduleIdentifier == module.moduleID
            )?.sequence;

            if (module.nestingType) {
              let items =
                module.nestingType == nestingTypes.ColumnsInRows
                  ? module.rows
                  : module.columns;

              items.forEach((element) => {
                let item =
                  module.nestingType == nestingTypes.ColumnsInRows
                    ? element.columns
                    : element.rows;
                this.assignAccess(item, module);
              });
            }

            if (module.type != moduleTypes.Offspring)
              navigationItems.push({
                type: module.name == "NAE KIT" ? "dropdown" : "link",
                label: module.name,
                route:
                  module.routeRef +
                  (module.type == moduleTypes.Dynamic
                    ? `/${module.moduleID}`
                    : ``),
                icon:
                  module.name == "NAE KIT" ||
                  module.name == "Roster" ||
                  module.name === "Test Site"
                    ? module.iconRef
                    : this.iconService[module.iconRef],
                sequence: module.sequence,
                children: module.children,
                dataattributes: module.dataattributes,
                badge: module.badge,
              });
          }
          navigationItems = navigationItems.sort(function (a, b) {
            return a.sequence - b.sequence;
          });
          this.store.dispatch(recheckCurModuleIndex());
          this.navigationService.navigationSubject.next(navigationItems);
        }
      });
  }
  ngOnInit() {
    // console.log("Release Date: 30-03-2021-12:30 PM IST");
    this.routerEventSub = this.router.events.subscribe((event) => {
      if (event instanceof ActivationEnd) {
        this.roleName = {
          tsmemail: event.snapshot.queryParams["tsmemail"],
          roleName: event.snapshot.queryParams["roleName"],
        };
        this.global.rolename = this.roleName;
        const code = event.snapshot.queryParams["token"];
        const email = event.snapshot.queryParams["email"];
        const personTenantroleId =
          event.snapshot.queryParams["personTenantroleId"];
        this.personTenantRoleId =
          +event.snapshot.queryParams["personTenantroleId"];
        this.store.dispatch<Action>(
          setUserDetailsFromToken({
            token: code ? code : null,
            email: email
              ? email
              : personTenantroleId
              ? personTenantroleId
              : null,
          })
        );
        this.routerEventSub?.unsubscribe();
      }
    });
    this.store
      .select(get_decodeInfo)
      .subscribe((data: decodedIdentityToken) => {
        if (data) {
          this.userName = data.given_name + " " + data.family_name;
          this.email = data.email;
          this.global.PublishEmail = data.email;
          this.global.firstName = data.family_name;
          this.global.lastName = data.given_name;
          this.global.email = data.email;
          // this.getOpenReplay(data.email)
          setTimeout(() => {
            this.roleName.roleName == "Proctor" && this.roleName.tsmemail != ""
              ? this.store.dispatch<Action>(
                  getUserDetails({ emailId: data.email })
                )
              : this.store.dispatch<Action>(
                  getUserDetails({
                    personTenantRoleId: this.personTenantRoleId
                      ? this.personTenantRoleId
                      : +data.email,
                  })
                );
          }, 2000);

          // this.store.dispatch<Action>(getUserDetails({ personTenantRoleId: this.personTenantRoleId?this.personTenantRoleId: +data.email }));
          // this.getProductFruitsKey(data.email)
        }
      });

    this.store.select(get_userDetails).subscribe((data) => {
      if (data) {
        // the hardcoded index 0 can later be changed to some
        // global variable that can be selected from a dropdown

        this.global.userDetails.next({
          personTenantRoleId: data.roles[0].personTenantRoleId,
          roleId: data.roles[0].roleId,
          roleName: data.roles[0].roleName,
          clientORtenantId: data.roles[0].tenantId,
          personId: data.personId,
          stateId: data.stateId,
          stateName: data.state,
          clientId: data.clientId,
          stateCode: data.roles[0].stateCode,
          userName: this.userName,
          cantest: data.cantest,
        });

        data.roles[0].roleId === Roles.StateClient ||
        data.roles[0].roleId === Roles.OperationStaff ||
        data.roles[0].roleId === Roles.SupportingStaff ||
        data.roles[0].roleId === Roles.Sponsor ||
        data.roles[0].roleId === Roles.TrainingInstitue
          ? this.getProductFruitsKey()
          : null;

        this.store.dispatch<Action>(
          fetchAccountAccess({
            personTenantRoleId:
              this.global.userDetails.value.personTenantRoleId,
            consoleId: 4,
            stateId:
              this.global.userDetails.value.stateId == null
                ? 0
                : this.global.userDetails.value.stateId,
          })
        );
      }
    });
    this.store.select(selectAccessData).subscribe((data: Array<Access>) => {
      if (data["moduleAccess"] && data["moduleAccess"].length) {
        this.accessData = data["moduleAccess"];
        if (
          this.global.userDetails.value.roleId == 14 &&
          data["manageApplication"] == false
        ) {
          this.accessData = this.accessData.filter(
            (item) => item.moduleName !== "Manage Applications"
          );
          this.router.navigate(["loader", "manage-all-users"]);
        }
        this.store.dispatch(fetchConsoleLoderJSON());
      }
    });
    this.store.select(getErrorMessage).subscribe((data: any) => {
      if (data.message) {
        if (typeof data.message.error == "string") {
          this.snackbar.callSnackbaronError(data.message.error);
        } else if (data.message.message == "string") {
          this.snackbar.callSnackbaronError(data.message.message);
        } else {
          this.snackbar.callSnackbaronError(data.message.message.error);
        }
      }
    });
  }
  private assignAccess(item: row[] | column[], module: module) {
    item.forEach((element) => {
      let moduleMap = this.accessData.find(
        (y) => y.moduleIdentifier == module.moduleID
      );
      let access = moduleMap.widgetAccess.find(
        (y) => y.widgetName == element.widget.type
      );
      if (access) {
        element.widget.accessData = {
          add: access.add,
          edit: access.edit,
          view: access.read,
          delete: access.delete,
          status: access.status,
        };
      }
    });
  }

  getOpenReplay(UserId: string) {
    const tracker = new Tracker({
      projectKey: "TvbjFO7yd99GZZVKtgFY",
      ingestPoint: "https://openreplay.examroom.ai/ingest",
      capturePerformance: true,
      __DISABLE_SECURE_MODE: true,
    });
    tracker.start();
    tracker.setUserID(UserId);
    tracker.setUserAnonymousID(UserId);
  }

  getProductFruitsKey() {
    const userDatadetails: decodedIdentityToken = jwt_decode(
      sessionStorage.getItem("token")
    );
    this.global
      .getProductFruitsKey(userDatadetails.email)
      .subscribe((response: harshDetails) => {
        if (response) {
          this.harshDetails.expirationDate = response.expirationDate;
          this.harshDetails.hmacHash = response.hmacHash;
          this.harshDetails.personId = this.global.personId;
          this.harshDetails.username = response.username;
          this.getProductsLoad();
        }
      });
  }

  getProductsLoad() {
    let userInfo = {
      username: this.harshDetails.username,
      email: this.global.email,
      firstname: this.global.firstName,
      lastname: this.global.lastName,
      signUpAt: moment(new Date()).format("YYYY-MM-DDTHH:mm:SS"),
      role: this.global.userDetails.getValue().roleName,
      hmac: {
        // this is important
        hash: this.harshDetails.hmacHash, // hmacHash is the code you generated in your backend, not the private key!
        expiration: this.harshDetails.expirationDate, // if you use the expiration feature, pass the final datetime
        personId: this.harshDetails.personId,
        userName: this.harshDetails.username,
      },
      props: {
        stateId: this.global.userDetails.getValue().stateId,
      },
    };
    productFruits.init("gPr6y9Dt39SKllAp", "en", userInfo);
  }
}
export interface harshDetails {
  hmacHash: string;
  expirationDate: string;
  username?: string;
}
