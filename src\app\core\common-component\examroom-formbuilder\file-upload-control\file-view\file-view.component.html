<div>
    <span class="" [style.position]="'relative'" [ngStyle]="{'display' : fileName != null ? 'block' : 'none'}">
        <div>
            <p class="text-xs f-medium" *ngIf="fileName">
                {{fileName}}
                <button
                    *ngIf="isResponseEditable && fileName"
                    (click)="removeFile()" class=" text-gray-800 font-semibold border-gray-400 rounded  m-0.5">
                    <mat-icon class="cursor-pointer text-base align-text-top">close</mat-icon>
                </button>
                <button *ngIf="fileName" (click)="getDocUrls()"
                    class=" text-gray-800 font-semibold border-gray-400 rounded  m-0.5 never-disabled">
                    <mat-icon class="cursor-pointer text-base align-text-top">visibility</mat-icon>
                </button>
            </p>
        </div>
    </span>  
    <div class="doc-container" *ngIf="toggleFileView">
        <!-- <div class="text-base flex  justify-end py-2 dynamicPopUp touch-auto overflow-auto" #PopupHeader>
            <div class="flex justify-end cursor-pointer">
                <mat-icon class="text-base flex" (click)="closeFiles()">close</mat-icon>
            </div>
        </div> -->
        <hr class="popUpCard">
        <ng-container
            *ngIf="(viewUrl$ | async) != null && ['png','jpg','jpeg','PNG','JPG','JPEG'].includes(this.fileExtension)"
            class="h-full cont">
            <div class="img-container">
                <img [src]="(viewUrl$ | async)" alt="uploaded img file" class="h-full view-image">
            </div>
        </ng-container>
        <ng-container
            *ngIf="(viewUrl$ | async) != null && !['png','jpg','jpeg','PNG','JPG','JPEG'].includes(this.fileExtension)"
            class="h-full">
            <ngx-doc-viewer [url]="(viewUrl$ | async)" [viewer]="'google'" class="w-full h-full view-document">
            </ngx-doc-viewer>
        </ng-container>
    </div>
</div>