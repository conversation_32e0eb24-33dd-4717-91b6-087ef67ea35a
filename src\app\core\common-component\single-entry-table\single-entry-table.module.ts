import { NgModule, Type } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SingleEntryTableComponent } from './single-entry-table.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GivanceViewService } from '../../grivance-view';
import { GrievanceViewEditComponent } from './grievance-view-edit/grievance-view-edit.component';
import { GrievanceQacListComponent } from './grievance-qac-list/grievance-qac-list.component';
import { FileViewerComponent } from './file-viewer/file-viewer.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { MatDialogModule } from '@angular/material/dialog';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { FormBuilderModule } from '../examroom-formbuilder/form-builder.module';
import { DYNAMIC_FORM_CONTROL_MAP_FN, DynamicFormControl, DynamicFormControlModel,DynamicFormsCoreModule, } from '@ng-dynamic-forms/core';
import { DynamicFileUploadControlComponent } from '../examroom-formbuilder/dynamic-file-upload-control/dynamic-file-upload-control.component';
import { DynamicFormUploadComponent } from '../examroom-formbuilder/dynamic-form-upload/dynamic-form-upload.component';
import { DynamicFormLinkComponent } from '../examroom-formbuilder/dynamic-form-link/dynamic-form-link.component';
import { DynamicFormsMaterialUIModule } from '@ng-dynamic-forms/ui-material';

@NgModule({
  declarations: [SingleEntryTableComponent,GrievanceViewEditComponent, GrievanceQacListComponent, FileViewerComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    FlexLayoutModule,
    MatIconModule,
    MatTooltipModule,
    MatAutocompleteModule,
    MatDatepickerModule,
    PdfViewerModule,
    MatDialogModule,
    FormBuilderModule,
    DynamicFormsCoreModule.forRoot(),
    DynamicFormsMaterialUIModule,
  ],
  exports: [SingleEntryTableComponent],
  providers: [ GivanceViewService,  {
    provide: DYNAMIC_FORM_CONTROL_MAP_FN,
    useValue: (
      model: DynamicFormControlModel
    ): Type<DynamicFormControl> | null => {
      switch (model.type) {
        case "FILE_UPLOAD":
          return DynamicFileUploadControlComponent;
          case "RATING":
          return DynamicFormUploadComponent;
           case "COLORPICKER":
          return DynamicFormLinkComponent;
      }
    },
  }, ]
})
export class SingleEntryTableModule { }
