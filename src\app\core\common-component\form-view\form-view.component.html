<div *ngIf="!registerForExam" class="flex flex-row absolute top-0 viewBtns">
    
    <button class="btn-2 t-xs re-evaluate" (click)="getVideoImage()" mat-button *ngIf="isGrievance && [statuses.Pending, statuses.Approved].includes(currentFormResponseStatus) && isRescoreEnable">
        Rescoring
    </button>

    <button  class="btn-2 t-xs custom1 -ml-5" (click)="toPdf()" mat-button *ngIf="downloadReady && globalUserService.userDetails.value.roleId != 15">
        Download Application
    </button>
    <button *ngIf="formOptions.changeRequestAllowed && !isGrievance && globalUserService.userDetails.value.roleId == 15" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.ChangeRequest})" mat-button>
        Add Change Request
    </button>
    <button *ngIf="recentLinkClickEvent.column.linkMetaData.navigateToLabel =='View Accommodation Details'" class="btn-2 t-xs mr-2" (click)="qacRespose()" mat-button>
        QAC Response
    </button>
    <ng-container *ngIf="![statuses.Approved,statuses.Rejected,statuses.ChangeRequest,statuses.Completed].includes(currentFormResponseStatus)">
        <button *ngIf="formOptions.addORviewNotesAllowed" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Note})">
            Add/View Notes
        </button>
        <button *ngIf="formOptions.changeRequestAllowed && !isGrievance" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.ChangeRequest})" mat-button>
            Add Change Request
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-1 t-xs mr-2" (click)="openConfirmation(formActionTypes.Approved)" mat-button>
            Approve
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-3 t-xs mr-2" (click)="openConfirmation(formActionTypes.Rejected)" mat-button>
            Reject
        </button>
    </ng-container>
</div> 
<div *ngIf="registerForExam" class="flex flex-row absolute top-0 viewBtns">
    <button class="btn-1 t-xs" (click)="router.navigate(['scheduleExam',this.recentLinkClickEvent.element.personTenantRoleId])" *ngIf="formOptions.approveRejectAllowed" mat-button>
        Register For Exam
    </button>
</div> 

<div *ngIf="!registerForExam" class="flex flex-row absolute top-0 viewBtns">
    <ng-container *ngIf="isGrievance">
        <button  class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.GrievanceResponse})" mat-button>
            Grievance Response
        </button>
        <button  class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.NAEResponse})" mat-button>
            
            NAE Response
        </button>
        <button class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Evaluator_Result})" mat-button>
            
            Measurement Details
        </button>
        <button *ngIf="formOptions.changeRequestAllowed" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.Note})">
            Add/View Notes
        </button>
    </ng-container>
    <ng-container *ngIf="isGrievance && ![statuses.Approved,statuses.Rejected,statuses.ChangeRequest].includes(currentFormResponseStatus)">
        <button *ngIf="recentLinkClickEvent.element.statusId === 2 && this.formtypeId == 16" class="btn-2 t-xs mr-2" (click)="notesAddView.emit({whatToOpen:PersonFormNoteTypeEnum.AppealResponse})" mat-button>
            Appeal Response
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-1 t-xs mr-2" (click)="openConfirmation(formActionTypes.Approved)" mat-button>
            Approve
        </button>
        <button *ngIf="formOptions.approveRejectAllowed" class="btn-3 t-xs mr-2" (click)="openConfirmation(formActionTypes.Rejected)" mat-button>
            Reject
        </button>
    </ng-container>

</div> 


<div id="printMe" *ngIf="!formOptions.hideForm && formJSON" class="card shadow-none cardBorder mt-4" fxFlex="auto" [style.overflow-x]="'hidden!important'">
    <div  class="float-right ">
        <button class=" t-xs textsave" matTooltip="Edit"  *ngIf="takeValue ==null && takeValue ==undefined && globalUserService.personEventId.statusId == 2 && recentLinkClickEvent.column.linkMetaData.navigateToLabel == 'View Grievance Details'" mat-button (click)="changeForm()">
            <mat-icon>edit</mat-icon>
        </button>
    </div> 

    <div class="float-right">
        <div class="ml-auto" *ngIf="takeValue !=null && takeValue !=undefined" >
            <button class="text-xs lh textcancel" mat-button type="button" (click)="cancelForm()" matTooltip=
            "Cancel" ><mat-icon>cancel</mat-icon></button>
            <button class=" text-xs  textsave"  mat-button type="button"  (click)="submit(true, true)" matTooltip=
            "Update form changes"><mat-icon>save</mat-icon></button>
        </div>
    </div>
    <ng-container *ngIf=" formJSON && !userResponse">
        <exai-form-builder [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [submitButtonRef]="submitButton" [disabled]="formOptions.formDisabled" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4" [openAll]="false">
        </exai-form-builder>

    </ng-container>
    <ng-container *ngIf=" formJSON && userResponse && takeValue ==null && takeValue ==undefined">
        <exai-form-builder [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse" [submitButtonRef]="submitButton" [disabled]="getDisable(formOptions.formDisabled)" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4"
            [openAll]="true" (initalSectionValidationEvent)="downloadReady = true">
        </exai-form-builder>

        <div>
            <div class="form-cards" id='gallery-1'>
                <mat-accordion *ngIf=" formJSON && userResponse && takeValue ==null && formtypeId == 3 && globalUserService.userDetails.value .roleId != 15 && ![15,20,22463,22613].includes(recentLinkClickEvent.element.stateId) && ![117,118,63,64,22331].includes(recentLinkClickEvent.element.eligibilityRouteId)">
                    <!-- <mat-expansion-panel class="cardBorder application-expansion mb-2" [expanded]="true"> -->
                    <mat-expansion-panel class="cardBorder  mb-2" [expanded]="true">
                        <mat-expansion-panel-header class="matheader">
                            <mat-panel-title class="text-xs title ">
                                Testing Preference
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <h6 class="mat-text-header">
                            1. Are you planning to take your Nurse Aide Skills Exam at an In-facility or Regional
                            Testing Center?</h6>
                            <mat-radio-group [formControl]="radioselect" [disabled]="NonEditData">
                                <ng-container *ngFor="let examType of examTypeModels; let i = index">
                                  <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
                                    (change)="getSelectedRoute(examType.id)">{{ examType.name }}
                                  </mat-radio-button>
                                </ng-container>
                              </mat-radio-group>
                        <form [formGroup]="INFValue">
                            <ng-container *ngIf="INFSelected" >
                                <h6 class="mat-text-header">
                                    2. What In-facility are you interested in testing at? Kindly include their INF code below if you know it:</h6>
                                <mat-form-field class="mt-2 ml-2" appearance="outline">
        
        
                                    <span class="INFTEXT mb-1" id="basic-addon3">INF</span>
                                    <input class="form-control INF "formControlName="infcode" matInput autocomplete="off" maxlength="5" id='testId' [disabled]="NonEditData" (input)="onKeydownMain($event.target.value)" (keypress)="onlyNumberKey($event)">
        
                                </mat-form-field>
        
                               
                            </ng-container>
                        </form>
              
                        <ng-container  *ngIf="INFSelected">
                            <h6 class="-mt-2 mat-text-header">
                                3. I acknowledge that I am not required to test at this location.</h6>
                             

                                <mat-radio-group [formControl]="radioselectAcceptance" [disabled]="NonEditData">
                                    <ng-container *ngFor="let examType of Acceptance; let i = index">
                                      <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"  (change)="getSelectedAccept(examType.id)"
                                        >{{ examType.name }}
                                      </mat-radio-button>
                                    </ng-container>
                                  </mat-radio-group>
                        </ng-container>
                    </mat-expansion-panel>
                    <!-- <mat-expansion-panel class="cardBorder application-expansion1"> -->
                    <!-- <mat-expansion-panel class="cardBorder application-expansion1" *ngIf="AccomodationPerformLogs && AccomodationPerformLogs.length">
                        <mat-expansion-panel-header>
                            <mat-panel-title class="text-xs font-bold">
                                {{this.lngSrvc.curLangObj.value.accommodationProcess}}
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <p class="expand">
                            <exai-progress-bar [performlogs]="AccomodationPerformLogs"></exai-progress-bar>
                        </p>
                    </mat-expansion-panel> -->
                </mat-accordion>
            </div>
        </div>
    </ng-container>
    <ng-container *ngIf=" formJSON && userResponse && takeValue">
        <exai-form-builder #formBuilder id="formBuilder" [existingForm]="formJSON" [showToolbar]="false" [showOnlyForm]="true" [existingUserData]="userResponse" [submitButtonRef]="submitButton" [disabled]="getDisable(formOptions.formDisabled)" [element]="element" [showStatic]="formtypeId == 2 || formtypeId == 4"
        (formSubmitEvent)="saveResponse($event)"   [openAll]="true" (initalSectionValidationEvent)="downloadReady = true" >
        </exai-form-builder>
    </ng-container>
   
</div>




<button [style.display]="'none'" #hiddenUnnecessaryButton></button> 