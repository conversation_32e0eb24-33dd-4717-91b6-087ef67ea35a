import { GlobalUserService } from 'src/app/core/global-user.service';
import { Component, OnInit, SimpleChanges } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import data from '@iconify/icons-ic/twotone-more-horiz';
import { Store } from '@ngrx/store';
import { crumb } from 'src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types';
import { linkClickEvent } from 'src/app/core/common-component/table/dynamic-table-types';
import { moduleTypes } from '../dynamic-component-loader/dynamic-component-loader.types';
import { PopFromBreadCrumbUptoIndex, PushIntoBreadCrumb, setRecentLinkClickEvent } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.actions';
import { breadCrumbsSelector, recentLinkClickEventSelector } from '../dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { getcertificatelink, getDisNotesbyId, getFormsFilled, imageuploadinput, revokeRegistration, savedDisNotes, saveDisNotes, updateNotes } from './state/cert.action';
import { certLink, formsFilled, getsavedNotesId, getupdatedNotesId, RevokedRegistration, savedNotesId, uploadedFiles } from './state/cert.selector';
import { AbuseFileDetailsModel, AbuseItemModel, certificationData, dispilinary, RegistryStatuses, revoke,  } from './state/cert.types';
import * as FS from 'file-saver';
import { ConfirmationPopupComponent } from 'src/app/core/common-component/confirmation-popup/confirmation-popup.component';
import { LanguageService } from 'src/app/core/language.service';
import { SnackbarService } from 'src/app/core/snackbar.service';
import { takeUntil } from 'rxjs/operators';
import { forkJoin, Observable, Observer, Subject } from 'rxjs';
import { FormTypes } from '../application/application.types';
import domtoimage from 'dom-to-image';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

import { HttpService } from 'src/app/core/http.service';
import { DynamicFileUploadModel, DynamicFormHook, DynamicFormModel, DynamicFormService } from '@ng-dynamic-forms/core';
import { Note } from 'src/app/core/common-component/notes-sidebar/notes-sidebar.component';
import { FormBuilderService } from 'src/app/core/common-component/examroom-formbuilder/form-builder.service';

@Component({
  selector: 'app-candidate-certifacte-view',
  templateUrl: './candidate-certifacte-view.component.html',
  styleUrls: ['./candidate-certifacte-view.component.scss']
})
export class CandidateCertifacteViewComponent implements OnInit {
  public certificateDetails: any;
  private unsubscribe:Subject<any> = new Subject();
  updatedFiles=[]
  ReinstateValue:string
  formActionTypes=formActionTypes;
  certUrl: any;
  persontanantRoleId:number;
  events: string[] = [];
  isSelected = true;
  disableTitle = false;
  addNewNote: FormGroup;
  opened: boolean;
  noteData: certificationData;
  notetypeId: dispilinary;
  notetype: number;
  showAbbuse:boolean=false;
  updatedData: certificationData;
  uploaddetails:any=null;
  fileDoc:any=[];
  files:any=[]
  notedescription: Array<any> = [];
  abuseAlligations:Array<any>=[];
  viewNotes = true;
  open: boolean = false;
  uploadFile: boolean = false;
  form = new FormData();
  heading: string;
  roleId: number;
  stateId:number;
  existingId: number = null;
  abusefileDetails:AbuseFileDetailsModel
  breadCrumbsArray: crumb[] = [];
  userDetails: any;
  revokeCertRegistration: revoke;
  nonviewable:string
  isInActive:boolean = true;
  recentLinkClickEvent: linkClickEvent;
  certDetails:any;
  numberofProblemReportAssociated:number=null
  formGroupUpload
  formGroupModel
  
  notes:Note[];
  
  
 
  constructor(public store: Store, public dialog: MatDialog,
    private global: GlobalUserService,
    public lngSrvc: LanguageService,
    private snackbar: SnackbarService,
    private router: Router, 
    private http:HttpClient,
    private httpService:HttpService,
    private dynamicFormService: DynamicFormService,private services:FormBuilderService) { }

   
    
    // fileUploadModel: DynamicFormModel = [
    //   new DynamicFileUploadModel({
    //   required: false,
    //   id: 'attachments',
    //   name: 'attachments',
    //   label: 'Supporting Documents',
    //   accept: ['.pdf', '.doc', '.docx'],
    //   multiple: true,
    //   updateOn: DynamicFormHook.Change,
    //   showFileList: true,
    //   additional: {
    //     appearance: 'outline'
    //   }
    //   })
    // ]
    fileUploadModel: DynamicFormModel=[];
    fileUploadFormGroup: FormGroup[] =[];
    dataUpload = new DynamicFileUploadModel({
      required: false,
      id:'1',
      name:'2',
      label: 'Upload Supporting Documents',
      accept: ['.pdf', '.doc', '.docx','.jpg','.png','.jpeg'],
      multiple: true,
      updateOn: DynamicFormHook.Change,
      showFileList:true,
      additional: {
        appearance: 'outline'
      }
    })
  ngOnInit(): void {
   
 this.formGroupModel = [this.dataUpload] 

    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel)

    this.store.select(recentLinkClickEventSelector).subscribe(data=>{
      this.recentLinkClickEvent = data
      setTimeout(()=>{
        this.files =[]
        this.uploaddetails =null
      },2400)
      if(data !=null){
        data.element.PersonTenantRoleId !=undefined && data.element.PersonTenantRoleId !='' && data.element.PersonTenantRoleId!=null?  this.httpService.getshowAbbusebuttonDetails(data.element.PersonTenantRoleId).subscribe(data=>{
          if(data){
            this.showAbbuse= data==true?true:false;
          }
         
      }) :null
      }else{
        this.router.navigateByUrl('/loader/manage-registry')
      }
    })
    this.formGroupModel = [this.dataUpload]
    this.formGroupUpload = this.dynamicFormService.createFormGroup(this.formGroupModel)
   
      
   

    this.store.select(recentLinkClickEventSelector).subscribe(data=>{
      setTimeout(()=>{
        this.files =[]
        this.uploaddetails =null
      },2400)
      if(data.element.RegistryStatusId==6){
        this.isInActive=false;
      }
    })
    this.addNewNote = new FormGroup({
      title: new FormControl("", [Validators.required]),
      body: new FormControl("", [Validators.required]),
      fileName:new FormControl(" ", [Validators.required]),
    });
    this.global.userDetails.subscribe(data => {
      if (data != null) {
        
        this.userDetails = data;
        this.roleId = data.roleId;
      }
    })
    try {

      this.store.select(breadCrumbsSelector).pipe(takeUntil(this.unsubscribe)).subscribe((breadCrumbs: Array<crumb>) => this.breadCrumbsArray = breadCrumbs)
      //this.store.select(recentLinkClickEventSelector).pipe(takeUntil(this.unsubscribe)).subscribe((event: linkClickEvent) => {  this.recentLinkClickEvent = event})
      this.nonviewable=this.breadCrumbsArray[2].navigationMetaData.moduleID;
      this.store.select(recentLinkClickEventSelector).subscribe((data: any) => {
        setTimeout(()=>{
          this.files =[]
          this.uploaddetails =null
        },2400)
  
        if (data.element.Id) {
          this.certificateDetails = data;
          this.ReinstateValue = this.certificateDetails.element.RegistryStatusId == RegistryStatuses.Revoked?" Reinstate Revoked":this.certificateDetails.element.RegistryStatusId == RegistryStatuses.SummarySuspension?" Reinstate Summary Suspension":this.certificateDetails.element.RegistryStatusId == RegistryStatuses.Suspension?" Reinstate Suspension":null
          this.store.dispatch(getcertificatelink({ id: data.element.Id }))
        }
      })
      this.store.dispatch(PushIntoBreadCrumb({
        crumb: {
          label: 'View Registration Details',
          navigationMetaData: {
            moduleID: 'certificate',
            linkClickEvent: null,
            navigateToType: moduleTypes.Independent,
          }
        },

      }));
      this.store.select(certLink).subscribe(data => {
        if (data) {
          this.certUrl = data;
          this.transformUrlsToDataUris(data);
        }
      })
    }
    catch (e) {
      this.router.navigateByUrl['/']

    }
    this.getnote();
    setTimeout(()=>{
      this.store.select(uploadedFiles).subscribe((data:any)=>{ 
        if (data && data.element && data.element.Id){
          this.uploaddetails=data 
        }
        else if(data !=null ){
         
           
          this.uploaddetails=data
          this.files.push(this.uploaddetails)
          this.updatedFiles = this.files.reduce((unique, o) => {
            if(!unique.some(obj => obj.fileName === o.fileName )) {
              unique.push(o);
            }
            return unique;
        },[]);
       
        
        
        //  let  uniqueArray = Array.from(uniqueSet).map(JSON.parse);
    
      } 
       
        // }
      })
    },2000)
   

    this.http.get(`${environment.baseUrl}client/api/form/GetProblemReports?formTypeId=11&pageNumber=1&pageSize=1000&searchText=${this.certificateDetails.element.PersonId}&searchColumn=CandidateId`).subscribe((data:any)=>{
      if(data.result.length>0){
        this.numberofProblemReportAssociated=data.result.length;
      }
      else{
        this.numberofProblemReportAssociated=0;
      }
    });
  }


 
  toPdf(i:any) {
    const dashboard = document.getElementById(`fullDiv1${i}`);
    const dashboardHeight = dashboard.clientHeight;
    const dashboardWidth = dashboard.clientWidth;
    const options = { background: 'white', width: dashboardWidth, height: dashboardHeight, allowTaint : true,
    useCORS: true
     };

    domtoimage.toPng(dashboard, options).then((imgData) => {
         const doc = new jsPDF(dashboardWidth > dashboardHeight ? 'l' : 'p', 'pc', [dashboardWidth, dashboardHeight],false);
         doc.internal.scaleFactor = 1;
         const imgProps = doc.getImageProperties(imgData);
         const pdfWidth = doc.internal.pageSize.getWidth()*0.3;
         const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

         doc.addImage(imgData, 'jpeg', 0, 0, pdfWidth, pdfHeight,"NONE",);
         doc.save('Notes Summary.pdf');
    });
}
  getnote() {
 
    if(this.notetype!=undefined){
     
      // this.store.dispatch(getDisNotesbyId({ noteTypeId: this.notetype, id: this.certificateDetails.element.Id }))
      // this.store.select(getsavedNotesId).subscribe(details => {
      //   this.notedescription = [];
      //   this.abuseAlligations= [];
      //   if (details) {
      //     details.forEach(element => {
      //       this.notedescription.push(element)
      //      
      //     })
      //   }
        
      // })
    


    this.store.dispatch(getDisNotesbyId({ noteTypeId: this.notetype, id: this.certificateDetails.element.Id }))
     this.store.select(getsavedNotesId).subscribe((data: Note[]) => {
      
    
    
    

      this.notes = data;
      //this.notes = data
      this.files=[]
          this.fileUploadModel = [];
          this.fileUploadFormGroup = [];
          if(data){
            this.notes.forEach(note=>{
              this.fileUploadModel .push (
                new DynamicFileUploadModel({
                  required: false,
                  id: 'attachments',
                  name: 'attachments',
                  label: 'Supporting Documents',
                  disabled: true,
                  value: (Object.values(note.files)as any).map((x: any) => {
                    if (x.name && x.filePath)
                      return x.name + '|' + x.filePath
                    return ''
                  }),
                  accept: ['.pdf', '.doc', '.docx','.jpeg'],
                  multiple: true,
                  updateOn: DynamicFormHook.Change,
                  showFileList: true,
                  additional: {
                    appearance: 'outline'
                  }
                })
              )
              this.fileUploadFormGroup.push(this.dynamicFormService.createFormGroup(this.fileUploadModel)) ;  
            })
          }
          

     })

    
     }
  
    }



  problemreport(){
    this.global.problemReportCandidateId=this.certificateDetails.element.PersonId
this.http.get(`${environment.baseUrl}client/api/form/GetProblemReports?formTypeId=11&candidateId=${this.certificateDetails.element.PersonId}&pageNumber=1&pageSize=1000`).subscribe((data:any)=>{
  if(data.result.length>0){
    this.numberofProblemReportAssociated=data.result.length;
    this.router.navigateByUrl(`loader/problem-report`)
  }
  else{
    this.numberofProblemReportAssociated=0;
    this.router.navigateByUrl(`application/11/0/0/4/0/${this.certificateDetails.element.Id}`)
  }
})

  }
  switch() {
    if(this.notetype){
      this.isSelected = !this.isSelected;
      this.disableTitle = !this.disableTitle
      this.viewNotes = !this.viewNotes;
      this.existingId = null;
      this.formGroupModel[0].value=[];
      this.addNewNote.reset();
      
    }
    // else{
    //      this.router.navigate([
    //   "application",
    //   9,
    //   this.certificateDetails.element.PersonTenantRoleId,
    //   this.certificateDetails.element.EligibilityRouteId,
    //   this.certificateDetails.element.StateId,
    //   0,
    // ]);
    // }
  }
  handleCrumbNavigation(crumb: crumb) {
    if (!(this.breadCrumbsArray.indexOf(crumb) == this.breadCrumbsArray.length - 1)) {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom) this.router.navigate([crumb.navigationMetaData.moduleID]);
      else {
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
          this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
          this.store.dispatch(setRecentLinkClickEvent({event:this.recentLinkClickEvent}));
        }
        this.router.navigate(['loader', crumb.navigationMetaData.moduleID]);
      }
      this.store.dispatch(PopFromBreadCrumbUptoIndex({ indexToPopUpto: this.breadCrumbsArray.indexOf(crumb) + 1}));
    }
  }

  download() {
    const dashboard = document.getElementById('fullDiv');
    const options = {
    };
    window.scrollTo(0,0);
    
    html2canvas(dashboard, options).then((canvas) => {
      var img = canvas.toDataURL("image/PNG");
      var doc = new jsPDF('l', 'mm', 'a4', true);
      const bufferX = 5;
      const bufferY = 5;
      const imgProps = (<any>doc).getImageProperties(img);
      const pdfWidth = doc.internal.pageSize.getWidth() - 2 * bufferX;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      doc.addImage(img, 'PNG', bufferX, bufferY, pdfWidth, pdfHeight, undefined, 'FAST');
      return doc;
    }).then((doc) => {
      doc.save('Certificate.pdf');
    });
  }
  
  transformUrlsToDataUris(data: string) {
    let allIndexes = [];
    let allFoundUrls = [];
    while (true) {
      let nextOccurence = (allIndexes.length > 0) ? data.indexOf("src=", allIndexes[allIndexes.length - 1]) : data.indexOf("src=");
      if (nextOccurence == -1) break;
      else {
        allIndexes.push(nextOccurence + 4);
        let url = "";
        for (let i = nextOccurence + 5; data[i] != '"' && i < data.length; i++) url += data[i];
        if (url != "") allFoundUrls.push(url);
      }
    }
    let allDataUris = allFoundUrls.map((x: string) => this.getBase64ImageFromURL(x));
    forkJoin(allDataUris).subscribe((images: Array<string>) => {
      let allBlobs = images.map((x: string) => this.dataURItoBlob(x));
      forkJoin(allBlobs).subscribe((blobs: Array<Blob>) => {
        for (let i = 0; i < blobs.length; i++) {
          data = data.replace(
            allFoundUrls[i],
            window.URL.createObjectURL(new File(
              [blobs[i]], this.generateName(),
              { type: 'image/jpeg' }
            ))
          );
        }
        this.certUrl = data;
      })
    })
  }

  getBase64ImageFromURL(url: string): Observable<string> {
    return new Observable((observer: Observer<string>) => {
      // create an image object
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.src = url;
      if (!img.complete) {
        // This will call another method that will create image from url
        img.onload = () => {
          observer.next(this.getBase64Image(img));
          observer.complete();
        };
        img.onerror = err => {
          observer.error(err);
        };
      } else {
        observer.next(this.getBase64Image(img));
        observer.complete();
      }
    });
  }

  getBase64Image(img: HTMLImageElement): string {
    // We create a HTML canvas object that will create a 2d image
    let canvas: HTMLCanvasElement = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx: CanvasRenderingContext2D = canvas.getContext('2d');
    // This will draw image
    ctx.drawImage(img, 0, 0);
    // Convert the drawn image to Data URL
    const dataURL: string = canvas.toDataURL('image/png');
    return dataURL.replace(/^data:image\/(png|jpg);base64,/, '');
  }

  dataURItoBlob(dataURI: string): Observable<Blob> {
    return new Observable((observer: Observer<Blob>) => {
      const byteString: string = window.atob(dataURI);
      const arrayBuffer: ArrayBuffer = new ArrayBuffer(byteString.length);
      const int8Array: Uint8Array = new Uint8Array(arrayBuffer);
      for (let i = 0; i < byteString.length; i++) {
        int8Array[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([int8Array], { type: 'image/jpeg' });
      observer.next(blob);
      observer.complete();
    });
  }
  generateName(): string {
    const date: number = new Date().valueOf();
    let text = '';
    const possibleText =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 5; i++) {
      text += possibleText.charAt(
        Math.floor(Math.random() * possibleText.length)
      );
    }
    // Replace extension according to your media type like this
    return date + '.' + text + '.jpeg';
  }


  saveNotes() {
    
    if (this.existingId == null) {
     
      this.uploaddetails==null?
      this.noteData = {
        id: 0,
        title: "test",
        body: this.addNewNote.value.body,
        files: [],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        personEventRegistryId: this.certificateDetails.element.Id,
        userId:this.global.userDetails.value.personTenantRoleId,
      }:
      
      this.abusefileDetails = new AbuseFileDetailsModel();
      this.abusefileDetails.id =  0;
      this.abusefileDetails.title ="test",
      this.abusefileDetails.body =  this.addNewNote.value.body
      let voucherItems: AbuseItemModel[] = [];
     
      if(this.updatedFiles){
        if(this.services.remainigFiles !=null){
          for(let i=0;i<this.services.remainigFiles.length;i++){
             let a = this.updatedFiles.filter(x=>this.services.remainigFiles[i].split('|')[0].includes(x.fileName))
              for (let i = 0; i < a.length; i++) {
                if (a.length > 0) {
                  voucherItems.push(new AbuseItemModel({
                    id:0,
                    name:a[i].fileName,
                    filePath:a[i].systemFileName,
                    mime:"",
                    personFormNoteId:0,
                    deleted:false
                  })
                  );
                }
              }
          }
          // let a =this.updatedFiles.filter(x=>(this.global.remainigFiles.split(",")).includes(x.fileName))
         
        }else{
          for (let i = 0; i < this.updatedFiles.length; i++) {
            if (this.updatedFiles.length > 0) {
              voucherItems.push(new AbuseItemModel({
                id:0,
                name:this.updatedFiles[i].fileName,
                filePath:this.updatedFiles[i].systemFileName,
                mime:"",
                personFormNoteId:0,
                deleted:false
              })
              );
            }
          }
        }
       
      }
    
      this.abusefileDetails.files = voucherItems;
      this.abusefileDetails.personFormId=null;
      this.abusefileDetails.noteTypeId = this.notetype
     
      this.abusefileDetails.userId = this.global.userDetails.value.personTenantRoleId;
      this.abusefileDetails.candidateId=null;
      this.abusefileDetails.personEventRegistryId=this.certificateDetails.element.Id;
    
     
    
 
    
      this.store.dispatch(saveDisNotes({ notesData:  this.uploaddetails !=null?this.abusefileDetails:this.noteData }))
    this.switch()
      this.store.select(savedNotesId).subscribe(data => {
        if (data) {
          this.uploaddetails=null;
          this.files=[]
          this.services.remainigFiles =null
          this.store.dispatch(getDisNotesbyId({ noteTypeId: this.notetype, id: this.certificateDetails.element.Id }))
          
        }
    this.files=[]
      })
this.switch()
    }
    else if (this.notetype == 4) {
      this.updatedData = {
        id: this.existingId,
        title: "test",
        body: this.addNewNote.value.body,
        files: [],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        personEventRegistryId: this.certificateDetails.element.Id,
        userId:this.global.userDetails.value.personTenantRoleId,
      }
      this.store.dispatch(updateNotes({ UpdatednotesData: this.updatedData }))
      this.store.select(getupdatedNotesId).subscribe(data => {
        this.uploaddetails=null
        this.store.dispatch(getDisNotesbyId({ noteTypeId: this.notetype, id: this.certificateDetails.element.Id }))
      })
      this.switch();
    }
    else if (this.notetype == 6) {
      if(this.uploaddetails!=null && this.fileDoc.length==0){
     
      this.updatedData = {
        id: this.existingId,
        title: "test",
        body: this.addNewNote.value.body,
        files: [
          {
            id: 0,
            name: this.uploaddetails.fileName,
            filePath: this.uploaddetails.systemFileName,
            mime: "",
            personFormNoteId: 0,
            deleted: false
          }
        ],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        personEventRegistryId: this.certificateDetails.element.Id,
        userId:this.global.userDetails.value.personTenantRoleId,
      }
    }
    else if(this.uploaddetails!=null && this.fileDoc.length!=0){
      
      this.updatedData = {
        id: this.existingId,
        title: "test",
        body: this.addNewNote.value.body,
        files: [
          {
            id: this.existingId,
            name: this.uploaddetails.fileName,
            filePath: this.uploaddetails.systemFileName,
            mime: "",
            personFormNoteId: 0,
            deleted: false
          }
        ],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        personEventRegistryId: this.certificateDetails.element.Id,
        userId:this.global.userDetails.value.personTenantRoleId,
      }

    }
    else if(this.fileDoc.length!=0){
      this.updatedData = {
        id: this.existingId,
        title: "test",
        body: this.addNewNote.value.body,
        files: [
          {
            id: this.existingId,
            name: this.fileDoc[0].name,
            filePath: this.fileDoc[0].filePath,
            mime: "",
            personFormNoteId: 0,
            deleted: false
          }
        ],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        personEventRegistryId: this.certificateDetails.element.Id,
        userId:this.global.userDetails.value.personTenantRoleId,
      }
    }
    else{
      this.updatedData = {
        id: this.existingId,
        title: "test",
        body: this.addNewNote.value.body,
        files: [],
        personFormId: null,
        noteTypeId: this.notetype,
        candidateId: null,
        userId:this.global.userDetails.value.personTenantRoleId,
        personEventRegistryId: this.certificateDetails.element.Id
      }
    }
      this.store.dispatch(updateNotes({ UpdatednotesData: this.updatedData }))
      
      this.store.select(getupdatedNotesId).subscribe(data => {
       
        this.uploaddetails=null;
        this.store.dispatch(getDisNotesbyId({ noteTypeId: this.notetype, id: this.certificateDetails.element.Id }))
        this.switch()
      })

    }
    this.isSelected = !this.isSelected;
    this.disableTitle = !this.disableTitle;
    this.viewNotes = !this.viewNotes;
   
  }

  edit(note) {
    var fileDetails=[];
    this.fileDoc=[];
    note.files.forEach(file =>{
     fileDetails.push(file.name)
     this.fileDoc.push(file);
      })
    this.existingId = note.id;
    this.addNewNote.patchValue({
      title: note.title,
      body: note.body,
      fileName:fileDetails
    });
    this.isSelected = !this.isSelected;
    this.disableTitle = !this.disableTitle;
    this.viewNotes = !this.viewNotes;
  }


  revoke(status: formActionTypes,statusValue?:string) {
 
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      type: formActionTypes[status],
      message:  confirmationMessage[statusValue],
      title: this.recentLinkClickEvent
    };
    const dialogRef = this.dialog.open(ConfirmationPopupComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((confirmed) => {
        if (confirmed.confirmed) {
          this.revokeCertRegistration = {
            // registryId: this.certificateDetails.element.Id,
            // statusId: 3,
            // certificateTypeId: this.certificateDetails.element.CertType,
            // actionBy: this.userDetails.personTenantRoleId,
            // comment: confirmed.message
            id: this.certificateDetails.element.Id,
            statusId:statusValue == formActionTypes.Approved ?RegistryStatuses.Revoked:statusValue == formActionTypes.Suspension?RegistryStatuses.Suspension: statusValue ==formActionTypes.Summary?RegistryStatuses.SummarySuspension:statusValue == formActionTypes.Reinstate?RegistryStatuses.Active:null,
            actionBy: this.userDetails.personTenantRoleId,
          }
          this.store.dispatch(revokeRegistration({ revoke: this.revokeCertRegistration }))
          this.store.select(RevokedRegistration).subscribe(data => {
            if (data !=null) {
            statusValue == formActionTypes.Approved ?this.snackbar.callSnackbaronSuccess("Revoked Sucessfully"):statusValue == formActionTypes.Suspension?this.snackbar.callSnackbaronSuccess("Suspended Sucessfully"): statusValue ==formActionTypes.Summary?this.snackbar.callSnackbaronSuccess(" Summary Suspended Sucessfully"):statusValue == formActionTypes.Reinstate?this.snackbar.callSnackbaronSuccess("Reinstate Sucessfully"):null
              this.router.navigateByUrl('/loader/manage-registry');
            }
          })
         
        }
      });

  }
  opensidenav(input) {
  
    if (input == 1) {
      this.heading = "Add/View Disciplinary";
      this.uploadFile = false;
      this.notetype = 4;
      this.getnote();
    }
    else {
      this.heading = "Report Abuse";
      this.uploadFile = true;
      this.notetype = 6;
      this.getnote();
    }

    this.open = true;
  }
  getAllAbuseAlligations() {
    this.store.dispatch(getFormsFilled({ personTenantRoleId: this.certificateDetails.element.PersonTenantRoleId,formTypeId:9}))

    this.store.select(formsFilled).subscribe(details => {
      this.abuseAlligations= [];
      this.notedescription = [];
      if (details) {
        details.forEach(element => {
          this.abuseAlligations.push(element)
        })
      }
    })
  }
  viewSubmittedAlligations(personFormId:number){
    this.router.navigate([
      "application",
      FormTypes.AbuseAlligation,
      this.certificateDetails.element.PersonTenantRoleId,
      this.certificateDetails.element.EligibilityRouteId,
      this.certificateDetails.element.StateId,
      0,
      personFormId, 
      0
    ]);
  }
  uploaddoc(event) {
   
    this.form=new FormData();
    const files = event.$event.target.files as Array<File>;
   
    this.form.append('File', files[0], files[0].name);

    this.store.dispatch(imageuploadinput({details:this.form}))
  }

  closesidenav() {
    this.open = !this.open
  }

  breadCrumb(event){
  }
}


export enum formActionTypes {
  Approved = 'Approved',
  Summary = 'Summary',
  Suspension = 'Suspension',
  Reinstate = 'Reinstate',
}

export interface confirmationPopupData {
  type: formActionTypes,
  message: string,
}

export enum confirmationMessage {
  Approved = 'Do You want to Revoke Registration?',
  Summary ="Do You want to  add Summary Suspension?",
  Suspension ="Do You want to  add  Suspension?",
  Reinstate ="Do You want to  add  Reinstate Suspension?"
}
