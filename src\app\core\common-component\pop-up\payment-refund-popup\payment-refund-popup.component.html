<div class="flex justify-end cursor-pointer" fxLayout="row">
  <mat-icon class="text-sm flex justify-end" mat-dialog-close (click)="close()">close</mat-icon>
</div>
<div class="bg-pattern bottomSpace" *ngIf="( data?.id != 4 && data?.id != 6 && data?.id != 7 && data?.id != 80 && data?.id != 3 )"  >
    <div class="w-full login" fxLayout="column" fxLayoutAlign="center center">
      <div class=" overflow-hidden w-full max-w-md">
        <div class="">
        <div class="text-center font-medium mb-3" *ngIf="data?.message !='RefundPaymentDownlaod'"  >{{TextDetails}}</div>
        <div class=" font-medium mb-1 ml-2" *ngIf="data?.message =='RefundPaymentDownlaod'"  >{{TextDetails}}</div>
          <div class="px-3" fxLayout="column" fxLayoutAlign="" *ngIf="data?.message !='RefundPaymentDownlaod'">
            <form [formGroup]="myForm" >
              <div fxLayout="row" class="flex justify-between mb-1">
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>User Name</mat-label>
                  <input type="text" formControlName="CandidateName" matInput>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full ml-2">
                  <mat-label> {{Id}}</mat-label>
                  <input type="text" formControlName="TransactionId" matInput>
                </mat-form-field>
              </div>
              <div fxLayout="row" class="flex justify-between mb-1 -mt-3">
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Paid Amount</mat-label>
                  <input type="text" formControlName="paidAmount" matInput>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full ml-2">
                  <mat-label>{{Date}}</mat-label>
                  <input type="text" formControlName="TransactionDate" matInput>
                </mat-form-field>
              </div>
              <div fxLayout="row" class="flex justify-between mb-1 -mt-3">
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Email Id</mat-label>
                  <input type="text" formControlName="Email" matInput>
                </mat-form-field>
                <mat-form-field appearance="outline" class="w-full ml-2">
                  <mat-label>Refund Amount</mat-label>
                  <input type="text" formControlName="refundAmount" matInput>
                </mat-form-field>
              </div>
              <div fxLayout="column" class="w-full flex justify-between mb-3 -mt-3">
                <mat-form-field appearance="outline" class="w-full ">
                  <textarea matInput placeholder="Description" formControlName="description">
                    </textarea>      
                </mat-form-field>
              </div>
              <div fxLayout="column" class="w-full flex justify-between mb-3 -mt-3">
                <mat-checkbox color="primary"  formControlName="myCheckbox">
                  Email transaction receipt to customer (if email provided)
                </mat-checkbox>
              </div>
              <div>
              </div>
            </form>
            <div fxLayout="row" class="w-full flex justify-between mb-2"  fxLayoutAlign="center center" >
              <button mat-dialog-close class="btn-3" mat-button (click)="closeApp()"> Cancel</button>
              <button  class="btn-1 ml-2" mat-button color=primary [disabled]="!myForm.valid" (click)="refund()"> Ok</button>
          </div>
          </div>
          <div class="px-2" fxLayout="column" fxLayoutAlign="" *ngIf="data?.message =='RefundPaymentDownlaod'">
            <div fxLayout="row" class="justify-between -ml-4">
              <mat-radio-group [formControl]="radioselect">
                <ng-container *ngFor="let examType of DownloadOptions; let i = index">
                    <mat-radio-button class="-ml-1 px-5 mb-3 pt-1 t-xs" [checked]="examType.checked"  [value]="examType.name" (change)="options(examType.id)" >{{ examType.name }}
                    </mat-radio-button>
                </ng-container>
  
            </mat-radio-group>
            </div>
            <form [formGroup]="myForm" *ngIf="DeatilsPayment" >
              <div fxLayout="column" class="w-full flex justify-between">
                 <mat-form-field class="mt-2" appearance="outline">
                  <mat-label class="text-xs fontColor2">Select Your Exam Date Range</mat-label>
                  <mat-date-range-input [formGroup]="range" [rangePicker]="picker" >
                    <input class="text-xs" matStartDate formControlName="start" autocomplete="off" required
                      placeholder="Start date">
                    <input class="text-xs" matEndDate formControlName="end" autocomplete="off" required
                      placeholder="End date">
                  </mat-date-range-input>
                  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                  <mat-date-range-picker #picker></mat-date-range-picker>
                </mat-form-field>
              </div>
         
              <div>
              </div>
            </form>
            <div fxLayout="row" class="w-full  justify-between mb-1 -mt-5 -ml-4" *ngIf="DeatilsPayment">
              <mat-checkbox class=" px-5 mb-3 pt-1 t-xs" color="primary" *ngFor="let n of examSubmitOptions" 
              [checked]="n.checked" (change)='showOptions($event,n)' [disabled]="n.disabled">
              {{n.name}}
          </mat-checkbox>
            </div>
            <div fxLayout="row" class="w-full flex justify-between mb-2"  fxLayoutAlign="center center" *ngIf="data?.message !='RefundPaymentDownlaod'" >
              <button mat-dialog-close class="btn-3" mat-button (click)="closeApp()" > Cancel</button>
              <button  class="btn-1 ml-2" mat-button color=primary [disabled]="!myForm.valid" (click)="refund()"> Ok</button>
          </div>
          <div fxLayout="row" class="w-full flex justify-between mb-2"  fxLayoutAlign="start start" >
            <button  *ngIf="data?.message =='RefundPaymentDownlaod'"
            class="btn-1 t-xs mr-2 flex items-center justify-items-center flex-row" color="primary" fxFlex="none"
            type="button" (click)="DownLoadRefundorPayment()">
            <mat-icon class="text-lg mt-1" >download</mat-icon>
              Download
        </button>
        </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <div class="p-4"*ngIf="( data?.id == 4 || data?.id == 6 || data?.id == 7 || data?.id == 80 ) && (data?.id != 3 && data?.id !=1) " >
    <div class="custom-class">
        <div class="">
            <div class="flex pb-2">
                <div class="text-sm font-semibold">{{TextDetails}}</div>
            </div>
            <hr>
        </div>
        <form [formGroup]="datevalue">
          <mat-form-field  appearance="outline" class="" *ngIf="data.id == 4">
            <mat-label class="text-xs fontColor2">Elapsed Date</mat-label>
            <input matInput  class="text-xs" formControlName="variable" [matDatepicker]="picker" [min]="minDate"  />
            <mat-datepicker-toggle matSuffix [for]="picker" >
            </mat-datepicker-toggle>
            <mat-datepicker #picker ></mat-datepicker>
        </mat-form-field>

     
        </form>
  
      

        <div class=" p-2 " fxLayout="column">
            <form [formGroup]="addNewNote">
                <!-- <mat-form-field *ngIf=""  class=" eligibility-list" appearance="outline" autocomplete="off">
                    <mat-label>Enter</mat-label>
                    <input matInput placeholder="title" formControlName="title">
                </mat-form-field> -->

                <mat-form-field class="pt-2 pb-1 w-full" appearance="outline" *ngIf="data.id == 6" >
                  <mat-label class="text-xs  fontColor2">Issue</mat-label>
                  <mat-select  placeholder="Issue" formControlName="Incident"
                  class=" border-0 outline-none w-full bg-transparent cardBorder" fxFlex.lt-md="auto">
                  <mat-option *ngFor="let type of Incidient " (click)="IncidentType(type)" [value]=type>
                      {{type.incidentName}}
                  </mat-option>
              </mat-select>
              </mat-form-field>

                <mat-form-field appearance="outline" class="block">
                    <!-- <mat-label>Description</mat-label> -->
                    <textarea *ngIf="data.id != 4 && data.id !=80" class="editor flex-wrap" matInput formControlName="body" placeholder="Description"
                        cols="80" rows="10"></textarea>
                        <textarea *ngIf="data.id == 4" class="editor flex-wrap" matInput formControlName="body" placeholder="Notes"
                        cols="80" rows="10"></textarea>
                        <textarea *ngIf="data.id == 80" class="editor flex-wrap" matInput formControlName="body" placeholder="Description"
                        cols="80" rows="10"></textarea>
                    <!-- <input matInput [style.display]="'none'">
                    <ngx-editor-menu class="editor flex-wrap" [editor]="editor" [toolbar]="toolbar">
                    </ngx-editor-menu>
                    <ngx-editor class="editor" [editor]="editor" formControlName="body">
                    </ngx-editor> -->
                </mat-form-field>
            </form>
        </div>
    </div>
    <div class="flex justify-end switch-group">
      <button *ngIf="data.id == 6 || data.id == 80 || data.id == 12"  mat-button class="btn-3 t-xs mr-2" mat-dialog-close (click)="close()">
        Cancel
    </button>
        <button *ngIf="data.id == 6"  mat-button class="btn-1 t-xs" [disabled]="!addNewNote.valid" (click)="SubmitBookletAssign()">
          Save
        </button>
        <button  *ngIf="data.id != 6" mat-button class="btn-1 t-xs"  (click)="SubmitBookletAssign()">
          Save
        </button>
    </div>
</div>

<div class="bg-pattern bottomSpace" *ngIf="data?.id == 3 && matchBarcode">
  <div class="w-full login" fxLayout="column" fxLayoutAlign="center center">
    <div class=" overflow-hidden w-full max-w-md">
      <div class="">

     
        <!-- <div class="" fxLayout="column">
          <div class="flex justify-end cursor-pointer" fxLayout="row" mat-dialog-close>
              <mat-icon class="text-sm flex justify-end" (click)="closeBarcode()">close</mat-icon>
          </div>
      </div> -->

      <div class="text-center font-medium mb-6 mt-4" >{{TextDetails}}</div>
        <div class="px-3" fxLayout="column" fxLayoutAlign="" >
          <div class="video mb-3">
            <video #barcodeScannerVideo id="barcode-scanner" style="border-radius: 8px;" width="100%" height="50%"></video>
            <canvas #snapshotCanvas id="snapshot" style="display:none;"></canvas> 
          </div>
          <form [formGroup]="scanner" >
       
            <div fxLayout="column" class="w-full flex justify-between ">
              <!-- <mat-form-field appearance="outline" class="w-full ">
                <textarea matInput placeholder="Bar code" formControlName="scanner_code">
                  </textarea>
                        
              </mat-form-field> -->
              <mat-form-field appearance="outline" > 
                <mat-label>Bar Code</mat-label>
                <textarea matInput placeholder="Bar code" formControlName="scanner_code" matInput
                          cdkTextareaAutosize
                          #autosize="cdkTextareaAutosize"
                          (input)="onValueChange($event)"
                          cdkAutosizeMinRows="1"
                          cdkAutosizeMaxRows="5"></textarea>
                          <mat-error>
                            <div *ngIf="scanner.get('scanner_code').touched && scanner.get('scanner_code').invalid"
                                class='invalid-feedback '>
                                <div *ngIf="scanner.get('scanner_code').errors.required">Barcode is
                                    Required
                                </div>
                                <div *ngIf="scanner.get('scanner_code').hasError('customError')">
                                  invalid Booklet
                                </div>
                                <div>

                                </div>
                            </div>
                        </mat-error>
              </mat-form-field>
            </div>

           
            <div>
            </div>
          </form>

          <!-- <div class="video mb-3">
            <video #barcodeScannerVideo id="barcode-scanner" style="border-radius: 8px;" width="100%" height="50%"></video>
            <canvas #snapshotCanvas id="snapshot" style="display:none;"></canvas> 
          </div> -->
          <div fxLayout="row" class="w-full flex buttonFixes justify-between mb-2"   >
            <!-- (click)="captureSnapshot()"  *ngIf="scanner.value.scanner_code ==''" -->
            <button  class="btn-1" mat-button (click)="captureSnapshot()"   > 
              Capture Barcode
              <!-- <span  class="material-icons" style=" transform: rotate(104deg); font-size: 40px; -webkit-text-stroke-color: rgb(0, 76, 0);">camera</span> -->
            </button> &nbsp;

            <!-- <button  class="btn-1" mat-button (click)="captureSnapshot()" > 
              Start Shredding
            </button> &nbsp; -->
            <!-- <button  class="btn-1 ml-2" mat-button color=primary [disabled]="!scanner.valid" (click)="submitBookletId()" >Submit</button> -->
            <!-- <div class="" fxLayout="column"> -->
              
              <div class="flex justify-end cursor-pointer" fxLayout="row" >
                <button class="btn-1 closePopUp" mat-dialog-close mat-button (click)="close()"> Close</button>
                  <!-- <mat-icon class="close lex justify-end" style="color: #f55a4f !important;">Close Popup</mat-icon> -->
              </div>
          <!-- </div> -->
        </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="!matchBarcode && !data?.name">
  <app-video [PersonEventID]='PersonEventId'></app-video>
</div>
