import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { DynamicFormArrayModel, DynamicFormControlModel, DynamicFormGroupModel } from '@ng-dynamic-forms/core';
import { Subject } from 'rxjs';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { candidateDetails, editData, form } from './form-builder.types';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class FormBuilderService {
stateId:number
  formGroup: FormGroup[] = [];
  RemoveGetuserDataafterpopupopen:string
  stateName:string
  remainigFiles
  Details:any
  genesisForm: form = null;
  addForms:Subject<boolean> = new Subject<boolean>();
  editData: editData = new editData();
  defaultQGPcss: any;
  defaultFeildCss: any;
  selectedCandidateDetails:candidateDetails;
  attachedForm:Array<any>=[];
  cellclickedDetails:any=null;
  examDate:string;
  SearchValue:any
  editFormValue
   userDetails = new BehaviorSubject<candidateDetails>({});
   HideEmployerList = new BehaviorSubject<any>("");
   HideEmployerValues = new BehaviorSubject<any>("");
   HideEmployerValue = new BehaviorSubject<any>("");
   HideNOEmployerValue = new BehaviorSubject<any>("");
   HideYesEmployerValue = new BehaviorSubject<any>("");
  constructor(private http:HttpClient) { }

  getAllQuestionGroupIDsOfAParticualarSection(sectionIndex: number): Array<string>{
    return this.genesisForm.sections[sectionIndex].QuestionGroups.map((x:DynamicFormControlModel )=>{ return x.id});
  }
  getAllFieldIdsOfASection(sectionIndex: number) {
    var returnValue:Array<retValue> = [];
    this.genesisForm.sections[sectionIndex].QuestionGroups.forEach((x: DynamicFormGroupModel & DynamicFormArrayModel) => { 
      x.type == "ARRAY" ?
        x.groupPrototype.forEach((field: DynamicFormControlModel) => {
        returnValue.push({ label: field.label, feildId: field.id })
      })
        :
        x.group.forEach((field: DynamicFormControlModel) => {
          returnValue.push({ label: field.label, feildId: field.id })
        });
    })
    
    return returnValue;
  }

  getCandidateData(){
      this.addForms.next(true);
  }

  getAgeLimitsrestriction(StateCode:string){
    var url =`${environment.baseUrl}login/StateExamAgeLimit?clientStateCode=${StateCode}`
    return this.http.get(url)
 }

}
interface retValue {
  label: string;
  feildId: string;
}
