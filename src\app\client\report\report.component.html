<!-- <div class="pb-2" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" exaiContainer>
    <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1" fxLayout="column">
        <h5><strong>Reports</strong></h5>
    </div> -->
<!-- <div class="w-full pt-2" fxAlign="start" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1" fxLayout="row">
        <button mat-button class="btn-1 text-xs mr-2" (click)="changeView()">
           Search
        </button>
        <button mat-button class="btn-1 text-xs" (click)="changeView()">
            Pinboard
        </button> -->
<!-- </div> -->



<!-- <div fxLayout="row wrap" fxLayoutGap="10px grid"> -->
<!-- <button (click)="myFunction()" class="content-box w-32">Select a Report</button>
  <div class="hidden" id="list">
    <input type="text" [(ngModel)]="selectedUser" class= "p-2" placeholder="Search.." (input) = "filterUsers()">
        <p *ngFor="let view of duplicate_tableau_data" class="cursor-pointer p-4" (click)="viewReport(view)">{{view.viewName}}</p>
  </div>     -->
<!-- <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
        *ngFor="let view of tableau_data"> -->
<!-- <div *ngFor="let view of tableau_data" > -->
<!-- </div> -->
<!-- </div> -->
<!-- </div> -->

<!-- <mat-autocomplete class="w-full" #auto="matAutocomplete">
    <mat-option (click)="loadpinboard(view.id)" class="w-full text-xs" *ngFor="let view of duplicate_tableau_data" [value]="view.name">
      {{view.name}}
    </mat-option>
  </mat-autocomplete>

  <input type="text"
       placeholder="Select Report"
       matInput
       id="selction"
       class="custom card cardBorder shadow-none text-xs p-2 pb-2"
       [(ngModel)]="selectedUser"
       (input) = "filterUsers()"
       [matAutocomplete]="auto"> -->



<!-- <div class="report-img pt-4 w-full" id="vizContainer"></div>  -->



<!-- <div>
  <mat-accordion>
    <mat-expansion-panel *ngFor="let i  of idName; let isFirst = first; let ind = index;" class="cardBorder mb-2 detail"
      [expanded]="isFirst">
      <mat-expansion-panel-header class="report-details">
        <mat-panel-title class="text-xs font-bold">
          {{i}}
        </mat-panel-title>
      </mat-expansion-panel-header>
      <p class="color-1 w-full">&nbsp;</p>
      <p class="expand" id="embed{{ind}}">
      </p>
    </mat-expansion-panel>

  </mat-accordion>
</div> -->
<!-- <div id="search" *ngIf="searchborad" style="height: 1000px !important;"></div> -->




<!-- <input matInput  formControlName="inputData"   class= "customInput" placeholder="Would you like to create an additional report?"> -->
<div class=" -mt-3 pb-2" gdGap="16px">
  <!-- <div *ngIf="viewSearch==false && isOperationStaff" class="bg-card rounded border search cardBorder" fxFlex="500px"
    fxFlex.lt-md="500px" fxLayout="row" fxLayoutAlign="end center">
    <input matInput formControlName="inputData"
      class="px-4 py-2 border-0 outline-none w-full bg-transparent cursor-pointer" (focus)="enable()"
      placeholder="Would you like to create an additional report?">
    <mat-icon class="cursor-pointer mr-4 mb-2" (click)="enable()">search</mat-icon>
  </div> -->
  <div *ngIf="viewSearch==false" class="bg-card rounded border search cardBorder" fxFlex="500px"
    fxFlex.lt-md="500px" fxLayout="row" fxLayoutAlign="end center">
    <input matInput formControlName="inputData"
      class="px-4 py-2 border-0 outline-none w-full bg-transparent cursor-pointer" (focus)="enable1()"
      placeholder="Global Search">
    <mat-icon class="cursor-pointer mr-4 mb-2" (click)="enable1()">search</mat-icon>
  </div>

</div>

<div *ngIf="viewSearch">
  <p id="loader">Loading...</p>
  <div id="authExpiredBanner"></div>
  <div id="no-data" style="display: none">Please contact <a class="color" href="https://examroom.ai/"
      target="_blank">ExamRoom.AI</a></div>
  <div class="close">
    <mat-icon class="cursor-pointer" (click)="viewSearch=false">close</mat-icon>
  </div>
  <div id="searchembed" class="expand1">
    <div id="appembed" class="expand1"></div>
  </div>
</div>


<div *ngIf="!viewSearch">
  <div class="w-full">
    <div fxLayout="row wrap" fxLayoutGap="10px grid">
      <div class="justify-start" [fxFlex]="100 / 4 + '%'" fxFlex.xs="100%" fxFlex.sm="33%"
        *ngFor="let view of headerArray">

        <div class="card p-2 h-full shadow-none cardBorder flex justify-between cursor-pointer"
          (click)="loadpinboard(view.metadata_header.owner);selectedItems(view)" [ngClass]="
          selectId == view.metadata_header.id
            ? 'selectedView'
            : 'NonselectedView' ">
          {{view.metadata_name }} <mat-icon class="cursor-pointer ">keyboard_arrow_right</mat-icon>
        </div>
      </div>

    </div>
  </div>
  
  <div class="flex justify-around  SelectedName" *ngIf="viewSearch==false" >
       {{selectedName}}

  </div>

  <div class="flex justify-end pt-2"  *ngIf="viewSearch==false && isOperationStaff" >
    <button mat-button class="btn-1 text-xs mr-2" (click)="enable()">
      <mat-icon >add </mat-icon>&ensp;
      Create Your Report
    </button>

  </div>
</div>

<!-- <div class="addReport">
<mat-icon matTooltip="Create Report" class="cursor-pointer customadd " (click)="enableinPage()">add</mat-icon>
</div> -->
<!-- <div class="addReport1">
  <p >Create your custom report</p> 
  </div> -->

<div *ngIf="viewIsOn">
  <p id="loader">Loading...</p>
  <div id="authExpiredBanner"></div>
  <div id="no-data" style="display: none">Please contact <a class="color" href="https://examroom.ai/"
      target="_blank">ExamRoom.AI.</a></div>

  <mat-icon (click)="back()" class="cursor-pointer ">keyboard_backspace</mat-icon>
  <div id="embed" class="expand">
  </div>

</div>

<!-- <button *ngIf="viewSearch==false" class="btn-1 customButtom " (click)="enable()">Would you like to create an additional report?</button> -->
<!-- <div *ngIf="viewIsOn">
  <p id="loader"></p>
<div id="authExpiredBanner"></div>
<div id="no-data">Please contact ExamRoom.AI</div>
<div class="close">
<mat-icon class="cursor-pointer" (click)="viewSearch=false">close</mat-icon>
</div>
  <div id="searchembed" class="expand1" >
  </div>
</div> -->