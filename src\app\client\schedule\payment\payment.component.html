<div [ngClass]="{hide1:untilPaymentProcess(0)}" class="px-gutter" exaiContainer>
    <div>
        <mat-spinner class="spinner"></mat-spinner>
        <div class="loading">
            <div class="flex justify-center test-base fontcustom">
                We're processing...
            </div>
            <div class="flex justify-center text-xs font-bold">
                This process may take a few seconds, so please be patient.
            </div>
        </div>
    </div>
</div>



<div [ngClass]="{hide:untilPaymentProcess(1)}" gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap=""
    exaiContainer>
    <div class="w-full pb-4" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <div class="text-sm" fxLayout="column">
            <b>Payment</b>
            <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
                [fontSize]="'0.65rem'">
            </app-ng-dynamic-breadcrumb>
        </div>
    </div>

    <div class="py-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
        gdGap="12px" exaiContainer>
        <div class="shadow-none justify-start overflow-auto dashboard" gdColumn="1 / 7" gdColumn.lt-md="1/-1"
            gdColumn.lt-sm="1">
            <div class="shadow-none exam accord" fxFlex="auto">
                <!-- comment for authorize .net payment -->
                <!-- <mat-expansion-panel *ngIf="roleId==15 || roleId==18 " class="mb-2 cardBorder" [expanded]="step === 6"
                    (opened)="setStep(6)">
                    <mat-expansion-panel-header class="header">
                        <mat-panel-title class="text-xs">
                            Saved Cards
                        </mat-panel-title>
                        <mat-panel-description>
                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="w-full px-6" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
                        <div class="t-xs" gdColumn="5 / 8" gdColumn.lt-md="5 / 8" gdColumn.lt-sm="5 / 8">
                            Name on card
                        </div>
                        <div class="t-xs" gdColumn="8 / -1" gdColumn.lt-md="8 / -1" gdColumn.lt-sm="8 / -1">
                            Expires on
                        </div>
                    </div>
                    <mat-accordion>
                        <mat-radio-group>
                            <mat-expansion-panel class="cardBorder" *ngFor="let card of paymentMethod; let i = index"
                                [expanded]="i==step2" (afterExpand)="setStep2(i)" (closed)="setStep2(-1)"
                                hideToggle="true">
                                <mat-expansion-panel-header class="header">
                                    <div class="w-full" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr">
                                        <div gdColumn="1 / 4" gdColumn.lt-md="1 / 4" gdColumn.lt-sm="1 / 4">
                                            <mat-radio-button [id]=i [value]="card.id" [checked]="i==step2">
                                                <mat-panel-title class="text-xs">xxxx xxxx xxxx {{card.card_last_four}}
                                                    {{card.card_type}}
                                                </mat-panel-title>
                                            </mat-radio-button>
                                        </div>
                                        <div class="t-xs" gdColumn="5 / 8" gdColumn.lt-md="5 / 8"
                                            gdColumn.lt-sm="5 / 8">
                                            <b> {{card.person_name}}</b>
                                        </div>
                                        <div class="t-xs" gdColumn="8 / -1" gdColumn.lt-md="8 / -1"
                                            gdColumn.lt-sm="8 / -1">
                                            <b>{{card.card_exp_datetime | date : "MM/yyyy"}}</b>
                                        </div>
                                    </div>
                                </mat-expansion-panel-header>
                                <span class="text-xs">Enter CVV( ? ):</span> &nbsp;
                                <mat-form-field appearance='outline'  [formGroup]="cvvForm" class="phone">
                                    <mat-label class="text-xs">CVV</mat-label>
                                    <input matInput autocomplete="off" name='securitycode' id='securitycode'
                                        type="password" formControlName='cvv' placeholder='CVV'
                                        (keypress)="onlyNumberKey($event)"
                                        [ngClass]="{ 'is-invalid': form.get('CVV').touched && form.get('CVV').invalid }">
                                    <mat-error>
                                        <div *ngIf="form.get('CVV').touched && form.get('CVV').invalid"
                                            class='invalid-feedback'>
                                            <div *ngIf="form.get('CVV').errors.required">CVV is Required</div>
                                        </div>
                                    </mat-error>
                                </mat-form-field>
                                <button mat-button class="btn-1 t-xs ml-4" (click)="payment(card,'card')"[disabled]='!cvvForm.valid || disablePaybutton '>Pay</button>
                                <button mat-button class="btn-3 t-xs ml-4 mb-2"
                                (click)="deleteCard(card)">Delete</button>

                            </mat-expansion-panel>
                        </mat-radio-group>
                    </mat-accordion>
                </mat-expansion-panel> -->
                <mat-expansion-panel #mep="matExpansionPanel" class="mb-2 cardBorder" [(expanded)]="closePanel"
                    [disabled]="closeExapanel()">
                    <mat-expansion-panel-header class="header">
                        <mat-panel-title class="text-xs font-bold">
                            Payment Method
                        </mat-panel-title>
                        <mat-panel-description>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <hr class="">
                    <div class="mt-2 px-4" gdColumns="1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
                        gdGap="12px" exaiContainer>
                        <div class=" shadow-none w-full" gdColumn="1 / -1" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                            <div class="text-active-color mt-2 ml-2"><strong class="text-black">Note : </strong>A
                                non-refundable 6.2% service charge will be applied to the transaction amount when using
                                PayPal. If you would prefer not to continue using PayPal, kindly select an alternative
                                payment method.
                            </div>
                            <div class="mt-2 -ml-3 flex">
                                <ng-container *ngFor="let paymentOption of paymentOptions; let i = index">
                                    <button type="button" class="px-4 mr-3 mb-3 state paymemntoptions buttom6"
                                        mat-stroked-button color="light"
                                        [ngClass]="{ active: selectedPaymentMethod == paymentOption.name }"
                                        (click)="selectrangeactive(paymentOption)">
                                        {{ paymentOption.name }}
                                        <br />
                                    </button>
                                </ng-container>
                                <div id="payPalButton"></div>
                            </div>

                            <form *ngIf="selectedPaymentMethod === PaymentOptions.Credit" [formGroup]='form'
                                class="cart container">
                                <div>
                                </div>
                                <div fxLayout="column" class="payment eligibility-list2">
                                    <mat-form-field appearance='outline'>
                                        <mat-label class="text-xs">Enter Card Number</mat-label>
                                        <input matInput formControlName='CardNumber' name="cardnumber" id='cardnumber'
                                            (keypress)="onlyNumberKey($event)"
                                            [ngClass]="{ 'is-invalid': form.get('CardNumber').touched && form.get('CardNumber').invalid }"
                                            placeholder='CardNumber'>
                                        <mat-error>
                                            <div *ngIf="form.get('CardNumber').touched && form.get('CardNumber').invalid"
                                                class='invalid-feedback '>
                                                <div *ngIf="form.get('CardNumber').errors.required">CardNumber is
                                                    Required
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance='outline'>
                                        <mat-label class="text-xs">Enter Card Name</mat-label>
                                        <input matInput formControlName='CardName' placeholder='CardName'
                                            id='first_last_name'>
                                        <mat-error>
                                            <div *ngIf="form.get('CardName').touched && form.get('CardName').invalid"
                                                class='invalid-feedback'
                                                [ngClass]="{ 'is-invalid': form.get('CardName').touched && form.get('CardName').invalid }">
                                                <div *ngIf="form.get('CardName').errors.required">Card Name is Required
                                                </div>
                                                <div *ngIf="form.get('CardName').errors.pattern">ImProper Name
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px"
                                        fxLayoutGap.lt-sm="0">
                                        <mat-form-field appearance='outline'>
                                            <mat-label class="text-xs">YYYY-MM</mat-label>
                                            <input matInput formControlName='Year' name='expirationdate'
                                                id='expirationdate' (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': form.get('Year').touched && form.get('Year').invalid }"
                                                placeholder='YYYY-MM'>
                                            <mat-error>
                                                <div *ngIf="form.get('Year').touched && form.get('Year').invalid"
                                                    class='invalid-feedback'>
                                                    <div *ngIf="form.get('Year').errors.required">Year is Required</div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                        <mat-form-field appearance='outline' class="phone">
                                            <mat-label class="text-xs">CVV</mat-label>
                                            <input matInput autocomplete="off" name='securitycode' id='securitycode'
                                                type="password" formControlName='CVV' placeholder='CVV'
                                                (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': form.get('CVV').touched && form.get('CVV').invalid }">
                                            <mat-error>
                                                <div *ngIf="form.get('CVV').touched && form.get('CVV').invalid"
                                                    class='invalid-feedback'>
                                                    <div *ngIf="form.get('CVV').errors.required">CVV is Required</div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </form>
                            <form *ngIf="selectedPaymentMethod === PaymentOptions.ACH" [formGroup]='ACHform'
                                class="cart container">
                                <div>
                                </div>
                                <div fxLayout="column" class="payment eligibility-list2">
                                    <mat-form-field appearance='outline' class="">
                                        <mat-label class="text-xs">Enter Account Number</mat-label>
                                        <input matInput formControlName='AccountNumber' name="Accountnumber"
                                            id='Accountnumber' (keypress)="onlyNumberKey($event)"
                                            [ngClass]="{ 'is-invalid': ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid }"
                                            placeholder='AccountNumber'>
                                        <mat-error>
                                            <div *ngIf="ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid"
                                                class='invalid-feedback '>
                                                <div *ngIf="ACHform.get('AccountNumber').errors.required">Account Number
                                                    is
                                                    Required
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field class="" appearance='outline'>
                                        <mat-label class="text-xs">Enter AccountHolder Name</mat-label>
                                        <input matInput formControlName='AccountHolderName'
                                            placeholder='AccountHolderName'
                                            pattern="^[a-zA-Z]*\s[a-zA-Z]*\s?([a-zA-Z]*)?\s?([a-zA-Z]*)?"
                                            id='first_last_name'>
                                        <mat-error>
                                            <div *ngIf="ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid"
                                                class='invalid-feedback'
                                                [ngClass]="{ 'is-invalid': ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid }">
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.required">
                                                    AccountHolder
                                                    Name is Required
                                                </div>
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.pattern">improper
                                                    name
                                                </div>
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.ValidateLastName">
                                                    Name
                                                    is less
                                                    than 3 letters
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <div class="block-1" fxLayout.lt-sm="column" fxLayoutGap.lt-sm="0"
                                        fxLayoutGap="16px">
                                        <mat-form-field appearance='outline' class="form-bank">
                                            <mat-label class="text-xs">Enter Routing Number</mat-label>
                                            <input matInput formControlName='RoutingNumber' name="Routingnumber"
                                                id='Routingnumber' (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid }"
                                                placeholder='RoutingNumber'>
                                            <mat-error>
                                                <div *ngIf="ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid"
                                                    class='invalid-feedback '>
                                                    <div *ngIf="ACHform.get('RoutingNumber').errors.required">Routing
                                                        Number is
                                                        Required
                                                    </div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                        <mat-form-field appearance="outline" class="form-bank">
                                            <mat-label>Bank Type</mat-label>
                                            <mat-select formControlName="bankType">
                                                <mat-option *ngFor="let state of bankType" [value]="state">{{state}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>

                                        <mat-form-field class="" appearance='outline'>
                                            <mat-label class="text-xs">Enter Bank Name</mat-label>
                                            <input matInput formControlName='bankName' placeholder='bankName'
                                                pattern="^[a-zA-Z]*\s[a-zA-Z]*\s?([a-zA-Z]*)?\s?([a-zA-Z]*)?"
                                                id='first_last_name'>
                                            <mat-error>
                                                <div *ngIf="ACHform.get('bankName').touched && ACHform.get('bankName').invalid"
                                                    class='invalid-feedback'
                                                    [ngClass]="{ 'is-invalid': ACHform.get('bankName').touched && ACHform.get('bankName').invalid }">
                                                    <div *ngIf="ACHform.get('bankName').errors.required">Bank Name
                                                        Name is Required
                                                    </div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </form>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                                <button mat-button class="btn-7 text-xs my-2" type="button"
                                    (click)='cancel()'>Cancel</button>

                                <button *ngIf="roleId==15 || roleId==18  else elseBlock" class="btn-1 text-xs my-2"
                                    mat-button type="button" (click)='payment(selectedPaymentMethod)' [disabled]='(selectedPaymentMethod == PaymentOptions.Credit && !form.valid ) ||
                                    (selectedPaymentMethod == PaymentOptions.ACH && !ACHform.valid ) ||
                                    disablePaybutton' fxLayout="row">Pay</button>
                                <ng-template #elseBlock>
                                    <button class="btn-1 text-xs my-2" mat-button type="button"
                                        (click)='payment(selectedPaymentMethod)' [disabled]='(selectedPaymentMethod == PaymentOptions.Credit && !form.valid ) ||
                                                    (selectedPaymentMethod == PaymentOptions.ACH && !ACHform.valid ) ||
                                                    disablePaybutton' fxLayout="row">Pay

                                    </button>
                                </ng-template>

                            </div>
                        </div>
                        </div>
                </mat-expansion-panel>


                <!-- <mat-expansion-panel class="mb-2 cardBorder" [expanded]="step === 3" (opened)="setStep(3)">
                    <mat-expansion-panel-header class="header">
                        <mat-panel-title class="text-xs">
                            ACH
                        </mat-panel-title>
                        <mat-panel-description>

                        </mat-panel-description>
                    </mat-expansion-panel-header>
                    <hr class="-mr-6 -ml-6">
                    <div class="px-6 p-mt-2" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="12px"
                        exaiContainer>
                        <div class="w-full" gdColumn="1 / 1" gdColumn.lt-md="1" gdColumn.lt-sm="1">
                            <div class="h2 mb-2 pt-2 active text-xs">ACH</div>
                            <form [formGroup]='ACHform' class="cart">
                                <div>
                                </div>
                                <div fxLayout="column" class="payment w-full">
                                    <mat-form-field appearance='outline' class="">
                                        <mat-label class="text-xs">Enter Account Number</mat-label>
                                        <input matInput formControlName='AccountNumber' name="Accountnumber"
                                            id='Accountnumber' (keypress)="onlyNumberKey($event)"
                                            [ngClass]="{ 'is-invalid': ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid }"
                                            placeholder='AccountNumber'>
                                        <mat-error>
                                            <div *ngIf="ACHform.get('AccountNumber').touched && ACHform.get('AccountNumber').invalid"
                                                class='invalid-feedback '>
                                                <div *ngIf="ACHform.get('AccountNumber').errors.required">Account Number is
                                                    Required
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <mat-form-field class="" appearance='outline'>
                                        <mat-label class="text-xs">Enter AccountHolder Name</mat-label>
                                        <input matInput formControlName='AccountHolderName'
                                            placeholder='AccountHolderName'
                                            pattern="^[a-zA-Z]*\s[a-zA-Z]*\s?([a-zA-Z]*)?\s?([a-zA-Z]*)?"
                                            id='first_last_name'>
                                        <mat-error>
                                            <div *ngIf="ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid"
                                                class='invalid-feedback'
                                                [ngClass]="{ 'is-invalid': ACHform.get('AccountHolderName').touched && ACHform.get('AccountHolderName').invalid }">
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.required">AccountHolder
                                                    Name is Required
                                                </div>
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.pattern">improper name
                                                </div>
                                                <div *ngIf="ACHform.get('AccountHolderName').errors.ValidateLastName">Name
                                                    is less
                                                    than 3 letters
                                                </div>
                                            </div>
                                        </mat-error>
                                    </mat-form-field>
                                    <div class="block-1" fxLayout.lt-sm="column"
                                        fxLayoutGap.lt-sm="0" fxLayoutGap="16px">
                                        <mat-form-field appearance='outline' class="form-bank">
                                            <mat-label class="text-xs">Enter Routing Number</mat-label>
                                            <input matInput formControlName='RoutingNumber' name="Routingnumber"
                                                id='Routingnumber' (keypress)="onlyNumberKey($event)"
                                                [ngClass]="{ 'is-invalid': ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid }"
                                                placeholder='RoutingNumber'>
                                            <mat-error>
                                                <div *ngIf="ACHform.get('RoutingNumber').touched && ACHform.get('RoutingNumber').invalid"
                                                    class='invalid-feedback '>
                                                    <div *ngIf="ACHform.get('RoutingNumber').errors.required">Routing
                                                        Number is
                                                        Required
                                                    </div>
                                                </div>
                                            </mat-error>
                                        </mat-form-field>
                                        <mat-form-field appearance="outline" class="form-bank">
                                            <mat-label>Bank Type</mat-label>
                                            <mat-select formControlName="bankType">
                                                <mat-option *ngFor="let state of bankType" [value]="state">{{state}} 
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                        <mat-form-field appearance="outline" class="form-bank">
                                            <mat-label>Bank Holder Type</mat-label>
                                            <mat-select formControlName="bankHoldeType">
                                                <mat-option *ngFor="let state of bank_holder_type" [value]="state">{{state}} 
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                            </form>
                            <div class="mb-2" fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                                <button mat-button class="btn-7 text-xs my-2" type="button"
                                    (click)='cancel()'>Cancel</button>
                                    <button *ngIf="roleId==15 || roleId==18  else elseBlock1" mat-button class="btn-1 text-xs my-2"  type="button" (click)='achPayment(null,"bank")'
                                    [disabled]='!ACHform.valid'>Pay</button>
                                <ng-template #elseBlock1>
                                    <button class="btn-1 text-xs my-2" mat-button type="button" (click)='payment(null,"bank")'
                                    [disabled]='!ACHform.valid'>Pay</button>
                                </ng-template>
                            </div>
                        </div>
                    </div>
                </mat-expansion-panel> -->

            </div>
        </div>

        <div *ngIf="roleId!=15 && roleId!=18" gdColumn="7 / -1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="shadow-none justify-start">
                <div class="card shadow-none exam cardBorder px-4" fxFlex="auto">
                    <div class="mb-2 text-xs py-2 font-bold">Payment Summary</div>
                    <div class="payment1">

                        <div class="mb-1 content1" exaiContainer>
                            <div class="" fxLayoutGap="6px grid">
                                <div fxLayout="row wrap" fxLayoutGap="10px grid">
                                    <div class="shadow-none w-full" fxFlex="(100/1) + '%'" fxFlex.xs="100%"
                                        fxFlex.sm="33%" *ngFor="let item of listExam; let i = index">
                                        <div class="card shadow-none cardBorder mb-2" fxFlex="auto">
                                            <div class="bg-color -pt-2">
                                                <div class="flex justify-between pt-2" fxLayout="row">
                                                    <div class="pl-3">
                                                        <h5 class="t-xs font "><strong>{{item.examName}}</strong></h5>
                                                    </div>
                                                    <div class="flex text-left text-base pb-2 pr-4 edit-icon1">
                                                        <span class="text-xs">${{item.amount}} </span>
                                                        <mat-icon class="-mr-3 ml-2 moreIcon"
                                                            (click)="deleteItem(item, i)">delete
                                                        </mat-icon>
                                                    </div>
                                                </div>
                                                <div fxLayout="row" class="px-3 -mt-2 ">
                                                    <h6 class="t-xs mb-1 status1 ">{{item.examNameDesc}}</h6>
                                                </div>
                                            </div>

                                            <div fxLayout="column">
                                                <div class="pt-2 mb-2 px-3" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                                    <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                                        <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">
                                                            ExamMode</div>
                                                        <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs">
                                                            Test Center</div>
                                                    </div>
                                                    <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                                        <div class="h4  status t-xs">Exam Date</div>
                                                    </div>
                                                    <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                                        <div class="h4 status t-xs">Exam Time</div>
                                                    </div>
                                                </div>
                                                <div class="px-3 mb-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                                    <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                                        <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">
                                                            {{item.examMode}}</div>
                                                        <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs">
                                                            {{item.testCenterName}}</div>
                                                        <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->
                                                    </div>
                                                    <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                                        <div class="h4 status1 t-xs">{{item.eventDate | date:
                                                            'MM/dd/yyyy'}}</div>
                                                    </div>
                                                    <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                                        <div class="h4  status1 ml-2 t-xs">
                                                            {{item.eventDate | date:'shortTime':'+0000'}}
                                                            {{item.timeZoneAbbreviation}}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- </div> -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div gdColumn="1/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="1" class='eligibility-list1'>
                                <form [formGroup]='Validator' class="cart pt-2">
                                    <mat-form-field fxFlex="auto" appearance='outline' class="">
                                        <mat-label class="text-xs">Enter Code</mat-label>
                                        <input matInput required formControlName='code' class="tetx-xs"
                                            placeholder='Enter a Code'><span class="inline">
                                            <button class="button text-xs float-right -mt-3" mat-button type="button"
                                                (click)='SponsorVocherCode()'
                                                [disabled]="subtotal == 0 && listExam.length > 0">Apply
                                                Code</button></span>
                                        <mat-error>
                                        </mat-error>
                                    </mat-form-field>
                                </form>

                            </div>
                            <!-- <div class="px-4">
                                <div class="pt-3 mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr"
                                    gdColumns.lt-sm="1fr 1fr" exaiContainer>
                                    <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                                        <div class="h4 total text-xs">
                                            Tax
                                        </div>
                                    </div>
                                    <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                                        <div class="h4 text-right status1 text-xs">
                                            $1.00
                                        </div>
                                    </div>
                                </div> -->
                            <hr class='ml-5 mr-5 status1'>
                            <div class="pt-3 mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
                                exaiContainer>
                                <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                                    <div class="h4 mb-2 total font-bold  text-xs">
                                        Grand Total
                                    </div>
                                </div>
                                <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                                    <div class="h4 text-right mb-2 total font-bold text-xs">
                                        ${{total}}
                                    </div>
                                </div>
                            </div>
                            <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="8px">
                                <button *ngIf='subtotal == 0 && listExam.length > 0' class="btn-2 text-xs" mat-button
                                    type="button" (click)='schedule()'>Schedule Now</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div *ngIf="roleId==15 || roleId==18 " gdColumn="7 / -1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="shadow-none justify-start dashboard">
                <div class="card shadow-none exam cardBorder px-4" fxFlex="auto">
                    <div class="mb-2 text-xs py-2 font-bold">Payment Summary</div>
                    <div class="payment1">

                        <div class="mb-1 content1" exaiContainer>
                            <div class="" fxLayoutGap="6px grid">
                                <div fxLayout="row wrap" fxLayoutGap="10px grid">
                                    <div class="shadow-none w-full" fxFlex="(100/1) + '%'" fxFlex.xs="100%"
                                        fxFlex.sm="33%" *ngFor="let item of voucherCartList; let i = index">
                                        <div class="card shadow-none cardBorder mb-2" fxFlex="auto">
                                            <div class="bg-color -pt-2">
                                                <div class="flex justify-between pt-2" fxLayout="row">
                                                    <div class="pl-3">
                                                        <h5 class="t-xs font "><strong>{{item.examName}}</strong>
                                                        </h5>
                                                    </div>

                                                    <div class="flex text-left text-base pb-2 pr-4 edit-icon1">
                                                        <span class="text-xs">${{item.value}} </span>
                                                        <mat-icon class="-mr-3 ml-2 edit-icon1"
                                                            (click)="deleteVoucherItem(item, i)">
                                                            delete
                                                        </mat-icon>
                                                        <mat-icon *ngIf="editable==true" class="-mr-3 ml-2 edit-icon1"
                                                            (click)="edit(item)">
                                                            edit
                                                        </mat-icon>
                                                        <mat-icon
                                                            *ngIf="editable==false && setEditFlag==item.personEventCartId"
                                                            class="-mr-3 ml-2 edit-icon1" (click)="save(item)">
                                                            save
                                                        </mat-icon>

                                                    </div>
                                                </div>

                                            </div>
                                            <div fxLayout="column">
                                                <div class="pt-2 mb-2 px-3" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                                    <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/3">
                                                        <div class="h4 status t-xs">QTY</div>
                                                    </div>
                                                    <div gdColumn="3/4" gdColumn.lt-md="3/4" gdColumn.lt-sm="3/4">
                                                        <div class="h4  status t-xs">*</div>
                                                    </div>
                                                    <div gdColumn="4/4" gdColumn.lt-md="4/4" gdColumn.lt-sm="5/-1">
                                                        <div class="h4 status t-xs">Price</div>
                                                    </div>
                                                </div>
                                                <div class="px-3 mb-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr"
                                                    gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr" exaiContainer>
                                                    <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                                        <!-- <div class="h4 status1 t-xs">{{item.quantity}}</div> -->
                                                        <input onPaste="return false" matInput
                                                            id={{item.personEventCartId}} value="{{item.quantity}}"
                                                            (keyup.enter)="onEnter($event,item)" (focus)="edit(item)"
                                                            (keypress)="numberOnly($event)">
                                                        <!-- <div class="h4 status1 t-xs"></div> -->
                                                    </div>
                                                    <div gdColumn="3/4" gdColumn.lt-md="3/4" gdColumn.lt-sm="3/4">
                                                        <div class="h4  status t-xs"></div>
                                                    </div>

                                                    <div gdColumn="4/4" gdColumn.lt-md="4/4" gdColumn.lt-sm="6/-1">
                                                        <div class="text-sm">
                                                            {{item.price}}</div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="px-4">

                            <hr class='ml-5 mr-5 status1'>
                            <div class="pt-3 mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
                                exaiContainer>
                                <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                                    <div class="h4 mb-2 total font-bold  text-xs">
                                        Total
                                    </div>
                                </div>
                                <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                                    <div class="h4 text-right mb-2 total font-bold text-xs">
                                        ${{subtotalVoucherAmount}}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>

</div>