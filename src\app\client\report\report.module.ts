import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReportRoutingModule } from './report-routing.module';
import { ReportComponent } from './report.component';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { appReducer } from '../state/client.state';
import { SharedEffects } from '../state/shared/shared.effects';
import { SharedReducer } from '../state/shared/shared.reducers';
import { SHARED_STATE_NAME } from '../state/shared/shared.selectors';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { SupersetDashboardComponent } from './superset/superset.component';


@NgModule({
  declarations: [
    ReportComponent,
    SupersetDashboardComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ReportRoutingModule,
    FlexLayoutModule,
    FormsModule,
    MatAutocompleteModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    CdkAccordionModule,
    MatExpansionModule,
    MatIconModule,
    EffectsModule.forFeature([SharedEffects]),
    StoreModule.forFeature(SHARED_STATE_NAME, SharedReducer),
  ],
  providers: [],
  bootstrap: [ReportComponent],
})
export class ReportModule { }
