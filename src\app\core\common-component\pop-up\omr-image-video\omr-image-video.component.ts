import { HttpClient } from "@angular/common/http";
import {
  Component,
  Inject,
  Input,
  OnInit,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";
import input from "@openreplay/tracker/lib/modules/input";
import { Validators } from "ngx-editor";
import { BehaviorSubject, Observable } from "rxjs";
import { MeasurementDetails } from "src/app/client/state/shared/shared.state";
import { GlobalUserService } from "src/app/core/global-user.service";
import { SnackbarService } from "src/app/core/snackbar.service";
import { environment } from "src/environments/environment";


@Component({
  selector: "app-omr-image-video",
  templateUrl: "./omr-image-video.component.html",
  styleUrls: ["./omr-image-video.component.scss"],
})
export class OMRVideoImage implements OnInit {
  TextDetails: string
  Today:Date = new Date()
  MeasureDetails:MeasurementDetails[]
  files:Array<any>=[]
  FileProgress:boolean = false
  AnswerSheetPDF:Array<object>=[]
  AnswerSheetImage:Array<object>=[]
  ShredingVideo:Array<object>=[]
  isLoading:boolean = true
  ErrorListArray:Array<string> =[]
  dataSource :Array<any> = [];
  orderId:string
  Form:FormGroup
  shippingInformation:any;
url='https://examroomcorp-my.sharepoint.com/:x:/g/personal/yinkaos_examroom_ai/EZizRd87bz5MqFWIiNIt2TwBzbKut9QEQoN75K7ijQfUfw?rtime=Y32kQAI63Ug&download=1'

  isConsentCheckbox = false;

  displayColumnPayment:Array<string> =["CandidateId","ExamCode","Examtype",'Quantity',]
  constructor(  private dialogRef: MatDialogRef<OMRVideoImage>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialog: MatDialog,private http:HttpClient,private service:SnackbarService,private route:Router,private global:GlobalUserService){
      this.TextDetails = data?.message
      this.MeasureDetails = data?.measurescore
      this.orderId = data?.element?.orderNumber
    }

  ngOnInit(): void {
    this.data?.actiontype ==='visibility' || this.data?.actiontype == 'booklet_view'?null:this.getVideoImage()       
    this.data?.actiontype ==='visibility'? this.formatString():null
     this.data?.actiontype ==='booklet_view'?this.getOrderDetails():null

     this.Form = new FormGroup({
      sign: new FormControl(''),
      date: new FormControl(""),
    });
  }

  getVideoImage(){
   this.http.get(`${environment.baseUrl}client/api/form/GetArkivDocument/${this.data.element.personEventId}`).subscribe((data:any)=>{
        if(data){
          this.AnswerSheetPDF = (this.TextDetails !='video' || this.data.actiontype !='featured_video')? (data.arkivOMRSheets.filter((x)=> x.extension =='pdf')):data.arkivVideos
          this.AnswerSheetImage = this.TextDetails !='video' || this.data.actiontype !='featured_video'? (data.arkivOMRSheets.filter((x)=> x.extension =='image')):data.arkivVideos
          this.ShredingVideo = this.data.actiontype =='featured_video' && data.arkivVideos.length > 0?data.arkivVideos:null
        }
   })
  }

  onFileDropped($event) {
    this.prepareFilesList($event);
  }

  /**
   * handle file from browsing
   */
  fileBrowseHandler(files) {
    this.prepareFilesList(files);
  }

  /**
   * Delete file from files list
   * @param index (File index)
   */
  deleteFile(index: number) {
    this.files.splice(index, 1);
    this.FileProgress = false
  }

  /**
   * Simulate the upload process
   */
  uploadFilesSimulator(index: number) {
    setTimeout(() => {
      if (index === this.files.length) {
        return;
      } else {
        const progressInterval = setInterval(() => {
          if (this.files[index].progress === 100) {
            this.FileProgress =true
            this.service.callSnackbaronSuccess('Uploaded SucessFully')
            clearInterval(progressInterval);
            this.uploadFilesSimulator(index + 1);
          } else {
            this.files[index].progress += 5;
          }
        }, 200);
      }
    }, 1000);
  }

  /**
   * Convert Files list to normal array list
   * @param files (Files List)
   */
  prepareFilesList(files: Array<any>) {
    for (const item of files) {
      item.progress = 0;
      this.files.push(item);
    }
    this.uploadFilesSimulator(0);
  }

  /**
   * format bytes
   * @param bytes (File size in bytes)
   * @param decimals (Decimals point)
   */
  formatBytes(bytes, decimals) {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const dm = decimals <= 0 ? 0 : decimals || 2;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  formatString(){
  const validationMessages = this.data.element.Error.split(/!\s*\.?\s*/).filter(msg => msg.trim().length > 0);
  this.ErrorListArray =validationMessages
  }

  Submit() {
    let personId:any = this.global.userDetails.getValue().personTenantRoleId
    const formData = new FormData();
    formData.append('File', this.files[0]);
    // simply removing commas so that later while parsing 
    // the response the.split is possible
    formData.append('SubFolderName', 'examroomv2uat');
    formData.append('BucketName', 'preregcandidate')
    formData.append('PersonTenantRoleId', personId)

    this.http.post(`${environment.baseUrl}formmsvc/api/File/upload-precandidate`, formData).subscribe((data) => {
      if (data) {
        this.dialogRef.close({ confirmed: true })

      }
    }, (err) => {
      this.service.callSnackbaronError(`${err.message.message.error}`)
    })
    // var url = environment.baseUrl + `formmsvc/api/File/upload`;
    //  this.http.post(url, formData).
    
  }

  getDownloadTemplate() {
    fetch("https://examroomcorp-my.sharepoint.com/:x:/g/personal/yinkaos_examroom_ai/EZizRd87bz5MqFWIiNIt2TwBzbKut9QEQoN75K7ijQfUfw?rtime=Y32kQAI63Ug')/$value", {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + sessionStorage.getItem('token'), // You need to get an access token first
        'Accept': 'application/json'
      }
    })
      .then(response => response.blob())
      .then(blob => {
        const a = document.createElement('a');
        document.body.appendChild(a);
        const url = URL.createObjectURL(blob);
        a.href = url;
        a.download = 'Training_List.xlsx'; // Name of the downloaded file
        a.click();
        URL.revokeObjectURL(url); // Clean up the URL after downloading
        document.body.removeChild(a); // Clean up the link

        setTimeout(() => {
          this.service.callSnackbaronSuccess('Downloaded Successfully');
        }, 1500);
      })
      .catch(error => {
        console.error('Error downloading file:', error);
        this.service.callSnackbaronSuccess('Download failed');
      });




    // }
    // })
  }



  getOrderDetails(){
    this.http.get(`${environment.baseUrl}client/api/credentia/order-inventory/${this.data.element.id}`).subscribe((data:{exams:{id:number,examTitle:string,formCode:string,testCenterId:string,quantity:string,examCode:string}[],id:string,
      shippingInformation:{contactName:string,contactEmail:string,contactPhoneNumber:string,addressLine1:string,preferredCarrier:string}})=>{
      if(data){
   
        data.exams.forEach(element => {
           this.dataSource.push({
            id: element.id,
            examTitle: element.examTitle,
            examCode:element.examCode,
            formCode: element.formCode,
            testCenterId: element.testCenterId,
            quantity:element.quantity,
      })
        });
  
    this.dataSource = [...this.dataSource];
    this.shippingInformation = data.shippingInformation;
        
      }
   },(err)=>{
       this.service.callSnackbaronError(`${err.message.message.error}`)
   })
  }

  onPdfLoadComplete(){
    this.isLoading = false;
  }

  Save(){
    if(!this.isConsentCheckbox) return;
    this.dialogRef.close({confirmed:true})
  }

  consentCheck() {
    this.isConsentCheckbox = !this.isConsentCheckbox;
  }

}