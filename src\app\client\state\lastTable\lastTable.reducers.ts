import { setLastTable } from "./lastTable.actions";
import { createReducer, on } from "@ngrx/store";
import { LastTableState, initialState } from "./lastTable.state";

const _lastTableReducer = createReducer<LastTableState>(initialState,
    on(setLastTable, (state, { lastTable})=> {
        return {
            ...state,
            lastTable: lastTable
        }
    }),
)

export function lastTableReducer(state, action){
    return _lastTableReducer(state, action)
}