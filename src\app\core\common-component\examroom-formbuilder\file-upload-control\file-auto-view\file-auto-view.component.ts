// Angular
import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';

// RxJs
import { BehaviorSubject, Observable } from 'rxjs'; 'rxjs';

// Env 
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-file-auto-view',
    templateUrl: './file-auto-view.component.html',
    styleUrls: ['./file-auto-view.component.scss']
})
export class FileAutoViewComponent implements OnInit {

    @Input() fileName;
    @Output() toggleFileViewEvent = new EventEmitter();

    fileExtension: string;

    viewUrl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
    viewUrl$: Observable<string> = this.viewUrl.asObservable();

    constructor(private http: HttpClient) {
    }

    ngOnInit(): void {
        this.getDocUrls();
    }

    closeFiles() {
        this.toggleFileViewEvent.emit(false);
    }

    getDocUrls() {
        this.fileExtension = this.fileName.split('.').pop()
        var url = environment.baseUrl + `formmsvc/api/file/url?systemFileName=${this.fileName.split('|')[1]}`;
        this.http.get(url).subscribe((response: any) => {
            this.viewUrl.next(response.url);
        });
    }
}
