import { HttpClient } from '@angular/common/http';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, forwardRef, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ControlValueAccessor, FormBuilder, FormControl, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { DynamicFormService } from '@ng-dynamic-forms/core';
import { Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { environment } from 'src/environments/environment';
import { FormBuilderService } from '../form-builder.service';
import { CertStatus, Details, feildName, Role, sectionName } from '../form-builder.types';




@Component({
  selector: 'app-form-upload-control',
  templateUrl: './form-upload-control.component.html',
  styleUrls: ['./form-upload-control.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormUploadControlComponent),
      multi: true
    }
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormUploadControlComponent implements ControlValueAccessor, OnInit, OnChanges {
  outputValue: any;
  userData: any;
  typedText:any;
  filteredDatas:any;
  selectedUser: any = null;
  registryNumber = new FormControl('');
  @ViewChild('divClick') divClick: ElementRef;
  prefilledValue: any = [];
  prefilledValue1: any = [];
  selectedData
  feildName: string;
  @Input() disabled: boolean;
  @Input() model: any;
  writeValue(value: any) {
    if (value !== undefined && value != null) {
      try {
        this.prefilledValue = [JSON.parse(value)];
        this.prefilledValue1 = [JSON.parse(value)];
        this.formService.selectedCandidateDetails = {
          candidateName: this.prefilledValue[0].candidatename,
          candidateId: this.prefilledValue[0].candidateId,
          registryNumber: this.prefilledValue[0].registryNumber
        }
        //this.outputValue = Array.isArray(value) ? value : value.split(',').map(x=>JSON.parse(x));
        this.outputValue = [JSON.parse(value)];
      }
      catch (e) {

      }
    }
    else
      this.outputValue = [];
  }
  propagateChange = (_: any) => { };

  registerOnChange(fn) {
    this.propagateChange = fn;
  }



  registerOnTouched() { }
  constructor(private http: HttpClient,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private formService: FormBuilderService,
    private activatedRoute: ActivatedRoute,
    public global: GlobalUserService,
    private service: DynamicFormService,
  ) { }
  radioLabel = [{}];
  SelectedRadio: String;
  selecteddata: any;
  certstaus: CertStatus
  candiateDetails: FormGroup;
  enableSerachDetails: boolean = false;
  enableSerach: boolean = false;
  routeParamData: any;
  CandidateDetails: Array<CandidateDetails>
  DetailsUser: Details
  ShowRating: boolean = false
  ValueDetails: boolean = true
  CandidateDetailsValue = []
  moduleId:string 
  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe((param: Params) => {
      this.moduleId = param.moduleId

      this.DetailsUser = {
        stateId: param.params.stateId,
        formTypeId: param.params.formTypeId,
        certStatus: param.params.certStatusId
      }
      if ((this.DetailsUser.stateId == '4' && this.DetailsUser.formTypeId == '5') || ((this.global.personEventId?.formTypeId == 5 || this.global.personEventId?.stateId == 4))) {
        this.radioLabel = [{ name: '', value: 'FullName' }];
        ((this.global.personEventId?.statusName =="Pending" || this.global.personEventId?.statusName =="Approved" || this.global.personEventId?.status =="Pending" || this.global.personEventId?.status =="Approved" || (this.DetailsUser?.certStatus == '2' || this.DetailsUser?.certStatus == '7')))?null: this.http.get(`${environment.baseUrl}tenantmsvc/api/employer/listOfEmployers?pageNumber=1&pageSize=10000`).subscribe((data: any) => {
            if (data) {
              let ActiveEmployerUser = data.filter((x)=>x.isEnable == true)
             let Values =this.removeDuplicates(ActiveEmployerUser, 'employerId');
             this.selecteddata = Values;
              this.userData = Values
              this.cdr.detectChanges();
            }
          })

      } else {
        this.radioLabel = [{ name: 'Registry Number', value: 'CertNumber' }, { name: 'Candidate Name', value: 'FullName' }];
        setTimeout(() => {
          this.ShowRating = this.prefilledValue.length == 0 ? true : false
          this.cdr.markForCheck()
        }, 1500)
      }
    })

    setTimeout(() => {
      this.model.id == feildName.E9Eligibility || this.model.id == feildName.SCE6Eligibility || this.model.id == feildName.SCE7Eligibility || this.model.id == feildName.E2EligibilityNV || this.model.id == feildName.E3eligibilityNV || this.model.id == feildName.E4eligibilityNV ? this.divClick.nativeElement.click() : null;
    }, 500)
    this.feildName = this.model.id == feildName.E9Eligibility ? feildName.E9Eligibility : this.model.id == feildName.SCE6Eligibility ? feildName.SCE6Eligibility : this.model.id == feildName.SCE7Eligibility ? feildName.SCE7Eligibility : this.model.id == feildName.E2EligibilityNV ? feildName.E2EligibilityNV : this.model.id == feildName.E3eligibilityNV ? feildName.E3eligibilityNV : this.model.id == feildName.E4eligibilityNV ? feildName.E4eligibilityNV : null;
    this.activatedRoute.paramMap.subscribe((data: any) => {
      if (data) {
        this.routeParamData = data.params;
      }
    })
    if (this.routeParamData.registryId) {
      this.http.get(environment.baseUrl + `registry/api/certificate/get-details?registryId=${this.routeParamData.registryId}`).subscribe((data: any) => {
        if (data) {
          this.prefilledValue = [{
            candidatename: data.FirstName + data.MiddleName + data.LastName,
            candidateId: String(data.PersonTenantRoleId),
            registryNumber: data.CertNumber
          }]
          this.formService.selectedCandidateDetails = {
            candidateName: `${data.FirstName}${data.MiddleName}${data.LastName}`,
            candidateId: data.PersonTenantRoleId,
            registryNumber: data.CertNumber
          }
          this.formService.getCandidateData();
          this.cdr.detectChanges();
          this.outputValue = this.prefilledValue;
          this.propagateChange(this.outputValue.map(x => JSON.stringify(x)).join(','));
          this.cdr.markForCheck();
        }
      })
    }
    this.candiateDetails = this.formBuilder.group({
      registryNumber: new FormControl("")
    });


  }

  ngOnChanges(changes: SimpleChanges): void {

    if (changes.model.currentValue.parent.id != sectionName.problemReport && this.global.userDetails.getValue().roleId == Role.OperationStaff) {

      this.formService.HideEmployerValues.subscribe((data) => {

        if (data) {

          if (data.model.value == "yes") {
            this.enableSerach = true

            this.formService.SearchValue = this.selectedUser !=null?this.selectedUser:this.prefilledValue

            this.CandidateDetailsValue = this.CandidateDetails

            this.prefilledValue1 = this.prefilledValue

            this.cdr.markForCheck();

          } else if (data.model.value == "no" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {

   

            this.formService.HideEmployerValue.subscribe((data) => {

              if (data) {

                data.forEach((x) => {

                  return x.hidden = true

                })

                if (data != null && data != undefined && data?.model?.id != "197637c815704235a8126b2f4503a40a") {

                  this.enableSerach = false

                  this.prefilledValue1 = []

                  this.CandidateDetailsValue = []

                  this.cdr.markForCheck();

                  data.forEach((x) => {

                    return x.hidden = false

                  })

                }




              } else {

                this.enableSerach = false

                this.prefilledValue1 = []

                this.CandidateDetailsValue = []

                this.cdr.markForCheck();

              }

            })




          }

        }

        this.formService.HideYesEmployerValue.subscribe((data) => {

          if (data != "" && data != null) {

            if (data.model.value == "yes" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {

              this.formService.HideNOEmployerValue.subscribe((value) => {

                if (value) {

                  let Values = data.model.options.filter((x) =>

                    x.label == "2. Add Other Employer details"

                  )

                  let ValuesYesNO = data.model.options.filter((x) => x.value == data.model.value)

                  if (Values.length > 0) {

                    value.forEach((x) => {

                      return x.hidden = false

                    })

                  } else if (ValuesYesNO[0].value == "no") {

                    value.forEach((x) => {

                      return x.hidden = true

                    })

                  } else if (ValuesYesNO[0].value == "yes") {

                    // value.forEach((x)=>{

                    //   return x.hidden = false

                    //   })

                  }




                }

              })

              this.enableSerach = true

              this.CandidateDetailsValue = this.CandidateDetails

              this.prefilledValue1 = this.prefilledValue




              this.cdr.markForCheck();

            } else {

              if (data.model.value == "no" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {

                this.formService.HideNOEmployerValue.subscribe((data) => {

                  if (data) {

                    data.forEach((x) => {

                      return x.hidden = true

                    })

                  }

                })

                this.enableSerach = false

                this.CandidateDetailsValue = []

                this.prefilledValue1 = []

                this.cdr.markForCheck();

              }

            }

          }




        })

      })

    } else if (changes.model.currentValue.parent.id != sectionName.problemReport && this.global.roleId == Role.Candidate) {

      this.formService.HideEmployerValues.subscribe((data) => {

        if (data) {

          if (data.model.value == "yes") {

            this.enableSerach = true

            this.formService.SearchValue = this.selectedUser !=null?this.selectedUser:this.prefilledValue

            this.CandidateDetailsValue = this.CandidateDetails

            this.prefilledValue1 = this.prefilledValue

            this.cdr.markForCheck();

          } else if (data.model.value == "no" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {

            this.formService.SearchValue = data.model.value

            this.formService.HideEmployerValue.subscribe((data) => {

              if (data) {

                data.forEach((x) => {

                  return x.hidden = true

                })

                if (data != null && data != undefined && data?.model?.id != "197637c815704235a8126b2f4503a40a") {

                  this.enableSerach = false

                  this.prefilledValue1 = []

                  this.CandidateDetailsValue = []

                  this.cdr.markForCheck();

                  data.forEach((x) => {

                    return x.hidden = false

                  })

                }

              } else {

                this.enableSerach = false

                this.prefilledValue1 = []

                this.CandidateDetailsValue = []

                this.cdr.markForCheck();

              }

            })

          }else{

            this.formService.SearchValue = data.model.value =="no" && data.model.id == "197637c815704235a8126b2f4503a40a"?"no" :this.selectedUser




          }

        }

        this.formService.HideYesEmployerValue.subscribe((data) => {


          if (data != "" && data != null) {

            if (data.model.value == "yes" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {
              this.formService.HideNOEmployerValue.subscribe((value) => {

                if (value) {

                  let Values = data.model.options.filter((x) =>

                    x.label == "2. Add Other Employer details"

                  )

                  let ValuesYesNO = data.model.options.filter((x) => x.value == data.model.value)

                  if (Values.length > 0) {

                    value.forEach((x) => {

                      return x.hidden = false

                    })

                  } else if (ValuesYesNO[0].value == "no") {

                    value.forEach((x) => {

                      return x.hidden = true

                    })

                  } else if (ValuesYesNO[0].value == "yes") {

                    // value.forEach((x)=>{

                    //   return x.hidden = false

                    //   })

                  }




                }

              })

              this.enableSerach = true

              this.CandidateDetailsValue = this.CandidateDetails

              this.prefilledValue1 = this.prefilledValue




              this.cdr.markForCheck();

            } else {

              if (data.model.value == "no" && data.model.id == "e75e0b3101a04273a2bd0f4a374f93a6") {




                this.formService.HideNOEmployerValue.subscribe((data) => {

                  if (data) {

                    data.forEach((x) => {

                      return x.hidden = true

                    })

                  }

                })

                this.enableSerach = false

                this.CandidateDetailsValue = []

                this.prefilledValue1 = []




                this.cdr.markForCheck();

              }

            }

          }




        })




     

      })





    }

  }


  removeDuplicates(myArray, Prop) {
    return myArray.filter((obj, pos, arr) => {
      return arr.map((mapObj) => mapObj[Prop]).indexOf(obj[Prop]) === pos;
    });
  }
  
  filterUsers(value) {
    if (value == '') {
      this.enableSerachDetails = false;
    }
    else if (this.DetailsUser.formTypeId != "5") {
      this.http.get(`${environment.baseUrl}registry/api/certificate/get-all?pageNumber=1&pageSize=1000&searchText=${value}&searchColumn=${this.SelectedRadio}&stateId=4`).subscribe((data: any) => {
        if (data) {
          this.selecteddata = data.result;
          this.enableSerachDetails = true;
          this.cdr.detectChanges();
        }
      })
    } else {
    }

  }



  filter() {
    
    if (this.selectedUser == "") {
      this.userData = this.selecteddata
      this.CandidateDetailsValue=[]

    } else {
      this.userData = this.selecteddata.filter(
        (item) => {
          if (item) {
            return (
              item.employerName?.toLowerCase().includes(this.selectedUser.toLowerCase())
              ||
              item.addressLine?.toLowerCase().includes(this.selectedUser.toLowerCase())
              ||
              item.zipCode?.toLowerCase().includes(this.selectedUser.toLowerCase())
              ||
              item.mobilePhoneNumber?.toLowerCase().includes(this.selectedUser.toLowerCase())

            )
          }
        }
      );
      this.userData = this.userData.length == 0 ? [{ value: "No Data Found" }] : this.userData
      this.cdr.markForCheck();
    }
  }

  selected(item) {
    this.updatedname(item)
  }

  checkDisabled() {
    if (this.model.id == feildName.E9Eligibility) {
      this.outputValue = '10/07/2022'
    }
    else if (this.model.id == feildName.SCE6Eligibility || this.model.id == feildName.SCE7Eligibility) {

      this.outputValue = '06-06-2024'
    }
    else if (this.model.id == feildName.E2EligibilityNV) {
      this.outputValue = '9992'
    }
    else if (this.model.id == feildName.E3eligibilityNV) {

      this.outputValue = '9990'
    }
    else if (this.model.id == feildName.E4eligibilityNV) {
      this.outputValue = '9993'
    }
    this.propagateChange(this.outputValue);
    this.cdr.markForCheck();
  }
  serachresult() {
    this.filterUsers(this.candiateDetails.value.registryNumber)
  }
  
  updatedname(item) {
    this.SelectedRadio = item.value;
    this.enableSerach = true;
    this.filterUsers(this.candiateDetails.value.registryNumber);
    this.userData = this.selecteddata
    // }


  }

  appendData(element) {
    this.candiateDetails.setValue({ registryNumber: '' })
    this.enableSerachDetails = false;
    if (this.DetailsUser.formTypeId != '5') {
      this.CandidateDetails = [{
        candidatename: element.FullName,
        candidateId: String(element.PersonTenantRoleId),
        registryNumber: element.CertNumber
      }];
      this.formService.selectedCandidateDetails = {
        candidateName: element.FullName,
        candidateId: element.PersonTenantRoleId,
        registryNumber: element.CertNumber
      }
    } else {
      
      this.CandidateDetails = []
      this.prefilledValue = []
      this.CandidateDetails = [{
        EmployerName: element.employerName,
        AddressName: element.addressLine,
        City: element.city,
        State: element.state,
        Zipcode: element.zipCode,
        mobilePhoneNumber: element.mobilePhoneNumber,
      }];

      this.formService.selectedCandidateDetails = {
        EmployerName: element.employerName,
        AddressName: element.addressLine,
        City: element.city,
        State: element.state,
        Zipcode: element.zipCode,
        mobilePhoneNumber: element.mobilePhoneNumber,
      }
      this.formService.userDetails.next({
        EmployerName: element.employerName,
        AddressName: element.addressLine,
        City: element.city,
        State: element.state,
        Zipcode: element.zipCode
      })
      this.CandidateDetailsValue = this.CandidateDetails
      this.formService.SearchValue =this.CandidateDetails
    }

    this.formService.getCandidateData();
    this.outputValue = this.CandidateDetails;
    this.propagateChange(this.outputValue.map(x => JSON.stringify(x)).join(','));
    this.cdr.markForCheck();
    this.cdr.detectChanges()
  }

  closeList() {
    this.enableSerachDetails = false;
  }

  onSelectionChange($event) {
    let value = this.selecteddata.filter((x) => x.employerName.toLowerCase() == $event.option.value.toLowerCase())
    this.appendData(value[0])
  }
  clearEmployerDetails (evt) {

    this.selectedUser = ""

    this.userData = this.selecteddata

    this.formService.SearchValue =null

    this.prefilledValue = []

    this.CandidateDetails = []

    this.CandidateDetailsValue=[]

    this.prefilledValue1=[]

    this.outputValue = []

    this.formService.getCandidateData();

    this.propagateChange(this.outputValue.map(x => JSON.stringify(x)).join(','));

    // evt.stopPropagation();

  }
  
enterData(event){
  this.typedText=event.target.value;
  setTimeout(() => {
  //this.filteredDatas=this.selectedData.filter((x)=>x==this.typedText);
    let value = this.selecteddata.filter((item)=>item.zipCode?.toLowerCase()==this.typedText.toLowerCase()||item.employerName?.toLowerCase()==this.typedText.toLowerCase()||item.mobilePhoneNumber==this.typedText||item.addressLine?.toLowerCase()==this.typedText.toLowerCase());
    this.selectedUser=value[0].employerName;
    this.appendData(value[0]);
  }, 500);
  } 
}



export interface CandidateDetails {
  candidatename?: string;
  candidateId?: string;
  registryNumber?: string;
  AddressName?: string;
  City?: string;
  State?: string;
  Zipcode?: string;
  EmployerName?: string;
  mobilePhoneNumber?: string | number;
}
