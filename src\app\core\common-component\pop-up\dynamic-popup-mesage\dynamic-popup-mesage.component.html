<div class="p-2 touch-auto overflow-auto" gdColumns="1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div *ngIf="data?.message !='There are no slots available at a test center for that date, would you like to schedule an online exam instead?'" class="flex justify-end cursor-pointer" fxLayout="row" mat-dialog-close>
                <mat-icon class="text-sm flex justify-end">close</mat-icon>
            </div>
        </div>
    </div>
    
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div fxLayout="column">
            <div class="flex justify-center submit" fxLayout="row">
                <h3 > <b class="txs1">{{data.title}}</b></h3>
            </div>
        </div>
    </div>
    <div class="" gdColumn="1 / -1" gdColumn.lt-md="1 / -1" gdColumn.lt-sm="1">
        <div class="pb-3 flex justify-center t-xs confirm items-center text-center " fxLayout="row">
            <h6 *ngIf="data?.status !='manage-training-programs'"  class="txs">{{data.message}}</h6>
            <h6 *ngIf="data.status =='manage-training-programs'" [innerHTML]="data.message"  class="txs"></h6>
            
        </div>
    </div>
    <div class="button-1 pb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr 1fr" gdColumns.lt-sm="1fr 1fr" gdGap="12px">
        <div class="" gdColumn="1 / 2" gdColumn.lt-md="1 / 2" gdColumn.lt-sm="1 / 2">
            <div class="" fxLayout="row" fxLayoutAlign="center center" >
                <button mat-dialog-close class="btn-3" mat-button (click)="closeApp()">{{data.cancelButton}}</button>

            </div>
        </div>
        <div class="" gdColumn="2 / -1" gdColumn.lt-md="2 / -1" gdColumn.lt-sm="2 / -1">
            <div class="" fxLayout="row" fxLayoutAlign="center center" >
                <button (click)="submitApp()" class="btn-1" mat-button color=primary>{{data.OkButton}}</button>
            </div>
        </div>
    </div>
</div>