<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Credentia | Client</title>
    <base href="/">

    <meta content="width=device-width, initial-scale=1" name="viewport">
    <link rel="icon" type="image/svg" href="./assets/img/Icons/Cred-logo.svg">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
        rel="stylesheet" crossorigin="anonymous">
    <!-- <script src='https://cdn.jsdelivr.net/npm/@thoughtspot/visual-embed-sdk/dist/tsembed.js'></script>    -->
</head>

<body dir="ltr">
    <style>
        #exai-splash-screen {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #e5e5e5a8;
            z-index: 99999;
            pointer-events: none;
        }

        #exai-splash-screen>.wrapper {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            -webkit-transform: translate(-50%, -50%);
        }

        @-webkit-keyframes ball-scale-multiple {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                opacity: 0;
            }

            5% {
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 0;
            }
        }

        @keyframes ball-scale-multiple {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                opacity: 0;
            }

            5% {
                opacity: 1;
            }

            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                opacity: 0;
            }
        }

        .ball-scale-multiple {
            position: relative;
            -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
        }

        .ball-scale-multiple>div:nth-child(2) {
            -webkit-animation-delay: -0.4s;
            animation-delay: -0.4s;
        }

        .ball-scale-multiple>div:nth-child(3) {
            -webkit-animation-delay: -0.2s;
            animation-delay: -0.2s;
        }

        .ball-scale-multiple>div {
            /* background-color: #fff; */
            background-color: var(--text-color2);
            border-radius: 100%;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            position: absolute;
            left: -30px;
            top: 0;
            opacity: 0;
            margin: 0;
            width: 60px;
            height: 60px;
            -webkit-animation: ball-scale-multiple 1s 0s linear infinite;
            animation: ball-scale-multiple 1s 0s linear infinite;
        }

        /* ::part(acsb-trigger) {
    color: rgba(0, 0, 0, 0.87) !important;
    border-color: rgba(82, 63, 105, 0.06) !important;
    border-style: solid !important;
    right: inherit !important;
} */

.zsiq_custommain .zsiq_unrdcnt, .zsiq_theme1 .zsiq_unrdcnt {
    display: block!important;
    left: 43px!important;
    border: 1px solid #fff;
}
.siqico-call:before, .siqico-mincall:before {
    content: "8"!important;
}
.zsiq_theme1 div.zsiq_cnt {
    background-color: #0071ce !important;
}
.zsiq_theme1 .zsiq_cnt p, .zsiq_theme1 div.zsiq_cnt {
    color: #fff!important;
}
div.zsiq_flt_rel {
    background-color: #0066cc !important;
}
.zsiq_theme1 .zsiq_cnt:after {
    background-color: #0071ce !important;
    box-shadow: 0 0 0 0 #eee!important;
}
    </style>

    <div id="exai-splash-screen">
        <div class="wrapper">
            <div class="ball-scale-multiple">
                <div></div>
                <div></div>
                <div></div>
            </div>
        </div>
    </div>

    <exai-root></exai-root>
</body>
<script> (function(){ var s = document.createElement('script'); var h = document.querySelector('head') || document.body; s.src = 'https://acsbapp.com/apps/app/dist/js/app.js'; s.async = true; s.onload = function(){ acsbJS.init({ statementLink : '', footerHtml : `<a href='https://examroom.ai/'>Accessibility by ExamRoom.AI</a>`, hideMobile : false, hideTrigger : false, disableBgProcess : false, language : 'en', position : 'left', leadColor : '#146FF8', triggerColor : '#146FF8', triggerRadius : '50%', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerIcon : 'people', triggerSize : 'bottom', triggerOffsetX : 20, triggerOffsetY : 20, mobile : { triggerSize : 'small', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerOffsetX : 20, triggerOffsetY : 20, triggerRadius : '20' } }); }; h.appendChild(s); })();</script>

</html>