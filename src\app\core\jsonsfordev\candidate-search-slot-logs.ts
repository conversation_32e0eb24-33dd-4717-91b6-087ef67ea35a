import {
  consoleLoaderJSON,
  moduleTypes,
  nestingTypes,
  paramTypes,
  widgetTypes,
} from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Dynamic,
      name: "Candidate Search Slot",
      moduleID: "candidate-search-slot",
      nestingType: nestingTypes.ColumnsInRows,
      iconRef: "candidateSearch",
      routeRef: "/loader",
      dataattributes: "candidatesearchslotlogs",
      sequence: 202,
      rows: [
        {
          class: "",
          gdColumns: "1fr",
          gdColumnsltlg: "1fr",
          gdColumnsltmd: "1fr",
          gdColumnsltsm: "1fr",
          gdGap: "12px",
          columns: [
            {
              class: "w-full",
              gdColumn: "1 / 1",
              gdColumnltlg: "1 / 1",
              gdColumnltmd: "1 / 1",
              gdColumnltsm: "1",
              fxLayout: "",
              fxLayoutAlign: "start center",
              fxFlex: "none",
              fxFlexltmd: "auto",
              fxHidexs: "false",
              fxHidesm: "false",
              widget: {
                type: widgetTypes.DynamicTable,
                tableOptions: {
                  headerRowCssClasses: ["tab-col"],
                  showPaginator: true,
                  pageSizeOptions: [8, 20, 100],
                  pageSize: 8,
                  showColumnFilter: true,
                  showColumnWiseFilter: true,
                  showAddButton: [false],
                  showSearch: true,
                  verticalScroll: true,
                  horizontalScroll: true,
                  isPaginationServerSide: [true],
                  isSearchServerSide: [true],
                  showStateSelectionDropdown: true,
                  showSlectdropstatedataattributes:
                    "candidatesearchslotlogsdrop",
                  ShowFilenameFilter: true,
                  showMatTabs: false,
                  showOrderStatusDropdown: true,
                  showExamModeDropdown: true,
                },
                displayedColumns: [
                  [
                    <tableTypes.column>{
                      visible: true,
                      id: "candiateId",
                      name: "Candidate ID",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: true,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: ["firstName", "middleName", "lastName"],
                      name: "Candidate Name",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-7"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "emailId",
                      name: "Email ID",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-8"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: true,
                    },
                    <tableTypes.column>{
                      visible: false,
                      id: "phoneNumber",
                      name: "Phone Number",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "stateName",
                      name: "State",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "zipCode",
                      name: "Zip Code",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: true,
                      isFilter: true,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "trainingProgrameName",
                      name: "Training Program",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-7"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "applicationReviewDate",
                      name: "Application Review Date",
                      cellType: "date",
                      dateCellProps: "shortDate",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "elapsedDateTime",
                      name: "Approval Elapsed Date",
                      cellType: "date",
                      dateCellProps: "shortDate",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-6"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "examModeName",
                      name: "Type of Slot",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "slotStartDate",
                      name: "Start Date",
                      cellType: "date",
                      dateCellProps: "shortDate",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "slotEndDate",
                      name: "End Date",
                      cellType: "date",
                      dateCellProps: "shortDate",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                    <tableTypes.column>{
                      visible: true,
                      id: "statusName",
                      name: "Issue",
                      cellType: "text",
                      headerType: "sort",
                      headerAlign: "center",
                      cellAlign: "center",
                      headerCssClasses: ["width-5"],
                      cellCssClasses: [],
                      filterable: false,
                      isFilter: false,
                    },
                  ],
                ],
                requestDetails: [
                  {
                    fetchUrl: [
                      "client",
                      "api",
                      "Account",
                      "UserSearchExamSlotLog",
                    ],
                    requestBody: null,
                    requestParams: [
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageNumber",
                        paramValue: 1,
                      },
                      {
                        paramType: paramTypes.Optional,
                        paramName: "pageSize",
                        paramValue: 8,
                      },
                    ],
                    method: "GET",
                  },
                ],
              },
            },
          ],
        },
      ],
    },
  ],
};
