import { Component, ElementRef, EventEmitter, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { MatExpansionPanel } from '@angular/material/expansion';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ManageEligibilityRoutesService } from '../manage-eligibilty-routes.service';
import { EligibilityRouteStore, ExamDetails, ExamMode, GetAttemptRulesResponse, GetExamModeResponse, GetExamTypeResponse, GetPaidByResponse, PostEligibilityRoute } from '../store/eligibility.model';
import { HttpErrorResponse } from '@angular/common/http';
import { MatRadioChange } from '@angular/material/radio';
import { MatDialog } from '@angular/material/dialog';
import { QueryBuilderConfig } from 'angular2-query-builder';
import { Action, select, Store } from '@ngrx/store';
import { forkJoin, Observable, Subscription } from 'rxjs';
import { ELIGIBILITY_ROUTE_STATE } from '../store/store';
import { getAttemptRules, getExamMode, getExamType, getPaidBy } from '../store/selector';
import * as actions from '../store/action';
import { take } from 'rxjs/operators';
import { MatSelectChange } from '@angular/material/select';
import { SnackbarService } from 'src/app/core/snackbar.service';

@Component({
  selector: 'exai-exam-details',
  templateUrl: './exam-details.component.html',
  styleUrls: ['./exam-details.component.scss']
})
export class ExamDetailsComponent implements OnInit, OnDestroy {
  form: FormGroup;
  draftedForm: FormGroup;
  examType: Array<GetExamTypeResponse> = [];
  examMode: Array<GetExamModeResponse> = [];
  paymentBy: Array<GetPaidByResponse> = [];
  days: Array<{ id: number, name: string }> = [];
  draftedExams: Array<any> = [];
  attempts: Array<GetAttemptRulesResponse> = [];
  attemptedList: Array<any> = [];
  _duration: number = 30;
  @ViewChild('expressionDialog') expressionDialog: TemplateRef<any>;
  @Output() backStep: EventEmitter<boolean> = new EventEmitter();
  expressionExams: Array<any> = [];
  public query = {
    condition: 'and',
    rules: []
  };

  public config: QueryBuilderConfig = {
    fields: {
      exams: {
        name: 'Exams',
        entity: 'physical',
        type: 'select',
        options: this.expressionExams
      }
    }
  };
  selectedInformation: PostEligibilityRoute = null;
  eligibility$: Observable<EligibilityRouteStore>;
  subscription: Subscription[] = [];
  expression: string = '';

  constructor(private formBuilder: FormBuilder, private router: Router,
    private service: ManageEligibilityRoutesService, private dialog: MatDialog,
    private store: Store<{ ELIGIBILITY_ROUTE_STATE: EligibilityRouteStore }>,
    private _snackBar: SnackbarService) {
    this.eligibility$ = this.store.pipe(select(ELIGIBILITY_ROUTE_STATE));

    for (let i = 1; i <= 45; i++) {
      this.days.push({ id: i, name: `${i} Days` })
    }

    this.selectedInformation = this.service.selectedInformation;
  }

  async ngOnInit() {
    this.form = this.formBuilder.group({
      'examName': ['', Validators.compose([Validators.required])],
      'examType': ['', Validators.compose([Validators.required])],
      'examMode': ['', Validators.compose([Validators.required])],
      'examCode': ['', Validators.compose([Validators.required])],
      'examCodeO': [''], /* exam code field when the exam mode is both */
      'examPrice': ['', Validators.compose([Validators.required])],
      'duration': [this._duration, Validators.compose([Validators.required])],
      'attempts': ['', Validators.compose([Validators.required])],
      'paymentBy': [null], /* , Validators.compose([Validators.required]) */
      'optionsToReschedule': [false, Validators.compose([Validators.required])],
      'rescheduleDays': [null],
      'optionsToCancel': [false, Validators.compose([Validators.required])],
      'cancelDays': [null],
    });

    this.draftedForm = this.formBuilder.group({
      exams: this.formBuilder.array([])
    });

    const a = this.store.select(getExamType).subscribe(data => data == null ? this.store.dispatch<Action>(actions.loadExamType()) : this.examType = data.filter(ele => ele.active == true));
    const b = this.store.select(getExamMode).subscribe(data => data == null ? this.store.dispatch<Action>(actions.loadExamMode()) : this.examMode = data.filter(ele => ele.active == true));
    const c = this.store.select(getAttemptRules).subscribe(data => data == null ? this.store.dispatch<Action>(actions.loadAttemptRules()) : this.getAttemptRules(data));
    const d = this.store.select(getPaidBy).subscribe(data => data == null ? this.store.dispatch<Action>(actions.loadPaidBy()) : this.paymentBy = data);
    const subscriptions = [a, b, c, d];
    subscriptions.forEach((n: Subscription) => this.subscription.push(n))

    if (this.selectedInformation) {
      const a = this.eligibility$.subscribe((data: EligibilityRouteStore) => {
        try {
          if (data.examType?.length > 0 && data.examMode?.length > 0 && data.attemptRules?.length > 0 && data.paidBy?.length > 0) {
            const exams = this.selectedInformation.examDetail.exams.map(ele => {
              const examCodeO = ele.supportedExamMode.length > 1 ? ele.supportedExamMode.find(ele => ele.examModeTypeId == 2) : null;
              return {
                id: ele.id,
                examName: ele.name,
                examType: ele.type,
                examMode: ele.mode,
                examCode: ele.tenantCode,
                examCodeO: ele.mode == 3 ? examCodeO.examCode : null,
                examPrice: ele.price,
                duration: ele.durationOfExam,
                attempts: ele.noOfAttempts,
                paymentBy: ele.paymentBy,
                optionsToReschedule: ele.rescheduleAllowed,
                rescheduleDays: ele.rescheduleDays,
                optionsToCancel: ele.cancelAllowed,
                cancelDays: ele.cancelDays,
                attempRules: ele.attempRules
              }
            });
            exams.forEach(ele => {
              const attempts = JSON.parse(JSON.stringify(this.attempts));
              ele.attempRules.forEach((ele: number) => {
                const index = attempts.findIndex(n => n.id == ele);
                index > - 1 ? attempts[index].checked = true : null;
              })
              this.mockExam(ele, attempts);
            });
            this.query = JSON.parse(this.selectedInformation.examExpression);
            this.decodeQuery(this.query.rules, this.query.condition)
          }
        } catch (e) { }
      });
      this.subscription.push(a);
    }
  }

  decodeQuery(rules: RuleSet[], condition: string) {
    for (var i = 0; i < rules.length; i++) {
      var item: RuleSet = rules[i];
      if (item.rules == null) {
        this.expression =
          this.expression +
          this.logicExpression('(', item.field, item.operator, item.value, ')');
        if (i != rules.length - 1) {
          this.expression = this.expression + ' ' + condition + ' ';
        }
      } else {
        condition = item.condition;
        this.expression = this.expression + '(';
        this.decodeQuery(item.rules, condition);
        this.expression = this.expression + ')';
      }
    }
  }

  logicExpression(openParenthesis: string, field: string, operator: string, value: string | number | string[], closeParenthesis: string) {
    operator = '=';
    const expression = `${openParenthesis} ${field} ${operator} ${value} ${closeParenthesis}`;
    return expression;
  }

  back(): void {
    this.backStep.emit(true);
  }

  getAttemptRules(data: Array<GetAttemptRulesResponse>) {
    const _data = JSON.parse(JSON.stringify(data));
    const active = [];
    _data.forEach(ele => {
      if (ele.active) {
        ele.checked = false;
        active.push(ele);
      }
    })
    this.attempts = active;
    /* return new Promise(resolve => {
      this.service.getAttemptRules().subscribe((data: Array<GetAttemptRulesResponse>) => {
        const active = [];
        data.forEach(ele => {
          if (ele.active) {
            ele.checked = false;
            active.push(ele);
          }
        })
        this.attempts = active;
        resolve(true);
      }, (error: HttpErrorResponse) => {
        resolve(false);
      })
    }) */
  }

  mockExamModeChange(event: MatSelectChange, group: FormGroup): void {
    if (event.value == 3) {
      group.get('examCodeO').setValidators(Validators.compose([Validators.required]));
      group.get('examCodeO').updateValueAndValidity();
    } else {
      group.get('examCodeO').clearValidators();
      group.get('examCodeO').updateValueAndValidity();
    }
  }

  examModeChange(event: MatSelectChange): void {
    if (event.value == 3) {
      this.form.get('examCodeO').setValidators(Validators.compose([Validators.required]));
      this.form.get('examCodeO').updateValueAndValidity();
    } else {
      this.form.get('examCodeO').clearValidators();
      this.form.get('examCodeO').updateValueAndValidity();
    }
  }

  optionsToRescheduleChange(event: MatRadioChange): void {
    if (event.value) {
      this.form.get('rescheduleDays').setValidators(Validators.compose([Validators.required]));
      this.form.get('rescheduleDays').updateValueAndValidity();
    } else {
      this.form.get('rescheduleDays').clearValidators();
      this.form.get('rescheduleDays').updateValueAndValidity();
    }
  }

  optionsToCancelChange(event: MatRadioChange): void {
    if (event.value) {
      this.form.get('cancelDays').setValidators(Validators.compose([Validators.required]));
      this.form.get('cancelDays').updateValueAndValidity();
    } else {
      this.form.get('cancelDays').clearValidators();
      this.form.get('cancelDays').updateValueAndValidity();
    }
  }

  cancel(): void {
    this.router.navigateByUrl('/manage-eligiblity');
  }

  removeExam(i: number): void {
    const exams = this.draftedForm.get('exams') as FormArray;
    exams.removeAt(i);
    this.expressionExams.splice(i, 1);
  }

  mockExam(value: any, _attempts: Array<GetAttemptRulesResponse>): void {
    this.expressionExams.push(value);
    const exams = this.draftedForm.get('exams') as FormArray;
    const createItem = (): FormGroup => {
      const form = this.formBuilder.group({
        'id': [value.id ? value.id : 0],
        'examName': [value.examName, Validators.compose([Validators.required])],
        'examType': [value.examType, Validators.compose([Validators.required])],
        'examMode': [value.examMode],
        'examCode': [value.examCode, Validators.compose([Validators.required])],
        'examCodeO': [value.examMode == 3 ? value.examCodeO : '', value.examMode == 3 ? Validators.compose([Validators.required]) : Validators.compose([])],
        'examPrice': [value.examPrice, Validators.compose([Validators.required])],
        'duration': [value.duration, Validators.compose([Validators.required])],
        'attempts': [value.attempts, Validators.compose([Validators.required])],
        'paymentBy': [value.paymentBy],
        'optionsToReschedule': [value.optionsToReschedule, Validators.compose([Validators.required])],
        'rescheduleDays': [value.optionsToReschedule ? value.rescheduleDays : null, value.optionsToReschedule ? Validators.compose([Validators.required]) : Validators.compose([])],
        'optionsToCancel': [value.optionsToCancel, Validators.compose([Validators.required])],
        'cancelDays': [value.optionsToCancel ? value.cancelDays : null, value.optionsToCancel ? Validators.compose([Validators.required]) : Validators.compose([])],
      });
      // form.disable();
      return form;
    }
    exams.push(createItem());
    const attempts = JSON.parse(JSON.stringify(_attempts))
    const index = this.attemptedList.length;
    this.attemptedList.push({ index: index, attempts: attempts })
  }

  addExam(value: any): void {
    if (!this.form.valid) return;
    this.mockExam(value, this.attempts);
    this.form.reset();
    this.form.patchValue({
      'optionsToReschedule': false, 'rescheduleDays': null,
      'optionsToCancel': false, 'cancelDays': null,
      'paymentBy': null, 'duration': this._duration
    });
    this.form.get('rescheduleDays').clearValidators();
    this.form.get('rescheduleDays').updateValueAndValidity();
    this.form.get('cancelDays').clearValidators();
    this.form.get('cancelDays').updateValueAndValidity();
  }

  getSubmitState(): boolean {
    const draftedExam = this.draftedForm.controls.exams as FormArray;
    if (draftedExam.length >= 2) {
      const controls = draftedExam.controls as Array<FormGroup>;
      // const status = controls.every((ele: FormGroup) => ele.valid === true); /* This is returning empty string that's why I have commented this. */
      const status = [];
      controls.forEach(ele => ele.valid ? status.push(true) : null);
      return controls.length == status.length;
    } else if (this.form.valid) {
      return true;
    } else {
      return false;
    }
  }

  submit(status: boolean = true): void {
    const value = this.form.value;
    const exams: Array<ExamDetails> = [];
    const _attempts = [];

    if (this.form.valid) {
      this.attempts.forEach(ele => ele.checked ? _attempts.push(ele.id) : null);
      const supportedExamMode: Array<ExamMode> = [];
      value.examMode != 3 ?
        supportedExamMode.push({ examCode: value.examCode, examModeTypeId: value.examMode }) :
        value.examMode == 3 ?
          supportedExamMode.push(
            { examCode: value.examCode, examModeTypeId: 1 },
            { examCode: value.examCodeO, examModeTypeId: 2 }
          ) : null;

      const _exam: ExamDetails = {
        id: 0,
        name: value.examName,
        type: value.examType,
        tenantCode: value.examCode, //Exam code
        mode: value.examMode, //Online, Offline
        price: parseFloat(value.examPrice),
        durationOfExam: parseInt(value.duration),  //Exam duration
        parentId: 0, //To add the exam pass 0
        noOfAttempts: parseInt(value.attempts),
        rescheduleAllowed: value.optionsToReschedule,
        rescheduleDays: value.optionsToReschedule ? value.rescheduleDays : 0,
        cancelAllowed: value.optionsToCancel,
        cancelDays: value.optionsToCancel ? value.cancelDays : 0,
        paymentBy: value.paymentBy,
        active: true, //Default true
        displayExamResult: true,
        attempRules: _attempts,
        supportedExamMode: supportedExamMode
      }
      exams.push(_exam);
    }

    if (this.draftedForm.value?.exams.length > 0) {
      const draftedExam = this.draftedForm.controls.exams as FormArray;
      const controls = draftedExam.controls as Array<FormGroup>;
      controls.forEach((ele: FormGroup, i: number) => {
        if (!ele.valid) return;
        const __attempts = [];
        const a = this.attemptedList[i].attempts as Array<any>;
        a.forEach(ele => ele.checked ? __attempts.push(ele.id) : null);
        const supportedExamMode: Array<ExamMode> = [];
        ele.value.examMode != 3 ?
          supportedExamMode.push({ examCode: ele.value.examCode, examModeTypeId: ele.value.examMode }) :
          ele.value.examMode == 3 ?
            supportedExamMode.push(
              { examCode: ele.value.examCode, examModeTypeId: 1 },
              { examCode: ele.value.examCodeO, examModeTypeId: 2 }
            ) : null;

        const __exam: ExamDetails = {
          id: ele.value.id,
          name: ele.value.examName,
          type: ele.value.examType,
          tenantCode: ele.value.examCode, //Exam code
          mode: ele.value.examMode, //Online, Offline
          price: parseFloat(ele.value.examPrice),
          durationOfExam: parseInt(ele.value.duration),  //Exam duration
          parentId: 0, //To add the exam pass 0
          noOfAttempts: parseInt(ele.value.attempts),
          rescheduleAllowed: ele.value.optionsToReschedule,
          rescheduleDays: ele.value.optionsToReschedule ? ele.value.rescheduleDays : 0,
          cancelAllowed: ele.value.optionsToCancel,
          cancelDays: ele.value.optionsToCancel ? ele.value.cancelDays : 0,
          paymentBy: ele.value.paymentBy,
          active: true, //Default true
          displayExamResult: true,
          attempRules: __attempts,
          supportedExamMode: supportedExamMode
        }
        exams.push(__exam);
      });
      /* @Deprticated */
      /* this.draftedForm.value.exams.forEach((ele: any, i: number) => {
        const __attempts = [];
        const a = this.attemptedList[i].attempts as Array<any>;
        a.forEach(ele => ele.checked ? __attempts.push(ele.id) : null);
        const __exam: ExamDetails = {
          id: 0,
          name: ele.examName,
          type: ele.examType,
          tenantCode: ele.examCode, //Exam code
          mode: ele.examMode, //Online, Offline
          price: parseFloat(ele.examPrice),
          durationOfExam: parseInt(ele.duration),  //Exam duration
          parentId: 0, //To add the exam pass 0
          noOfAttempts: parseInt(value.attempts),
          rescheduleAllowed: ele.optionsToReschedule,
          rescheduleDays: ele.optionsToReschedule ? ele.rescheduleDays : 0,
          cancelAllowed: ele.optionsToCancel,
          cancelDays: ele.optionsToCancel ? ele.cancelDays : 0,
          paymentBy: ele.paymentBy,
          active: true, //Default true
          displayExamResult: true,
          attempRules: __attempts
        }
        exams.push(__exam);
      }); */
    }
    if (exams.length == 0) return;
    this.service.exams = exams;
    this.service.expressionQuery = JSON.stringify(this.query);
    this.service.addEligibilityRoute(status).subscribe((data) => {
      if (data) {
        this.service.steps = [];
        this.service.routeInformation = null;
        this.service.exams = [];
        this.service.examSubmitOptions = [];
        this.service.expressionQuery = '';
        this.store.dispatch<Action>(actions.loadRoutes());
        this.router.navigateByUrl('/manage-eligiblity');
      }
    }, (error: HttpErrorResponse) => {
      typeof error.message == 'object' ? this._snackBar.callSnackbaronError(JSON.stringify(error.message)) : this._snackBar.callSnackbaronError(error.message)
    })
  }

  _createExpression(): void {
    this.dialog.open(this.expressionDialog, {
      hasBackdrop: true,
      disableClose: true,
      minWidth: 768,
      maxHeight: '80vh',
      panelClass: 'expression-dialog'
    });
    const draftedExam = this.draftedForm.controls.exams as FormArray;
    if (this.form.valid && draftedExam.controls.length >= 2) {
      const unique = this.expressionExams.find(ele => ele.examCode == this.form.value?.examCode);
      unique ? null : this.expressionExams.push(this.form.value);
    }
  }

  timeConvert(duration: number): string {
    const num = duration;
    const hours = (num / 60);
    const _hours = Math.floor(hours);
    const minutes = (hours - _hours) * 60;
    const _minutes = Math.round(minutes);
    const __hours = _hours < 10 ? `0${_hours}` : _hours;
    const __minutes = _minutes < 10 ? `0${_minutes}` : _minutes;
    return `${__hours}:${__minutes}`;
  }

  mockIncrement(value: number, group: FormGroup): void {
    var duration = group.get('duration').value;
    if (duration < 600) {
      duration += value;
      group.get('duration').setValue(duration);
    }
  }

  mockDecrement(value: number, group: FormGroup): void {
    var duration = group.get('duration').value;
    if (duration > 31) {
      duration -= value;
      group.get('duration').setValue(duration)
    }
  }

  decrement(value: number): void {
    if (this._duration > 31) {
      this._duration -= value;
      this.form.get('duration').setValue(this._duration)
    }
  }

  increment(value: number): void {
    if (this._duration < 600) {
      this._duration += value;
      this.form.get('duration').setValue(this._duration);
    }
  }

  checkExamCode(form: FormGroup, controller: string): void {
    const opposite = (controller: string) => {
      switch (controller) {
        case 'examCode':
          return 'examCodeO';
        case 'examCodeO':
          return 'examCode';
      }
    }
    if (form.get(controller).value == form.get(opposite(controller)).value) {
      form.get(controller).setErrors({ duplicate: true });
    } else {
      form.get(controller).setErrors(null);
    }

    const draftedExam = this.draftedForm.controls.exams as FormArray;
    if (draftedExam.length == 0) return;
    const controls = draftedExam.controls as Array<FormGroup>;
    var status: boolean = false;
    controls.forEach((ele: FormGroup, i: number) => {
      ele.get('examCode').value == form.get(controller).value ||
        ele.get('examCodeO').value == form.get(controller).value ?
        status = true : status = false;
    });
    status ? form.get(controller).setErrors({ duplicate: true }) : form.get(controller).setErrors(null);
  }

  onlyPrice(event: KeyboardEvent) {
    const pattern = /[0-9\.\ ]/;
    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  onlyNumber(event: KeyboardEvent) {
    const pattern = /[0-9\ ]/;
    let inputChar = String.fromCharCode(event.charCode);
    if (event.keyCode != 8 && !pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  createExpression(): void {
  }

  ngOnDestroy(): void {
    this.subscription.forEach((n: Subscription) => n ? n.unsubscribe() : null);
  }
}

class RuleSet {
  field?: string;
  value?: string | string[] | number;
  operator?: string;
  entity?: string;
  condition?: string;
  rules?: RuleSet[];
}
