import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class IconService {
  dashboardIcon = 'dashboard';
  scheduleIcon ='feed'
  manageStateIcon = 'place';
  manageAttemptIcon = 'history';
  manageFormIcon = 'article';
  managaeUserRoleIcon = 'groups';
  manageAccommodationIcon = 'accessible';
  manageTypeExamIcon = 'description';
  manageEligibilityIcon = 'class';
  manageVoucherCodes = 'class';
  manageUserIcon = 'supervisor_account';
  cmsIcon = 'ballot';
  demographicManageIcon = 'manage_accounts';
  manageApplicationIcon='article';
  eligibilityRoutes='face';
  gift_Card='card_giftcard';
  ticket='flag';
  training='dashboard';
  candidateSearch='ballot'
  manageGrievance='how_to_vote';
  manageProfileRequest='person';
  manageTrainigUsers='supervisor_account';
  reciprocityRequestIcon='swap_calls';
  certificateRenewal='swap_calls';
  manageRegistry='gradient';
  duplicateCertificates='swap_calls';
  state_report='report';
  insert_chart_outlined='insert_chart_outlined';
  publish='publish'
  constructor() { }

}
