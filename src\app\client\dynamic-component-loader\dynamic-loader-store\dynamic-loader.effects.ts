import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { createEffect, Actions, ofType } from "@ngrx/effects";
import { forkJoin, Observable, of } from "rxjs";
import {
  map,
  catchError,
  concatMap,
  exhaustMap,
  switchMap,
} from "rxjs/operators";
import { setErrorMessage } from "../../state/shared/shared.actions";
import { Router } from "@angular/router";
import {
  genericDelete,
  fetchedData,
  fetchData,
  changeStatus,
  statusChangeSuccessfull,
  statusChangeUnsuccessfull,
  createGeneric,
  creationSuccessfull,
  deleteSuccessfull,
  deleteUnsuccessfull,
  getFormJsonAndResponse,
  setPopupConfig,
  creationUnsuccessfull,
  successfullyAssignedFormViewToCurrentUser,
  assignFormViewToCurrentUser,
  unsuccessfullyAssignedFormViewToCurrentUser,
  fetchStates,
  fetchedStates,
  updateAccount,
  updatedAccount,
  disableAccount,
} from "./dynamic-loader.actions";
import {
  genericPopupConfig,
  paramTypes,
  requestParam,
} from "../dynamic-component-loader.types";
import lodash from "lodash";
import { environment } from "src/environments/environment";
import { URL } from "src/app/core/url";
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService } from "src/app/core/global-user.service";
import { FormTypes } from "../../application/application.types";
import { StateLists } from "src/app/core/common-component/examroom-formbuilder/form-builder.types";
@Injectable({
  providedIn: "root",
})
export class DynamicComponentLoaderEffects {
  constructor(
    private httpClient: HttpClient,
    private actions$: Actions,
    public action$: Actions,
    private snackbar: SnackbarService,
    private global: GlobalUserService
  ) {}

  private preprocessAction(requestDetails): Observable<any> {
    if (requestDetails) {
      var fetchUrlArray = lodash.cloneDeep(requestDetails.fetchUrl);
      requestDetails?.requestParams?.forEach(
        (x: requestParam, index: number) => {
          if (x.paramType == paramTypes.Required)
            fetchUrlArray.splice(x.position, 0, x.paramValue?.toString());
        }
      );
      let FetchUrlArray =
        this.global.personEventId?.personFormVersion == null &&
        this.global.personEventId != undefined &&
        this.global.personEventId.formTypeId == FormTypes.Application &&
        (this.global.personEventId.stateId == StateLists.CO ||
          this.global.personEventId.stateId == StateLists.VA) &&
        JSON.stringify(fetchUrlArray) ==
          JSON.stringify(["client", "api", "form", "formsbyformtypeid"])
          ? ["client", "api", "form", "formbyformtypeidforversion"]
          : fetchUrlArray;
      var fetchUrl: string = URL.BASE_URL_SHORTER + FetchUrlArray.join("/");
      var filteredOptionalParams = requestDetails?.requestParams?.filter(
        (x: requestParam) => {
          return (
            x.paramType == paramTypes.Optional && x.paramValue != undefined
          );
        }
      );
      filteredOptionalParams?.forEach((x: requestParam, index: number) => {
        if (x.paramValue)
          fetchUrl +=
            (index == 0 ? "?" : "&") +
            x.paramName +
            "=" +
            x.paramValue.toString();
      });
      var returnObs: Observable<any>;
      switch (requestDetails.method) {
        case "GET":
          returnObs = this.httpClient.get(fetchUrl);
          break;
        case "POST":
          returnObs = this.httpClient.post(
            fetchUrl,
            requestDetails.requestBody
          );
          break;
        case "PUT":
          returnObs = this.httpClient.put(fetchUrl, requestDetails.requestBody);
          break;
        case "DELETE":
          returnObs = this.httpClient.delete(fetchUrl);
          break;
        default:
          returnObs = this.httpClient.get(fetchUrl);
      }
      return returnObs;
    }
  }

  effectivelyfetchData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchData),
      concatMap((action) => {
        return (
          Array.isArray(action.requestDetails)
            ? forkJoin(this.CreateObservableList(action.requestDetails))
            : this.preprocessAction(action.requestDetails)
        ).pipe(
          map((data) => {
            return fetchedData({
              widgetType: action.widgetType,
              moduleIndex: action.moduleIndex,
              requestDetails: action.requestDetails,
              response: data,
            });
          }),
          catchError((err) => of(setErrorMessage({ message: err.message })))
        );
      })
    )
  );

  effectivelyChangeStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(changeStatus),
      concatMap((action) => {
        return this.preprocessAction(action.requestDetails).pipe(
          map((data) =>
            statusChangeSuccessfull({
              cellClickEvent: action.cellClickEvent,
            })
          ),
          catchError((err) => {
            return of(
              statusChangeUnsuccessfull({
                cellClickEvent: action.cellClickEvent,
              })
            );
          })
        );
      })
    )
  );

  effectivelyCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(createGeneric),
      concatMap((action) => {
        return this.preprocessAction(action.requestDetails).pipe(
          map((data) =>
            data.message
              ? this.snackbar.callSnackbaronSuccess(data.message)
              : data
          ),
          catchError((err) => {
            this.snackbar.callSnackbaronError(err.message.message.error);
            return of(setErrorMessage({ message: err.message.message.error }));
          })
        );
      })
    )
  );

  public effectivelyUpdateAccount$ = createEffect(() => {
    return this.action$.pipe(
      ofType(updateAccount),
      switchMap((action) => {
        return this.httpClient
          .post(
            `${environment.baseUrl}client/api/Account/update-email?personId=${action.personId}&personTenantRoleId=${action.personTenantRoleId}&newEmailId=${action.newEmailId}`,
            {}
          )
          .pipe(
            map((data: boolean) => {
              this.snackbar.callSnackbaronSuccess("Updated Successfully!");
              return updatedAccount({ details: data });
            }),
            catchError((err) => {
              this.snackbar.callSnackbaronWarning(
                err.message.message.error.slice(
                  err.message.message.error.search("Content"),
                  -1
                )
              );
              return of(
                setErrorMessage({
                  message: err.message.message.error.slice(
                    err.message.message.error.search("Content"),
                    -1
                  ),
                })
              );
            })
          );
      })
    );
  });

  public effectivelyDisableAccount$ = createEffect(() => {
    return this.action$.pipe(
      ofType(disableAccount),
      switchMap((action) => {
        return this.httpClient
          .post(
            `${environment.baseUrl}client/api/Account/deactivate-user?personId=${action.personId}&personTenantRoleId=${action.personTenantRoleId}`,
            {}
          )
          .pipe(
            map((data: any) => {
              this.snackbar.callSnackbaronSuccess(
                "Candidate Deactivated Successfully!"
              );
              return updatedAccount({ details: data });
            })
          );
      })
    );
  });

  effectivelyDelete$ = createEffect(() =>
    this.actions$.pipe(
      ofType(genericDelete),
      exhaustMap((action) => {
        return this.preprocessAction(action.requestDetails).pipe(
          map((data) =>
            deleteSuccessfull({
              success: true,
              performedAction: action.performedAction,
              widgetRowIndex: action.widgetRowIndex,
              widgetColumnIndex: action.widgetColumnIndex,
            })
          ),
          catchError((err) =>
            of(
              deleteUnsuccessfull({
                success: false,
                performedAction: action.performedAction,
                widgetRowIndex: action.widgetRowIndex,
                widgetColumnIndex: action.widgetColumnIndex,
              })
            )
          )
        );
      })
    )
  );

  effectivelyAssignFormView$ = createEffect(() =>
    this.actions$.pipe(
      ofType(assignFormViewToCurrentUser),
      exhaustMap((action) => {
        return this.preprocessAction(action).pipe(
          map((data) => successfullyAssignedFormViewToCurrentUser()),
          catchError((err) => of(unsuccessfullyAssignedFormViewToCurrentUser()))
        );
      })
    )
  );

  effectivelyFetchStates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fetchStates),
      exhaustMap((action) => {
        return this.httpClient
          .get<Array<any>>(URL.BASE_URL_SHORTER + "client/api/state/3")
          .pipe(map((data) => fetchedStates({ states: data })));
      })
    )
  );

  effectivelyGetFormJSONandUserResponse = createEffect(() =>
    this.actions$.pipe(
      ofType(getFormJsonAndResponse),
      concatMap((action) => {
        var obsList: Observable<any>[] = this.CreateObservableList([
          action.requestDetailsOne,
          action?.requestDetailsTwo,
        ]);
        return forkJoin(obsList).pipe(
          map((data) => {
            var popupConfig: genericPopupConfig = {
              type: action.popupConfig.type,
              popupDataType: action.popupConfig.popupDataType,
              popupData: {
                formJSON: Array.isArray(data)
                  ? JSON.parse(data[0].formJson)
                  : JSON.parse(data),
                userResponse:
                  Array.isArray(data) && data.length > 1
                    ? JSON.parse(data[1][0]?.formResponse)
                    : null,
                element: action.element,
              },
            };
            return setPopupConfig({
              popupConfig,
            });
          }),
          catchError((err) => of(setErrorMessage({ message: err.message })))
        );
      })
    )
  );

  CreateObservableList(array: Array<any>): Array<Observable<any>> {
    var listOfObservable = [];
    array.forEach((x: any) => {
      if (x != undefined && x.fetchUrl) {
        let temp = this.preprocessAction(x);
        if (temp && temp != undefined) listOfObservable.push(temp);
      }
    });
    return listOfObservable;
  }
}
