import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Actions, act, createEffect, ofType } from "@ngrx/effects";
import { of, throwError } from "rxjs";
import { switchMap, map, catchError } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { consoleLoaderJSON } from "../../dynamic-component-loader/dynamic-component-loader.types";
import { fetchConsoleLoderJSON, setErrorMessage, fetchedConsoleLoaderJSON,fetchAccountAccess, fetchedAccountAccess, reportSpinBoardList, gotReportSpinBoardList, getuser, gotUser, UpdateAbsentDetails, GotAbsentDetails,BooketOrderStatusJson, getOrderStatus, gotOrderStatus } from "./shared.actions";
import { AbsentResponse, Access } from "./shared.state";
import { getUserDetails, gotUserDetails } from "./shared.actions";
import { URL } from 'src/app/core/url';
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService } from "src/app/core/global-user.service";

@Injectable({
    providedIn: "root",
})
export class SharedEffects {
    constructor(
        private httpClient: HttpClient,
        private actions$: Actions,
        private services:SnackbarService,
        private global:GlobalUserService
    ) { }

    effectivelyGetUserDetails$ = createEffect(() =>
        this.actions$.pipe(
            ofType(getUserDetails),
            switchMap((action) => {
                let url; 
                // Ensure emailId is a string if it exists
                
                    // For Proctor role, always use a valid email format
           
                    // Use personTenantRoleId if available
                 
      
                
                
                return this.httpClient
                    .get<any>(this.global.rolename.roleName =='Proctor' && this.global.rolename.tsmemail !=''? URL.ACCOUNT_BASE_URL +`getParameters?EmailId=${action.emailId}`:  URL.ACCOUNT_BASE_URL + `get-parameters-new?personTenantRoleId=${action.personTenantRoleId}`)
                    .pipe(
                        map((data) => {
                            return gotUserDetails({
                                userDetails: data,
                            });
                        }),
                        catchError((err) => {
                            return of(setErrorMessage({ message: err }));
                        })
                    );
            })
        )
    );

    effectivelyGetUser$ = createEffect(() =>
    this.actions$.pipe(
        ofType(getuser),
        switchMap((action) => {
            return this.httpClient
                .get<any>(
                   URL.BASE_URL +
                    `Account/getusers?personTenantRoleId=${action.personTentantId}`
                )
                .pipe(
                    map((data) =>
                        gotUser({
                            getuserDetails: data,
                        })
                    ),
                    catchError((err) => of(setErrorMessage({ message: err })))
                );
        })
    )
);
    effectivelyGetReportSpinBoardList$ = createEffect(() =>
        this.actions$.pipe(
            ofType(reportSpinBoardList),
            switchMap((action) => {
                return this.httpClient
                    .post<any>(
                       `${environment.reportUrlapi}metadata/search`,{username :action.username,org_id:action.org_id,dependent_object_version:action.dependent_object_version,include_auto_created_objects:action.include_auto_created_objects,include_dependent_objects:action.include_dependent_objects,include_details:action.include_details,include_headers:action.include_headers,include_hidden_objects:action.include_hidden_objects,include_incomplete_objects:action.include_incomplete_objects,include_visualization_headers:action.include_visualization_headers,record_offset:action.record_offset,record_size:action.record_size,include_stats:action.include_stats}
                    )
                    .pipe(
                        map((data) =>
                        gotReportSpinBoardList({
                            json: data
                        })
                        ),
                        catchError((err) =>{
                            this.services.callSnackbaronError(err.message.message.error.Message);
                            return of(setErrorMessage({ message: err.message.message.error.Message }));
                        } )
                    );
            })
        )
    );



    effectivelyFetchConsoleLoaderJSON$ = createEffect(() => this.actions$.pipe(
        ofType(fetchConsoleLoderJSON),
        switchMap((action) => {
            return this.httpClient.get<any>(`assets/jsons/console-loader-json.json`).pipe(
                map(data =>
                    fetchedConsoleLoaderJSON({
                        consoleLoaderJSON: data.json as consoleLoaderJSON
                    })),
                catchError(err =>
                    of(setErrorMessage({ message: err.message }))),
            );
        }),

    ));

    effectivelyFetchAccountAccess$ = createEffect(() => this.actions$.pipe(
        ofType(fetchAccountAccess),
        switchMap((action) => {
            return this.httpClient
                .get<any>(
                    environment.baseUrl +
                    `login/api/Access/list?personTenantRoleId=${action.personTenantRoleId}&consoleId=${action.consoleId}&stateId=${action.stateId}`
                )
                .pipe(
                    map(data =>
                        fetchedAccountAccess({
                            accessData: data as Array<Access>
                        })),
                    catchError(err =>
                        of(setErrorMessage({ message: err.message }))),
                );
        }),
    ));

    effectiveAbsentDetails$ = createEffect(()=>this.actions$.pipe(
        ofType(UpdateAbsentDetails),
        switchMap((action)=>{
            return this.httpClient
            .post<AbsentResponse>(
                environment.baseUrl +
                `client/api/` + `arkiv/absent`,
                action.body
              )
            .pipe(map(
                (data )=>
                GotAbsentDetails({
                     response:data
                })
            ),
            catchError((err) => {
              this.services.callSnackbaronError(err.error);
              return of(setErrorMessage({ message: err.message }));
            })
            
            )
        })
    ))
    effectiveGetBookletOrderStatus$ = createEffect(() => 
    this.actions$.pipe(
        ofType(getOrderStatus),
        switchMap((action) => {
            if(action.text =='booklet_inventory'){
                return  this.httpClient.post<BooketOrderStatusJson[]>(
                    `${environment.baseUrl}client/api/credentia/order-status`,action.body 
                )        .pipe(map(
                    (data) => 
                        gotOrderStatus({
                            response:data
                        }) 
                ),
                catchError((err) => {
                    this.services.callSnackbaronError(err.error);
                    return of(setErrorMessage({ message: err.message }));
                  })
            )   
            }else{
                return  this.httpClient.get<any[]>(
                    `${environment.baseUrl}client/api/form/QAC/grievance-action?indicator=S`
                )       .pipe(map(
                    (data) => 
                        gotOrderStatus({
                            response:data
                        }) 
                ),
                catchError((err) => {
                    this.services.callSnackbaronError(err.error);
                    return of(setErrorMessage({ message: err.message }));
                  })
            )
            }
      
     
        }
    ))
)
}

