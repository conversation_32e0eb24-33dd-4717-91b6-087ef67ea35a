import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes,Roles } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import { comparisionOperators } from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from 'src/app/core/common-component/single-entry-table/single-entry-table.types';

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Offspring,
            name: "Training Program Details",
            moduleID: "training-institute-details",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "manageAttemptIcon",
            routeRef: "/loader",
            rows: [
                {
                    class: "py-4",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",

                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicSingleEntryTables,
                                displayedColumns: [
                                    [
                                        <singleEntryTableTypes.column>{
                                            id: ["firstName", "middleName", "lastName"],
                                            name: "Training Program Admin",
                                            editable: false,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "contactNumber",
                                            name: "Contact Number",
                                            editable: true,
                                        },
                                        <singleEntryTableTypes.column>{
                                            id: "emailId",
                                            name: "Email Id",
                                            editable: true,
                                        },
                                    ],
                                ],
                                //  the below hard coding needs to be removed
                                requestDetails: [
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "traininginstitute",
                                            "admins",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Required,
                                                paramName: null,
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "id",
                                                position: 3,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                ],
                                backwardsDataDependency: true,
                                grandParentReference: "manage-states",
                            },
                        },
                    ],
                },
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ["tab-col"],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter:false,
                                    showAddButton: [false,false],
                                    showMatTabs: true,
                                    matTabs: [
                                        { id: 0, name: "Eligibility Routes" },
                                        { id: 1, name: "Candidates" },
                                        { id: 2, name: "Training Program Users" },
                                    ],
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "eligibilityName",
                                            name: "Eligibility Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "lastUpdatedDate",
                                            name: "Created Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "active",
                                            name: "Status",
                                            cellType: "toggle",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            showOnlyData:true,
                                            headerCssClasses: ['w-12'],
                                            cellCssClasses: [],
                                            statusUpdateDetails: {
                                                fetchUrl: ["client", "api", "state", "status"],
                                                requestBody: null,
                                                requestParams: null,
                                                method: "PUT",
                                            },
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "middleName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "link",
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "candidate-details", //off spring ID
                                                eventDataUseful: false,
                                                navigateToLabel: "Candidate Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: ["link-animation"],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "address",
                                            name: "Address",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "emailId",
                                            name: "Email ID",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "contactNumber",
                                            name: "Contact Number",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "createdDate",
                                            name: "Joined Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                    ],






//added



                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [ "w-4"],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "middleName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "", //off spring ID
                                                eventDataUseful: false,
                                                navigateToLabel: "",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            //cellCssClasses: ["link-animation"],
                                        },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "address",
                                        //     name: "Address",
                                        //     cellType: "text",
                                        //     headerType: "normal",
                                        //     headerAlign: "center",
                                        //     cellAlign: "center",
                                        //     headerCssClasses: [],
                                        //     cellCssClasses: [],
                                        // },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "emailId",
                                            name: "Email ID",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "contactNumber",
                                        //     name: "Contact Number",
                                        //     cellType: "text",
                                        //     headerType: "normal",
                                        //     headerAlign: "center",
                                        //     cellAlign: "center",
                                        //     headerCssClasses: [],
                                        //     cellCssClasses: [],
                                        // },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "createdDate",
                                        //     name: "Joined Date",
                                        //     cellType: "date",
                                        //     headerType: "normal",
                                        //     headerAlign: "center",
                                        //     cellAlign: "center",
                                        //     headerCssClasses: [],
                                        //     cellCssClasses: [],
                                        // },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                              <tableTypes.action>{
                                                icon: "person",
                                                type: tableTypes.actionTypes.Person,
                                                tooltip: "Emulate",
                                                color: "#7d7d7d",
                                          
                                                visibilityRule:[
                                                    // {
                                                    //     operator:tableTypes.Operators.OR,
                                                    //     elementProperty:"personId",
                                                    //     extractedFromGlobal:true,
                                                    //     compareWithOperator:tableTypes.comparisionOperators.EQUAL,
                                                    //     shouldHaveValue:9
                                                    
                                                    // },
                                                    {
                                                    operator:tableTypes.Operators.OR,
                                                    elementProperty:"roleId",
                                                    extractedFromGlobal:true,
                                                    compareWithOperator:tableTypes.comparisionOperators.NOTEQUAL,
                                                    shouldHaveValue:Roles.StateClient
                                                
                                                },
                                              
                                             
                                              
                                            ]
                                                },
                                                <tableTypes.action>{
                                                    icon: "card_giftcard",
                                                    type: tableTypes.actionTypes.Voucher,
                                                    tooltip: "Manage Vouchers",
                                                    color: "#7d7d7d",

                                                    visibilityRule:[
                                                        {
                                                            operator:tableTypes.Operators.OR,
                                                            elementProperty:"personId",
                                                            extractedFromGlobal:true,
                                                            compareWithOperator:tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue:2133825
                                                        
                                                        },
                                                        {
                                                        operator:tableTypes.Operators.OR,
                                                        elementProperty:"personId",
                                                        extractedFromGlobal:true,
                                                        compareWithOperator:tableTypes.comparisionOperators.EQUAL,
                                                        shouldHaveValue:2102699
                                                    
                                                    }
                                                ]
                                                    },
                                              <tableTypes.action>{
                                                icon: "email",
                                                type: tableTypes.actionTypes.email,
                                                tooltip: "Resend Invite Email",
                                                color: "#7d7d7d",
                                                visibilityRule: [
                                                
                                                    {
                                                        operator:tableTypes.Operators.OR,
                                                        elementProperty:"roleId",
                                                        extractedFromGlobal:true,
                                                        compareWithOperator:tableTypes.comparisionOperators.NOTEQUAL,
                                                        shouldHaveValue:Roles.StateClient
                                                    
                                                    },
                                                ],
                                                requestDetails: [
                                              
                                                    {
                                                      fetchUrl: [
                                                        "client",
                                                        "api",
                                                        `Account`,
                                                        "resend-invite-email",

                                                      ],
                                                      requestBody: null,
                                                      requestParams: [
                                                        {
                                                          paramType: paramTypes.Optional,
                                                          paramName: "personId",
                                                          paramValue: null,
                                                          extractedFromElement: true,
                                                          elementPropertyToBeExtracted: "personId",
                                                        },
                                                        {
                                                          paramType: paramTypes.Optional,
                                                          paramName: "personTenantRoleId",
                                                          paramValue: null,
                                                          extractedFromElement: true,
                                                          elementPropertyToBeExtracted: "personTenantRoleId",
                                                        },
                                                      ],
                                                      method: "POST",
                                                    }
                                                ],
                                              }
                                              
                                            ],
                                            linkMetaData: {
                                              navigateToType: moduleTypes.Independent,
                                              navigateTo: "scheduleExam",
                                              eventDataUseful: false,
                                              navigateToLabel: "Training Program Details",
                                              requestDetails: {
                                                fetchUrl: [],
                                                requestBody: null,
                                                requestParams: [
                                                  {
                                                    paramType: paramTypes.RouteParam,
                                                    paramName: null,
                                                    paramValue: null,
                                                    extractedFromElement: true,
                                                    elementPropertyToBeExtracted: "candidateId",
                                                  },
                                                  {
                                                    paramType: paramTypes.RouteParam,
                                                    paramName: null,
                                                    paramValue: null,
                                                    extractedFromElement: true,
                                                    elementPropertyToBeExtracted: "id",
                                                  },
                                                  
                                                ],
                                              },
                                            },
                                            
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [""],
                                          },

                                          
                                    ],



















                                    
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "traininginstitute",
                                            "eligibilityroutes",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Required,
                                                paramName: null,
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "id",
                                                position: 3,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "traininginstitute",
                                            "candidates",
                                        ],


                                        requestBody: null,
                                        requestParams: [
                                             {
                                                paramType: paramTypes.Optional,
                                                paramName: 'id',
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "id",
                                                extractedFromGlobal: true,
                                                position: 4,
                                             },
                                             {
                                                paramType: paramTypes.Optional,
                                                paramName: 'id',
                                                paramValue: null,
                                                extractedFromElement: true,
                                                elementPropertyToBeExtracted: "tenantId",
                                                extractedFromGlobal: true,
                                                position: 4,
                                             } 
                                        ],
                                        method: "GET",
                                    },


                                   

                                 

                                    {
                                        fetchUrl: [
                                          "client",
                                          "api",
                                          "traininginstitute",
                                          "user",

                                        ],
                                        requestBody: null,
                                        requestParams: [
                                          {
                                            paramType: paramTypes.Optional,
                                            paramName: "trainingInstituteId",
                                            paramValue: null,
                                            extractedFromElement: true,
                                            elementPropertyToBeExtracted: "id",
                                          },
                                          {
                                            paramType: paramTypes.Optional,
                                            paramName: "showAll",
                                            paramValue: "true",
                                           
                                          },
                                        ],
                                        method: "GET",
                                      },
















                                ],
                                backwardsDataDependency: true,
                                grandParentReference: new Map([
                                  [Roles.SuperAdmin, 'manage-states'],
                                  [Roles.OperationStaff, 'manage-training-programs']
                                ]),
                            },
                        },
                    ],
                },
            ],
        }
    ]
}