import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { GlobalUserService } from 'src/app/core/global-user.service';
import { getProfileDetals, updateProfile } from './state/edit-profile.action';
import { completeProfileStatus, upadatedProfileMessage } from './state/edit-profile.selector';
import { profileState } from './state/edit-profile.state';
import { profileDetails, updateDetails } from './state/edit-profile.types';

@Component({
  selector: 'app-edit-profile',
  templateUrl: './edit-profile.component.html',
  styleUrls: ['./edit-profile.component.scss']
})
export class EditProfileComponent implements OnInit {
  
  editForm: FormGroup;
  updateDetails:updateDetails;
  profileDetails:profileDetails;
  personTenantRoleId:any;
  validations = {
    'zipCode': [
      { type: 'required', message: 'Zip Code is required' },
      { type: 'pattern', message: 'Must contain 5 digits' }
    ],
    'city': [
      { type: 'pattern', message: 'City should not contain numbers' },
      { type: 'required', message: 'City is required' },
    ],
    'phoneNumber': [
      { type: 'required', message: 'Phone number is required' },
      { type: 'minlength', message: 'Enter a valid phone number' },
      { type: 'maxlength', message: 'Enter a valid phone number' }
    ],
    'emailId': [
      { type: 'pattern', message: 'email must be valid' }
    ],
    'address': [
      { type: 'required', message: 'Address is required' }
    ],
    'gender': [
      { type: 'required', message: 'Gender is required' }
    ],
  };

  constructor(
    private formBuilder: FormBuilder,
    public store: Store<profileState>,
    private global:GlobalUserService,
    private router:Router) { }

  ngOnInit(): void {
    this.global.userDetails.subscribe(data=>{
      if(data){
        this.personTenantRoleId=data;
        this.store.dispatch(getProfileDetals({personTenantRoleId:this.personTenantRoleId.personTenantRoleId}))
    this.store.select(completeProfileStatus).subscribe(data=>{
      if(data){
        this.profileDetails={
          firstName:data.firstName,
          middleName:data.middleName,
          lastName:data.lastName,
          phonenumber:data.contactNumber,
          email:data.emailId
        }
        this.editProfile();
      }
    })
      }
    })
    this.editForm = this.formBuilder.group({
      phoneNumber: [ this.profileDetails?this.profileDetails.phonenumber:"", [Validators.required,
      Validators.minLength(12), Validators.maxLength(14)]],
      emailId: [this.profileDetails?this.profileDetails.email:"", [Validators.required]],
      firstName: [this.profileDetails?this.profileDetails.firstName:"", [Validators.required]],
      lastName: [ this.profileDetails?this.profileDetails.lastName:"", [Validators.required]],
      middleName: [this.profileDetails?this.profileDetails.middleName:"",  [Validators.required]],
    });
    
   
  }

  editProfile(){
    this.editForm = this.formBuilder.group({
      phoneNumber: [ this.profileDetails?this.profileDetails.phonenumber:"", [Validators.required,
      Validators.minLength(12), Validators.maxLength(14)]],
      emailId: [{value:this.profileDetails?this.profileDetails.email:"", disabled: true }],
      firstName: [this.profileDetails?this.profileDetails.firstName:"", [Validators.required]],
      lastName: [ this.profileDetails?this.profileDetails.lastName:"", [Validators.required]],
      middleName: [this.profileDetails?this.profileDetails.middleName:""],
    });
  }

  submit(){
    this.editForm.markAllAsTouched();
    if (this.editForm.valid) {
      this.updateDetails={
        firstName:this.editForm.controls.firstName.value,
          middleName:this.editForm.controls.middleName.value,
          lastName:this.editForm.controls.lastName.value,
          phonenumber:this.editForm.controls.phoneNumber.value,
          id:this.personTenantRoleId.personId,
          tenantId:this.personTenantRoleId.clientORtenantId
      }
      this.store.dispatch(updateProfile({newDetails:this.updateDetails}))
      this.store.select(upadatedProfileMessage).subscribe(data=>{
        if(data){
          this.store.dispatch(getProfileDetals({personTenantRoleId:this.personTenantRoleId.personTenantRoleId}))
        }
      })
    }      
  }

}
