var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq || {widgetcode: "siq82ea5af9567ced271524dd594b2cda6d38d1d16c39814bda130b05a3445c6a29", values:{},ready:function(){}};var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;s.src="https://salesiq.zohopublic.com/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);
 $zoho.salesiq.ready = function()
 {
 $zoho.salesiq.customfield.add( 
 { 
    "name" :"Select State", 
    "hint" :"Select State", 
    "required" :"true", 
    "type" :"selectbox", 
    "visibility" :"both", 
    "options" : [{ 
      "text" :"Alabama", 
      "value":"Alabama" 
    },
    { 
      "text" :"Alaska", 
      "value":"Alaska" 
    },
    { 
      "text" :"California", 
      "value":"California" 
    },
    { 
      "text" :"Colorado", 
      "value":"Colorado" 
    },
    { 
      "text" :"District of Columbia", 
      "value":"District of Columbia" 
    },
    { 
      "text" :"Georgia", 
      "value":"Georgia" 
    },
    { 
      "text" :"Maryland", 
      "value":"Maryland" 
    },
    { 
      "text" :"Mississippi", 
      "value":"Mississippi" 
    },
    { 
      "text" :"North Carolina", 
      "value":"North Carolina" 
    },
    { 
      "text" :"Nevada", 
      "value":"Nevada" 
    },
    { 
      "text" :"Pennsylvania", 
      "value":"Pennsylvania" 
    },
    { 
      "text" :"Rhode Island", 
      "value":"Rhode Island" 
    },
    { 
      "text" :"South carolina", 
      "value":"South carolina" 
    },
    { 
      "text" :"Virginia", 
      "value":"Virginia" 
    },
    { 
      "text" :"Washington", 
      "value":"Washington" 
    }
    ] 
 });
  
 $zoho.salesiq.customfield.add( 
 { 
    "name" :"Select Exam", 
    "hint" :"Select Exam", 
    "required" :"true", 
    "type" :"selectbox", 
    "visibility" :"both", 
    "options" : [{ 
      "text" :"MACE", 
      "value":"MACE" 
    },
    { 
      "text" :"NNAAP", 
      "value":"NNAAP" 
    },
    { 
      "text" :"HHA", 
      "value":"HHA" 
    },
    { 
      "text" :"Other", 
      "value":"Other" 
    }
    ] 
 });
 let personDetails =JSON.parse(sessionStorage.userData)
 $zoho.salesiq.visitor.email(personDetails.email);
 $zoho.salesiq.visitor.contactnumber(personDetails.phone_number);
 $zoho.salesiq.visitor.name(personDetails.given_name);
  
  
 }