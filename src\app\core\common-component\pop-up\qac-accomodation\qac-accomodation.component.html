<div class="ww" *ngIf="data.id !=1">
    <div class="flex py-3 borderr">
        <div class="">
            <h3>QAC Response</h3>
        </div>
        <div class="ml-auto cursor-pointer status" (click)="close()">
            <mat-icon>close</mat-icon>
        </div>
    </div>
    <div class="p-3">
        <form [formGroup]="formGroup">
            <div class="grid-container">
                <div *ngIf="data.id !=1">
                    <mat-form-field appearance="outline" class="w-full">
                        <mat-select formControlName="accomodationtype" multiple placeholder="Accomodation Type">
                        <mat-option
                            *ngFor="let option of accomodationList"
                            [value]="option.id"
                        >
                            {{ option.name }}
                        </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                
                <div class="">
                    <mat-form-field appearance="outline" class="editor textEditor1" >
                        <textarea class="editor flex-wrap h-full " matInput formControlName="response" placeholder="QAC Response" cols="70"></textarea>
                    </mat-form-field>
                </div>
            </div>
        </form>
    </div>
    <div class="flex p-3">
        <div class="ml-auto">
            <button class="text-xs btn-3 lh mr-3" mat-button type="button" (click)="close()">No</button>
            <button class="btn-11 text-xs" mat-button type="button" [disabled]="!formGroup.valid" (click)="save()" >Yes</button>
        </div>
    </div>
</div>

<div class="" *ngIf="data.id ==1">
    <div class="flex py-3 borderr">
        <div class="">
            <h3>QAC Response</h3>
        </div>
        <div class="ml-auto cursor-pointer status" (click)="close()">
            <mat-icon>close</mat-icon>
        </div>
    </div>
    <div class="p-3">
        <form [formGroup]="formGroup">
            <div class="grid-container">
                <div class="text-center font-medium mb-3">Candidate Name :&nbsp;<strong>{{data.items.firstName}} {{data.items.lastName}}</strong></div>
                <div class="">
                    <mat-form-field appearance="outline" class="editor textEditor1 " >
                        <textarea readonly class="editor flex-wrap h-full" matInput formControlName="response" placeholder="QAC Response" cols="70"></textarea>
                    </mat-form-field>
                </div>

            </div>
        </form>
    </div>
   
</div>


