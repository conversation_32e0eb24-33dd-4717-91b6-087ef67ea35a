<div class="-mt-5 t-xs" exaiContainer>
  <div class=" shadow-none justify-start w-full " gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
    <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" class='eligibility-list px-4 pt-6 exam'
      fxLayoutGap.lt-sm="0">



      <form [formGroup]="Validators" #form="ngForm" class="w-full">
        <div class="shadow-none justify-start w-full" gdColumn="1 / 3" gdColumn.lt-md="1" gdColumn.lt-sm="1">
          <div class="exam">
            <h2 class="px-2 pt-3 text-xs font-bold ">2. Select Your Exam Date Range</h2>
          </div>
          <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px" class="exam flex-wrap" fxLayoutGap.lt-sm="0">
            <!-- TIME ZONE PICKER -->
            <!-- <mat-form-field class="mat-field-1-4" appearance="outline">
              <mat-label class="text-xs">Select Time Zone</mat-label>
              <mat-select class="text-xs" formControlName="timezone">
                <mat-option class="text-xs" *ngFor="let time of timezones" [value]="time">
                  {{ time.timeZoneStandardName }}
                </mat-option>
              </mat-select>
            </mat-form-field> -->
            <!-- END TIME ZONE PICKER -->


            <!-- DATE PICKER -->
            <mat-form-field class="mat-field-1-4" appearance="outline">
              <mat-label class="text-xs">Select Your Exam Date Range</mat-label>
              <mat-date-range-input [formGroup]="range" [rangePicker]="picker" [min]="minDate" [max]="maxDate">
                <input matStartDate formControlName="start" class="start" autocomplete="off" required
                  placeholder="Start date">
                <input matEndDate formControlName="end" class="end" autocomplete="off" required placeholder="End date">
              </mat-date-range-input>
              <mat-datepicker-toggle matSuffix [for]="picker" (click)="changeCalendar($event)"></mat-datepicker-toggle>
              <mat-date-range-picker #picker></mat-date-range-picker>
            </mat-form-field>

            <!-- LOCATION PICKER -->

          </div>
        </div>
      </form>
    </div>
    <div class="exam">
      <h2 class="px-4 pt-3 text-xs font-bold ">3. Search by Mileage Radius or Test Center</h2>
    </div>
    <mat-radio-group [formControl]="radioselect">
      <ng-container *ngFor="let examType of examTypeModels; let i = index">
        <mat-radio-button class=" px-4 mb-3 pt-1 t-xs" [checked]="examType.checked" [value]="examType.name"
          (change)="getSelectedRoute(examType.id)">{{ examType.name }}
        </mat-radio-button>
      </ng-container>
    </mat-radio-group>
    <div class=" pt-2 px-3 exam flex-wrap" gdColumns="1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr"
      gdColumns.lt-sm="1fr" gdGap="12px" exaiContainer [formGroup]="testCenterLocation">
      <div class="flex " gdColumn="2 / 5" gdColumn.lt-md="2 / 5" gdColumn.lt-sm="1" fxLayout="column"
        fxLayout.lt-sm="column" *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
        <div class="text-center mt-2 tx font-bold ">
          Search for Test Center Near You by Mileage Radius
        </div>
        <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px" fxLayoutGap.lt-sm="0">
          <mat-form-field fxFlex='50%' class="exai-flex-form-field mt-2" appearance="outline">
            <mat-label class="text-xs fontColor2">Your Address</mat-label>
            <input class="form-control text-xs" formControlName="testAddress" placeholder="Your Address:"
              autocomplete="off" matInput>
          </mat-form-field>
          <mat-form-field fxFlex='50%' class="exai-flex-form-field mt-2" appearance="outline">
            <mat-label class="text-xs fontColor2">Mileage Radius</mat-label>
            <input class="form-control text-xs" formControlName="testRadius" placeholder="Radius:" type="number" min="0"
              autocomplete="off" matInput>
          </mat-form-field>
        </div>
        <div class="flex justify-center">
          <button [disabled]="testCenterLocation.status === 'INVALID' || isSearching || !form.form.valid"
            class="btn-1 mb-2 text-xs" mat-raised-button type="button" (click)="getTimeSlots()">
            <ng-container *ngIf="isSearching; else searchText">
              <div class="pulse-dots">
                <div class="dot dot1"></div>
                <div class="dot dot2"></div>
                <div class="dot dot3"></div>
                <div class="dot dot4"></div>
              </div>
            </ng-container>
            <ng-template #searchText>Search by Radius</ng-template>
          </button>
        </div>


      </div>

      <!-- <div class="flex text-sm font-bold justify-center items-center" gdColumn="3 / 4" gdColumn.lt-md="3 / 4"
        gdColumn.lt-sm="1" fxLayout="row" fxLayout.lt-sm="column">
        OR
      </div> -->
      <div class="flex " gdColumn="2 / 5" gdColumn.lt-md="2 / 5" gdColumn.lt-sm="1" fxLayout="column"
        fxLayout.lt-sm="column" *ngIf="radioselect.value == 'Search a Test Center by Code'">
        <div class="text-center mt-2 tx font-bold">
          Search for a Test Center Near You by Code
        </div>
        <mat-form-field class="exai-flex-form-field mt-2" appearance="outline">
          <!-- <span class="INFTEXT mb-1  " id="basic-addon3">INF</span> -->
          <mat-label class="text-xs fontColor2">Test Center Code</mat-label>
          <input class="form-control" formControlName="testId" matInput autocomplete="off">
        </mat-form-field>
        <div class="flex justify-center">
          <button [disabled]="isSearching" class="btn-1 mb-2 text-xs" mat-raised-button type="button"
            (click)="getTimeSlots()">
            <ng-container *ngIf="isSearching; else searchText">
              <div class="pulse-dots">
                <div class="dot dot1"></div>
                <div class="dot dot2"></div>
                <div class="dot dot3"></div>
                <div class="dot dot4"></div>
              </div>
            </ng-container>
            <ng-template #searchText>Search by Code</ng-template>
          </button>
        </div>
      </div>
    </div>
    <hr class="mr-5 ml-5 line" />


    <div fxLayout="row wrap" *ngIf="isSelectDate">
      <ng-container *ngIf="TSMSLOT ==true">
        <div [fxFlex]="100 / 10 + '%'" *ngFor="let aSlotDate of availableDates">
          <button id="{{'button'}}" class=" px-4 ml-3 mb-3 pt-3 pb-3 my-2 state slots2 buttom6"
            [ngClass]="{ active: (step | date: 'MM/dd/yyyy':'+0000') == (aSlotDate.day | date: 'MM/dd/yyyy':'+0000') }"
            (click)="setTestCentersForSelectedDate(aSlotDate.day);selectedDateTime(aSlotDate);selectedDate(aSlotDate)"
            mat-stroked-button color="light">
            {{ aSlotDate.day | date: "MM/dd/yyyy":'+0000'}}<br />
          </button>
        </div>
      </ng-container>
      <ng-container *ngIf="TSMSLOT !=true">
        <div [fxFlex]="100 / 10 + '%'" *ngFor="let aSlotDate of availableDates">
          <button id="{{'button'}}" class=" px-4 ml-3 mb-3 pt-3 pb-3 my-2 state slots2 buttom6"
            [ngClass]="{ active: step == aSlotDate.slotDate }"
            (click)="setTestCentersForSelectedDate(aSlotDate.slotDate);selectedDateTime(aSlotDate);selectedDate(aSlotDate)"
            mat-stroked-button color="light">
            {{ aSlotDate.slotDate | date: 'M/d/y'}}<br />
          </button>
        </div>
      </ng-container>
    </div>
    <hr class="mr-5 ml-5 line" />
    <!--  TEST CENTERS -->
    <div class="mat-testCenter1  mb-2" *ngIf="isSelectDate">
      <div class="pl-4">
        <div class="accord1" *ngFor="let item of selectedDateTestCenters,let i = index">
          <mat-expansion-panel *ngIf="TSMSLOT ==true" class="mt-4" [expanded]="expanded === 0" (opened)="setStep(0);"
            hideToggle>
            <mat-expansion-panel-header class="header">
              <mat-panel-title class="titled t-xs minimise" matTooltip={{item.testSiteName}}>
                {{item.testCenter}}
              </mat-panel-title>
              <mat-panel-title class=" ml-3 titled  minimise" matTooltip={{item.testSiteAddress}}>
                {{item.address}}
              </mat-panel-title>
              <mat-panel-title *ngIf="item.getMap !=''" class="ml-3 titled" matTooltip={{item.getMap}}>
                <a class="link" (click)="getDirection(item.getMap)">GetDirections</a>
              </mat-panel-title>
              <mat-panel-title class=" ml-3 titled"
                *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
                Distance {{item.distance}} miles
              </mat-panel-title>
            </mat-expansion-panel-header>
            <hr class="-mr-6 -ml-6" />
            <div class=" flex overflow-auto  -ml-4 mt-3">
              <ng-container>
                <div *ngFor="let time of item.siteSlot">
                  <button
                    *ngIf="(time.examdate | date: 'MM/dd/yyyy' :'+0000')==(selectedTestCenterSoltDate| date: 'MM/dd/yyyy':'+0000')"
                    class=" mr-3 mb-3 state slots2 buttom6" mat-stroked-button color="light"
                    (click)=" selectedId(item,time)" [ngClass]="{active: isActive(time)}">
                    {{ time.slotime }}
                  </button>

                  <div [ngClass]="(time.examCode =='VS-PR' || time.examCode =='CBT-WR')?'highlight':'highlight1'" class="ml-3 -mt-2 t-xs ">{{time.examCode
                    =='VS-PR'?' In Person Virtual':time.examCode
                    =='CBT-WR'?' In Person CBT ':'In Person'}}</div>

                </div>
              </ng-container>
            </div>
            <div *ngIf="checkbox" class="p-3">
              <mat-checkbox (change)='showOptions($event)'></mat-checkbox><span class="ml-1">{{AcceptCondidtion}}</span>
            </div>
          </mat-expansion-panel>
          <mat-expansion-panel *ngIf="TSMSLOT !=true" class="mt-4" [expanded]="expanded === 0" (opened)="setStep(0);"
            hideToggle>
            <mat-expansion-panel-header class="header">
              <mat-panel-title class="titled t-xs minimise" matTooltip={{item.testSiteName}}>
                {{item.testSiteName}}
              </mat-panel-title>
              <mat-panel-title class=" ml-3 titled  minimise" matTooltip={{item.testSiteAddress}}>
                {{item.testSiteAddress}}
              </mat-panel-title>
              <mat-panel-title *ngIf="item.directions !=''" class="ml-3 titled" matTooltip={{item.directions}}>
                <a class="link">GetDirections</a>
              </mat-panel-title>
              <mat-panel-title *ngIf="item.directions ==''" class="ml-3 titled" matTooltip="No Directions Found">
                <a class="link">GetDirections</a>
              </mat-panel-title>
              <mat-panel-title class=" ml-3 titled"
                *ngIf="radioselect.value == 'Search a Test Center by Mileage Radius'">
                Distance {{item.distanceTo}} miles
              </mat-panel-title>
            </mat-expansion-panel-header>
            <hr class="-mr-6 -ml-6" />
            <div class=" flex overflow-auto  -ml-4 mt-3">
              <ng-container>
                <div *ngFor="let time of item.slots">
                  <button *ngIf="time.strSlotDate==selectedTestCenterSoltDate" class=" mr-3 mb-3 state slots2 buttom6"
                    mat-stroked-button color="light" (click)=" selectedId(item,time)"
                    [ngClass]="{active: isActive(time)}">
                    {{ time.strSlotTime }}
                  </button>

                </div>
              </ng-container>
            </div>


          </mat-expansion-panel>
        </div>
      </div>
    </div>
    <!-- END TEST CENTERS -->
  </div>
</div>


<div fxLayout="column" fxLayoutAlign="start" class="ml-2">
  Disclaimer: "Please note that your exam slot is not reserved until
  payment is processed and you receive a confirmation email."
</div>


<!-- PAYMENT BOTTOM PART -->
<div fxLayout="row" fxLayoutAlign="end center" *ngIf="displayPaymentMethods" class="flex justify-end" fxLayoutGap="8px">


  <button *ngIf="!Reschedule" mat-button type="button" class="btn-2 mb-4 -mt-1 mr-4 t-xs" (click)="checkalreadyinCart()"
    [disabled]="!displayPaymentMethods">
    Add Cart
  </button>
  <button *ngIf="!Reschedule && examstatusId != this.global.scheduling_error" mat-button
    class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button" (click)="getPayment()" [disabled]="!displayPaymentMethods">
    Pay Now
  </button>
  <button
    *ngIf="((Reschedule && examstatusId != this.global.scheduling_error) && Reschedule && examstatusId != this.global.Exam_Cancelled)&& rescheduleloading==false && displayPaymentMethods"
    mat-button class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button" (click)='reschedule()'>
    Reschedule
  </button>
  <button
    *ngIf="(examstatusId == this.global.scheduling_error || examstatusId == this.global.Exam_Cancelled)&&scheduleLoading==false && displayPaymentMethods"
    mat-button class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button" (click)='retry_Schedule_Without_payment()'>
    Schedule
  </button>

  <div *ngIf="rescheduleloading||scheduleLoading">

    <div class="spinner__loading">
      <div>
        <mat-progress-spinner diameter="60" [value]="value" mode="determinate">
        </mat-progress-spinner>
      </div>
    </div>
  </div>


</div>
<!-- END PAYMENT BOTTOM PART -->