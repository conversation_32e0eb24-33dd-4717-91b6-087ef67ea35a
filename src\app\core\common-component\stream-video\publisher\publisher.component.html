<div class="w-full h-full flex flex-col" [class.hidden]="isLoading">
    <div class="h-full height"  [id]="streamId" #elementRef></div>
    <div class="absolute p-3">
        <div class="flex flex-row space-x-2 justify-end items-center">
          <button class="switchButton" *ngIf="archiveId" mat-icon-button (click)="switch()" >
            <img style="width: 32px;" src="assets/img/Icons/switch-camera.png">
            <!-- <span class="material-icons" style=" color:#ed1b0c; font-size: 40px;">cameraswitch</span> -->
          </button>
          <span class="font-semibold text-red-600 animate-pulse RECORDING" *ngIf="archiveId">
            RECORDING...
          </span>

          
        </div>
    </div>
</div>
<ng-container *ngIf="isLoading" [ngTemplateOutlet]="loading" >
</ng-container>

<ng-template #loading>
  <div class="flex justify-center">
      <mat-progress-spinner class="calenderSpinner flex justify-center " color="primary"
      diameter="60" mode="indeterminate" >
      </mat-progress-spinner>
  </div>
</ng-template>