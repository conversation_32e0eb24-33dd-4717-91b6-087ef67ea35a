import { console<PERSON>oa<PERSON><PERSON><PERSON><PERSON> } from "../../dynamic-component-loader/dynamic-component-loader.types";
import { CartItem } from "../../schedule/state/models/cartItem";

export interface SharedState {
  showLoading: boolean;
  errorMessage: string;
  userData: userDetails,
  consoleLoaderJSON: consoleLoaderJSON,
  cartItems: CartItem[],
  accessData: Array<Access>
  userdetails: userDetails,
  decodeInfo: decodedIdentityToken,
  reportJSON: ReportJSON
  getuserdata:getUsers,
  refreshData:boolean,
  response:boolean
  BookletOrderStatusInformation:BooketOrderStatusJson[]
}

export const initialState: SharedState = {
  showLoading: false,
  errorMessage: '',
  userData: null,
  consoleLoaderJSON: null,
  cartItems: [],
  accessData: [],
  userdetails: null,
  decodeInfo: null,
  reportJSON :null,
  getuserdata:null,
  refreshData:null,
  response:null,
  BookletOrderStatusInformation:null
}

export interface userDetails {
  accessToken: string;
  identityToken: string;
  refreshToken: string;
  decodedIdentityToken: decodedIdentityToken;
  tokenType: string;
  state: string;
  personId: number;
  roles: Array<role>;
  stateId: number,
  stateName: string;
  clientId: number;
  stateCode:string;
  cantest?:boolean
}

export interface getUser {
    personTenantRoleId: number,
    personId: number,
    tenantId: number,
    personRoleId: number,
    firstName: "",
    lastName: "",
    middleName: "",
    fullName: null,
    contactNumber: "+",
    tenantName: "",
    roleName: "",
    address: "",
    emailId: "",
    createdDate: "",
    active: true,
    stateName: "",
    stateId: number,
    id: number,
    ssn: "",
    dateofBirth: "",
    isRegistryEnabled: null,
    isShowFullSSN: boolean,
    fullSSN: null
  
}
 export interface getUsers {
   userDetails:getUser[]
 }

export interface role {
  roleId?: number;
  tenantId?: number;
  roleName?: string;
  stateCode?:string
  personTenantRoleId?: number;
}

export interface Address {
  formatted: string;
}

export interface decodedIdentityToken {
  sub: string;
  "cognito:groups": string[];
  email_verified: boolean;
  address: Address;
  birthdate: string;
  gender: string;
  iss: string;
  phone_number_verified: boolean;
  "cognito:username": string;
  given_name: string;
  middle_name: string;
  origin_jti: string;
  "cognito:roles": string[];
  aud: string;
  event_id: string;
  token_use: string;
  auth_time: number;
  phone_number: string;
  exp: number;
  iat: number;
  family_name: string;
  jti: string;
  email: string;
  personTenantroleId?:number
}

export interface Access {
  moduleId: number;
  moduleName: string;
  moduleIdentifier: string;
  moduleType: string;
  moduleIcon: string;
  sequence: number;
  widgetAccess: WidgetAccess[];
}

export interface WidgetAccess {
  widgetId: number;
  widgetName: string;
  read: boolean;
  add: boolean;
  edit: boolean;
  delete: boolean;
  status: boolean;
}
export interface ResolvedObjects {
}

export interface Header {
    hasTabs: boolean;
    modelVersion: string;
    id: string;
    indexVersion: number;
    generationNum: number;
    name: string;
    author: string;
    authorName: string;
    authorDisplayName: string;
    created: any;
    modified: any;
    modifiedBy: string;
    owner: string;
    isDeleted: boolean;
    isHidden: boolean;
    isAutoCreated: boolean;
    isAutoDelete: boolean;
    resolvedObjects: ResolvedObjects;
    tags: any[];
    isExternal: boolean;
    isDeprecated: boolean;
    description: string;
}

export interface DebugInfo {
}

export interface ReportJSON {
    headers: Header[];
    isLastBatch: boolean;
    debugInfo: DebugInfo;
    token: string;
}

export interface MeasurementDetails{
  firstName: string,
  lastName: string,
  emailId: string,
  examId: number,
  candidateId: number,
  examName: string,
  totalScore:number,
  cutScore: number,
  candidateScored: number,
  overAllComment: string,
  isPassed: boolean,
  skills:SkillsName[]
}

export interface SkillsName{
  skillName: string,
  totalScore:number,
  cutScore: number,
  candidateScored: number,
  candidateFinalValue: string,
  proctorFinalValue: null,
  isPassed: boolean,
  steps:steps[]
}
export interface steps{
  stepName:string,
   isPassed: boolean,
    isMandatory: boolean,
     comment: string
}
export interface AbsentResponse{
  candidateId: number
  testCenterId: string
  personEventId: string
  naeUserId: number
}

export interface BooketOrderStatusJson {
  orderStatusId: number;
  statusDescription:string
}
