<!-- <div (mouseenter)="onMouseEnter()" (mouseleave)="onMouseLeave()" [class.collapsed]="collapsed"
  [class.open]="collapsed && collapsedOpen$ | async" class="sidenav cardBorder flex flex-col"> -->

  <div [class.collapsed]="collapsed" [class.open]="collapsed && collapsedOpen$ | async"
  class="sidenav cardBorder1 flex flex-col">

  <exai-scrollbar class="flex-auto">
    <div class="sidenav-items mt-4">
      <exai-sidenav-item *ngFor="let item of items; trackBy: trackByRoute" [item]="item" [level]="0">
      </exai-sidenav-item>
    </div>
  </exai-scrollbar>

  <div class="sidenav-toolbar flex">
    <!-- <mat-icon (click)="toggleCollapse()" class="select-none flex-none hidden lg:block cursor-pointer">
      toggle_on
    </mat-icon> -->
    <h2 class="">{{ title$ | async }}</h2>
    <img (click)="toggleCollapse()" src="assets/img/side-toggle1.svg"
      class="select-none flex-none hidden lg:block cursor-pointer">
      <!-- <mat-icon (click)="toggleCollapse()" class="select-none flex-none hidden lg:block cursor-pointer">
        icon/menu/toggle
      </mat-icon> -->
    <span class="ml-4 item-label1 select-none flex-none hidden lg:block cursor-pointer">Toggle Sidebar</span>
    <img (click)="toggleCollapse()" [src]="imageUrl$ | async"
      class="w-4 cursor-pointer">
    
    <!-- <button (click)="toggleCollapse()" *ngIf="showCollapsePin$ | async"
      class="w-8 h-8 -mr-2 leading-none flex-none hidden lg:block" mat-icon-button type="button"> -->

      <!-- <mat-icon *ngIf="!collapsed" [icIcon]="icRadioButtonChecked" size="14px">
      </mat-icon> -->

      <!-- <mat-icon *ngIf="collapsed" [icIcon]="icRadioButtonUnchecked" size="14px">
      </mat-icon> -->

    <!-- </button> -->
  </div>
</div>