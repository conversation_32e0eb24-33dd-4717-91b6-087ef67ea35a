import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, Roles, statuses, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Candidate Search",
            moduleID: "candidate-search",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "candidateSearch",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ['tab-col'],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter:true,
                                    showAddButton: [false,false,false,false],
                                    showMatTabs: true,
                                    showSearch: true,
                                    matTabs: [
                                      
                                        { id: 1, name: "Pending" },
                                        { id: 2, name: "Approved" },
                                        { id: 3, name: "Rejected" },
                                        { id: 4, name: "All" },
                                    ],
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                },
                                displayedColumns: [
                                    
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "candidateName",
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "Last 4 SSN",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingProgramName",
                                            name: "Training Program  Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "completionDate",
                                            name: "Completion Date",
                                            cellType: "onlydate",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "status",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [],
                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                    visibilityRule: [
                                                        {
                                                            operator: tableTypes.Operators.AND,
                                                            elementProperty: 'status',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: 'Pending'
                                                        },
                                                    ]
                                                },
                                            ],
                                            headerCssClasses: ["w-12"],
                                            cellCssClasses: ["cursor-pointer"],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Custom,
                                                navigateTo: 'loader/candidate-search/candidate-verification',
                                                eventDataUseful: false,
                                                navigateToLabel: 'verification',
                                            },
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "candidateName",
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                       
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "Last 4 SSN",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingProgramName",
                                            name: "Training Program  Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "completionDate",
                                            name: "Completion Date",
                                            cellType: "onlydate",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "status",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },

                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        },
                                        // <tableTypes.column>{
                                        //     visible: true,
                                        //     id: "action",
                                        //     name: "Action",
                                        //     cellType: "action",
                                        //     headerType: "normal",
                                        //     headerAlign: "center",
                                        //     actions: [
                                        //         <tableTypes.action>{
                                        //             icon: "visibility",
                                        //             type: tableTypes.actionTypes.Link,
                                        //             tooltip: "View",
                                        //             color: "#7d7d7d",
                                        //             visibilityRule: [
                                        //                 {
                                        //                     operator: tableTypes.Operators.AND,
                                        //                     elementProperty: 'statusId',
                                        //                     compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                                        //                     shouldHaveValue: statuses.Approved
                                        //                 },
                                        //                 {
                                        //                     operator: tableTypes.Operators.AND,
                                        //                     elementProperty: 'statusId',
                                        //                     compareWithOperator: tableTypes.comparisionOperators.NOTEQUAL,
                                        //                     shouldHaveValue: statuses.Rejected
                                        //                 }
                                        //             ]
                                        //         },
                                        //     ],
                                        //     headerCssClasses: ["w-12"],
                                        //     cellCssClasses: [""],
                                        //     linkMetaData: {
                                        //         navigateToType: moduleTypes.Custom,
                                        //         navigateTo: 'training/verification',
                                        //         eventDataUseful: false,
                                        //         navigateToLabel: 'verification',
                                        //     },
                                        // },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "candidateName",
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                       
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "Last 4 SSN",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingProgramName",
                                            name: "Training Program  Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "completionDate",
                                            name: "Completion Date",
                                            cellType: "onlydate",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "status",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "candidateName",
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                       
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "ssn",
                                            name: "Last 4 SSN",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "trainingProgramName",
                                            name: "Training Program  Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "completionDate",
                                            name: "Completion Date",
                                            cellType: "onlydate",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                       <tableTypes.column>{
                                            visible: true,
                                            id: "status",
                                            name: "Status",
                                            statuses: tableTypes.tableStatuses,
                                            cellType: "status",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["width-6"],
                                            cellCssClasses: [],
                                        },


                                      
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: '#7d7d7d',
                                                    visibilityRule: [
                                                        {
                                                            operator: tableTypes.Operators.OR,
                                                            elementProperty: 'status',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: 'Pending'
                                                        },
                                                        {
                                                            operator: tableTypes.Operators.OR,
                                                            elementProperty: 'status',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: 'Approved'
                                                        }
                                                    ]
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "view-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "View Application Details",
                                            },
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["w-12","text-center"],
                                            cellCssClasses: ["text-center","cursor-pointer"],
                                        }
                                    ],
                                ],
                                requestDetails: [
                                    
                                    {
                                        fetchUrl: ['client', 'api', 'client', 'tarininginstitute-search'],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'trainingInstituteId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'statusId',
                                                paramValue: statuses.Pending,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageNumber',
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageSize',
                                                paramValue: 1000,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'reviewerCode',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'personId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'personId'
                                            },

                                        ],
                                        method: 'GET'
                                    },
                                    {
                                        fetchUrl: ['client', 'api', 'client', 'tarininginstitute-search'],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'trainingInstituteId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'statusId',
                                                paramValue: statuses.Approved,
                                            },

                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageNumber',
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageSize',
                                                paramValue: 1000,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'reviewerCode',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'personId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'personId'
                                            },
                                        ],
                                        method: 'GET'
                                    },
                                    {
                                        fetchUrl: ['client', 'api', 'client', 'tarininginstitute-search'],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'trainingInstituteId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'statusId',
                                                paramValue: statuses.Rejected,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageNumber',
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageSize',
                                                paramValue: 1000,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'reviewerCode',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'personId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'personId'
                                            },

                                        ],
                                        method: 'GET'
                                    },
                                    {
                                        fetchUrl: ['client', 'api', 'client', 'tarininginstitute-search'],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'trainingInstituteId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },

                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageNumber',
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'pageSize',
                                                paramValue: 1000,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'reviewerCode',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'clientORtenantId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: 'personId',
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'personId'
                                            },

                                        ],
                                        method: 'GET'
                                    },
                                ],
                            }
                        },
                    ],
                },
            ],
        }
    ]
}