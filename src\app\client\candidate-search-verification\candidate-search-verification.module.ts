import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CandidateSearchVerificationRoutingModule } from './candidate-search-verification-routing.module';
import { CandidateSearchVerificationComponent } from './candidate-search-verification.component';

import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { FlexLayoutModule } from '@angular/flex-layout';
import { IconModule } from '@visurel/iconify-angular';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { PageLayoutModule } from 'src/@exai/components/page-layout/page-layout.module';
import { MatNativeDateModule, MatRippleModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSortModule } from '@angular/material/sort';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import {MatGridListModule} from '@angular/material/grid-list';
import {MatRadioModule} from '@angular/material/radio';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { TRAININGPROGRAM_STATE_NAME } from '../training/state/training-program.selector';
import { TrainingDetails } from '../training/state/training-program.reducer';
import { TraningEffects } from '../training/state/training-program.effect';
import { TableModule } from 'src/app/core/common-component/table/table.module';
import { NgDynamicBreadcrumbModule } from 'ng-dynamic-breadcrumb';
import { SingleEntryTableModule } from 'src/app/core/common-component/single-entry-table/single-entry-table.module';


@NgModule({
  declarations: [
    CandidateSearchVerificationComponent
  ],
  imports: [
    CommonModule,
    CandidateSearchVerificationRoutingModule, FlexLayoutModule,
    PageLayoutModule,
    MatTableModule,
    MatPaginatorModule,
    MatRadioModule,
    IconModule,
    MatButtonModule,
    MatIconModule,
    MatIconModule,
    MatInputModule,
    MatTooltipModule,
    MatSelectModule,
    MatCheckboxModule,
    MatRippleModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatMenuModule,
    MatSortModule,
    MatFormFieldModule,
    MatSnackBarModule,
    FormsModule,
    MatGridListModule,
    NgDynamicBreadcrumbModule,
    TableModule,
    StoreModule.forFeature(TRAININGPROGRAM_STATE_NAME, TrainingDetails),
    EffectsModule.forFeature([TraningEffects]),
    SingleEntryTableModule
  ]
})
export class CandidateSearchVerificationModule { }
