import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { State, Store } from '@ngrx/store';
import { logout } from '@thoughtspot/visual-embed-sdk';
import { Editor } from 'ngx-editor';
import { BehaviorSubject, Observable } from 'rxjs';
import { genericPopupConfig, popupTypes, Roles } from 'src/app/client/dynamic-component-loader/dynamic-component-loader.types';
import { recentLinkClickEventSelector } from 'src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors';
import { environment } from 'src/environments/environment';
import { GlobalUserService } from '../../global-user.service';
import { getObject } from '../form-view/form-view.component';
import { state } from '../table/dynamic-table-types';

@Component({
  selector: 'app-dynamic-popup',
  templateUrl: './dynamic-popup.component.html',
  styleUrls: ['./dynamic-popup.component.scss']
})
export class DynamicPopupComponent implements OnInit {

  popupTypes = popupTypes;
  popupData: any = null;
  formJSON: any;
  userResponse: any;
  richText: FormControl;
  ShowData:boolean
  element: any = null;
  editor: Editor;
  Dowmloadbutton: boolean = false
  AcceptanceName:boolean
  radioselect = new FormControl("");
  radioselectAcceptance = new FormControl("");
  INFValue:FormGroup
  examTypeModels=[{id: 1, name: 'INF (In-facility)',checked: false},{id: 2, name: 'RTS (Regional Testing Center)',checked: false,}]
  Acceptance=[{id: 1, name: 'Yes',checked: false}]
   INFSelected :boolean = false
  AcceptanceValue:boolean 
  TestReferenceId:number
  TestRefrenceName:string
  NonEditData:boolean 
  stateId
  recentEventSelect
  resourceurl: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  $resourceurl: Observable<string> = this.resourceurl.asObservable();

  @ViewChild('hiddenUnnecessaryButton') submitButton: any;
  constructor(@Inject(MAT_DIALOG_DATA) public popupConfig: genericPopupConfig, public global: GlobalUserService, private store: Store,private fb:FormBuilder) {
    this.popupData = popupConfig.popupData;
    this.formJSON = this.popupData.formJSON;
    this.userResponse = this.popupData.userResponse;
    this.element = this.popupData.element;
    this.element = {
      ...this.element,
      candidateId: this.element.personTenantRoleId,
      personEventId: this.element.personEventRegistryId ? this.element.personEventRegistryId : this.element.personEventId
    }
    if (this.popupConfig.type == popupTypes.EmulateCandidate) {
      let Email  =this.element.element.emailId?this.element.element.emailId:this.global.email
      let firstName =this.element.element.firstName?this.element.element.firstName:this.global.firstName
      let lastName =this.element.element.lastName?this.element.element.lastName:this.global.lastName
      this.resourceurl
        .next((!environment.production ? 'http://localhost:8011' : (environment.redirectUrl + 'candidate')) + '/dashboard/?token=' + sessionStorage.getItem('token') + '&email=' + encodeURIComponent(Email) + '&firstName=' + firstName + '&lastName=' + lastName);
    }
    if (this.popupConfig.type == popupTypes.EmulateClient) {
      let Email  =this.element.emailId
      let EmployerEmail = this.element.emailid
      let EmailId = (Email !=null && Email !=undefined)?Email:EmployerEmail
      this.resourceurl
        .next((!environment.production ? 'http://localhost:8002' : (environment.redirectUrl + 'client')) + '/dashboard/?token=' + sessionStorage.getItem('token') + '&email=' + encodeURIComponent(EmailId) + '&firstName=' + this.element.firstName + '&lastName=' + this.element.lastName + "&personTenantroleId=" + this.element?.personTenantRoleId);    }
    this.richText = new FormControl({ disabled: true, value: this.popupData.richText });
    this.editor = new Editor();
  }

  ngOnInit(): void {
    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      this.recentEventSelect = data
      this.stateId = data.element.stateId
      // if ((this.stateId == state.District_of_Columbia || this.stateId == state.Colorado) && (this.global.column.linkMetaData.navigateTo === "candidate Details-sc") || (this.global.column.linkMetaData.navigateTo === "candidate-details-sc")) {
      //   this.Dowmloadbutton = true
      // }
      this.ShowData =data.element.formTypeId == 3 && this.global.userDetails.value.roleId != 15?true:false

      this.INFValue = this.fb.group({
        infcode:['',[Validators.required ,Validators.maxLength(8)]],
  
      
     })
     if(data.element.status !='Drafted' && data.element.status !='Change Request'){
      this.radioselect.setValue(data.element.testingPreferenceName)
      let isTestCenterAcknowledge = data.element.isTestCenterAcknowledge == false && data.element.isTestCenterAcknowledge !=null?"No":data.element.isTestCenterAcknowledge !=null?'Yes':null
          this.radioselectAcceptance.setValue(isTestCenterAcknowledge)
      this.getSelectedRoute(data.element.testingPreferenceId)
      this.INFValue = this.fb.group({
        infcode:[data.element.testCenterId?this.cleanINFString(data.element.testCenterId):'',[Validators.required ,Validators.maxLength(8)]],
     })
      this.NonEditData = true
      this.INFValue.get('infcode').disable()
    }else{
      this.NonEditData = false
      this.radioselect.setValue(data.element.testingPreferenceName)
      this.radioselectAcceptance.setValue(data.element.isTestCenterAcknowledge)
      this.getSelectedRoute(data.element.testingPreferenceId)
      this.INFValue = this.fb.group({
        infcode:[data.element.testCenterId?this.cleanINFString(data.element.testCenterId):'',[Validators.required,Validators.maxLength(8)]],
     })
    }
    })
  }

  
  cleanINFString(str) {
    const match = str.match(/^INF(\d+)$/);
    return match ? match[1] : null;
  }
  toPdf() {
    var html = '<HTML>\n<HEAD>\n';
    if (document.getElementsByTagName != null) {
      var headTags = document.getElementsByTagName("head");
      if (headTags.length > 0) html += headTags[0].innerHTML;
    }
    html += '\n</HE' + 'AD>\n<BODY>\n';
    var printReadyElem = document.getElementById("printMe");
    if (printReadyElem != null) html += printReadyElem.innerHTML;
    html += '\n</BO' + 'DY>\n</HT' + 'ML>';
    var printWin = window.open("", "processPrint");
    printWin.document.open();
    printWin.document.write(html);
    //logic to manually put values into text and date fields

    var inputElements = printWin.document.getElementsByTagName("input");
    var dateElemens = printWin.document.getElementsByClassName("mat-datepicker-input");
    var textAreaElements = printWin.document.getElementsByTagName("textarea");

    for(let i = 0; i<inputElements.length; i++){
        let inputId = inputElements[i].id;
        let value = getObject(this.userResponse,inputId,false);
        if(value){
          printWin.document.getElementById(inputId).setAttribute("value",value);
        }
    }
    for(let i = 0; i<dateElemens.length; i++){
      let inputId = dateElemens[i].id;
      let value = getObject(this.userResponse,inputId,false);
      let date = new Date(value);
      value = date.getUTCMonth()+1 + '/'+date.getDate()+ '/'+date.getFullYear();
      if(value){
        printWin.document.getElementById(inputId).setAttribute("value",value);
      }
    }
    for(let i = 0; i<textAreaElements.length; i++){
        let inputId = textAreaElements[i].id;
        let value = getObject(this.userResponse,inputId,false);
        if(value){
          printWin.document.getElementById(inputId).textContent = value;
        }
    }
    setTimeout(() => {
      printWin.print();
      printWin.close()
    },500)
  }

  close() {
    logout()
  }

  onlyNumberKey(event: KeyboardEvent) {
    // returns number from keyboard
    if (event.charCode !== undefined) {
      return event.charCode == 8 || event.charCode == 0
        ? null
        : event.charCode >= 48 && event.charCode <= 57;
    } else {
      return event.keyCode == 8 || event.keyCode == 0
        ? null
        : event.keyCode >= 48 && event.keyCode <= 57;
    }
  }

  onKeydownMain(event){
  }

  getSelectedAccept(event){
    this.AcceptanceName = event ==1?true:false
    event ===1?this.radioselectAcceptance.setValue('Yes'):this.radioselectAcceptance.setValue('No')

  }

  
  getSelectedRoute(event) {
    if (event == 1) {
      this.INFSelected = true
      this.TestReferenceId = event

    } else if (event == 2) {
    

    }
  }
}
