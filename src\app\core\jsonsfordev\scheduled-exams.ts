import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
  modules: [{
    type: moduleTypes.Dynamic,
    name: "Scheduled Exams",
    moduleID: "scheduled-exams",
    nestingType: nestingTypes.ColumnsInRows,
    iconRef: "manageTypeExamIcon",
    routeRef: "/loader",
    rows: [
      {
        class: "",
        gdColumns: "1fr",
        gdColumnsltlg: "1fr",
        gdColumnsltmd: "1fr",
        gdColumnsltsm: "1fr",
        gdGap: "12px",
        columns: [
          {
            class: "w-full",
            gdColumn: "1 / 1",
            gdColumnltlg: "1 / 1",
            gdColumnltmd: "1 / 1",
            gdColumnltsm: "1",
            fxLayout: "",
            fxLayoutAlign: "start center",
            fxFlex: "none",
            fxFlexltmd: "auto",
            fxHidexs: "false",
            fxHidesm: "false",
            widget: {
              type: widgetTypes.DynamicTable,
              tableOptions: {
                headerRowCssClasses: ["tab-col"],
                showPaginator: true,
                pageSizeOptions: [8, 20, 100],
                pageSize: 8,
                showColumnFilter: true,
                showAddButton: [true],
                addButtonTexts:['Add User'],
                addPopupData: [{
                  isCustom: true,
                  customTemplateRef: 'customAddPopupFormManageAllUsers',
                  title: ['Add User', 'Edit User Details'],
                  id: 'addUsersPopup',
                  buttons: [
                    {
                      buttonText: "Submit",
                      requestDetails: {
                        fetchUrl: ["Login", "register"],
                        requestBody: null,
                        requestParams: null,
                        method: "POST",
                      },
                    },
                  ],
                }],
                showMatTabs: false,
                showSearch: true,
                verticalScroll: true,
                horizontalScroll: true,
                isPaginationServerSide: [true],
                isSearchServerSide: [true],
              },
              displayedColumns: [
                [
                  <tableTypes.column>{
                    visible: true,
                    id: "checkbox",
                    name: "Checkbox",
                    cellType: "text",
                    headerType: "checkbox",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [ "w-4"],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "candidateId",
                    name: "Candidate ID",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "candidateName",
                    name: "Candidate Name",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "candidateEmail",
                    name: "Email",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "examName",
                    name: "Exam Name",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "examDate",
                    name: "Exam Date",
                    cellType: "onlydate",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "examStatus",
                    name: "Exam Status",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                  <tableTypes.column>{
                    visible: true,
                    id: "examMode",
                    name: "Exam Mode",
                    cellType: "text",
                    headerType: "normal",
                    headerAlign: "center",
                    cellAlign: "center",
                    headerCssClasses: [],
                    cellCssClasses: [],
                  },
                ],
              ],
              requestDetails: [
                {
                  fetchUrl: [`client`,`api`, `operationstaff`, `candidate-exams`],
                  requestBody: null,
                  requestParams: [
                  {
                    paramType: paramTypes.Optional,
                    paramName: 'pageNumber',
                    paramValue: 1,
                },
                {
                    paramType: paramTypes.Optional,
                    paramName: 'pageSize',
                    paramValue: 8,
                },
                ],
                  method: "GET",
                },
              ],
            },
          },
        ],
      },
    ],
  }
  ]
}