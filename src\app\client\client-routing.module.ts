import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CustomLayoutComponent } from './custom-layout/custom-layout.component';
import { VerificationComponent } from './training/verification/verification.component';

const routes: Routes = [
  { path: "", pathMatch: "full", redirectTo: 'dashboard' },

  { path: "Scan", loadChildren: () => import('./scan/scan.module').then(m => m.ScanModule) },
  { path: "SkillNAE/:vmId/:personId/:eventId/:NAE_Id/:callScoreAPI", loadChildren: () => import('./publish_event/publish_event.module').then(m => m.PublishEventModule) },


  {
    path: '', component: CustomLayoutComponent,
    children: [
      { path: 'loader/:moduleId', loadChildren: () => import('./dynamic-component-loader/dynamic-component-loader.module').then(m => m.DynamicComponentLoaderModule) },
      { path: 'dashboard', loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule) },
      { path: 'scheduleExam', loadChildren: () => import('./schedule/schedule.module').then(m => m.ScheduleModule) },
      { path: "manage-eligiblity", loadChildren: () => import('./manage-eligibilty-routes/manage-eligibilty-routes.module').then(m => m.ManageEligibiltyRoutesModule) },
      { path: "training", loadChildren: () => import('./training/training.module').then(m => m.TrainingModule) },
      { path: "voucher", loadChildren: () => import('./voucher/voucher.module').then(m => m.VoucherModule) },
      { path: "application", loadChildren: () => import('./application/application.module').then(m => m.ApplicationModule) },
      { path: "verification", component: VerificationComponent },
      { path: "certificate", loadChildren: () => import('./candidate-certifacte-view/candidate-certifacte-view.module').then(m => m.CandidateCertifacteViewModule) },
      { path: "", loadChildren: () => import('./report/report.module').then(m => m.ReportModule) },
      { path: "voucher-codes", loadChildren: () => import('./voucher-codes/voucher-codes.module').then(n => n.VoucherCodesModule) },
      { path: "loader/candidate-search/candidate-verification", loadChildren: () => import('./candidate-search-verification/candidate-search-verification.module').then(m => m.CandidateSearchVerificationModule) },
    
      { path: "Skills", loadChildren: () => import('./skills/skills.module').then(m => m.SkillsModule) },
      { path: 'support-ticket/support-ticket-details', loadChildren: () => import('./support-ticket/support-ticket.module').then(m => m.SupportTicketModule) },
      { path: 'edit-profile', loadChildren: () => import('./edit-profile/edit-profile.module').then(m => m.EditProfileModule) },

    ]
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ClientRoutingModule { }
