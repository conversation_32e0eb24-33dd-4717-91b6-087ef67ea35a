<a (click)="resetPageNumber()" 
  *ngIf="isLink(item) && !isFunction(item.route)"
   [fragment]="item.fragment"
   [routerLinkActiveOptions]="item.routerLinkActiveOptions || { exact: false }"
   [routerLink]="item.route"
   class="item"
   fxLayout="row"
   matRipple
   routerLinkActive="active"
   #rla="routerLinkActive">
   
  <mat-icon *ngIf="level === 0" class="item-icon" [ngClass] = "rla.isActive ?  '' : 'material-icons-outlined'" fxFlex="none">{{item.icon}}</mat-icon>
  <!-- <img *ngIf="level === 0" [src]="item.icon" class="item-icon" fxFlex="none"> -->
  <span [ngClass]="(item.label == 'Getting Started') ? 'dropdowncontent':'item-label'" fxFlex="auto">{{ global.userDetails.getValue().roleName =='State Client' && item.label =='Events'?'GIS': item.label }}</span>
  <span *ngIf="item.badge"
        [ngClass]="[item.badge.bgClass, item.badge.textClass]"
        [class]="item.badge.value === 'NEW' ? 'reports-new-badge' : 'item-badge'"
        fxFlex="none">{{ item.badge.value }}</span>
</a>

<div (click)="item.route()"
     *ngIf="isLink(item) && isFunction(item.route)"
     class="item"
     fxLayout="row"
     matRipple
     routerLinkActive="active">
  <mat-icon *ngIf="level === 0" [icIcon]="item.icon" class="item-icon" fxFlex="none"></mat-icon>
  {{item.label}}
  <span class="item-label" fxFlex="auto">{{ item.label }}</span>
  <span *ngIf="item.badge"
        [ngClass]="[item.badge.bgClass, item.badge.textClass]"
        [class]="item.badge.value === 'NEW' ? 'reports-new-badge' : 'item-badge'"
        fxFlex="none">{{ item.badge.value }}</span>
</div>

<ng-container *ngIf="isDropdown(item)">
  <div (click)="toggleOpen()"
       [class.active]="isOpen || isActive"
       [class.open]="isOpen"
       class="item"
       fxLayout="row"
       matRipple>
    <mat-icon *ngIf="level === 0"  class="item-icon" fxFlex="none">{{item.icon}}</mat-icon>
    <span class="item-label" fxFlex="auto">{{ item.label }}</span>
    <span *ngIf="item.badge"
          [ngClass]="[item.badge.bgClass, item.badge.textClass]"
          [class]="item.badge.value === 'NEW' ? 'reports-new-badge' : 'item-badge'"
          fxFlex="none">{{ item.badge.value }}</span>
    <mat-icon [icIcon]="icKeyboardArrowRight" class="item-dropdown-icon" fxFlex="none"></mat-icon>
  </div>
  <div [@dropdown]="isOpen" class="item-dropdown">
    <exai-sidenav-item *ngFor="let subItem of item.children" [item]="subItem" [level]="level + 1"></exai-sidenav-item>
  </div>
</ng-container>


<ng-container *ngIf="isSubheading(item)">
  <div class="subheading">{{ item.label }}</div>
  <exai-sidenav-item *ngFor="let subItem of item.children" [item]="subItem" [level]="0"></exai-sidenav-item>
</ng-container>

