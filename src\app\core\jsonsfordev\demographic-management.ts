import { FormTypes } from "src/app/client/application/application.types";
import { consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";

export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Demographic Management",
            moduleID: "demographic-management",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "demographicManageIcon",
            routeRef: "/loader",
            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ['tab-col'],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showColumnFilter:true,
                                    showAddButton: [false],
                                    showMatTabs: false,
                                    showSearch: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                    isPaginationServerSide: [true],
                                    isSearchServerSide: [true]
                                },
                                displayedColumns: [
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "Checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['w-4'],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "title",
                                            name: "List Of Fields",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "createdOn",
                                            name: "Created Date",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "modifiedOn",
                                            name: "Modified Date",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "active",
                                            name: "Status",
                                            cellType: "toggle",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            showOnlyData:true,
                                            headerCssClasses: ['w-12'],
                                            cellCssClasses: [],
                                            statusUpdateDetails: {
                                                fetchUrl: ['client', 'api', `content`, `demographic-items`, 'status'],
                                                requestBody: null,
                                                requestParams: null,
                                                method: 'POST'
                                            }
                                        }
                                    ]],
                                requestDetails: [{
                                    fetchUrl: ['client', 'api', 'content', 'demographic-items'],
                                    requestBody: null,
                                    // hardcode as of now
                                    requestParams: [
                                        {
                                            paramType: paramTypes.Optional,
                                            paramName: 'pageNumber',
                                            paramValue: 1,
                                        },
                                        {
                                            paramType: paramTypes.Optional,
                                            paramName: 'pageSize',
                                            paramValue: 8,
                                        },
                                        {
                                            paramType: paramTypes.Optional,
                                            paramName: "reviewRoleId",
                                            paramValue: null,
                                            extractedFromGlobal: true,
                                            elementPropertyToBeExtracted: 'roleId'
                                        },
                                        {
                                            paramType: paramTypes.Optional,
                                            paramName: 'formTypeId',
                                            paramValue: FormTypes.Application,
                                        }
                                    ],
                                    method: 'GET'
                                },
                                ]
                            }
                        },
                    ],
                },
            ],
        }
    ]
}