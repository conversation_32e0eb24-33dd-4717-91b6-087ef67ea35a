import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from "@angular/core";
import { DatePipe } from '@angular/common';
import icMoreHoriz from "@iconify/icons-ic/twotone-more-horiz";
import icCloudDownload from "@iconify/icons-ic/twotone-cloud-download";
import { MatTableDataSource } from "@angular/material/table";
import { SelectionModel } from "@angular/cdk/collections";
import {
  tableOptions,
  column,
  performedAction,
  cellClickEvent,
  headerClickEvent,
  headerDropdownSelectionEvent,
  checkboxClickEvent,
  tabSelectionEvent,
  status,
  action,
  Operators,
  VisibilityRule,
  comparisionOperators,
  tablePageEvent,
  accMerge,
  state,
  examstatus,
  ModuleID,
  ColumnID,
  tabs
} from "./dynamic-table-types";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatSort } from "@angular/material/sort";
import icDelete from '@iconify/icons-ic/twotone-delete';
import icSearch from '@iconify/icons-ic/twotone-search';
import icAdd from '@iconify/icons-ic/twotone-add';
import icFilterList from '@iconify/icons-ic/twotone-filter-list';
import { MatSlideToggleChange } from "@angular/material/slide-toggle";
import { linkClickEvent, actionTypes } from './dynamic-table-types';
import { Observable, of } from "rxjs";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { Access, ModuleIdName, Roles, State, statuses } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import { NavigationService } from "src/@exai/services/navigation.service";
import lodash from "lodash";
import { GlobalUserService } from "../../global-user.service";
import { debounceTime, switchMap } from "rxjs/operators";
import { recentLinkClickEventSelector, statesSelector } from "src/app/client/dynamic-component-loader/dynamic-loader-store/dynamic-loader.selectors";
import { Store } from "@ngrx/store";
import data from "@iconify/icons-ic/twotone-more-horiz";
import { setStateFilter } from "src/app/client/state/stateFilter/stateFilter.actions";
import { getState } from "src/app/client/state/stateFilter/stateFilter.selectors"
import { SnackbarService } from "../../snackbar.service";
import { bookletOrderStatus, get_userDetails } from "src/app/client/state/shared/shared.selectors";
import { getPageSize } from "src/app/client/state/pageSize/pageSize.selectors";
import { MatMenuTrigger } from "@angular/material/menu";
import { setPageSize } from "src/app/client/state/pageSize/pageSize.actions";
import moment from "moment";
import { HttpClient, HttpErrorResponse, HttpHeaders } from "@angular/common/http";
import { environment } from "src/environments/environment";
import * as FS from 'file-saver'
import { RefundUserAllow, RenewalExtendUserAllow, StateAllow, StateLists } from "../examroom-formbuilder/form-builder.types";
import { URL } from "../../url";
import { DynamicPopupMesageComponent } from "../pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component";
import { MatDialog } from "@angular/material/dialog";
import { LanguageService } from "../../language.service";
import { PaymentRefundPopupComponent } from "../pop-up/payment-refund-popup/payment-refund-popup.component";
import { MatDatepickerInputEvent } from "@angular/material/datepicker";
import { OMRVideoImage } from "../pop-up/omr-image-video/omr-image-video.component";
import { Router } from "@angular/router";
import { getOrderStatus } from "src/app/client/state/shared/shared.actions";
import { BooketOrderStatusJson } from "src/app/client/state/shared/shared.state";
@Component({
  selector: "exai-table",
  templateUrl: "./table.component.html",
  styleUrls: ["./table.component.scss"],
})
export class TableComponent implements OnInit {
  StateAllowRefund:boolean = false
  state;
  dateFromElement;
  datafromGlobalElement;
  icSearch = icSearch;
  icDelete = icDelete;
  icAdd = icAdd;
  icFilterList = icFilterList;
  icMoreHoriz = icMoreHoriz;
  icCloudDownload = icCloudDownload;
  tabselected: string
  Roles = Roles;
  data
  SelectedColoumWiseFilter: string = '';
  recentLinkClickEvent: linkClickEvent;
  isDateallow: boolean = false

  // @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }
  @Input() tableOptions: tableOptions;
  @Input() dataSource: MatTableDataSource<any>;
  @Input() displayedColumns: Array<any>;
  @Input() selectedIndexObs: Observable<number>;
  @Input() accessData: Access = null;
  @Input() totalRecords: number = null;
  @Input() prevSearchKey?: string = null;
  @Input() preSelecetedIndex?: string = null;
  @Input() prevStateKey?: string = null;
  @Input() states?: Array<any> = [];
  @Input() scores?: Array<any> = [];
  @Input() breadCrumbsArray?: any;
  @Input() curModuleId?: string = "";
  
  searchText = '';
  searchStates?: Array<any> = [];
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger;
  @Output("actionPerformed") action: EventEmitter<performedAction> =
    new EventEmitter<performedAction>();
  @Output("cellClickEvent") cellClicked: EventEmitter<cellClickEvent> =
    new EventEmitter<cellClickEvent>();
  @Output("headerClicked") headerClicked: EventEmitter<headerClickEvent> =
    new EventEmitter<headerClickEvent>();
  @Output("headerDropdownSelection") headerDropdownSelection: EventEmitter<headerDropdownSelectionEvent> =
    new EventEmitter<headerDropdownSelectionEvent>();
  @Output("checkboxClicked") checkboxClicked: EventEmitter<checkboxClickEvent> =
    new EventEmitter<checkboxClickEvent>();
  @Output("tabSelected") tabSelected: EventEmitter<tabSelectionEvent> =
    new EventEmitter<tabSelectionEvent>();
  @Output("toggled") toggleEmitter: EventEmitter<cellClickEvent> = new EventEmitter<cellClickEvent>();
  @Output() addAction: EventEmitter<boolean> = new EventEmitter();

  @Output('linkClicked') linkClicked: EventEmitter<linkClickEvent> = new EventEmitter<linkClickEvent>();

  @Output('add') add: EventEmitter<any> = new EventEmitter<any>();

  @Output('searched') searchEvent: EventEmitter<{ searchKey: string }> = new EventEmitter<any>();

  @Output('preselectedIndex') preselectedIndexEvent: EventEmitter<{ preSelectedIndex: string }> = new EventEmitter<any>();
  @Output('nextPageDataFetchEvent') nextPageDataFetchEvent: EventEmitter<tablePageEvent> = new EventEmitter<tablePageEvent>(null);
  @Output('stateSelected') stateSelectionEvent: EventEmitter<{ value: any, serverside: any }> = new EventEmitter<any>();
  @Output('scoreSelected') scoreSelectionEvent: EventEmitter<any> = new EventEmitter<any>();
  @Output('mergeRequested') mergeRequested: EventEmitter<accMerge> = new EventEmitter<any>();
  @Output('certificateSelect') CertificateEvent: EventEmitter<accMerge> = new EventEmitter<any>();
  @Output('bookletorderStatusSelect') bookletorderStatusSelect:EventEmitter<any> = new EventEmitter<any>();

  @Output('dateSelected') dateSelectedEvent: EventEmitter<{ value: {fromDate,toDate,tableselected,curModuleId}, serverside: any }> = new EventEmitter<any>();

  @Input() min: any;

  @ViewChild(MatSort, { static: false })
  set sort(value: MatSort) {
    if (this.dataSource) {
      this.dataSource.sort = value;
      this.data = this.dataSource.filteredData.length
    }
  }


  get minOfDataSizeAndPageLastRecordIndex() {


    return (this.pageSizeControl.value > this.dataSource.data.length) ? this.totalRecords : this.pageIndexControl.value * this.pageSizeControl.value;

  }
  selectedIndex: number = 0;
  displayedCols: Array<any>;
  displayedFilterColmns: Array<any>;
  searchControl: FormControl;
  searchColumn?: any;
  pageIndexControl: FormControl = new FormControl(1);
  selection = new SelectionModel<''>(true, []);
  displayedColsIndices: Array<string>;
  getVisiblibityColumn: Array<any>;
  pageSizeControl: FormControl;
  stateControl: FormControl;
  scoreControl: FormControl;
  stateName;
  orderStatusControl: FormControl;
  form:FormGroup
  formReview:FormGroup
  ExamTypes:Array<object>=[]
  yesterday=new Date()
  bookletOrderStatus$: Observable<BooketOrderStatusJson[]>
  constructor(
    public globalUserService: GlobalUserService, private store: Store, private snackbar: SnackbarService, private http: HttpClient,private dialog:MatDialog,private lngSrvc:LanguageService,private router:Router) {
      this.yesterday.setDate(this.yesterday.getDate() +1 - 0)
  }

  ngOnInit(): void {

    this.form = new FormGroup({
      start: new FormControl(""),
      end: new FormControl(""),
    });
    this.formReview =new FormGroup({
      reviewstart: new FormControl(""),
      reviewend: new FormControl(""),
    });

    this.store.select(getState).subscribe(res=> {
      if(res){
        this.prevStateKey = res as any;
      }
    });

    this.getVisiblibityColumn = this.displayedColumns[this.selectedIndex].filter(item => item.headerDisable == true);
    this.getVisiblibityColumn = this.getVisiblibityColumn.filter(item => item.visibilityRule != undefined);
    this.getVisiblibityColumn.forEach(element => {
      let result
      result = this.getHeaderVisibility(element.visibilityRule);
      if (result == false) { this.displayedColumns[this.selectedIndex] = this.displayedColumns[this.selectedIndex].filter(item => item.id != element.id) }
    });
    if (this.curModuleId != ModuleID.Suuport_Ticket && this.breadCrumbsArray?.length == 2) {
      this.globalUserService.selecetedPageSize = null
    }
    this.searchControl = new FormControl("");
    // this.pageIndexControl = new FormControl(1, [Validators.min(1), Validators.max(1000000)]);
    // this.pageSizeControl = new FormControl(this.tableOptions.pageSize);
    this.store.select(getPageSize).subscribe((res) => {
      if (this.tableOptions.pageSizeOptions && !(this.tableOptions.pageSizeOptions.includes(res?.pageSize))) {
        this.pageSizeControl = new FormControl(this.tableOptions.pageSizeOptions[0]);
      } else {
        this.pageSizeControl = new FormControl(res?.pageSize);
      }
      this.pageIndexControl = new FormControl(res?.pageNumber);
    });

 
    this.store.dispatch(setPageSize({
      pageSize: this.pageSizeControl.value,
      pageNumber: this.pageIndexControl.value
    }));
    this.stateControl = new FormControl(this.prevStateKey);
    this.scoreControl = new FormControl();
    this.SelectedColoumWiseFilter = this.tableOptions.showColumnWiseFilter ? null : ''
    this.pageIndexControl.valueChanges.pipe(
      debounceTime(850),
      switchMap((value: number) => of(value))
    ).subscribe((x: number) => {
      if (x != null && this.pageIndexControl.value != x) {
        if (this.tableOptions.isPaginationServerSide && this.tableOptions.isPaginationServerSide[this.selectedIndex]) {
          if (this.pageIndexControl.valid) {

            this.getNewPageData({
              length: null,
              pageSize: this.pageSizeControl.value,
              pageIndex: x,
              previousPageIndex: x
            })
          }

        }
        else {
          if ((x - 1) > this.dataSource.data.length / this.dataSource.paginator.pageSize) {
            this.pageIndexControl.setErrors({ max: true }, { emitEvent: false })
          }
          this.dataSource.paginator.pageIndex = x - 1;
          this.dataSource.paginator.page.next({
            pageIndex: x - 1,
            pageSize: this.dataSource.paginator.pageSize,
            length: this.dataSource.paginator.length
          });
        }
      }
    })

    this.stateControl.valueChanges.pipe(

      debounceTime(850),
      switchMap((value: any) => of(value))
    ).
      subscribe((value: any) => {


        if (this.tableOptions.isStateSearchServerSide != undefined && !this.tableOptions.isStateSearchServerSide) {

          this.dataSource.filter = value.trim().toLocaleLowerCase();
          this.globalUserService.id = value;
        } else {
          this.getNewPageData({
            length: null,
            pageIndex: 1,
            pageSize: this.pageSizeControl.value,
            previousPageIndex: 1
          })
        }
        this.stateSelectionEvent.emit({ value: value, serverside: this.tableOptions?.isStateSearchServerSide })
      });



    // })
    this.searchControl.valueChanges.pipe(

      debounceTime(850),
      switchMap((value: string) => of(value))
    ).subscribe((value: string) => {

      this.tableOptions.isSearchServerSide && this.tableOptions.isSearchServerSide[this.selectedIndex]
        ?
        this.getNewPageData({
          length: null,
          pageIndex: 1,
          pageSize: this.pageSizeControl.value,
          previousPageIndex: 1
        })
        :
        this.dataSource.filter = value.trim().toLocaleLowerCase()
      this.globalUserService.id = value

      this.globalUserService.id = value;
      this.globalUserService.problemReportCandidateId = null
      this.searchEvent.emit({ searchKey: value });

    });
    if (this.globalUserService.problemReportCandidateId != null) {
      this.searchControl.setValue(String(this.globalUserService.problemReportCandidateId));
    }

    if (this.prevSearchKey) {
      this.searchControl.setValue(this.prevSearchKey)

    } else if (this.globalUserService.id && !this.tableOptions.isSearchServerSide) this.globalUserService.id ? this.searchControl.setValue(this.globalUserService.id) : null
    if (this.prevStateKey) {
      this.stateControl.setValue(this.prevStateKey);
      this.handleStateSelection({ value: String(this.prevStateKey) });

    } else if (this.globalUserService.state && !this.tableOptions.isSearchServerSide) {
      this.stateControl.setValue(this.globalUserService.state)

      this.handleStateSelection({ value: String(this.globalUserService.state) });
    }
    this.displayedCols = this.displayedColumns[this.selectedIndex];
    this.displayedColsIndices = this.displayedColumns[this.selectedIndex].map((x: column) => {
      return x.id;
    });
    this.displayedFilterColmns = this.displayedCols.filter(x => x.filterable);
    this.displayedFilterColmns = this.curModuleId ==='booklet_inventory'?[...this.displayedFilterColmns,{isFilter:false,name:"bookletNumber",cellType:"text",filterable: true,visible:true,id:'bookletNumber'},{isFilter:false,name:"appointmentId",cellType:"text",filterable: true,visible:true,id:'appointmentId'}]:this.curModuleId ==='manage-training-programs'?[...this.displayedFilterColmns,{isFilter:false,name:"Email",cellType:"text",filterable: true,visible:true,id:'emailid'},{isFilter:false,name:"Address",cellType:"text",filterable: true,visible:true,id:'addressfilter'}]:this.displayedFilterColmns
    this.displayedFilterColmns.forEach(x => x.isFilter = false);

    this.selectedIndexObs.subscribe((value: number) => {

      this.globalUserService.id = null
      this.globalUserService.state = null
      this.tabIndexValueChangeLogic(value);
    });
    this.searchStates = this.states;
    // was working fine so commented out setTimeout
    // setTimeout(() => {
    //   this.searchStates=this.states
    // }, 2000);
    this.store.select(recentLinkClickEventSelector).subscribe(data => {
      if (data != null && data) {
        this.recentLinkClickEvent = data
        this.globalUserService.personId = data.element.personId
        this.dateFromElement = data.element;
        this.isDateallow = StateAllow.includes(this.recentLinkClickEvent.element.stateId) && RenewalExtendUserAllow.includes(this.globalUserService.userDetails.getValue().personId)? true : false
      }
    })
    this.globalUserService.userDetails.getValue().roleId === Roles.StateClient && this.globalUserService.userDetails.value.stateId === StateLists.AL && this.curModuleId =='Candidate-Import'?this.getFileName():null

    // Get BookletOrderStatus

    if(this.globalUserService.userDetails.getValue().stateId != StateLists.KYC && this.globalUserService.userDetails.getValue().roleId === Roles.StateClient){
      this.tableOptions.matTabs = this.tableOptions.matTabs.filter((x)=>x.name !='Completed')
   }
    if( this.curModuleId =='booklet_inventory' ||this.curModuleId =='manage-grievence' ) {
      this.store.dispatch(getOrderStatus({body:{},text:this.curModuleId}))
      this.bookletOrderStatus$ = this.store.select(bookletOrderStatus);
    }

    this.StateAllowRefund = RefundUserAllow.includes(this.globalUserService.userDetails.getValue().personId)

  }

  // BulkCertificateDownload(){
    
  // }

  tabIndexValueChangeLogic(value: number, allowRecurseCall: boolean = true) {
    if (value != null) {
      this.selectedIndex = value;
      if (this.displayedColumns[this.selectedIndex]) {
        this.displayedCols = this.displayedColumns[this.selectedIndex];
        this.displayedColsIndices = this.displayedColumns[this.selectedIndex].filter((x: column) => {
          this.state = x
          setTimeout(()=>{
            return x.name = x.name =="Candidate Name" && (this.globalUserService.userDetails.getValue().roleId === Roles.StateClient || this.globalUserService.userDetails.getValue().roleId === Roles.SupportingStaff) && this.curModuleId ==='training-institute-details'?'User Name': x.name
           },2000)
          return !(x.cellType == 'action' && x.actions.length == 0)
        }).map((x: column) => {
          if (x.cellType == 'action' && x.actions.length == 0) return null;
          return this.getColumnId(x.id);
        });
        if (this.selectedIndex > 0 && allowRecurseCall) this.accessMappingLogic();
      }
      else {
        this.displayedCols = null;
        this.displayedColsIndices = null;
      }
      setTimeout(() => {
        if (this.state != null && this.state != '' && this.state != undefined) {
          if (this.state.id == "renewalAction") {
            return (this.dateFromElement.stateId === state.Mississippi || this.dateFromElement.stateId === state.South_Carolina) ? this.state.name = "" : this.state.name = 'Renewal'
          }
          if (this.state.id == "action") {
            return (this.globalUserService.userDetails.value.roleId == this.Roles.StateClient && this.state.linkMetaData.navigateTo == 'scheduleExam') ? this.state.name = '' : this.state.name = "Action";
          }
         
        }
      }, 3000)

    


      this.selection = new SelectionModel<''>(true, []);
    }



  }

  toggleDatePopup(event) {

    if (event && this.globalUserService.userDetails.getValue().roleId == Roles.OperationStaff) {
      const nodeList: any = document.querySelectorAll(".mat-datepicker-popup");
      nodeList[0].classList.add("addclass");
    }
  }

  selectDate(event,element){
    let body={
      newExpiryDate: event,
      registryId: element.Id
    }
    this.http.post(URL.BASE_URL + `registry/extend-registry-expiry`,body).subscribe((data)=>{
       if(data){
         this.snackbar.callSnackbaronSuccess(" Renewal Extented  SuccessFully")
       }
    })

  }


  onDateChange(event: MatDatepickerInputEvent<Date> | any) {
    const controlName = event.targetElement.id;
    const selectedDate = event.value;

    if (controlName != '') {
      this.form.get('start').setValue(selectedDate);
      this.form.get('end').setValue('')

    } else if (controlName === '') {
      this.form.get('end').setValue(selectedDate);
     
    }
    if (this.form.value.start != undefined && this.form.value.end != undefined && this.form.value.start != '' && this.form.value.end != '' && this.form.value.start != null && this.form.value.end != null && this.globalUserService.userDetails.value.roleId !=Roles.OperationStaff && this.globalUserService.userDetails.value.roleId !=Roles.TrainingInstitue ) {
      this.http.get(`${environment.baseUrl}formmsvc/api/form/get-candidate-import-files?startdate=${moment(this.form.value.start).format('YYYY-MM-DD')}&enddate=${moment(this.form.value.end).format('YYYY-MM-DD')}`).subscribe((data: any) => {
        if (data.length > 0) {
          this.states = data
          
        } else {
          this.states = [{id:0,fileName:'no record found'}]

        }
      }, (err: HttpErrorResponse | any) => {
        this.states = [{id:0,fileName:'no record found'}]
        this.snackbar.callSnackbaronError(`${err.message.message.error}`)

      })
    }else if(this.form.value.start != undefined && this.form.value.end != undefined && this.form.value.start != '' && this.form.value.end != '' && this.form.value.start != null && this.form.value.end != null){
      let Value:any ={fromDate:moment(this.form.value.start).format('MM-DD-YYYY'),toDate:moment(this.form.value.end).format('MM-DD-YYYY'),tableselected:this.tabselected,curModuleId:this.curModuleId,textValue:"",reviewFromDate:this.formReview.value.reviewstart != undefined && this.formReview.value.reviewend != undefined && this.formReview.value.reviewstart != '' && this.formReview.value.reviewend != '' && this.formReview.value.reviewstart != null && this.formReview.value.reviewend != null?moment(this.form.value.reviewstart).format('MM-DD-YYYY'):'',reviewToDate:this.formReview.value.reviewstart != undefined && this.formReview.value.reviewend != undefined && this.formReview.value.reviewstart != '' && this.formReview.value.reviewend != '' && this.formReview.value.reviewstart != null && this.formReview.value.reviewend != null?moment(this.form.value.reviewend).format('MM-DD-YYYY'):''}
      this.dateSelectedEvent.emit(Value);
      this.selectedMatTab()
    }

    // Optional: You can trigger additional actions here if needed
  }

  onReviewDateChange(event: MatDatepickerInputEvent<Date> | any) {
    const controlName = event.targetElement.id;
    const selectedDate = event.value;

    if (controlName != '') {
      this.form.get('reviewstart').setValue(selectedDate);
      this.form.get('reviewend').setValue('')

    } else if (controlName === '') {
      this.formReview.get('reviewend').setValue(selectedDate);
     
    }
   if(this.formReview.value.reviewstart != undefined && this.formReview.value.reviewend != undefined && this.formReview.value.reviewstart != '' && this.formReview.value.reviewend != '' && this.formReview.value.reviewstart != null && this.formReview.value.reviewend != null){
      let Value:any ={reviewFromDate:moment(this.formReview.value.reviewstart).format('MM-DD-YYYY'),reviewToDate:moment(this.formReview.value.reviewend).format('MM-DD-YYYY'),tableselected:this.tabselected,curModuleId:this.curModuleId,textValue:"approved date",fromDate:this.form.value.start != undefined && this.form.value.end != undefined && this.form.value.start != '' && this.form.value.end != '' && this.form.value.start != null && this.form.value.end != null?moment(this.form.value.start).format('MM-DD-YYYY'):'',toDate:this.form.value.start != undefined && this.form.value.end != undefined && this.form.value.start != '' && this.form.value.end != '' && this.form.value.start != null && this.form.value.end != null?moment(this.form.value.end).format('MM-DD-YYYY'):''}
      this.dateSelectedEvent.emit(Value);
      this.selectedMatTab()
    }

    // Optional: You can trigger additional actions here if needed
  }

  getFileName(){
    // this.states = this.states.length > 0 ?this.states:[{fileName:"no record found"}]
          this.globalUserService.getFilterData().subscribe((data: any) => {
               if(data){
                 this.states = data
                 this.searchStates = data
               }
          },(err)=>{
              this.states =[{fileName:"no record found"}]
          })

  }


  selectExamtypes(){
     this.http.get(environment.baseUrl + `client/api/exam/examtype`).subscribe((data:any)=>{
         if(data.length > 0){
           this.ExamTypes = data 
         }
     })
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      (
        (changes['accessData'] && changes['accessData'].currentValue && this.displayedColumns && this.displayedColumns.length > 0)
        ||
        (changes['displayedColumns'] && changes['displayedColumns'].currentValue && this.accessData)
      ) && this.selectedIndex != null
    )
      this.accessMappingLogic();
  }

  accessMappingLogic() {
    if (this.accessData) {
      Object.entries(this.accessData).forEach((element, index, self) => {
        if (!element[1]) {
          let actionIndex = this.displayedColumns[this.selectedIndex]?.findIndex((x: column) => { return x.cellType == 'action' });
          let statusColumnIndex = this.displayedColumns[this.selectedIndex]?.findIndex((x: column) => { return x.cellType == 'toggle' });
          switch (element[0]) {
            case 'add':
              for (let i = 0; i < this.tableOptions.showAddButton.length; i++) this.tableOptions.showAddButton[i] = false;
              break;
            case 'edit':
              if (actionIndex > -1) {
                let editIndex = this.displayedColumns[this.selectedIndex][actionIndex].actions.findIndex((x: action) => { return x.type == actionTypes.Edit });
                if (editIndex > -1) this.displayedColumns[this.selectedIndex][actionIndex].actions.splice(editIndex, 1);
              }
              break;
            case 'delete':
              if (actionIndex > -1) {
                let deleteIndex = this.displayedColumns[this.selectedIndex][actionIndex].actions.findIndex((x: action) => { return x.type == actionTypes.Delete });
                if (deleteIndex > -1) this.displayedColumns[this.selectedIndex][actionIndex].actions.splice(deleteIndex, 1);
              }
              break;
            case 'status':
              if (statusColumnIndex > -1) {
                this.displayedColumns[this.selectedIndex].splice(statusColumnIndex, 1);
              }
              break;
            case 'view':
              // let linkIndex = this.displayedColumns[this.selectedIndex].findIndex((x: column) => x.cellType == 'link');
              var linkActionIndex = -1;
              var viewActionIndex = -1;
              if (actionIndex > -1) {
                // viewActionIndex = this.displayedColumns[this.selectedIndex][actionIndex].actions.findIndex((x: action) => x.type == actionTypes.View);
                linkActionIndex = this.displayedColumns[this.selectedIndex][actionIndex].actions.findIndex((x: action) => x.type == actionTypes.Link);
              }
              // if (linkIndex > -1) this.displayedColumns[this.selectedIndex][linkIndex].cellType = 'text';
              // if (viewActionIndex > -1) this.displayedColumns[this.selectedIndex][actionIndex].actions.splice(viewActionIndex, 1);
              if (linkActionIndex > -1) this.displayedColumns[this.selectedIndex][actionIndex].actions.splice(linkActionIndex, 1);
              break;
          }
        }
      })
      this.tabIndexValueChangeLogic(this.selectedIndex, false);
    }
  }

  getObject(id: string, statuses: Array<status>) {
    return statuses.find((x: status) => { return x.text == id });
  }

  filterThings(things: Array<any>, searchTerm: string) {
    return things.filter((item: any) => {
      return JSON.stringify(item)
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    });
  }
  filterStates() {

    this.states = this.searchStates.filter(
      item => item.stateName.toLowerCase().includes(this.searchText.toLowerCase())
    );

    // if(this.globalUserService.userDetails.value.roleId === Roles.StateClient){
    //   // this.states = this.searchStates.filter(x=>this.globalUserService.userDetails.value.stateName === x.stateName)
    // }

  }

  downloadBulkImportTemplate(){
    const dialog = this.dialog.open(OMRVideoImage,
      {
        data: { actiontype: 'Bulk' },
        width: '600px',

      });
    dialog.afterClosed().subscribe((data) => {
      if (data.confirmed) {
        // this.getFileName()
        this.snackbar.callSnackbaronSuccess('Uploaded Successfuly')
      }
    });
  }


  PlaceOrder(){
     this.router.navigateByUrl('/Skills/skills-details')
  }

  



  filterStatesFiles() {
        this.states = this.searchStates.filter(
          item => item.fileName.toLowerCase().includes(this.searchText.toLowerCase())
        );
      }

  getNewPageData(event: PageEvent) {
    this.store.dispatch(setPageSize({ pageSize: event.pageSize, pageNumber: event.pageIndex }))

    this.pageIndexControl.setValue(event.pageIndex, { emitEvent: false });
    if (this.tableOptions.isPaginationServerSide && this.tableOptions.isPaginationServerSide[this.selectedIndex]) {
      var searchText = this.searchControl.value.trim();
      var date = new Date(searchText);

      if (this.searchColumn && (this.searchColumn == "dateofBirth" || this.searchColumn == "ExpirationDate") && Object.prototype.toString.call(date) === "[object Date]") {
        // it is a date
        if (isNaN(date.getTime())) { // d.getTime() or d.valueOf() will also work
          // date object is not valid
        } else {
          // date object is valid
          var datePipe
          searchText = moment(this.searchControl.value.trim()).format('YYYY-MM-DD')
        }
      }
      else if (this.searchColumn && typeof (this.searchColumn) == 'object' && this.searchColumn.find(x => x == "firstName")) {
        this.searchColumn = "candidateName"
      }
      else if (this.searchColumn && this.searchColumn == "id") {
        this.curModuleId == ModuleID.Suuport_Ticket ? this.searchColumn = ColumnID.TicketId : this.curModuleId == ModuleID.Problem_Report ? this.searchColumn = ColumnID.ReportId : null
      }
      this.nextPageDataFetchEvent.emit({
        searchText: searchText,
        searchColumn: this.searchColumn,
        pageEvent: {
          length: event.length,
          pageIndex: event.pageIndex,
          previousPageIndex: event.previousPageIndex,
          pageSize: event.pageSize
        }
      });
    }
  }

  columnText(data: any, id: Array<string> | string, cellArrayProps: Array<string>, cellType: string, cellDataMapping: any = null) {
    if ((id === "pastAddress" && this.globalUserService.userDetails.getValue().roleId == Roles.StateClient && this.globalUserService.userDetails.getValue().stateId == StateLists.PA)) {
      id = data.pastAddress == "NULL" ? "" : 'pastAddress';
    }
    if ((id === "pastFullName" && this.globalUserService.userDetails.getValue().roleId == Roles.StateClient && this.globalUserService.userDetails.getValue().stateId == StateLists.PA)) {
      id = data.pastFullName == "NULL" ? "" : 'pastFullName';
    }
    if ((id === "examCancelComments")) {
      id = data.examCancelComments == "Changing status from no show to cancel" ? "" : 'examCancelComments';
    }
    if(id === "transId" || id ==="transStatus" ||id ==="authAmount"|| id ==="settleAmount" || id ==="settleDateUTC" || id==="quantity" || id ==="unitPrice"){
      return id  = id ==="transId"? (data?.transaction?.transId !=undefined ?data?.transaction?.transId:data.transId):id ==="authAmount"?data?.transaction?.authAmount:id ==="settleAmount"?(data?.transaction?.settleAmount !=undefined ?data?.transaction?.settleAmount:data.settleAmount):id ==="transStatus"?data?.transaction?.transStatus:id ==="settleDateUTC"?data?.transaction?.settleDateUTC:id ==="quantity"?data?.lineItems[0].quantity:id ==="unitPrice"?data.lineItems[0].unitPrice:null
    }
    if(id === "refundTransactionId" || id ==="refundTotalAmount" ||id ==="refundDate"|| id ==="refundRemark" || id ==="refundInitiatedEmpID" || id ==="refundStatus" || id ==="refundId"){
      return id  = id ==="refundTransactionId"? data?.refundTransaction?.refundTransactionId:id ==="refundDate"?((data?.refundTransaction?.refundDate !=null )?data?.refundTransaction?.refundDate:data?.order?.refundDate):id ==="refundRemark"?(data?.refundTransaction?.refundRemark !=null?data?.refundTransaction?.refundRemark :data?.order?.refundRemark):id ==="refundTotalAmount"?(data?.refundTransaction?.refundTotalAmount !=null?data?.refundTransaction?.refundTotalAmount: data?.order?.refundTotalAmount):id ==="refundStatus"?(data?.refundTransaction?.refundStatus !=null?data?.refundTransaction?.refundStatus:data?.order?.refundStatus):id ==="refundInitiatedEmpID" ?(data?.refundTransaction?.refundInitiatedEmpID !=null?data?.refundTransaction?.refundInitiatedEmpID:data?.order?.refundInitiatedEmpID): id ==="refundId"?data?.order?.refundId:null
    }

    if(id === "amount" || (id ==="orderStatus" && this.curModuleId != 'booklet_inventory') ||id ==="authAmount"|| id ==="settleAmount" || id ==="settleDateUTC" || id==="quantity" || id ==="unitPrice" ||id ==="transStatus" ){
      return id  = id ==="amount"? (data?.order?.amount !=undefined ?data?.order?.amount:data.amount):id ==="orderStatus"?data?.order?.orderStatus:id ==="settleAmount"?(data?.transaction?.settleAmount !=undefined ?data?.transaction?.settleAmount:data.settleAmount):id ==="transStatus"?data?.transaction?.transStatus:id ==="settleDateUTC"?data?.transaction?.settleDateUTC:id ==="quantity"?data?.lineItems[0].quantity:id ==="unitPrice"?data.lineItems[0].unitPrice:null
    }
    if(id === 'examModeId' || (id === 'examName' && window.location.href ===( window.location.host ==="localhost:8002"?'http://localhost:8002/Skills':`${environment.redirectUrl}client/Skills`))){
       return id =id ==='examModeId' ?data.examModeId == '1'?"Online":"Test Center":id ==='examName'?data.exams[0].examName:null
    }
    if(id === 'createdBy'){
        return id  = id =="createdBy"?data.createdBy.emailId:''
    }
    if(id ==='waybillNumber'){
      return id  = id =="waybillNumber" && data.orderStatus !="Error"?data.waybillNumber:''
    }
    if(id ==='scoreReceived'){
      return id  = id == "scoreReceived" ? '' : '';
    }
    if((this.curModuleId =='certificate-renewal' || this.curModuleId =='duplicate-certificates' || this.curModuleId =='reciprocity-request') && (id =='expirationDate')){
       return id = data.registryDetail !=null && data.registryDetail !='' && data.registryDetail !=undefined?data.registryDetail.expirationDate:null
    }
    if((this.curModuleId =='certificate-renewal' || this.curModuleId =='duplicate-certificates' || this.curModuleId =='reciprocity-request' || this.curModuleId =='manage-Reinstatement') && (id =='certificateIssueDate')){
      return id = data.registryDetail !=null && data.registryDetail !='' && data.registryDetail !=undefined?data.registryDetail.certificateIssueDate:null
   }

    

    return typeof id == 'string' ? this.cellText(data[id], cellArrayProps, cellType, cellDataMapping) : id.map((x: string) => { return this.cellText(data[x], cellArrayProps, cellType, cellDataMapping) }).join(' ');
  }
  cellText(cellData: any, cellArrayProps: Array<string>, cellType: string, cellDataMapping: any) {
    if (cellType == "json" && cellData) {
      let temp = (typeof cellData == 'string') ? JSON.parse(cellData) : cellData;
      let retValue = '';
      cellArrayProps.forEach((x: string) => {
        if (temp[x])
          retValue == '' ? retValue += temp[x] : retValue += '-' + temp[x];
      })
      return retValue == '' ? 'N/A' : retValue;
    }
    return Array.isArray(cellData) ? cellData.map((cellEle: string) => {
      let retValue = '';
      cellArrayProps.forEach((x: string) => {
        retValue += cellEle[x];
      })
      return retValue;
    }).join(', ') : getCellData(cellData, cellDataMapping);
    function getCellData(cellData, cellDataMapping): string {
      if (cellDataMapping == null)
        return cellData;
      else {
        if (cellDataMapping[cellData])
          return cellDataMapping[cellData];
        else
          return cellDataMapping['default'];
      }
    }
  }
  toggleColumnVisibility(colIndex: number, $event: Event) {
    this.displayedColumns[this.selectedIndex][colIndex].visible = this.displayedColumns[this.selectedIndex][colIndex].visible ? false : true;
    this.displayedColsIndices = this.displayedColumns[this.selectedIndex].filter((x: column) => { return x.visible }).map((x: column) => this.getColumnId(x.id));
    $event.stopPropagation();
    $event.stopImmediatePropagation();
  }

  toggleColumnFilter(colIndex: number, $event: Event) {
    if (!this.displayedFilterColmns[colIndex].isFilter)
      this.displayedFilterColmns.forEach(x => x.isFilter = false);

    this.displayedFilterColmns[colIndex].isFilter = !this.displayedFilterColmns[colIndex].isFilter;

    this.searchColumn = (this.displayedFilterColmns[colIndex].isFilter) ? this.displayedFilterColmns[colIndex].id : null;
    this.SelectedColoumWiseFilter = this.displayedFilterColmns[colIndex].name
    if (this.searchControl.value) {
      this.tableOptions.isSearchServerSide && this.tableOptions.isSearchServerSide[this.selectedIndex]
        ?

        this.getNewPageData({
          length: null,
          pageIndex: 1,
          pageSize: this.pageSizeControl.value,
          previousPageIndex: 1
        })
        :
        this.dataSource.filter = this.searchControl.value.trim().toLocaleLowerCase();
      // this.searchEvent.emit({ searchKey: this.searchControl.value });

    }


    // if(this.stateControl.value)
    // {
    //   if(!this.tableOptions.isStateSearchServerSide)

    // this.dataSource.filter = this.stateControl.value.trim().toLocaleLowerCase();
    // this.searchEvent.emit({ searchKey: this.stateControl.value });

    // }
    //this.displayedColsIndices = this.displayedFilterColmns[this.selectedIndex].filter((x: column) => { return x.isFilter }).map((x: column) => this.getColumnId(x.id));
    $event.stopPropagation();
    $event.stopImmediatePropagation();
  }

  actionHandler(
    column: column,
    element: any,
    rowIndex: number,
    actionIndex: number
  ) {
    this.action.emit(<performedAction>{
      column,
      element,
      rowIndex,
      action: column.actions[actionIndex],
    });
  }
  cellClickHandler(column: column, element: any, rowIndex) {
    var index = this.dataSource.data.findIndex((x: any) => { return lodash.isEqual(x, element) });
    // var index = this.dataSource.data.findIndex((x:any)=>{ JSON.stringify(x)=== JSON.stringify(element)});
    column.cellType == "link" ?
      this.linkClicked.emit(<linkClickEvent>{
        column,
        element,
        rowIndex: index,
      })
      :
      this.cellClicked.emit(<cellClickEvent>{
        column,
        element,
        rowIndex: index,
      });
  }
  headerClickHandler(column: column) {
    this.headerClicked.emit(<headerClickEvent>{
      column,
    });
  }
  headerDropdownSelectionHandler(column: column, dropdownKey: any) {
    this.headerDropdownSelection.emit(<headerDropdownSelectionEvent>{
      column,
      dropdownKey,
    });
  }
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }
  checkBoxClickedHandler(row: any = null) {
    row ?
      this.selection.toggle(row)
      :
      this.masterToggle();
    this.checkboxClicked.emit({
      selection: this.selection.selected
    })
  }

  getColumnId(columnId: string | Array<string>) {
    return Array.isArray(columnId) ? columnId[0] : columnId;
  }

  tabSelectionHandler(tabIndex: number) {
    if (this.searchControl.value) this.searchControl.setValue(this.searchControl.value);
  }

  toggleHandler($event:MatSlideToggleChange, column, element, rowIndex) {
    var index = this.dataSource.data.findIndex((x: any) => { return lodash.isEqual(x, element) });
    $event.source.checked = !$event.checked;
    this.toggleEmitter.emit({ column, element, rowIndex: index });
  }
  
 /// Only for Manage training Program Enable/disable the toggle /////
  toggleHandlers($event, column, element, rowIndex){

      if($event.checked == false){
        this.dialog.open(DynamicPopupMesageComponent, {
          data: {
            title: `${element.name}`,
            message: this.lngSrvc.curLangObj.value.StatusUpdate,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
            status:this.curModuleId
          },
        }).afterClosed().subscribe((data)=>{

            if(data?.confirmed == true && (data !='' && data !=null && data !=undefined)){
              let body = [{
                id: element.id,
                active: $event.checked
             }]
             this.http.put(environment.baseUrl + `client/api/state/status`,body).subscribe((data)=>{
              if(data){
                this.snackbar.callSnackbaronSuccess("Disabled SucessFully")
              }
           })
            }else{
               $event.source.checked = true
            }
        })
      }else{
         let body = [{
          id: element.id,
          active:$event.checked
         }]
         this.http.put(environment.baseUrl + `client/api/state/status`,body).subscribe((data)=>{
          if(data){
            this.snackbar.callSnackbaronSuccess("Enabled SucessFully")
          }
       })
        
      }

  }
  getTabVisibility(rules: Array<VisibilityRule>) {
    if (rules && rules.length > 0) {
      if (rules != null && rules != undefined && rules.length > 0) {
        var result = null;
        for (let i = 0; i < rules.length; i++) {
          result = this.parseVisibilityLogic(rules[i], this.comparator(rules[i], this.globalUserService.userDetails.value[rules[i].elementProperty]), result);
        }
        return result;
      }
      else {
        return true;
      }
    }
    return true;
  }

  getHeaderVisibility(rules: Array<VisibilityRule>) {
    if (rules && rules.length > 0) {
      if (rules != null && rules != undefined && rules.length > 0) {
        var result = null;
        for (let i = 0; i < rules.length; i++) {
          // result = rules[i].extractedFromGlobal==true && ?this.parseVisibilityLogic(rules[i], this.comparator(rules[i], this.globalUserService.userDetails.value[rules[i].elementProperty]), result)
          if (rules[i].extractedFromGlobal) {
            result = this.parseVisibilityLogic(rules[i], this.comparator(rules[i], this.globalUserService.userDetails.value[rules[i].elementProperty]), result)
          }
          else if (rules[i].extractedFromElement) {
            result = this.parseVisibilityLogic(rules[i], this.comparator(rules[i], this.dateFromElement.rules[i].elementProperty), result);
          }
        }
        return result;
      }
      else {
        return true;
      }
    }
    return true;
  }

  checkHasVisibility(column) {
    if ('visibilityRule' in column) {
      return true;
    }
    else {
      return false;
    }
  }

  getActionVisibility(rowData: any, rules: Array<VisibilityRule>) {

    // if (!rowData.emailId) {
    if (rules != null && rules != undefined && rules.length > 0) {
      var result = null;
      for (let i = 0; i < rules.length; i++) {
        result = this.parseVisibilityLogic(
          rules[i],
          this.comparator(
            rules[i],
            rules[i].extractedFromGlobal
              ?
              this.globalUserService.userDetails.value[rules[i].elementProperty]
              :
            rowData[rules[i].elementProperty]),
          result);

      }
      return result;
    }
    else {
      return true;
    }
    // }
    // return true;
  }
  mergeAccount() {
    this.mergeRequested.emit({ selected: this.selection.selected });
  }

  BulkCertificateDownload() {
    this.CertificateEvent.emit({ selected: this.selection.selected });
  }

  download() {
    this.http.get(environment.baseUrl + `client/api/api/Employer/${this.globalUserService.userDetails.getValue().clientORtenantId}/employees/download`, { observe: 'body', responseType: 'blob' as 'json' }).subscribe((data: any) => {
      var blobResponse = data as Blob;
      FS.saveAs(blobResponse, 'Employees.xlsx');

    })
  }

  downloadCertificate(items) {
    this.http.post(`${environment.baseUrl}registry/api/Certificate/view-cetificatewithDetail?registryId=${items.Id}&certificateTypeId=${items.CertType}`, {}, { responseType: 'text' })
    .pipe()
    .subscribe((data) => {
        if (data) {
            const printWindow = window.open('', '_blank');
            printWindow.document.open();
            printWindow.document.write(`
                        ${data}
            `);
            printWindow.document.close();

            const images:any = printWindow.document.getElementsByTagName('img');
            const totalImages = images.length;
            let loadedImagesCount = 0;

            const handleImageLoad = () => {
                loadedImagesCount++;
                if (loadedImagesCount === totalImages) {
                    printWindow.print();
                    setTimeout(() => {
                        printWindow.close();
                    }, 500);
                }
            };

            for (let img of images) {
                img.onload = handleImageLoad;
                img.onerror = handleImageLoad; // Count errors as loaded
            }

            // Print immediately if there are no images
            if (totalImages === 0) {
                printWindow.print();
                setTimeout(() => {
                    printWindow.close();
                }, 500);
            }
        }
    }, (error) => {
        console.error('Error fetching certificate:', error);
    });

   
}


  downloadCandidatePayment() {
    const dialogRefund = this.dialog.open(PaymentRefundPopupComponent, {
      data: { pageNo: this.pageIndexControl.value, pageSize: this.pageSizeControl.value,message:"RefundPaymentDownlaod",tabselected:this.tabselected }
    })
  
    dialogRefund.afterClosed().subscribe((data: any) => {
    })
  }



  comparator(rule: VisibilityRule, rowDataValue: any) {
    switch (rule.compareWithOperator) {
      case comparisionOperators.EQUAL: return rule.shouldHaveValue == rowDataValue;
      case comparisionOperators.NOTEQUAL: return rule.shouldHaveValue != rowDataValue;
      case comparisionOperators.LESSTHAN: return rule.shouldHaveValue < rowDataValue;
      case comparisionOperators.LESSTHANEQUAL: return rule.shouldHaveValue <= rowDataValue;
      case comparisionOperators.GREATERTHAN: return rule.shouldHaveValue > rowDataValue;
      case comparisionOperators.GREATERTHANEQUAL: return rule.shouldHaveValue >= rowDataValue;
    }
  }

  handleScoreSelection($event) {
    this.scoreSelectionEvent.emit($event);
    this.globalUserService.score.next($event.value);
    this.selectedMatTab();
  }

  handleStateSelection($event) {

    this.stateSelectionEvent.emit($event);

    this.globalUserService.state = $event.value
    if (this.searchControl.value) this.searchControl.setValue(this.searchControl.value);
    if (this.stateControl.value == "Clear Selection") this.stateControl.setValue("")

  }

  handleOrderStatusSelection($event) {
    if ($event.value == "") {
      this.orderStatusControl.setValue("");
    }
    this.bookletorderStatusSelect.emit($event)
    this.selectedMatTab()

    // if ($event.value == "all") this.orderStatusControl.setValue("")
  }
  
  parseVisibilityLogic(visRule: VisibilityRule, tempRes: boolean, intermediateRes: boolean = null) {
    if (visRule.notApplied) tempRes = !tempRes;
    if (intermediateRes != null) {
      switch (visRule.operator) {
        case Operators.AND: intermediateRes = intermediateRes && tempRes; break;
        case Operators.OR: intermediateRes = intermediateRes || tempRes; break;
      }
      return intermediateRes;
    }
    return tempRes;
  }
  MatToggleDisaable(column) {
    return column.showOnlyData && (this.globalUserService.userDetails.value.roleId == Roles.StateClient || this.globalUserService.userDetails.value.roleId == Roles.SuperAdmin || (this.globalUserService.userDetails.value.roleId == Roles.OperationStaff && this.curModuleId === 'Manage State Client'))
  }

  clearData() {
    this.SelectedColoumWiseFilter = null
    this.displayedFilterColmns.forEach(x => x.isFilter = false);
    this.searchColumn = null
    this.searchControl.value ? this.searchControl.setValue(this.searchControl.value) : null
  }

  tabClick(event: any) {
    this.tabselected = event.tab.textLabel
    this.form.reset()
    if (this.globalUserService.userDetails.getValue().roleId == Roles.StateClient && this.globalUserService.userDetails.getValue().stateId == StateLists.PA) {
      let mat_tab = this.tableOptions.matTabs.filter(x => tabs.includes(x.name))
      if (mat_tab.length > 0) {
        this.tableOptions.matTabs = mat_tab
        this.displayedColumns = this.displayedColumns.filter(x => x.length != 6)
        this.tabSelected.emit({ tabIndex: this.tableOptions.matTabs.findIndex(x => x.name == event.tab.textLabel) })
      } else {
        this.tabSelected.emit({ tabIndex: this.tableOptions.matTabs.findIndex(x => x.name == event.tab.textLabel) })
      }
    }
    else {
      this.tabSelected.emit({ tabIndex: this.tableOptions.matTabs.findIndex(x => x.name == event.tab.textLabel) })
    }
    if(this.globalUserService.userDetails.getValue().roleId == Roles.SupportingStaff){

      this.tableOptions.showAddButton[1] = false
      
   }
  }

  selectedMatTab() {
    this.getNewPageData(
      {
        length: null,
        pageIndex: 1,
        pageSize: this.pageSizeControl.value,
        previousPageIndex: 0,
      })
    this.pageIndexControl.value != 1 ? this.pageIndexControl.setValue(1) : null;
  }
}

