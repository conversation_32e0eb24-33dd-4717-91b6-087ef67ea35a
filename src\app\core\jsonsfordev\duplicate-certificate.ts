import { FormTypes } from "src/app/client/application/application.types";
import { State, consoleLoaderJSON, moduleTypes, nestingTypes, paramTypes, statuses, widgetTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import * as tableTypes from "src/app/core/common-component/table/dynamic-table-types";
import * as singleEntryTableTypes from "src/app/core/common-component/single-entry-table/single-entry-table.types";


export const moduleJSON: consoleLoaderJSON = {
    modules: [
        {
            type: moduleTypes.Dynamic,
            name: "Duplicate Registration",
            moduleID: "duplicate-certificates",
            nestingType: nestingTypes.ColumnsInRows,
            iconRef: "duplicateCertificates",
            routeRef: "/loader",

            rows: [
                {
                    class: "",
                    gdColumns: "1fr",
                    gdColumnsltlg: "1fr",
                    gdColumnsltmd: "1fr",
                    gdColumnsltsm: "1fr",
                    gdGap: "12px",
                    columns: [
                        {
                            class: "w-full",
                            gdColumn: "1 / 1",
                            gdColumnltlg: "1 / 1",
                            gdColumnltmd: "1 / 1",
                            gdColumnltsm: "1",
                            fxLayout: "",
                            fxLayoutAlign: "start center",
                            fxFlex: "none",
                            fxFlexltmd: "auto",
                            fxHidexs: "false",
                            fxHidesm: "false",
                            widget: {
                                type: widgetTypes.DynamicTable,
                                tableOptions: {
                                    headerRowCssClasses: ['tab-col'],
                                    showPaginator: true,
                                    pageSizeOptions: [8, 20, 100],
                                    pageSize: 8,
                                    showAddButton: [false],
                                    showMatTabs: true,
                                    showSearch: true,
                                    showColumnFilter: true,
                                    showColumnWiseFilter: true,
                                    verticalScroll: true,
                                    horizontalScroll: true,
                                    ShowFilenameFilter:true,
                                    matTabs: [
                                        { id: 1, name: "Pending" },
                                        { id: 2, name: "Overdue" },
                                        { id: 3, name: "Approved" },
                                        { id: 4, name: "Rejected" },
                                        { id: 5, name: "All" },
                                    ],
                                    showStateSelectionDropdown:true,
                                    isPaginationServerSide: [true, true, true, true, true],
                                    isSearchServerSide: [true, true, true, true, true]
                                },
                                displayedColumns: [
                                    [
                                       
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "certificateIssueDate",
                                            name: "certificate Issue Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            cellType: "status",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                            statuses: tableTypes.tableStatuses
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "duplicate-certificates-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Duplicate Registration Details",
                                            },
                                        }
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false,
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false,
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "certificateIssueDate",
                                            name: "certificate Issue Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            cellType: "status",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                            statuses: tableTypes.tableStatuses
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "duplicate-certificates-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Duplicate Registration Details",
                                            },
                                        }
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "checkbox",
                                            name: "checkbox",
                                            cellType: "text",
                                            headerType: "checkbox",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ["bg-transparent", "w-4"],
                                            cellCssClasses: [],
                                          },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "certificateIssueDate",
                                            name: "certificate Issue Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "reviewCompletedDate",
                                            name: "Application approved date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            cellType: "status",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                            statuses: tableTypes.tableStatuses
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                },
                                                <tableTypes.action>{
                                                    icon: "download",
                                                    type: tableTypes.actionTypes.Certificdown,
                                                    tooltip: "Certificate Download",
                                                    color: "#7d7d7d",
                                                    visibilityRule:[
                                                        {
                                                            operator: tableTypes.Operators.AND,
                                                            elementProperty: 'stateId',
                                                            compareWithOperator: tableTypes.comparisionOperators.EQUAL,
                                                            shouldHaveValue: State.Pennsylvania
                                                        }
                                                    ]
                                                }
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "duplicate-certificates-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Duplicate Registration Details",
                                            },
                                        }
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                         
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "certificateIssueDate",
                                            name: "certificate Issue Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                           
                                            cellCssClasses: ["text-center"],
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            cellType: "status",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                           
                                            statuses: tableTypes.tableStatuses
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "duplicate-certificates-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Duplicate Registration Details",
                                            },
                                        }
                                    ],
                                    [
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "personId",
                                            name: "Candidate ID",
                                            cellType: "text",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: [],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false,
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: ["firstName", "lastName"],
                                            name: "Candidate Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            filterable: true,
                                            isFilter: false,
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "stateName",
                                            name: "State of Certification",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "examName",
                                            name: "Registration Name",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "expirationDate",
                                            name: "Expiry Date",
                                            cellType: "date",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                          
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "certificateIssueDate",
                                            name: "certificate Issue Date",
                                            cellType: "date",
                                            headerType: "sort",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: ["w-1/12"],
                                            
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "currentAssigneeUserName",
                                            name: "Working Status",
                                            cellType: "text",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table', "text-center"],
                                            cellCssClasses: ["text-center"],
                                           
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "statusName",
                                            name: "Status",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            cellType: "status",
                                            headerCssClasses: ["db-table", "text-center"],
                                            cellCssClasses: ["text-center"],
                                          
                                            statuses: tableTypes.tableStatuses,
                                        },
                                        <tableTypes.column>{
                                            visible: true,
                                            id: "action",
                                            name: "Action",
                                            cellType: "action",
                                            headerType: "normal",
                                            headerAlign: "center",
                                            cellAlign: "center",
                                            headerCssClasses: ['db-table'],
                                            cellCssClasses: [],
                                            actions: [
                                                <tableTypes.action>{
                                                    icon: "visibility",
                                                    type: tableTypes.actionTypes.Link,
                                                    tooltip: "View",
                                                    color: "#7d7d7d",
                                                    // requestDetails: [
                                                    //     {
                                                    //         fetchUrl: [
                                                    //             "client",
                                                    //             "api",
                                                    //             "form",
                                                    //             `assignpersonform`,
                                                    //         ],
                                                    //         requestBody: null,
                                                    //         requestParams: [
                                                    //             {
                                                    //                 paramType: paramTypes.Optional,
                                                    //                 paramName: "personFormId",
                                                    //                 paramValue: null,
                                                    //                 extractedFromElement: true,
                                                    //                 elementPropertyToBeExtracted: "personFormId",
                                                    //             },
                                                    //             {
                                                    //                 paramName: "personTenantRoleId",
                                                    //                 paramValue: null,
                                                    //                 extractedFromElement: true,
                                                    //                 elementPropertyToBeExtracted: "personTenantRoleId",
                                                    //             }
                                                    //         ],
                                                    //         method: "GET",
                                                    //     },
                                                    // ],
                                                },
                                            ],
                                            linkMetaData: {
                                                navigateToType: moduleTypes.Offspring,
                                                navigateTo: "duplicate-certificates-details",
                                                eventDataUseful: false,
                                                navigateToLabel: "Duplicate Registration Details",
                                            },
                                        }
                                    ]
                                ],
                                requestDetails: [
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformregistry",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Certificate_Duplicate,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Pending,
                                            },
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformregistry",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Certificate_Duplicate,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "isCritical",
                                                paramValue: 'true',
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Pending,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "certType",
                                                paramValue: 5,
                                            }
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformregistry",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Certificate_Duplicate,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Approved,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "certType",
                                                paramValue: 5,
                                            }
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformregistry",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Certificate_Duplicate,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "statusId",
                                                paramValue: statuses.Rejected,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "certType",
                                                paramValue: 5,
                                            }
                                        ],
                                        method: "GET",
                                    },
                                    {
                                        fetchUrl: [
                                            "client",
                                            "api",
                                            "eligibility",
                                            "personformregistry",
                                            "list",
                                        ],
                                        requestBody: null,
                                        requestParams: [
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageNumber",
                                                paramValue: 1,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "pageSize",
                                                paramValue: 8,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "reviewRoleId",
                                                paramValue: null,
                                                extractedFromGlobal: true,
                                                elementPropertyToBeExtracted: 'roleId'
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "formTypeId",
                                                paramValue: FormTypes.Certificate_Duplicate,
                                            },
                                            {
                                                paramType: paramTypes.Optional,
                                                paramName: "certType",
                                                paramValue: 5,
                                            }
                                        ],
                                        method: "GET",
                                    }
                                ]
                            }
                        }
                    ]
                }
            ]
        }
    ]
}