import { Injectable } from '@angular/core';
import { StreamService } from './stream.service';
import { MonitoringService } from './monitoring.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class StreamSignalsService {
  
  CANDIDATE_SIGNAL_NOTIFICATION = 'canNotif'

  RED_FLAG= "r_flg";
  YELLOW_FLAG= "y_flg";
  GREEN_FLAG= "g_flg";

  constructor(private streamService:StreamService, private http:HttpClient,
    private monitoringService:MonitoringService) { }

  // formatSignalRequest(candidateData, remark: string, flag) {

  //   const formatRequest = {
  //     JToken: candidateData.Token,
  //     CandidateID: candidateData.CandidateID,
  //     IsStatus: "1",
  //     MovementDetection: "",
  //     Occurate: "",
  //     Persons: "",
  //     Remark: remark,
  //     TimeStamp: JSON.stringify(new Date().getTime()),
  //     session: candidateData.sessionID,
  //     ExamID: candidateData.ExamID,
  //     flag: flag,
  //   }

  //   return JSON.stringify(formatRequest);
  // }
  formatAIReport(message, flag, candidateData) {
    const formatRequest = {
      Msg: message,
      type: flag,
      by: "360 View Web",
      timestamp: JSON.stringify(new Date().getTime()),
      id: candidateData.ExamID + "_" + candidateData.CandidateID,
      etbid: candidateData.sessionID,
      vid: candidateData.VMasterID
    }

    return formatRequest;
  }
  // candidateExitApplication() {
  //   // this.streamService.session?.signal({
  //   //   data: this.formatSignalRequest(
  //   //     this.monitoringService.getCandidateDataValue(), 
  //   //     "Application destroyed", 
  //   //     this.RED_FLAG)
  //   // })

  //   // this.monitoringService.storeAIReports(this.formatAIReport("Candidate destroyed application", this.RED_FLAG, this.monitoringService.getCandidateDataValue())).subscribe();
  // }

  storeAIReports(body) {
    const headers = new HttpHeaders();
    headers.append("Content-Type", "application/json");
    headers.append("Accept", "application/json");

    return this.http
      .post(
        "https://3l7giimka4.execute-api.us-east-1.amazonaws.com/Prod/gesturelog",
        body,
        { headers }
      )
      .pipe(
        catchError((err) => {
          return of();
        })
      );
  }

  async batteryPercentage() {
    const battery = await (navigator as any).getBattery();
    const batteryPercentage = battery.level * 100;


    return batteryPercentage;
  }

  formatSignalRequestBattery(batteryPercentage: number, candidateData) {

    const formatRequest = {
      JToken: candidateData.Token,
      CandidateID: candidateData.CandidateID,
      IsStatus: "1",
      MovementDetection: "",
      Occurate: "",
      Persons: "",
      Remark: "Exam 360 battery : " + batteryPercentage + " %",
      TimeStamp: JSON.stringify(new Date().getTime()),
      session: candidateData.sessionID,
      ExamID: candidateData.ExamID,
      flag: batteryPercentage < 50 ? this.RED_FLAG : this.GREEN_FLAG,
    }

    return JSON.stringify(formatRequest);
  }
  
  async userConnectedSignal() {

    const batteryPercentage = await this.batteryPercentage();

    if(!this.streamService.session) {
      
      return;
    };

    this.streamService.session.signal({
      type: this.CANDIDATE_SIGNAL_NOTIFICATION, 
      data: this.formatSignalRequestBattery(batteryPercentage, this.monitoringService.getCandidateDataValue())
    });

    this.monitoringService.storeAIReports(this.formatAIReport("Candidate started application", this.GREEN_FLAG, this.monitoringService.getCandidateDataValue())).subscribe();

  }
}
