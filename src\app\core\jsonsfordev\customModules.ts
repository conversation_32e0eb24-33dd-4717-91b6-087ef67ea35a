import { consoleLoaderJSON, moduleTypes } from "src/app/client/dynamic-component-loader/dynamic-component-loader.types";
import icFlag from '@iconify/icons-ic/twotone-flag';

export const customModulesJSON: consoleLoaderJSON = {
  modules: [
    {
      type: moduleTypes.Custom,
      name: "Dashboard",
      moduleID: "dashboard",
      routeRef: '/dashboard',
      iconRef: "dashboardIcon",
      dataattributes:'dashboard'
    },
    {
      type: moduleTypes.Custom,
      name: "Schedule-Exam",
      moduleID: "schedule-exam",
      routeRef: "/scheduleExam",
      iconRef: "scheduleIcon",
    },
    {
      type: moduleTypes.Custom,
      name: "Manage Eligibilty Routes",
      moduleID: "manage-eligibilty-route",
      routeRef: '/manage-eligiblity',
      iconRef: "manageEligibilityIcon",
    },
    {
      type: moduleTypes.Custom,
      name: "Manage Voucher Codes",
      moduleID: "manage-voucher-code",
      routeRef: '/voucher-codes',
      iconRef: "manageVoucherCodes",
    },
    {
      type: moduleTypes.Custom,
      name: "Course Completions",
      moduleID: "training-program",
      routeRef: '/training',
      iconRef: "training",
    },  
    {
      type: moduleTypes.Custom,
      name: "Voucher",
      moduleID: "voucher",
      routeRef: '/voucher',
      iconRef: "gift_Card",
    }, 
    {
      type: moduleTypes.Custom,
      name: "Reports",
      moduleID: "reports-parent",
      routeRef: '/report',
      iconRef: "insert_chart_outlined",
      dataattributes:"reports"
    },
    {
      type: moduleTypes.Custom,
      name: "Reports",
      moduleID: "Super_Set",
      routeRef: '/superset',
      iconRef: "gift_Card",
    }, 


    
    {
      type: moduleTypes.Custom,
      name: "Events",
      moduleID: "publish_event",
      routeRef: '/SkillNAE/0/0/0/0/0/""',
      iconRef: "publish",
      dataattributes:'events'
    },
    
    {
      type: moduleTypes.Custom,
      name: "FAQ",
      moduleID: "faq",
      routeRef: '/faq',
      iconRef: "publish",
      dataattributes:'faq'
    },

    
    {
      type: moduleTypes.Custom,
      name: "NAE KIT",
      moduleID: "NaeKit",
      iconRef: "get_app",
      children:[
        {
          type: 'link',
          label: 'Getting Started',
          route: '/Naekit',
          icon: "kitchen"
        }
        // {
        //   type: 'link',
        //   label: 'FAQ',
        //   route: '/apps/help-center/faq'
        // },
        // {
        //   type: 'link',
        //   label: 'Guides',
        //   route: '/apps/help-center/guides'
        // }
      ]
    }, 
    {
      type: moduleTypes.Custom,
      name: "Roster",
      moduleID: "Scan",
      routeRef: '/Skills',
      iconRef: "scanner",
    },
    // {
    //   type: moduleTypes.Custom,
    //   name: "Roster",
    //   moduleID: "scan-roaster",
    //   routeRef: '/Skills',
    //   iconRef: "scanner",
    //   dataattributes:"roaster"
    // },
    {
      type: moduleTypes.Custom,
      name: "Test Site",
      moduleID: "TSM",
      routeRef: '/SkillNAE/0/0/0/10/0/""',
      iconRef: "scanner",
    }
 
  ]
}