.item {
  align-items: center;
  box-sizing: border-box;
  color: var(--sidenav-item-color);
  cursor: pointer;
  display: flex;
  flex-direction: row;
  min-height: 30px;
  // padding: var(--padding-4) var(--sidenav-item-padding);
  padding: 0rem 0.75rem;
  position: relative;
  text-decoration: none;
  transition: var(--trans-ease-out);
  user-select: none;
  width: 100%;
  border-left: var(--sidenav-item-border);

  &:hover {
    background: var(--hover-color);
  }
  &.active {
    background: var(--sidenav-item-background-active);
    border-left-color: var(--sidenav-item-border-color-active);

    .item-icon {
      fill: #209e91;
      color: var(--sidenav-item-icon-color-active) !important;
    }

    .item-label {
      // color:#209E91 !important;
      // color: var(--sidenav-color)!important;
      color: var(--sidenav-item-icon-color-active) !important;
    }

    .item-dropdown-icon {
      color: var(--sidenav-item-color-active);
    }
  }

  &.open {
    .item-dropdown-icon {
      transform: rotate(90deg) !important;
    }
  }
}

@for $i from 1 through 6 {
  :host(.item-level-#{$i}) .item {
    // background: var(--sidenav-item-dropdown-background);
    background: #f9f9f9 !important;

    padding-inline-start: calc(
      var(--sidenav-item-icon-size) +
        var(--sidenav-item-icon-gap) +
        var(--sidenav-item-padding) +
        (var(--sidenav-item-dropdown-gap) * #{$i - 1})
    );

    &:hover {
      // background: var(--sidenav-item-dropdown-background-hover);
      background: #f9f9f9 !important;
    }
  }
}

.item-icon,
.item-label,
.item-dropdown-icon {
  transition: inherit;
}

.item-icon {
  color: var(--sidenav-item-icon-color);
  font-size: var(--sidenav-item-icon-size);
  height: var(--sidenav-item-icon-size);
  margin-inline-end: var(--sidenav-item-icon-gap);
  width: var(--sidenav-item-icon-size);
}

.item-label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--sidenav-item-label-size);
}

.item-badge {
  border-radius: 9999px;
  font-size: 11px;
  line-height: 20px;
  margin-inline-start: var(--padding-8);
  padding: 0 7px;
  text-align: center;
}

/* New specific class for Reports NEW badge */
.reports-new-badge {
  border-radius: 50%; /* Back to perfect circle */
  font-size: 7px;
  line-height: 14px;
  margin-inline-start: 2px; /* Very very close to the text */
  padding: 1px 4px;
  text-align: center;
  min-width: 18px;
  height: 18px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;

  /* Force blue background and white text by default */
  &.bg-blue-600 {
    background-color: #2563eb !important;
    color: #ffffff !important;
    font-weight: bold !important;
  }

  /* Additional specificity to override any other styles */
  &.bg-blue-600.text-white {
    background-color: #2563eb !important;
    color: #ffffff !important;
    font-weight: bold !important;
  }
}

.item-dropdown-icon {
  color: var(--sidenav-item-icon-color);
  font-size: 18px;
  height: 18px;
  line-height: 18px;
  margin-inline-start: var(--padding-8);
  transform: rotate(0deg) !important;
  width: 18px;
}

.item-dropdown {
  overflow: hidden;
}

.dropdowncontent{
   color: black !important;
}

.subheading {
  box-sizing: border-box;
  color: var(--sidenav-item-color);
  font: var(--font-caption);
  margin-top: var(--padding);
  padding: var(--padding-12) var(--padding);
  text-transform: uppercase;
  white-space: nowrap;
}
