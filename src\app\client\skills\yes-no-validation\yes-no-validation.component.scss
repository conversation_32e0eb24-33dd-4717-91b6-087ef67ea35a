.radio-validation {
    ::ng-deep .mat-radio-button.mat-accent.mat-radio-checked {
        
        &.yes-option{
            .mat-radio-outer-circle {
                border-color: #0076C1;
            }
            .mat-radio-inner-circle {
                background-color: #0076C1;
            }
            .mat-radio-persistent-ripple {
                background-color: #0076C1;
            }
        }

        &.no-option{
            .mat-radio-outer-circle {
                border-color: #cf202a !important;
            }
            .mat-radio-inner-circle {
                background-color: #cf202a !important;
            }
        }
    }

    ::ng-deep mat-radio-button {

        &.yes-option{
            margin-left: 5px;
            margin-right: 5px;
        }
        &.no-option{
            margin-left: 5px;
            margin-right: 0;
        }

        label {
            margin-bottom: 0;
            /* font-family: Open Sans, sans-serif; */
            font-family: Roboto, "Helvetica Neue", sans-serif;
        }
    }
}

