import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { ApplicationRoutingModule } from "./application-routing.module";
import { ApplicationComponent } from "./application.component";
import { FlexLayoutModule } from "@angular/flex-layout";
import { SecondaryToolbarModule } from "src/@exai/components/secondary-toolbar/secondary-toolbar.module";
import { PageLayoutModule } from "src/@exai/components/page-layout/page-layout.module";
import { ContainerModule } from "src/@exai/directives/container/container.module";
import { MatTabsModule } from "@angular/material/tabs";
import { MatButtonModule } from "@angular/material/button";
import { MatIconModule } from "@angular/material/icon";
import { IconModule } from "@visurel/iconify-angular";
import { BreadcrumbsModule } from "src/@exai/components/breadcrumbs/breadcrumbs.module";
import { MatSelectModule } from "@angular/material/select";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CdkStepperModule } from "@angular/cdk/stepper";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatStepperModule } from "@angular/material/stepper";
import { NgDynamicBreadcrumbModule } from "ng-dynamic-breadcrumb";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { PopUpComponent } from "./pop-up/pop-up.component";
import { MatDialogModule } from "@angular/material/dialog";
import { CdkAccordionModule } from "@angular/cdk/accordion";
import { FormBuilderModule } from "src/app/core/common-component/examroom-formbuilder/form-builder.module";
import { MatTooltipModule } from "@angular/material/tooltip";
import { ProgressBarComponent } from "./progress-bar/progress-bar.component";
import { ProgressStepComponent } from "./progress-bar/progress-step/progress-step.component";
import { StoreModule } from "@ngrx/store";
import { EffectsModule } from "@ngrx/effects";
import { applicationReducer } from "./state/application.reducers";
import { ApplicationEffects } from "./state/application.effects";
import { APPLICATION_STATE_NAME } from "./state/application.selectors";
import { CommonComponentModule } from "src/app/core/common-component/common-component.module";
import { ProgressBarModule } from "src/app/core/common-component/progress-bar/progress-bar.module";
import { MatRadioModule } from "@angular/material/radio";
import { MatAutocompleteModule } from "@angular/material/autocomplete";

@NgModule({
  declarations: [
    ApplicationComponent,
    PopUpComponent,
    ProgressBarComponent,
    ProgressStepComponent,
  ],
  imports: [
    CommonModule,
    ApplicationRoutingModule,
    FlexLayoutModule,
    SecondaryToolbarModule,
    PageLayoutModule,
    ContainerModule,
    MatRadioModule,
    MatTabsModule,
    MatStepperModule,
    MatAutocompleteModule,
    MatButtonModule,
    MatIconModule,
    IconModule,
    BreadcrumbsModule,
    MatSelectModule,
    MatExpansionModule,
    FormsModule,
    ReactiveFormsModule,
    CdkStepperModule,
    MatIconModule,
    NgDynamicBreadcrumbModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatFormFieldModule,
    MatDialogModule,
    CdkAccordionModule,
    FormBuilderModule,
    MatTooltipModule,
    CommonComponentModule,
    ProgressBarModule,
    StoreModule.forFeature(APPLICATION_STATE_NAME, applicationReducer),
    EffectsModule.forFeature([ApplicationEffects]),
  ],
  exports:[ApplicationComponent]
  
})
export class ApplicationModule {}
