<div gdColumns="1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr" gdGap="2px" exaiContainer>
    <div class="w-full text-sm text-header" gdColumn="1 / 1" gdColumn.lt-md="1 / 1" gdColumn.lt-sm="1">
        <b *ngIf="!scheduleId  && navigate!='Candidate Details' ">Schedule Accommodations</b>
        <b *ngIf="scheduleId && navigate=='Candidate Details' ">Reschedule Accommodations</b>
        <b *ngIf="navigate=='Training Program Details'">Reschedule Exam</b>
        <b *ngIf="navigate=='Candidate Details'">Schedule Exam</b>
        <app-ng-dynamic-breadcrumb [lastLinkColor]="'var(--text-color2)'" [fontColor]="'#A7A8AC'"
            [fontSize]="'0.65rem'">
        </app-ng-dynamic-breadcrumb>
        <div class="flex flex-row absolute viewBtns">
            <button (click)="openCart()" class="btn-2 t-xs" mat-button>
                View Cart
            </button>
        </div>
    </div>
    <!-- Select Exam Type section -->
    <div class="mt-3" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
        gdGap="12px">
        <div class="shadow-none justify-start dashboard" gdColumn="1 / 2" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
            <div class="card shadow-none cardBorder" fxFlex="auto">
                <div class="px-6 pt-4 eligibility-list3 exam" fxLayout="column">
                    <form [formGroup]='form' *ngIf="examName">
                        <!-- <mat-form-field appearance='outline' fxFlex='auto' floatLabel="always"> -->
                        <div class="flex" fxLayout="column">
                            <mat-label class="text-xs fontColor1 mb-2">Eligibility Route Name</mat-label>
                            <mat-form-field appearance="outline">
                                <input matInput class="text-xs my-1 ellipsis w-full" matInput
                                    placeholder='Eligibility Route Name' formControlName='CardNumber'
                                    [ngModel]="examName.title" [disabled]="true" [readonly]="true">
                                <mat-icon class="icon-eligible text-center" matTooltip="{{examName.title}}"
                                    matSuffix>error_outline
                                </mat-icon>
                                <mat-error>
                                    <div class='invalid-feedback '>
                                        <div>Eligibility Route Name is Required
                                        </div>
                                    </div>
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </form>
                </div>
                <div class="flex text-xs font-bold fontColor1 pb-2 px-6" fxLayout="row" fxLayoutAlign="start">
                    Select Your Exam Type
                </div>
                <div class="eligibility overflow-auto">
                    <div class="px-6 exam flex t-xs justify-between" fxLayout="column"
                        *ngFor='let data of examTenantTypes'>
                        <button class="mb-3 flex t-xs" mat-stroked-button fxFlex='auto'
                            [ngClass]="selectedExamType && selectedExamType.id === data.id ? 'active' : '' "
                            (click)='examIdSelectedEvent(data)'
                            [disabled]="disable(data) ||(examTypeDisable && selectedExamType && selectedExamType.id != data.id)">{{data.title}}
                            <mat-icon class="ml-auto -mx-3 icons"
                                *ngIf="selectedExamType && selectedExamType.id === data.id">
                                arrow_forward_ios
                            </mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="shadow-none card cardBorder justify-start dashboard overflow-auto" gdColumn="2/ -1"
            gdColumn.lt-md="1 " gdColumn.lt-sm="1" *ngIf='selectedExamType'>
            <div class='eligibility-desc  mb-6'>
                <div class="exam">
                    <h2 class="px-6 pt-3 text-xs font-bold fontColor1">1. Select Your Exam Location</h2>
                </div>
                <mat-radio-group [formControl]="radioselect">
                    <ng-container *ngFor="let exType of examTypeModels; let i = index">
                        <mat-radio-button class="px-6 py-2 t-xs" [value]="exType.name"
                            (change)='examTypeSelectedEvent(exType)' [checked]="exType.checked"
                            *ngIf="!exType.disabled">
                            {{exType.name}}
                        </mat-radio-button>
                    </ng-container>
                </mat-radio-group>
                <div class="-mt-5 t-xs" gdColumns="1fr 1fr 1fr 1fr"
                    *ngIf="radioselect.value === 'Online' && ShowTestCenter" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr"
                    gdGap="12px" exaiContainer>
                    <div class=" shadow-none justify-start w-full " gdColumn="1 / 3" gdColumn.lt-md="1"
                        gdColumn.lt-sm="1">
                        <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="12px"
                            class='eligibility-list px-4 pt-6 exam' fxLayoutGap.lt-sm="0">
                            <!-- <----Online--->
                            <mat-form-field appearance='outline' *ngIf="radioselect.value ==='Online'"
                                [formGroup]="myGroup">
                                <mat-label class="text-xs">Select Time Zone</mat-label>
                                <mat-select class="text-xs" formControlName="timeZoneControl">
                                    <mat-option class="text-xs" *ngFor="let time of timezones" [value]="time.id"
                                        (click)='timeZoneSelectedEvent(time)'>
                                        {{time.timeZoneStandardName}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <!-- <----Online--->
                            <!-- <mat-form-field appearance="outline" fxFlex='50%' *ngIf="radioselect.value=='Online'">
                                <mat-label class="text-xs fontColor2">Select Date</mat-label>
                            </mat-form-field> -->
                            <app-date-picker #datepicker (dateChange)="dateSelectedEvent($event)"
                                [predefinedDate]="dateSelected" [max]="eplasedDateTime"
                                *ngIf="radioselect.value ==='Online'">
                            </app-date-picker>
                        </div>
                    </div>
                </div>
                <!-- <hr class='mr-5 ml-5 line' *ngIf="radioselect.value=='Online'"> -->
                <!-- <----Online--->
                <div class="px-3 -mt-2" fxLayout="row" fxLayoutAlign="start center"
                    *ngIf="radioselect.value ==='Online' && ShowTestCenter && slotsAvaiable.length > 0 && this.slotRangeSelected && isSelect">
                    <h2 class="px-3 pt-1 text-xs font-bold fontColor1">Select Range</h2>
                </div>
                <div class="ml-3 mt-1"
                    *ngIf="radioselect.value ==='Online' && ShowTestCenter && slotsAvaiable.length > 0 && this.slotRangeSelected ">
                    <ng-container *ngFor='let time of timeSlotRange ; let i = index'>
                        <button *ngIf="time.data.length > 0 && isSelect"
                            class="px-4 ml-2 pt-3 pb-3 state slots2 buttom6 " mat-stroked-button color="light"
                            (click)='slotRangeSelected(time)'
                            [ngClass]="slots && slots.id==time.id ? (time.timeperiod =='MIDNIGHT' || time.timeperiod =='EVENING' || time.timeperiod =='NIGHT'  ?'midnight':'SelectedRange') : time.color =='blue'?'hightlight':'hightlight1' ">
                            <span class="t-xss block mb-">{{time.timeperiod}}</span>
                            {{time.title}}<br>
                        </button>
                    </ng-container>
                </div>
                <div class="px-4 -mt-2" fxLayout="row" fxLayoutAlign="start center"
                    *ngIf="radioselect.value ==='Online' && ShowTestCenter && slots.data.length > 0 && isSelect">
                    <h2 class=" m-0 px-2 pt-3 text-xs font-bold fontColor1">Available Slots</h2>
                </div>
                <div class=" mat-testCenter overflow-auto"
                    *ngIf="radioselect.value ==='Online' && slots.data.length > 0 && isSelect && ShowTestCenter">
                    <div class=" ml-3 mt-1  ">
                        <ng-container *ngFor='let slot of slots.data'>
                            <button class=" ml-3 mb-3 pt-1 pb-1 state slots2" mat-stroked-button color="light"
                                matTooltip="Avaiable Slots - {{slot.availableSlots}}" (click)='slotIdSelected(slot)'
                                [ngClass]="[selectedTimeSlot && selectedTimeSlot.slotId==slot.slotId ? 'active' : '' ,slot.availableSlots<=20 ? 'active1' : '' ]"
                                [attr.id]="slot.id" *ngIf='slot.availableSlots>0'>{{slot.strSlotTime}}</button>
                        </ng-container>
                    </div>
                </div>
                <div class="px-6"
                    *ngIf="radioselect.value ==='Online' && slots.data.length > 0 && isSelect && ShowTestCenter">
                    <!-- <img src='assets/img/Rectangle 279 (2).svg' class='inline img '> -->
                    <span class="ml-2 dot1"></span>
                    <span class="ml-2 slots3">Available</span>
                    <!-- <img src='assets/img/Rectangle 2944.svg' class='inline ml-2 img'> -->
                    <span class="ml-2 dot2"></span>
                    <span class="ml-2 slots3">Limited Slots</span>
                    <!-- <img src='assets/img/Rectangle 282 (1).svg' class='inline ml-2 img'> -->
                    <span class="ml-2 dot3"></span>
                    <span class="ml-2 slots3">Selected</span>
                </div>


                <exai-test-center *ngIf="radioselect.value ==='Test Center'" [selectedExam]="selectedExamType"
                    [EplasedDateTime]="eplasedDateTime" [Reschedule]="scheduleId" [candidateId]="candidateId" [drawer]="drawer"
                    [events]="scheduleEvent.asObservable()"></exai-test-center>

                <exai-test-center *ngIf="radioselect.value ==='Online' && !ShowTestCenter "
                    [selectedExam]="selectedExamType" [EplasedDateTime]="eplasedDateTime" [Reschedule]="scheduleId" [drawer]="drawer"
                    [candidateId]="candidateId" [events]="scheduleEvent.asObservable()"></exai-test-center>
            </div>
            <div fxLayout="row" fxLayoutAlign="end center" class="flex justify-end" fxLayoutGap="8px"
                *ngIf="radioselect.value === 'Online' && ShowTestCenter">
                <button mat-button type="button" class="btn-2 mb-4 -mt-1 mr-4 t-xs" *ngIf="!scheduleId"
                    (click)=' checkalreadyinCart(false)' [disabled]="!selectedTimeSlot">Add
                    Cart</button>
                <button mat-button class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button"
                    *ngIf="!scheduleId && examstatus != this.global.scheduling_error" (click)='pay()'
                    [disabled]="!selectedTimeSlot">Pay
                    Now</button>
                <button mat-button class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button" (click)='reschedule()'
                    [disabled]="!scheduleId"
                    *ngIf="(scheduleId && AllowReschedule.includes(examstatus))&&(rescheduleloading==false)">Reschedule
                </button>
                <button mat-button class="mr-5 btn-1 -mt-1 mb-4 t-xs" type="button"
                    (click)='retry_Schedule_Without_payment()' [disabled]="!scheduleId"
                    *ngIf="(AllowSchedule.includes(examstatus) )&&schedulingloading==false">Schedule
                </button>
               

                <!-- <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="8px">
                    Disclaimer: "Please note that your exam slot is not reserved until
                    payment is processed and you receive a confirmation email."
                  </div> -->
                <div *ngIf="rescheduleloading||schedulingloading">

                    <div class="spinner__loading">
                        <div>
                            <mat-progress-spinner diameter="60" [value]="value" mode="determinate">
                            </mat-progress-spinner>
                        </div>
                    </div>
                </div>
                
            </div>
            <div *ngIf="radioselect.value ==='Online'&& ShowTestCenter" fxLayout="column" fxLayoutAlign="start" class="ml-2 mb-2">
                Disclaimer: "Please note that your exam slot is not reserved until
                payment is processed and you receive a confirmation email."
            </div>




            <!-- <----Online && TestCenter--->
        </div>
        <!-- <----Before Selecting a Exam Type--->
        <div class="shadow-none card justify-start overflow-auto dashboard" gdColumn="2/ -1" gdColumn.lt-md="1 "
            gdColumn.lt-sm="1" *ngIf='!selectedExamType'>
            <div class="card shadow-none cardBorder " fxFlex="auto">
                <div class="flex justify-center pt-4 h-full overflow-auto" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                    gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr 1fr" gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr 1fr"
                    gdRows="1fr 1fr 1fr 1fr" gdRows.lt-md="1fr 1fr 1fr" gdRows.lt-sm="1fr 1fr 1fr">
                    <div class="flex item-center" fxLayoutAlign="start center" gdColumn="4 / 6" gdColumn.lt-md="3 / 5"
                        gdColumn.lt-sm="3 / 5" gdRow="2 / 3" gdRow.lt-md=" 2 / 3" gdRow.lt-sm="2 / 3" fxLayout="column">
                        <div class="" fxLayout="column">
                            <img src="assets/img/register-exam1.svg" alt="">
                        </div>
                    </div>
                    <div class="flex item-center" gdColumn="3 / 7" gdColumn.lt-md="3 / 7" gdColumn.lt-sm="2 / 7"
                        gdRow="3 / 4" gdRow.lt-md=" 3 / 4" gdRow.lt-sm="3 / 6" fxLayout="column">
                        <div class="" fxLayout="column">
                            <div class="text-center text-xs empty-eligible slots2">
                                Select an exam type to schedule your exam
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Select Exam Type section -->
</div>


<mat-sidenav #drawer position="end" [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
    [mode]="(isHandset$ | async) ? 'over' : 'side'" [opened]="(isHandset$ | async)">
    <!-- <mat-toolbar  class="sidenav "></mat-toolbar> -->

    <div class="shadow-none justify-start paymentCart" gdColumn="6 / -1" gdColumn.lt-md="1/-1" gdColumn.lt-sm="1">
        <div class="card shadow-none overflow-auto exam cardBorder px-4" fxFlex="auto">
            <div class="flex justify-between" fxLayout="row">
                <div class="flex justify-start text-xs pt-4 pb-1 font-bold">Payment Summary</div>
                <div class="flex justify-end pt-4 pb-1 cursor-pointer" (click)="closeSideNav()">
                    <mat-icon class="text-base flex  font-semibold">close</mat-icon>
                </div>

            </div>

            <div class="mb-1 content1" exaiContainer>
                <div class="" fxLayoutGap="6px grid">
                    <div fxLayout="row wrap" fxLayoutGap="10px grid">
                        <div class="shadow-none w-full" fxFlex="(100/1) + '%'" fxFlex.xs="100%" fxFlex.sm="100%"
                            *ngFor="let item of listExam | async">
                            <div class="card shadow-none cardBorder mb-2" fxFlex="auto">
                                <div class="bg-color -pt-2">
                                    <div class="flex justify-between pt-2" fxLayout="row">
                                        <div class="pl-3">
                                            <h5 class="t-xs font "><strong>{{item.examName}}</strong></h5>
                                        </div>
                                        <div class="flex text-left text-base pb-2 pr-4 edit-icon1">
                                            <span class="text-xs">${{item.amount}} </span>
                                            <mat-icon class="-mr-3 ml-2 moreIcon" (click)="deleteItem(item)">delete
                                            </mat-icon>
                                        </div>
                                    </div>
                                    <div fxLayout="row" class="px-3 -mt-2 ">
                                        <h6 class="t-xs mb-1 status1 ">{{item.examNameDesc}}</h6>
                                    </div>
                                </div>
                                <div fxLayout="column">
                                    <div class="pt-2 mb-2 px-3" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr  "
                                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr  " exaiContainer>
                                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                            <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">ExamMode
                                            </div>
                                            <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs">Test Center
                                            </div>
                                        </div>

                                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                            <div class="h4  status t-xs">Exam Date</div>
                                        </div>
                                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                            <div class="h4 status t-xs">Exam Time</div>
                                        </div>
                                    </div>
                                    <div class="px-3 mb-2" gdColumns="1fr 1fr 1fr 1fr 1fr 1fr "
                                        gdColumns.lt-md="1fr 1fr 1fr 1fr 1fr 1fr "
                                        gdColumns.lt-sm="1fr 1fr 1fr 1fr 1fr 1fr " exaiContainer>
                                        <div gdColumn="1/3" gdColumn.lt-md="1/3" gdColumn.lt-sm="1/3">
                                            <div *ngIf="item.testCenterName==null" class="h4 status1 t-xs">
                                                {{item.examMode}}</div>
                                            <div *ngIf="item.testCenterName!=null" class="h4 status1 t-xs minimise"
                                                matTooltip='{{item.testCenterName}}'>{{item.testCenterName}}</div>
                                            <!-- <div class="h4 status1 t-xs">{{item.mode}}</div> -->
                                        </div>
                                        <div gdColumn="3/5" gdColumn.lt-md="3/5" gdColumn.lt-sm="3/5">
                                            <div class="h4 status1 t-xs">{{item.eventDateUtc | date :
                                                'MM/dd/yyyy':'+0000'}}</div>
                                        </div>
                                        <div gdColumn="5/-1" gdColumn.lt-md="5/-1" gdColumn.lt-sm="5/-1">
                                            <div class="h4  status1  t-xs">
                                                {{item.eventDate |date:'shortTime':'+0000' }}
                                                {{item.timeZoneAbbreviation}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-1">
                <div class="" gdColumns="1fr 1fr " gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr" exaiContainer>

                </div>
                <div class="pt-3 mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
                    exaiContainer>

                </div>
                <hr class='ml-5 mr-5 status1'>
                <div class="pt-3 mb-3" gdColumns="1fr 1fr" gdColumns.lt-md="1fr" gdColumns.lt-sm="1fr 1fr"
                    exaiContainer>
                    <div gdColumn="1/2" gdColumn.lt-md="1/2" gdColumn.lt-sm="1/2">
                        <div class="h4 mb-2 total font-bold  text-xs">
                            Total
                        </div>
                    </div>
                    <div gdColumn="2/-1" gdColumn.lt-md="1/2" gdColumn.lt-sm="2/-1">
                        <div class="h4 text-right mb-2 total font-bold text-xs">
                            ${{subtotal}}
                        </div>
                    </div>
                </div>
                <div fxLayout="row" fxLayoutAlign="end center" class="flex justify-end">
                    <button *ngIf='cart.length > 0' mat-button class="btn-2 text-xs mr-2" (click)="pay()"> Pay</button>
                    <button *ngIf='cart.length > 0' mat-button class="btn-2 text-xs mr-2" (click)="deleteAllItems()"> Delete
                        All Items</button>
                        <button *ngIf='cart.length < 2' mat-button class="btn-2 text-xs" (click)="addExam()"> Add Exam</button>
                </div>
                <div class="p-3">
                    Disclaimer: "Please note that your exam slot is not reserved until
                    payment is processed and you receive a confirmation email."
                </div>

            </div>
        </div>
    </div>
</mat-sidenav>