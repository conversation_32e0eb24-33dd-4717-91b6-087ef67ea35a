.eligibility {
  @screen xl {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15.5rem);
  }

  @screen lg {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15.5rem);
  }

  @screen md {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 15.5rem);
  }
}

.pulse-dots {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 30px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: white;
  animation: pulse 1.2s infinite ease-in-out;
}

.dot1 {
  animation-delay: -0.16s;
}

.dot2 {
  animation-delay: -0.32s;
}

.dot3 {
  animation-delay: -0.48s;
}

.dot4 {
  animation-delay: -0.64s;
}

@keyframes pulse {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}


agm-map {
  height: 300px;
}

.minimise {
  width: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow: hidden;
}

.dashboard {
  @screen xl {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 80px);
  }

  @screen lg {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 80px);
  }

  @screen md {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 80px);
  }
}

.paymentCart {
  @screen xl {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 68px);
  }

  @screen lg {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 68px);
  }

  @screen md {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 68px);
  }
}

.eligibility-desc {
  @screen xl {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9rem);
  }

  @screen lg {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9rem);
  }

  @screen md {
    height: calc(100vh - var(--toolbar-height) - var(--navigation-height) - var(--footer-height) - 9rem);
  }
}

.state {
  color: #000000;
  border: var(--border);
}

// .scroll {
//   overflow-y: scroll;
// }

.active {
  color: var(--text-color2) !important;
  border: 1px solid var(--text-color2) !important;
}

.img {
  width: 5px;
}

.text-header {
  color: var(--text-header);
}

.slots3 {
  font-size: 8px;
}

.slot {
  display: contents;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  opacity: 0 !important;
}

.font {
  font-size: 10px;
}

.fontColor1 {
  color: #000000;
  font-weight: 550;
}

.images {
  font-size: 10px;
}

.icons {
  font-size: 14px;
  margin-top: 10px;
}

.fontColor2 {
  color: var(--menu-color);
}

.fontColor {
  color: var(--menu-color);
}

.slots2 {
  font-size: 10px;
  line-height: 20px;
  padding-right: 2px;
  padding-left: 1px;
}

.mat-testCenter {
  height: calc(100vh - 85vh);
}

.iconSize {
  font-size: 17px;
  margin-top: -30px;
}

::ng-deep .mat-button .mat-button-wrapper>*,
.mat-flat-button .mat-button-wrapper>*,
.mat-stroked-button .mat-button-wrapper>*,
.mat-raised-button .mat-button-wrapper>*,
.mat-icon-button .mat-button-wrapper>*,
.mat-fab .mat-button-wrapper>*,
.mat-mini-fab .mat-button-wrapper>* {
  vertical-align: middle;
  font-size: 10px;
}

.select-slot-btn {
  color: var(--text-color2);
  border: var(--btn-border);
}

.moreIcon {
  color: var(--text-color1);
}

.highlight1{
  color: #ee9400 !important;
}

.highlight{
  color: #0076C1 !important;
}

.edit-icon1 {
  color: var(--text-color1);
}

.icon-eligible {
  display: flex;
  justify-content: end !important;
  margin-top: 0.65rem !important;
}

.active1 {
  color: var(--change-request);
  border: var(--change-request-border);
}

.viewBtns {
  right: 0px !important;
}

.viewNotes {
  color: var(--text-color2);
}

.sidenav-container {
  background: var(--background-base);
  height: 100%;
  min-width: 200px !important;
  transition: transform 0.5s cubic-bezier(0.2, 1, 0.3, 1);
}

.sidenav-content {
  overflow-x: hidden;
  overflow-y: auto;
}

.mat-drawer.mat-drawer-end {
  margin-top: 51px;
  min-width: 400px;
  margin-bottom: 17px;
}

.btn-apply {
  margin-top: -1.6rem;
}

.mat-icon {
  font-size: 1rem !important;
  text-align: center;
  vertical-align: sub !important;
}

.bg-color {
  background-color: #fafafa;
}

.link {
  color: #0076C1 !important;
  text-decoration: underline;
}

.INF {
  position: absolute;
  font-size: 0.75rem;
  margin-top: 0.16rem;

}

.INFLABEL {
  font-size: 0.55rem;
}

.INFTEXT {
  font-size: 0.7rem;
  font-color: gray;
}


.header {
  height: 36px;
  background-color: #4444 !important;
  // position: sticky !important;

  .titled {
    color: #11263c;
    font-size: 10px;
    // position: sticky !important;
  }
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  align-items: center;
}

.example-headers-align .mat-expansion-panel-header-title,
.example-headers-align .mat-expansion-panel-header-description {
  flex-basis: 0;
}

.example-headers-align .mat-expansion-panel-header-description {
  justify-content: space-between;
  align-items: center;
}

.header {
  height: 36px;

  .titled {
    color: #11263c;
    font-size: 10px;
  }
}

.title1 {
  font-size: 10px;
  color: var(--button-background) !important;
}

.mat-field-1-4 {
  @apply pt-3 flex-grow;

  @screen xs {
    @apply max-w-full w-full;
  }

  @screen md {
    // @apply max-w-1/4 w-auto;
    @apply w-auto;
    max-width: 30%;
  }
}

.mat-field-1-2 {
  @apply flex-grow;

  @screen xs {
    @apply max-w-full w-full;
  }

  @screen md {
    // @apply max-w-1/2 w-auto;
    @apply w-auto;
    max-width: 33%;
  }
}

.mat-field-1-3 {
  @apply w-full;
}

@screen xs {
  ::ng-deep .testCenerMenu {
    padding: 16px !important;
    min-width: 85vw;

    .mat-menu-content {
      border: none;
    }
  }
}

// ::ng-deep .testCenerMenu{
//   .mat-menu-content{
//     border:none;
//   }
// }

@screen md {
  ::ng-deep .testCenerMenu {
    padding: 16px !important;
    min-width: 530px;

    .mat-menu-content {
      border: none !important;
    }
  }
}

.btn-1 {
  max-width: 160px;
}

.start {
  font-size: 11px;
  top: 3px !important;
}

.end {
  font-size: 11px;
}

::ng-deep {

  mat-tab-nav-bar,
  .mat-tab-header {
    border-bottom: 0px solid rgba(82, 63, 105, 0.06) !important;
  }

  .mat-tab-labels>.mat-tab-label:first-child {
    display: none;
  }
}

::ng-deep .mat-tab-label-active:not(.mat-tab-disabled),
.mat-tab-label.mat-tab-label-active.cdk-keyboard-focused:not(.mat-tab-disabled) {
  border: #0076C1 1px solid !important;
  color: #0076C1 !important;
  opacity: 1;
}

::ng-deep .mat-tab-header-pagination {
  display: flex !important;
}

.mat-tab-header-pagination {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  display: none;
  justify-content: center;
  align-items: center;
  min-width: 32px !important;
  cursor: pointer;
  z-index: 2;
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}

::ng-deep {
  mat-tab-group.button-tabs {
    .mat-tab-label {
      margin: 0 2px;
      height: 36px;
      padding: 0 10px;
      font-size: 12px;
      border: 1px solid gray;
      border-radius: 5px;
      min-width: 85px;

    }

    .mat-ink-bar {
      opacity: 0;
    }

  }
}

::ng-deep {

  mat-tab-nav-bar,
  .mat-tab-header {
    border-bottom: 0px solid rgba(82, 63, 105, 0.06) !important;
  }

  .mat-tab-labels>.mat-tab-label:first-child {
    display: block !important;
  }
}

// .active {
//   color: var(--button-background) !important;
//   border: 1px solid var(--button-background) !important;
// }
::ng-deep .mat-tab-label-active:not(.mat-tab-disabled),
.mat-tab-label.mat-tab-label-active.cdk-keyboard-focused:not(.mat-tab-disabled) {
  border: #0076C1 1px solid !important;
  color: #0076C1 !important;
  opacity: 1;
}