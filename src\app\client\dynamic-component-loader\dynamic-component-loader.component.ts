import { Component, Input, OnInit, ViewChild } from "@angular/core";
import {
  accMerge,
  actionTypes,
  cellClickEvent,
  linkClickEvent,
  performedAction,
  tablePageEvent,
  tabSelectionEvent,
} from "src/app/core/common-component/table/dynamic-table-types";
import { MatTableDataSource } from "@angular/material/table";
import {
  nestingTypes,
  widgetTypes,
  module,
  consoleLoaderJSON,
  widget,
  requestParam,
  requestDetails,
  genericPopupConfig,
  popupDataTypes,
  popupTypes,
  moduleTypes,
  Roles,
  paramTypes,
  ModuleIdName,
  statuses,
} from "./dynamic-component-loader.types";
import { AppComponent } from "src/app/app.component";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import {
  deleteResult,
  DynamicComponentLoaderState,
  statusChangeResult,
} from "./dynamic-loader-store/dynamic-loader.state";
import { State, Store } from "@ngrx/store";
import {
  fetchData,
  PushIntoBreadCrumb,
  PopFromBreadCrumbUptoIndex,
  clearBreadCrumbs,
  setRecentLinkClickEvent,
  changeStatus,
  creationUnsuccessfull,
  creationResultReset,
  genericDelete,
  setPopupConfig,
  getFormJsonAndResponse,
  assignFormViewToCurrentUser,
  fetchStates,
  clearData,
  disableAccount,
} from "./dynamic-loader-store/dynamic-loader.actions";
import {
  breadCrumbsSelector,
  tableDataSelector,
  recentLinkClickEventSelector,
  singleEntryTableDataSelector,
  selectStatusChangeResult,
  selectDeleteResult,
  processLogDataSelector,
  selectPopupConfig,
  selectFormJson,
  selectUserResponse,
  currentModuleIndexSelector,
  selectCreationResult,
  tableDataTotalRecordsSelector,
  statesSelector,
  UpdatedResults,
} from "./dynamic-loader-store/dynamic-loader.selectors";
import { crumb } from "src/app/core/common-component/dynamic-bread-crumbs/dynamic-bread-crumbs.types";
import { BehaviorSubject, Observable, Subject, Subscription } from "rxjs";
import { PopUpComponent } from "../../core/common-component/pop-up/pop-up.component";

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { BreakpointObserver } from "@angular/cdk/layout";
import lodash, { lowerFirst } from "lodash";
import { popupData } from "../../core/common-component/pop-up/pop-up.types";
import { DynamicPopupComponent } from "src/app/core/common-component/dynamic-popup/dynamic-popup.component";
import { SnackbarService } from "src/app/core/snackbar.service";
import { GlobalUserService, userData } from "src/app/core/global-user.service";
import { MatSidenav } from "@angular/material/sidenav";
import { takeUntil, take } from "rxjs/operators";
import {
  selectConsoleLoaderJSON,
  selectDatarefresh,
} from "../state/shared/shared.selectors";
import { HelpComponent } from "../help/help.component";
import { LanguageService } from "src/app/core/language.service";
import { PopUpComponent as popupcomponent } from "../application/pop-up/pop-up.component";
import { DynamicPopupMesageComponent } from "src/app/core/common-component/pop-up/dynamic-popup-mesage/dynamic-popup-mesage.component";
import { HttpService } from "src/app/core/http.service";
import { CandidateRecordPopupComponent } from "../candidate-record-popup/candidate-record-popup.component";
import { AddFormsComponent } from "src/app/core/common-component/pop-up/add-forms/add-forms.component";
import moment from "moment";
import { state } from "@angular/animations";
import { FormTypes } from "../application/application.types";
import { DatePipe } from "@angular/common";
import { getPageSize } from "../state/pageSize/pageSize.selectors";
import { FormBuilderService } from "src/app/core/common-component/examroom-formbuilder/form-builder.service";
import { environment } from "src/environments/environment";
import { HttpClient, HttpErrorResponse } from "@angular/common/http";
import { StateLists } from "src/app/core/common-component/examroom-formbuilder/form-builder.types";
import { TransactionListPopupComponent } from "src/app/core/common-component/pop-up/transaction-list-popup/transaction-list-popup.component";
import { PaymentRefundPopupComponent } from "src/app/core/common-component/pop-up/payment-refund-popup/payment-refund-popup.component";
import { ConfirmationPopupComponent } from "src/app/core/common-component/confirmation-popup/confirmation-popup.component";
import { EmployeesPopupComponent } from "src/app/core/common-component/pop-up/employees-popup/employees-popup.component";
import { OMRVideoImage } from "src/app/core/common-component/pop-up/omr-image-video/omr-image-video.component";
import * as FS from "file-saver";
import { ReEvaluationDialogComponent } from "src/app/core/common-component/re-evaluation-dialog/re-evaluation-dialog.component";
import { RichTextEditorDialogComponent } from "./rich-text-editor-dialog/rich-text-editor-dialog/rich-text-editor-dialog.component";

@Component({
  selector: "exai-dynamic-component-loader",
  templateUrl: "./dynamic-component-loader.component.html",
  styleUrls: ["./dynamic-component-loader.component.scss"],
})
export class DynamicComponentLoaderComponent implements OnInit {
  private unsubscribe: Subject<any> = new Subject();
  // enums to be used in html

  nestingTypes = nestingTypes;
  widgetTypes = widgetTypes;
  moduleTypes = moduleTypes;
  preresponse: any;
  actionTypes = actionTypes;
  Roles = Roles;
  stateId: number;
  TrainingId: number;
  migrated: string;
  dates;
  time;
  consoleLoaderJson: consoleLoaderJSON = null;
  curModuleId: string = null;
  curModuleIndex: number = null;
  curNestingType: nestingTypes = null;
  email: string;
  breadCrumbClearAllowed: boolean = true;
  breadCrumbsArray: Array<crumb> = [];
  recentLinkClickEvent: linkClickEvent = null;
  MisconductStatus: any;
  tabIndexStream: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  $tabIndex: Observable<number> = this.tabIndexStream.asObservable();
  personId: number;
  prevSearchKey: string = null;
  preSelecetedIndex: string = null;
  navigatedUsingBreadCrumb: boolean = false;
  persontentantRole;
  scores = [
    { label: "Yes", value: "true" },
    { label: "No", value: "false" },
  ];

  @ViewChild("sidenav") sidenavRef: MatSidenav;
  Noconfirm: any;
  active: any;
  constructor(
    private activatedRoute: ActivatedRoute,
    private store: Store<DynamicComponentLoaderState>,
    private router: Router,
    public dialog: MatDialog,
    private breakpointobserver: BreakpointObserver,
    private snackbar: SnackbarService,
    public globalUserService: GlobalUserService,
    private http: HttpService,
    private lngSrvc: LanguageService,
    private formservice: FormBuilderService,
    private https: HttpClient
  ) {}

  // below variables contain the current data for all the widgets
  tableData: MatTableDataSource<any> = null;
  tableDataTotalRecords: number = null;
  singleEntryTableData: any = null;
  processLogData: any = null;
  curPopupConfig: genericPopupConfig = null;

  formJSON: any;
  userResponse: any;
  userDetailSub: Subscription;
  cljSub: Subscription;
  states: Array<any> = null;
  selectedScore: string = "";
  selectedStateId: number = null;
  selectedbookletStatus: number = null;
  selectedExamMode: number = null;
  selectedTestCenterPreference: string = null;
  SelectedDateRange: {
    FormDate?: any;
    todate?: any;
    tableselected: string;
    curModuleId?: string;
    textValue?: string;
  };
  SelectedReviewDateRange: {
    tableselected: string;
    curModuleId?: string;
    textValue?: string;
    ReviewFormDate?: any;
    Reviewtodate?: string;
  };

  disableDisplinaryaction: boolean = false;

  curWidgetTypes: BehaviorSubject<Array<string>> = new BehaviorSubject<
    Array<string>
  >([]);
  curWidgetTypes$: Observable<Array<string>> =
    this.curWidgetTypes.asObservable();
  notesSidebarViewId: number = 0;
  counter = 0;

  ngOnInit(): void {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.sidenavRef.close();
        if (!this.navigatedUsingBreadCrumb) {
          this.prevSearchKey = null;
          this.preSelecetedIndex = null;
          this.selectedStateId = null;
          this.SelectedDateRange = null;
          this.SelectedReviewDateRange = null;
        } else this.navigatedUsingBreadCrumb = false;
      }
    });

    this.consoleLoaderJson = { modules: [] };
    this.store.dispatch(clearBreadCrumbs());
    this.store.select(breadCrumbsSelector).subscribe((res) => {
      this.breadCrumbsArray = res;
    });

    if (this.breadCrumbsArray.some((e) => e.label === "Home")) {
    } else {
      this.store.dispatch(
        PushIntoBreadCrumb({
          crumb: {
            label: "Home",
            navigationMetaData: {
              moduleID: "dashboard",
              linkClickEvent: null,
              navigateToType: moduleTypes.Custom,
            },
          },
        })
      );
    }

    this.userDetailSub = this.globalUserService.userDetails
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: userData) => {
        if (x) {
          if (x.roleId == 14) this.disableDisplinaryaction = true;
          this.userDetailSub?.unsubscribe();
          this.cljSub = this.store
            .select(selectConsoleLoaderJSON)
            .pipe(takeUntil(this.unsubscribe))
            .subscribe((clj: consoleLoaderJSON) => {
              if (clj && clj.modules.length > 0) {
                this.cljSub?.unsubscribe();
                this.activatedRoute.paramMap
                  .pipe(takeUntil(this.unsubscribe))
                  .subscribe((paramMap: any) => {
                    if (
                      this.consoleLoaderJson.modules.length !=
                      AppComponent.consoleLoaderJson.modules.length
                    )
                      this.consoleLoaderJson = AppComponent.consoleLoaderJson;
                    this.curModuleIndex =
                      this.consoleLoaderJson.modules.findIndex((x: module) => {
                        return x.moduleID == paramMap.params.moduleId;
                      });
                    this.curNestingType =
                      this.consoleLoaderJson?.modules[
                        this.curModuleIndex
                      ]?.nestingType;
                    this.$tabIndex = this.tabIndexStream.asObservable();

                    var tempSubsRLE: Subscription = this.store
                      .select(recentLinkClickEventSelector)
                      .pipe(takeUntil(this.unsubscribe))
                      .subscribe((event: linkClickEvent) => {
                        this.MisconductStatus =
                          event.column.linkMetaData.navigateToLabel;
                        this.recentLinkClickEvent = event;
                        this.email = event.element.emailId;
                        this.persontentantRole =
                          event.element.personTenantRoleId;
                        this.migrated = event.element.externalId;
                        this.persontentantRole =
                          event.element.personTenantRoleId;

                        tempSubsRLE?.unsubscribe();
                        if (
                          this.recentLinkClickEvent.column.cellType == "link" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateToLabel == "Candidate Details"
                        ) {
                          this.globalUserService.email = event.element.emailId;
                          this.globalUserService.migrated =
                            event.element.externalId;
                          this.globalUserService.firstName =
                            event.element.firstName;
                          this.globalUserService.lastName =
                            event.element.lastName;
                          this.globalUserService.PersonId =
                            event.element.personId;
                          this.globalUserService.PersonTentantRoleId =
                            event.element.personTenantRoleId;
                          this.globalUserService.trainingId = event.element.id;
                          this.globalUserService.traningInstitute =
                            event.element.trainingInstituteId;
                          this.formservice.stateId = event.element.stateId;
                          this.globalUserService.personEventId = event.element;
                          this.globalUserService.column = null;
                          this.globalUserService.stateName =
                            event.element.stateName;
                          this.globalUserService.navigate =
                            event.column.linkMetaData.navigateToLabel;
                          this.globalUserService.personEventId = event.element;
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "link" &&
                          (this.recentLinkClickEvent.column.linkMetaData
                            .navigateToLabel == "Training Program Details" ||
                            this.recentLinkClickEvent.column.linkMetaData
                              .navigateToLabel == "training-institute-details")
                        ) {
                          this.globalUserService.active =
                            this.recentLinkClickEvent.element.active;
                          this.globalUserService.trainingId = event.element.id;
                          this.globalUserService.stateId =
                            event.element.stateId;
                          this.globalUserService.userstatus.next(event.element);
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "link" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateTo == "candidate-details-sc"
                        ) {
                          this.globalUserService.column =
                            this.recentLinkClickEvent.column.linkMetaData
                              .navigateTo == "candidate-details-sc"
                              ? event.column
                              : null;
                          this.globalUserService.personEventId = event.element;
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "edit" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateTo == "employer-details"
                        ) {
                          this.globalUserService.isEnable =
                            event.element.isEnable;
                          this.globalUserService.employerId =
                            event.element.employerId;
                          this.globalUserService.active = null;
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "link" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateTo == "reciprocity-request-details"
                        ) {
                          this.globalUserService.personFormId =
                            event.element.personFormId;
                          this.globalUserService.formTypeId =
                            event.element.formTypeId;
                          this.globalUserService.stateId =
                            event.element.StateId != null &&
                            event.element.StateId != undefined
                              ? event.element.StateId
                              : event.element.stateId;
                          this.globalUserService.personEventId = event.element;
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "link" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateTo == "certificate-renewal-details"
                        ) {
                          this.globalUserService.personFormId =
                            event.element.personFormId;
                          this.globalUserService.formTypeId =
                            event.element.formTypeId;
                          this.globalUserService.stateId =
                            event.element.StateId;
                          this.globalUserService.RegistryID =
                            event.element.personFormRegistryId;
                          this.globalUserService.personEventId = event.element;
                        } else if (
                          this.recentLinkClickEvent != null &&
                          this.recentLinkClickEvent.column.actions[0].type ==
                            "link" &&
                          this.recentLinkClickEvent.column.linkMetaData
                            .navigateToLabel == "View Accomodation Details"
                        ) {
                          this.globalUserService.stateName =
                            event.element.stateName;
                        } else {
                          this.globalUserService.personEventId = event.element;
                          this.globalUserService.userstatus.next(
                            this.globalUserService.personEventId
                          );
                          this.globalUserService.navigate = this.curModuleId;
                        }
                      });
                    this.tabIndexStream.next(0);
                    this.fetchCompleteModuleData();
                    // no need to fetch states on load of each dynamic component
                    if (!this.states || this.states.length == 0)
                      this.store.dispatch(fetchStates());
                    if (
                      this.consoleLoaderJson.modules[this.curModuleIndex]
                        .type == moduleTypes.Dynamic
                    )
                      this.store.dispatch(clearBreadCrumbs());
                    if (
                      [moduleTypes.Dynamic, moduleTypes.Offspring].includes(
                        this.consoleLoaderJson.modules[this.curModuleIndex].type
                      )
                    ) {
                      if (this.breadCrumbsArray.length == 1) {
                        switch (
                          this.consoleLoaderJson.modules[this.curModuleIndex]
                            .moduleID
                        ) {
                          // Pushes 'Manage Candidate' crumbs between Home and Candidate Details when you navigate BACK to Candidate Detalis, to avoid letting the breadcrumbs become 'Home / Candidate Detalis',which was wrong
                          case "candidate-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Manage Candidates",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "candidate-management",
                                  },
                                },
                              })
                            );
                            break;

                          case "transaction-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Payment Refund",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "payment-refund",
                                  },
                                },
                              })
                            );
                            break;

                          // same as above, but for the "Home / Manage Registration / Registration Details" route.
                          case "manage-registry-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Manage Registration",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "manage-registry",
                                  },
                                },
                              })
                            );
                            break;

                          // and for "Home / Registration Renewal / View Details"
                          case "certificate-renewal-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Registration Renewal",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "certificate-renewal",
                                  },
                                },
                              })
                            );
                            break;

                          // and for "Home / Reciprocity Requests / View Details"
                          case "reciprocity-request-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Reciprocity Requests",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "reciprocity-request",
                                  },
                                },
                              })
                            );
                            break;

                          // for "Home / Duplicate Registration / View Details"
                          case "duplicate-certificates-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Duplicate Registration",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "duplicate-certificates",
                                  },
                                },
                              })
                            );
                            break;
                          case "regular-certificate-details":
                            this.store.dispatch(
                              PushIntoBreadCrumb({
                                crumb: {
                                  label: "Manage Regular Certificate  ",
                                  navigationMetaData: {
                                    navigateToType: moduleTypes.Dynamic,
                                    linkClickEvent: null,
                                    moduleID: "regular-certificate",
                                  },
                                },
                              })
                            );
                            break;
                          default:
                            break;
                        }
                      }

                      this.store.dispatch(
                        PushIntoBreadCrumb({
                          crumb: {
                            label:
                              this.consoleLoaderJson.modules[
                                this.curModuleIndex
                              ].name,
                            navigationMetaData: {
                              navigateToType:
                                this.consoleLoaderJson.modules[
                                  this.curModuleIndex
                                ].type,
                              linkClickEvent: this.recentLinkClickEvent,
                              moduleID:
                                this.consoleLoaderJson.modules[
                                  this.curModuleIndex
                                ].moduleID,
                            },
                          },
                        })
                      );
                    }
                  });
              }
            });
        }
      });

    this.store
      .select(tableDataSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((moduleData: Array<any> | any) => {
        this.tableData = Array.isArray(moduleData?.data)
          ? new MatTableDataSource<any>(moduleData?.data)
          : Array.isArray(moduleData)
          ? new MatTableDataSource<any>(moduleData)
          : new MatTableDataSource<any>([moduleData]);
      });
    this.store
      .select(tableDataTotalRecordsSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: number) => (this.tableDataTotalRecords = x));
    this.store
      .select(statesSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: Array<any>) => (this.states = x));
    this.store
      .select(currentModuleIndexSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: number) => {
        if (x != null) {
          this.curModuleId = this.consoleLoaderJson.modules[x].moduleID;
          this.setCurWidgetTypes();
        } else if (this.curModuleId) {
          this.curModuleIndex = this.consoleLoaderJson.modules.findIndex(
            (x: module) => {
              return x.moduleID == this.curModuleId;
            }
          );
        }
      });
    this.store
      .select(breadCrumbsSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(
        (breadCrumbs: Array<crumb>) => (this.breadCrumbsArray = breadCrumbs)
      );
    this.store
      .select(recentLinkClickEventSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((event: linkClickEvent) => {
        this.recentLinkClickEvent = event;
        if (
          this.recentLinkClickEvent != null &&
          this.recentLinkClickEvent.column.actions[0].type == "link"
        ) {
          this.globalUserService.active =
            this.recentLinkClickEvent.element.active;
          this.globalUserService.trainingId = event.element.id;
          this.globalUserService.stateId = event.element.stateId;

          // this.http.getUserstatus(this.recentLinkClickEvent.element.id).subscribe((data:any)=>{
          //   if(data !=null && data.length > 0){
          //     this.active =data[0].active
          //   }else{
          //     this.active =undefined
        }

        // })
      });

    this.store
      .select(singleEntryTableDataSelector)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((data: any) => {
        if (Array.isArray(data)) this.singleEntryTableData = data;
        else this.singleEntryTableData = [data];
      });
    this.store
      .select(selectFormJson)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: any) => {
        // here setTimeout is used to trigger changeDetection without using
        // ChangeDetectionStrategy.OnPush
        this.formJSON = null;
        setTimeout(() => {
          this.formJSON = x;
        });
      });
    this.store
      .select(selectUserResponse)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((x: any) => {
        this.userResponse = x;
      });
    this.store.select(processLogDataSelector).subscribe((data) => {
      if (data) {
        this.processLogData = null;
        setTimeout(() => {
          this.processLogData = data;
        });
      }
    });

    this.store
      .select(selectStatusChangeResult)
      .subscribe((result: statusChangeResult) => {
        if (result && !result.success) {
          var tableDataClone = lodash.cloneDeep(this.tableData.data);
          tableDataClone[result.cellClickEvent.rowIndex][
            result.cellClickEvent.column.id as string
          ] =
            result.cellClickEvent.element[
              result.cellClickEvent.column.id as string
            ];
          this.tableData = new MatTableDataSource<any>(tableDataClone);
          this.snackbar.callSnackbaronError("Status Change Unsuccessfull.");
        } else if (result && result.success)
          this.snackbar.callSnackbaronSuccess(
            (result.cellClickEvent.element[
              result.cellClickEvent.column.id as string
            ]
              ? "Disabled"
              : "Enabled") + " Successfully."
          );
        this.store.dispatch(clearData());
      });
    this.store
      .select(selectDeleteResult)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result: deleteResult) => {
        if (result && result.success) {
          // var tableDataClone = lodash.cloneDeep(this.tableData.data);
          // tableDataClone.splice(result.performedAction.rowIndex, 1);
          // this.tableData = new MatTableDataSource<any>(tableDataClone);
          if (result.performedAction.action.type == "deleteApp") {
            this.snackbar.callSnackbaronSuccess(
              "Deleted Application Successfully."
            );
          }
          this.refreshData(result.widgetRowIndex, result.widgetColumnIndex);
        } else if (result?.success == false) {
          this.snackbar.callSnackbaronError("Deletion Unsuccessfull.");
        }
        // Clear the delete result state after processing to prevent incorrect messages on navigation
        this.store.dispatch(clearData());
      });

    this.store
      .select(selectCreationResult)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result: boolean) => {
        if (result != null && result == true) {
          // this.snackbar.callSnackbaronSuccess("Creation successfully");

          //this.preresponse?this.snackbar.callSnackbaronSuccess('Updated Successfully.'):this.snackbar.callSnackbaronSuccess('Created Successfully.')
          // this.preresponse =false
          this.refreshData(0, 0);
          // making call to refresh data not needed here because the call is being made in the openAdd, dialogRef.afterClose()
        } else if (result != null)
          this.snackbar.callSnackbaronError("Creation Unsuccessful.");
      });
    this.store.select(UpdatedResults).subscribe((data) => {
      if (data) {
        this.refreshData(0, 0);
      }
    });

    this.store.select(selectDatarefresh).subscribe((data) => {
      if (data) {
        this.handleTabChange({ tabIndex: 2 }, 1, 0, 6);
      }
    });
    // this.store.select(UpdatedResults).subscribe((data:boolean)=>{
    //
    //     if(data){
    //       this.refreshData(0,0)
    //     }
    // })
    this.store
      .select(selectPopupConfig)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((config: genericPopupConfig) => {
        if (config) {
          this.curPopupConfig = config;
          if (this.curPopupConfig.popupData) {
            const dialogConfig = new MatDialogConfig();
            dialogConfig.hasBackdrop = true;
            this.breakpointobserver
              .observe(["(min-width : 1024px)"])
              .pipe(takeUntil(this.unsubscribe))
              .subscribe((result: { matches: any }) => {
                if (result.matches) {
                  dialogConfig.minWidth = "60vw";
                  dialogConfig.minHeight = "85vh";
                  dialogConfig.maxWidth = "70vw";
                  dialogConfig.maxHeight = "85vh";
                } else {
                  dialogConfig.minWidth = "90vw";
                  dialogConfig.minHeight = "90vh";
                }
              });

            dialogConfig.data = this.curPopupConfig;

            const dialogRef = this.dialog.open(
              DynamicPopupComponent,
              dialogConfig
            );
            dialogRef
              .afterClosed()
              .pipe(takeUntil(this.unsubscribe))
              .subscribe((data) => {
                this.store.dispatch(
                  setPopupConfig({
                    popupConfig: null,
                    count: (this.counter = 0),
                  })
                );
              });
          }
        }
      });
    this.curWidgetTypes.subscribe((value: Array<string>) => {
      if (value.includes(widgetTypes.DynamicForms)) {
        var indices = this.findWidgetRowAndColByWidgetType(
          widgetTypes.DynamicForms
        );
        var widget =
          this.curNestingType == nestingTypes.ColumnsInRows
            ? this.consoleLoaderJson.modules[this.curModuleIndex].rows[
                indices[0]
              ].columns[indices[1]].widget
            : this.consoleLoaderJson.modules[this.curModuleIndex].columns[
                indices[0]
              ].rows[indices[1]].widget;
        if (widget.requestDetails.length >= 3) {
          var requestDetailsClone = lodash.cloneDeep(widget.requestDetails[2]);
          this.setParamValues(requestDetailsClone);
          this.store.dispatch(assignFormViewToCurrentUser(requestDetailsClone));
        }
      }
    });

    // this.globalUserService.refreshData.subscribe(data=>{
    //   if(data !=null && data){
    //     setTimeout(()=>{
    //       this.refreshData(1,0)
    //       this.router.navigateByUrl('/loader/candidate-details')
    //     },2000)

    //   }
    // })
  }
  refreshData(rowIndex: number = 0, columnIndex: number = 0) {
    var widgetClone = lodash.cloneDeep(
      this.curNestingType == nestingTypes.ColumnsInRows
        ? this.consoleLoaderJson.modules[this.curModuleIndex].rows[rowIndex]
            .columns[columnIndex].widget
        : this.consoleLoaderJson.modules[this.curModuleIndex].columns[
            columnIndex
          ].rows[rowIndex].widget
    );
    this.setParamValues(widgetClone.requestDetails[this.tabIndexStream.value]);
    if (this.selectedStateId) {
      if (
        (this.consoleLoaderJson.modules[this.curModuleIndex].moduleID ==
          ModuleIdName.Manage_Candidate &&
          this.globalUserService.userDetails.value.roleId ===
            Roles.SupportingStaff &&
          this.globalUserService.userDetails.value.stateId != null) ||
        (this.consoleLoaderJson.modules[this.curModuleIndex].moduleID ==
          ModuleIdName.Training_Institute &&
          this.globalUserService.userDetails.value.roleId ===
            Roles.OperationStaff &&
          this.globalUserService.userDetails.value.stateId != null)
      ) {
        widgetClone.requestDetails[this.tabIndexStream.value].requestParams =
          widgetClone.requestDetails[
            this.tabIndexStream.value
          ].requestParams.filter((x) => x.paramName != "stateId");
      }
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "stateId",
        paramValue: this.selectedStateId,
      });
    }

    if (
      (this.SelectedDateRange?.FormDate &&
        this.SelectedDateRange?.todate &&
        this.SelectedDateRange?.tableselected == "Approved") ||
      (this.SelectedDateRange?.FormDate &&
        this.SelectedDateRange?.todate &&
        (this.curModuleId == "manage_nae" ||
          this.curModuleId == "regular-certificate" ||
          this.curModuleId == "Pre-RegisterCandidate" ||
          this.curModuleId == "booklet_inventory" ||
          this.curModuleId == "candidate-search-slot"))
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push(
        {
          paramType: paramTypes.Optional,
          paramName:
            this.curModuleId == "Pre-RegisterCandidate"
              ? "createdOnStartDate"
              : this.curModuleId == "booklet_inventory"
              ? "startDate"
              : this.curModuleId == "candidate-search-slot"
              ? "slotStartDate"
              : "fromDate",
          paramValue: this.SelectedDateRange.FormDate,
        },
        {
          paramType: paramTypes.Optional,
          paramName:
            this.curModuleId == "Pre-RegisterCandidate"
              ? "createdOnEndDate"
              : this.curModuleId == "booklet_inventory"
              ? "endDate"
              : this.curModuleId == "candidate-search-slot"
              ? "slotEndDate"
              : "toDate",
          paramValue: this.SelectedDateRange.todate,
        }
      );
    }
    if (
      this.SelectedReviewDateRange?.ReviewFormDate &&
      this.SelectedReviewDateRange?.Reviewtodate &&
      this.SelectedReviewDateRange?.tableselected == "Approved" &&
      this.curModuleId == "duplicate-certificates"
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push(
        {
          paramType: paramTypes.Optional,
          paramName: "reviewFromDate",
          paramValue: this.SelectedReviewDateRange.ReviewFormDate,
        },
        {
          paramType: paramTypes.Optional,
          paramName: "reviewToDate",
          paramValue: this.SelectedReviewDateRange.Reviewtodate,
        }
      );
    }

    if (this.selectedbookletStatus) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName:
          this.curModuleId == "manage-grievence"
            ? "grievanceStatusId"
            : this.curModuleId == "candidate-search-slot"
            ? "eventStatusId"
            : "orderStatusId",
        paramValue: this.selectedbookletStatus,
      });
    }

    if (this.selectedExamMode && this.curModuleId == "candidate-search-slot") {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "examModeId",
        paramValue: this.selectedExamMode,
      });
    }

    if (
      this.selectedTestCenterPreference &&
      this.curModuleId == "candidate-search-slot"
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "testCenterPreference",
        paramValue: this.selectedTestCenterPreference,
      });
    }

    if (this.selectedScore !== "") {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "IsScoreReceived",
        paramValue: this.selectedScore,
      });
    }

    this.store.dispatch(
      fetchData({
        widgetType: widgetClone.type,
        moduleIndex: this.curModuleIndex,
        requestDetails: widgetClone.requestDetails[this.tabIndexStream.value],
      })
    );
  }

  print($event: any) {}
  toggleHandler($event: cellClickEvent) {
    var requestDetailsClone: requestDetails = lodash.cloneDeep(
      $event.column.statusUpdateDetails
    );
    if ($event.element.id) {
      JSON.stringify(requestDetailsClone.fetchUrl) !==
      JSON.stringify(["client", "api", "api", "TypeFormSurvey", "CSATSurvey"])
        ? (requestDetailsClone.requestBody = [
            {
              id: $event.element.id,
              active: !$event.element.active,
            },
          ])
        : (requestDetailsClone.requestBody = {
            id: $event.element.id,
            active: !$event.element.active,
            personId: this.globalUserService.userDetails.getValue().personId,
            persontentantRole:
              this.globalUserService.userDetails.getValue().personTenantRoleId,
          });
    } else {
      requestDetailsClone.requestBody = [
        {
          id: $event.element.employerId,
          active: !$event.element.isEnable,
        },
      ];
    }

    var tableDataClone = lodash.cloneDeep(this.tableData.data);

    tableDataClone[$event.rowIndex][$event.column.id as string] =
      !$event.element[$event.column.id as string];
    this.tableData = new MatTableDataSource<any>(tableDataClone);

    this.store.dispatch(
      changeStatus({
        cellClickEvent: $event,
        requestDetails: requestDetailsClone,
      })
    );
  }
  handleLinkClick($event: linkClickEvent) {
    this.navigatedUsingBreadCrumb = true;

    var lmd = $event.column.linkMetaData;
    this.store.dispatch(setRecentLinkClickEvent({ event: $event }));
    switch (lmd.navigateToType) {
      case moduleTypes.Custom:
        this.router.navigate([lmd.navigateTo]);
        //this.router.navigate([lmd.navigateTo,$event.element.id]);
        break;
      case moduleTypes.Offspring:
        this.router.navigate(["loader", lmd.navigateTo]);
        break;
      case moduleTypes.Independent:
        var requestDetailsClone = lodash.cloneDeep(lmd.requestDetails);
        this.setParamValues(requestDetailsClone, $event);
        var routeUrl: Array<string> = [lmd.navigateTo];
        requestDetailsClone.requestParams.forEach((x: requestParam) => {
          if (x.paramValue == undefined) {
            x.paramValue = 0;
          }
          routeUrl.push(x.paramValue.toString());
        });
        this.router.navigate(routeUrl);
        break;
      case moduleTypes.Way_Bill_Url:
        const { waybillNumber, preferredCarrier } = $event.element || {};
        if (!waybillNumber) {
          return;
        }
        let trackingUrl = "";
        switch (preferredCarrier) {
          case "UPS":
            trackingUrl = `https://www.ups.com/track?loc=en_US&tracknum=${waybillNumber}&requester=ST/`;
            break;
          case "FEDEX":
            const waybill = waybillNumber.split(" ")[0];
            trackingUrl = `https://www.fedex.com/fedextrack/?trknbr=${waybill}&trkqual=2460726000~285618852767~FX`;
            break;
          default:
            return;
        }
        window.open(trackingUrl, "_blank");
        break;
      default:
      //handle exceptions related to malformed JSON here, as presumabaly links
      //inside dynamic widgets wont be to moduleTypes.dynamic
      //event if they are at a later stage, breadCumbs array needs to be cleared and simple the fetch metadata
      // in the store needs to be modified
    }

    if (
      $event.column.cellType == "link" &&
      $event.column.linkMetaData.navigateToLabel == "Payment_Refund"
    ) {
      const dialogRef = this.dialog.open(TransactionListPopupComponent, {
        data: $event.element,
        width: "750px",
        height: "200px",
      });
      dialogRef.afterClosed().subscribe((data) => {});
    }
  }
  handleCrumbNavigation(crumb: crumb) {
    this.navigatedUsingBreadCrumb = true;
    if (
      !(
        this.breadCrumbsArray.indexOf(crumb) ==
        this.breadCrumbsArray.length - 1
      )
    ) {
      if (crumb.navigationMetaData.navigateToType == moduleTypes.Custom)
        this.router.navigate([crumb.navigationMetaData.moduleID]);
      else {
        if (crumb.navigationMetaData.navigateToType == moduleTypes.Offspring) {
          this.recentLinkClickEvent = crumb.navigationMetaData.linkClickEvent;
          this.store.dispatch(
            setRecentLinkClickEvent({ event: this.recentLinkClickEvent })
          );
        }
        this.router.navigate(["loader", crumb.navigationMetaData.moduleID]);
      }
      this.store.dispatch(
        PopFromBreadCrumbUptoIndex({
          indexToPopUpto: this.breadCrumbsArray.indexOf(crumb),
        })
      );
    }
  }
  handleTabChange(
    $event: tabSelectionEvent,
    rowIndex: number,
    columnIndex: number,
    moduleIndex: number
  ) {
    if (moduleIndex == this.curModuleIndex) {
      var widgetClone = lodash.cloneDeep(
        this.curNestingType == nestingTypes.ColumnsInRows
          ? this.consoleLoaderJson.modules[this.curModuleIndex].rows[rowIndex]
              ?.columns[columnIndex]?.widget
          : this.consoleLoaderJson.modules[this.curModuleIndex].columns[
              columnIndex
            ]?.rows[rowIndex]?.widget
      );
      if (
        this.recentLinkClickEvent != undefined &&
        this.recentLinkClickEvent != null
      ) {
        if (
          this.globalUserService.userDetails.getValue().roleId ==
            Roles.StateClient &&
          this.globalUserService.userDetails.getValue().stateId ==
            StateLists.PA &&
          this.recentLinkClickEvent.column.linkMetaData.navigateTo ==
            ModuleIdName.Manage_Candidates
        ) {
          widgetClone.requestDetails = widgetClone.requestDetails.filter(
            (x) =>
              JSON.stringify(x.fetchUrl) !=
                JSON.stringify(["customerservice", "api", "tickets"]) &&
              JSON.stringify(x.fetchUrl) !=
                JSON.stringify([
                  "customerservice",
                  "api",
                  "tickets",
                  "user-notifications",
                ])
          );
        }
      }

      this.setParamValues(widgetClone.requestDetails[$event.tabIndex]);
      if (this.selectedStateId) {
        widgetClone.requestDetails[$event.tabIndex].requestParams.push({
          paramType: paramTypes.Optional,
          paramName: "stateId",
          paramValue: this.selectedStateId,
        });
      }
      if (
        (this.SelectedDateRange?.FormDate &&
          this.SelectedDateRange?.todate &&
          this.SelectedDateRange?.tableselected == "Approved") ||
        (this.SelectedDateRange?.FormDate &&
          this.SelectedDateRange?.todate &&
          (this.curModuleId == "manage_nae" ||
            this.curModuleId == "regular-certificate" ||
            this.curModuleId == "Pre-RegisterCandidate"))
      ) {
        widgetClone.requestDetails[
          this.tabIndexStream.value
        ].requestParams.push(
          {
            paramType: paramTypes.Optional,
            paramName:
              this.curModuleId == "Pre-RegisterCandidate"
                ? "createdOnStartDate"
                : "fromDate",
            paramValue: this.SelectedDateRange.FormDate,
          },
          {
            paramType: paramTypes.Optional,
            paramName:
              this.curModuleId == "Pre-RegisterCandidate"
                ? "createdOnEndDate"
                : "toDate",
            paramValue: this.SelectedDateRange.todate,
          }
        );
      }
      if (
        this.SelectedReviewDateRange?.ReviewFormDate &&
        this.SelectedReviewDateRange?.Reviewtodate &&
        this.SelectedReviewDateRange?.tableselected == "Approved" &&
        this.curModuleId == "duplicate-certificates"
      ) {
        widgetClone.requestDetails[
          this.tabIndexStream.value
        ].requestParams.push(
          {
            paramType: paramTypes.Optional,
            paramName: "reviewFromDate",
            paramValue: this.SelectedReviewDateRange.ReviewFormDate,
          },
          {
            paramType: paramTypes.Optional,
            paramName: "reviewToDate",
            paramValue: this.SelectedReviewDateRange.Reviewtodate,
          }
        );
      }
      if (this.selectedbookletStatus) {
        widgetClone.requestDetails[
          this.tabIndexStream.value
        ].requestParams.push({
          paramType: paramTypes.Optional,
          paramName:
            this.curModuleId == "manage-grievence"
              ? "grievanceStatusId"
              : this.curModuleId == "candidate-search-slot"
              ? "eventStatusId"
              : "orderStatusId",
          paramValue: this.selectedbookletStatus,
        });
      }

      if (
        this.selectedExamMode &&
        this.curModuleId == "candidate-search-slot"
      ) {
        widgetClone.requestDetails[
          this.tabIndexStream.value
        ].requestParams.push({
          paramType: paramTypes.Optional,
          paramName: "examModeId",
          paramValue: this.selectedExamMode,
        });
      }

      widgetClone.requestDetails[$event.tabIndex].requestParams.forEach(
        (ele) => {
          if (
            ele.paramValue == null &&
            ele.paramValue == undefined &&
            ele.paramType != paramTypes.Required &&
            ele.paramName != "candidateId"
          ) {
            widgetClone.requestDetails[$event.tabIndex].requestParams.push({
              paramType: paramTypes.Optional,
              paramName: "personId",
              paramValue: this.globalUserService.PersonId,
            });
          }
        }
      );
      this.store.dispatch(
        fetchData({
          widgetType: widgetClone.type,
          moduleIndex: this.curModuleIndex,
          requestDetails: widgetClone.requestDetails[$event.tabIndex],
        })
      );
      this.tabIndexStream.next($event.tabIndex);
    }
  }

  openAdd(data: Array<popupData>, rowIndex: number, columnIndex: number) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointobserver
      .observe(["(min-width : 1024px)"])
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = "32vw";
          dialogConfig.minHeight = "fit-content";
          dialogConfig.maxWidth = "47vw";
          dialogConfig.maxHeight = "90vh";
        } else {
          dialogConfig.minWidth = "90vw";
          dialogConfig.minHeight = "90vh";
        }
      });
    if (data != null) dialogConfig.data = data[this.tabIndexStream.value];
    const dialogRef = this.dialog.open(PopUpComponent, dialogConfig);
    dialogRef
      .afterClosed()
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((data) => {
        if (data?.creationSuccess) {
          this.store.dispatch(creationResultReset());
          this.refreshData(rowIndex, columnIndex);
        }
      });
  }
  openDialogToReEvaluate(icon, items): void {
    const header = items.scoreReceived ? "Re-Score" : "Score";
    const submitBtn = items.scoreReceived ? "Re-Score" : "Score";

    const dialogRef = this.dialog.open(ReEvaluationDialogComponent, {
      width: "95vw",
      height: "fit-content",
      minWidth: "95vw",
      id: "reEvaluationDialogComponent",
      panelClass: "full-width-dialog",
      data: {
        icon: icon,
        details: items,
        header: header,
        submitBtn: submitBtn,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {});
  }
  // so the below thing definitely needs improvement
  // popups are hard-coded, there should be just one popup,
  // that opens up widgets based on the type we pass to it
  handleAction($event: performedAction, rowIndex: number, columnIndex: number) {
    this.store.dispatch(setRecentLinkClickEvent({ event: $event }));
    switch ($event.action.type) {
      case actionTypes.Delete:
        const dialogRef = this.dialog.open(popupcomponent, {
          data: {
            title: `${$event.element.examName}`,
            message: this.lngSrvc.curLangObj.value.cancelExam,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
          },
        });
        dialogRef.afterClosed().subscribe((data) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            this.http
              .getCancelled(
                $event.element.id,
                $event.element.candidateId,
                this.globalUserService.userDetails.value.personTenantRoleId
              )
              .subscribe(
                (data) => {
                  if (data != null && data) {
                    var datePipe = new DatePipe("en-US");
                    this.dates = moment($event.element.examDateTime).format(
                      "MM/DD/YYYY"
                    );
                    this.time = datePipe.transform(
                      $event.element.examDateTime,
                      "shortTime",
                      "+0000"
                    );
                    let body = {
                      body: `${$event.element.mode} ${
                        $event.element.examName
                      } scheduled ${
                        $event.element.testCenterDetails.testCenterName
                          ? "for" +
                            $event.element.testCenterDetails.testCenterName
                          : ""
                      } on ${this.dates} at ${this.time} ${
                        $event.element.timeZoneAbbreviation
                      } was cancelled by  ${
                        this.globalUserService.userDetails.value.roleName
                      }`,
                      candidateId: $event.element.candidateId,
                      files: [],
                      id: 0,
                      noteTypeid: 5,
                      title: `${$event.element.mode} ${$event.element.examName} Cancelled `,
                      userId:
                        this.globalUserService.userDetails.value
                          .personTenantRoleId,
                      userName: `${$event.element.candidateName}`,
                    };
                    this.http.getAddnotes(body).subscribe((data) => {});
                    this.refreshData(1, 0);

                    this.snackbar.callSnackbaronSuccess(
                      "Exam Cancelled SuccessFully"
                    );
                  }
                },
                (error: any) => {
                  this.snackbar.callSnackbaronError(
                    `${error.message.mesage.error}`
                  );
                }
              );
            // var requestDetailsClone = lodash.cloneDeep($event.action.requestDetails[0]);
            // this.setParamValues(requestDetailsClone, $event);
            // this.store.dispatch(genericDelete({
            //   performedAction: $event,
            //   requestDetails: requestDetailsClone,
            //   widgetRowIndex: rowIndex,
            //   widgetColumnIndex: columnIndex
            // }));
          }
        });
        break;
      case actionTypes.DeleteApp:
        if (
          this.recentLinkClickEvent.element.stateId != 6 ||
          ($event.element.status != "Pending" &&
            ($event.element.formTypeId != FormTypes.Certificate_Reciprocity ||
              $event.element.formTypeId != FormTypes.Certificate_Renewal))
        ) {
          const deletedialogRef = this.dialog.open(popupcomponent, {
            data: {
              title: this.lngSrvc.curLangObj.value.deletePage,
              message: this.lngSrvc.curLangObj.value.deleteMsg,
              cancelButton: this.lngSrvc.curLangObj.value.cancel,
              OKButton: this.lngSrvc.curLangObj.value.cancel,
              OkButton: this.lngSrvc.curLangObj.value.delete,
            },
          });
          deletedialogRef.afterClosed().subscribe((data) => {
            if (data && data.confirmed) {
              var requestDetailsClone = lodash.cloneDeep(
                $event.action.requestDetails[0]
              );
              this.setParamValues(requestDetailsClone, $event);
              this.store.dispatch(
                genericDelete({
                  performedAction: $event,
                  requestDetails: requestDetailsClone,
                  widgetRowIndex: rowIndex,
                  widgetColumnIndex: columnIndex,
                })
              );
            }
          });
        } else {
          this.snackbar.callSnackbaronError("PersonFormId can't be deleted");
        }

        break;
      case actionTypes.Edit:
        if (
          $event.element.active == true ||
          this.globalUserService.userDetails.value.roleId != 13
        ) {
          var popupDataClone = lodash.cloneDeep(
            this.curNestingType == nestingTypes.ColumnsInRows
              ? this.consoleLoaderJson.modules[this.curModuleIndex].rows[
                  rowIndex
                ].columns[columnIndex].widget.tableOptions.addPopupData
              : this.consoleLoaderJson.modules[this.curModuleIndex].columns[
                  columnIndex
                ].rows[rowIndex].widget.tableOptions.addPopupData
          );
          popupDataClone[this.tabIndexStream.value].isEdit = true;
          popupDataClone[this.tabIndexStream.value].preResponse =
            $event.element;
          // the following hard coding of index of button has to be removed
          popupDataClone[this.tabIndexStream.value].buttons.length > 0
            ? (popupDataClone[
                this.tabIndexStream.value
              ].buttons[0].requestDetails = $event.action.requestDetails[0])
            : {};
          this.openAdd(popupDataClone, rowIndex, columnIndex);
          break;
        }

      case actionTypes.View:
        this.globalUserService.clickedviewIconDetails = $event;
        if (
          $event.element.active == true ||
          this.globalUserService.userDetails.value.roleId != 13
        ) {
          var requestDetailsCloneArray = lodash.cloneDeep(
            $event.action.requestDetails
          );
          requestDetailsCloneArray.forEach((x: requestDetails) => {
            this.setParamValues(x, $event);
          });

          this.store.dispatch(
            getFormJsonAndResponse({
              popupConfig: {
                type: popupTypes.FormViewPopup,
                popupData: null,
                popupDataType: popupDataTypes.FetchedFromApi,
              },
              requestDetailsOne: requestDetailsCloneArray[0],
              requestDetailsTwo: requestDetailsCloneArray[1],
              element: $event.element,
            })
          );
        } else {
          this.snackbar.callSnackbaronError("please enable the status");
        }
        break;
      case actionTypes.FormView:
        var requestDetailsCloneArray = lodash.cloneDeep(
          $event.action.requestDetails
        );
        requestDetailsCloneArray.forEach((x: requestDetails) => {
          this.setParamValues(x, $event);
        });

        this.store.dispatch(
          getFormJsonAndResponse({
            popupConfig: {
              type: popupTypes.FormViewPopup,
              popupData: null,
              popupDataType: popupDataTypes.FetchedFromApi,
            },
            requestDetailsOne: requestDetailsCloneArray[0],
            requestDetailsTwo:
              requestDetailsCloneArray[1] != undefined
                ? requestDetailsCloneArray[1]
                : null,
            element: $event.element,
          })
        );
        break;
      case actionTypes.Link:
        this.handleLinkClick({
          column: $event.column,
          element: $event.element,
          rowIndex: $event.rowIndex,
          action: $event.action,
        });
        this.globalUserService.icon = $event.action.icon;

        break;
      case actionTypes.RichTextView:
        this.store.dispatch(
          setPopupConfig({
            popupConfig: {
              type: popupTypes.RichTextView,
              popupDataType: popupDataTypes.FetchedFromProperties,
              popupData: {
                richText: $event.element.body,
                element: $event.element,
              },
            },
          })
        );
        break;
      case actionTypes.ComponentView:
        this.store.dispatch(
          setPopupConfig({
            popupConfig: {
              type: popupTypes.ComponentView,
              popupDataType: popupDataTypes.FetchedFromProperties,
              popupData: {
                element: $event.element,
              },
            },
          })
        );
        break;
      case actionTypes.Person:
        let userDetails1 = {};
        sessionStorage.setItem('emulateEmailId', $event.element.emailId)
     
        if (
          this.globalUserService.active == true ||
          this.globalUserService.isEnable == true
        ) {
          this.http
            .conformuser(userDetails1, $event.element.personId)
            .subscribe((data: any) => {
              if (data != null && data.status == "CONFIRMED") {
                this.emulateClient($event.element);
              } else {
                const dialogRef = this.dialog.open(popupcomponent, {
                  data: {
                    title: this.lngSrvc.curLangObj.value.emulateTitle,
                    message: this.lngSrvc.curLangObj.value.emulateMessage,
                    OKButton: this.lngSrvc.curLangObj.value.ok,
                  },
                });
              }
            });
        } else {
          this.snackbar.callSnackbaronError(
            "The user is not allowed to emulate"
          );
        }

        break;
      case actionTypes.StateemulatePerson:
        sessionStorage.setItem('emulateEmailId', $event.element.emailId)
        if ($event.element.active == true) {
          this.http
            .conformuser({}, $event.element.personId)
            .subscribe((data: any) => {
              if (data != null && data.status == "CONFIRMED") {
                this.emulateClient($event.element);
              } else {
                const dialogRef = this.dialog.open(popupcomponent, {
                  data: {
                    title: this.lngSrvc.curLangObj.value.emulateTitle,
                    message: this.lngSrvc.curLangObj.value.emulateMessage,
                    OKButton: this.lngSrvc.curLangObj.value.ok,
                  },
                });
              }
            });
        } else {
          this.snackbar.callSnackbaronError(
            "The user is not allowed to emulate"
          );
        }
        break;
      case actionTypes.email:
        let userDetails = {};
        this.http
          .conformuser(userDetails, $event.element.personId)
          .subscribe((data: any) => {
            if (data != null && data) {
              const dialogRef = this.dialog.open(popupcomponent, {
                data: {
                  title:
                    data.status == "CONFIRMED"
                      ? this.lngSrvc.curLangObj.value.user + " " + data.status
                      : this.lngSrvc.curLangObj.value.users,
                  message:
                    data.status == "CONFIRMED"
                      ? null
                      : this.lngSrvc.curLangObj.value.inivite_email,
                  cancelButton:
                    data.status == "CONFIRMED"
                      ? this.lngSrvc.curLangObj.value.ok
                      : this.lngSrvc.curLangObj.value.no,
                  OkButton:
                    data.status == "CONFIRMED"
                      ? null
                      : this.lngSrvc.curLangObj.value.yes,
                  OKButton:
                    data.status == "CONFIRMED"
                      ? this.lngSrvc.curLangObj.value.ok
                      : null,
                },
              });
              dialogRef.afterClosed().subscribe((data) => {
                if (
                  data.confirmed == true ||
                  data.confirmed.confirmed == true
                ) {
                  var requestDetailsClone = lodash.cloneDeep(
                    $event.action.requestDetails[0]
                  );
                  this.setParamValues(requestDetailsClone, $event);
                  this.store.dispatch(
                    genericDelete({
                      performedAction: $event,
                      requestDetails: requestDetailsClone,
                      widgetRowIndex: rowIndex,
                      widgetColumnIndex: columnIndex,
                    })
                  );
                  setTimeout(() => {
                    this.snackbar.callSnackbaronSuccess(
                      "Email Sent Successfully"
                    );
                  }, 2500);
                }
              });
            }
          });
        break;
      case actionTypes.AddFormView:
        if ($event.element.active == true) {
          const dialogRef1 = this.dialog.open(AddFormsComponent, {
            data: {
              datadetails: $event.element,
              title: this.lngSrvc.curLangObj.value.deletePage,
              message: this.lngSrvc.curLangObj.value.deleteMsg,
              cancelButton: this.lngSrvc.curLangObj.value.cancel,
              OKButton: this.lngSrvc.curLangObj.value.cancel,
              OkButton: this.lngSrvc.curLangObj.value.delete,
            },
          });
        } else {
          this.snackbar.callSnackbaronError(
            "Please enable the status to edit the form"
          );
        }
        // dialogRef1.afterClosed().subscribe(data => {

        //   if (data && data.confirmed) {
        //     var requestDetailsClone = lodash.cloneDeep($event.action.requestDetails[0]);
        //     this.setParamValues(requestDetailsClone, $event);

        //     this.store.dispatch(genericDelete({
        //       performedAction: $event,
        //       requestDetails: requestDetailsClone,
        //       widgetRowIndex: rowIndex,
        //       widgetColumnIndex: columnIndex
        //     }));
        //   }
        // })

        break;
      case actionTypes.Voucher:
        if (this.globalUserService.active == true) {
          this.router.navigateByUrl(
            `/voucher/${$event.element.personId}/${this.globalUserService.stateId}/${this.globalUserService.trainingId}/${$event.element.personTenantRoleId}`
          );
          let temp = {
            personId: $event.element.personId,
            stateId: this.globalUserService.stateId,
            training: this.globalUserService.trainingId,
            personTentantRole: $event.element.personTenantRoleId,
          };

          this.globalUserService.details.next(temp);
        } else {
          this.snackbar.callSnackbaronError(
            "The user is not allowed to generate the voucher"
          );
        }

        break;
      case actionTypes.Employee_delete:
        const dialog = this.dialog.open(popupcomponent, {
          data: {
            title: `${$event.element.firstName} ${$event.element.lastName} ${
              $event.element.middleName != null ? $event.element.middleName : ""
            }`,
            message: this.lngSrvc.curLangObj.value.delete_employee,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
          },
        });
        dialog.afterClosed().subscribe((data) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            this.https
              .delete(
                environment.baseUrl +
                  `client/api/api/Employer/employee?employeeId=${$event.element.personTenantRoleId}`
              )
              .subscribe(
                (data: any) => {
                  if (data) {
                    this.snackbar.callSnackbaronSuccess("Deleted SuccessFully");
                    this.refreshData(0, 0);
                  }
                },
                (error: any) => {
                  this.snackbar.callSnackbaronError(
                    `${error.message.message.error}`
                  );
                }
              );
          }
        });
        break;
      case actionTypes.Refresh_Noshow_to_cancel:
        const dialogNoshow = this.dialog.open(popupcomponent, {
          data: {
            title: `${$event.element.examName}`,
            message: this.lngSrvc.curLangObj.value.No_show,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
          },
        });
        dialogNoshow.afterClosed().subscribe((data: any) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            this.https
              .delete(
                environment.baseUrl +
                  `client/api/operationstaff/exam/cancel-for-no-show?examScheduleId=${
                    $event.element.id
                  }&candidatePersonTenentRoleId=${
                    $event.element.personTenantRoleId
                  }&createdBy=${
                    this.globalUserService.userDetails.getValue().personId
                  }&comment=${this.lngSrvc.curLangObj.value.Comment}`
              )
              .subscribe(
                (data: boolean) => {
                  if (data) {
                    this.snackbar.callSnackbaronSuccess(
                      "Status Changed SuccessFully"
                    );
                    this.refreshData(1, 0);
                  }
                },
                (error: any) => {
                  this.snackbar.callSnackbaronError(
                    `${error.message.message.error}`
                  );
                }
              );
          }
        });
        break;
      case actionTypes.Roast:
        if ($event.element.active == true) {
          this.router.navigateByUrl("/Skills");
        }

      case actionTypes.Refund:
        // const dialogRefund = this.dialog.open(TransactionListPopupComponent, {
        const dialogRefund = this.dialog.open(PaymentRefundPopupComponent, {
          data: { event: $event.element, id: 1, message: "Refund" },
        });
        dialogRefund.afterClosed().subscribe((data: any) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            const items = {
              transAmount: data.transAmount,
              description: data.description,
              transId: $event.element.transaction?.transId,
              orderId: $event.element.order?.orderId,
              personTenantRoleId: data.personTenantRoleId,
              capturedId: data.captureId,
            };
            data.title == "Order Id"
              ? this.http.refundPayPayment(items).subscribe({
                  next: (response: any) => {
                    if (response.message === "Successfully processed refund") {
                      this.snackbar.callSnackbaronSuccess(
                        "Successfully processed refund"
                      );
                      this.refreshData(0, 0);
                    }
                  },
                  error: (error: any) => {
                    this.snackbar.callSnackbaronError(
                      `${error.message.message.error}`
                    );
                  },
                })
              : this.http.refundPayment(items).subscribe({
                  next: (response: any) => {
                    if (response.message === "Successfully processed refund") {
                      this.snackbar.callSnackbaronSuccess(
                        "Successfully processed refund"
                      );
                      this.refreshData(0, 0);
                    }
                  },
                  error: (error: any) => {
                    this.snackbar.callSnackbaronError(
                      `${error.message.message.error}`
                    );
                  },
                });
          }
        });

        break;
      case actionTypes.Elapsed:
        // const dialogRefund = this.dialog.open(TransactionListPopupComponent, {
        const dialogElapsed = this.dialog.open(PaymentRefundPopupComponent, {
          data: { id: 4, elapseDateTime: $event.element.elapseDateTime },
        });
        dialogElapsed.afterClosed().subscribe((data: any) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            const body = {
              personFormId: $event.element.personFormId,
              elapsedDate: data.elapsedDate,
              notes: data.notes,
              personTenantRoleId:
                this.globalUserService.userDetails.getValue()
                  .personTenantRoleId,
            };

            this.http.UpdateElapsedDate(body).subscribe(
              (data: any) => {
                if (data) {
                  this.snackbar.callSnackbaronSuccess(`${data.message}`);
                  this.refreshData(1, 0);
                }
              },
              (err: HttpErrorResponse | any) => {
                this.snackbar.callSnackbaronError(
                  err.message.message.error.errors.notes[0]
                );
              }
            );
          }
        });
        break;
      case actionTypes.Transfer_Candidate:
        // const dialogRefund = this.dialog.open(TransactionListPopupComponent, {
        const dialogTransfer = this.dialog.open(EmployeesPopupComponent, {
          data: {
            id: "transfer_details",
            ExamDetails: $event.element,
            navigate: this.globalUserService.navigate,
          },
        });
        dialogTransfer.afterClosed().subscribe((data: any) => {
          // if ((data.confirmed == true || data.confirmed.confirmed == true)) {
          //   const body = {
          //     personFormId: $event.element.personFormId,
          //     elapsedDate: data.elapsedDate,
          //     notes: data.notes,
          //     personTenantRoleId: this.globalUserService.userDetails.getValue().personTenantRoleId
          //   }
          //   this.http.UpdateElapsedDate(body).subscribe((data: any) => {
          //     if (data) {
          //       this.snackbar.callSnackbaronSuccess(`${data.message}`);
          //       this.refreshData(1, 0)
          //     }
          //   },
          //     (err: HttpErrorResponse | any) => {
          //       this.snackbar.callSnackbaronError(err.message.message.error.errors.notes[0]);
          //     })
          // }
        });
        break;

      case actionTypes.omrsheetview:
        if ($event.action.icon == "credit_score") {
          this.openDialogToReEvaluate($event.action.icon, $event.element);
        } else {
          this.https
            .get(
              `${environment.baseUrl}client/api/form/GetArkivDocument/${$event.element.id}`
            )
            .subscribe((data: any) => {
              if (data) {
                let pdf = data.arkivOMRSheets.filter(
                  (x) => x.extension === "pdf"
                );
                let images = data.arkivOMRSheets.filter(
                  (x) => x.extension === "image"
                );
                if (
                  data.arkivOMRSheets.length > 0 &&
                  $event.action.icon != "featured_video"
                ) {
                  if (
                    pdf.length > 0 &&
                    $event.action.icon == "downpicture_as_pdfload"
                  ) {
                    const dialogsheet = this.dialog.open(OMRVideoImage, {
                      data: {
                        actiontype: $event.action.icon,
                        element: { personEventId: $event.element.id },
                      },
                      width: "400px",
                      height: "500px",
                    });
                    dialogsheet.afterClosed().subscribe((data) => {
                      // this.EventType({ eventSku: data.clientId })
                    });
                  } else if (images.length && $event.action.icon == "image") {
                    const dialogsheet = this.dialog.open(OMRVideoImage, {
                      data: {
                        actiontype: $event.action.icon,
                        element: { personEventId: $event.element.id },
                      },
                      width: "400px",
                      height: "500px",
                    });
                    dialogsheet.afterClosed().subscribe((data) => {
                      // this.EventType({ eventSku: data.clientId })
                    });
                  } else {
                    this.snackbar.callSnackbaronWarning(
                      $event.action.icon == "downpicture_as_pdfload"
                        ? "Pdf  is not available."
                        : " Image is not available."
                    );
                  }
                } else if (
                  data.arkivVideos.length > 0 &&
                  $event.action.icon == "featured_video"
                ) {
                  const dialogsheet = this.dialog.open(OMRVideoImage, {
                    data: {
                      actiontype: $event.action.icon,
                      element: { personEventId: $event.element.id },
                    },
                    width: "400px",
                    height: "500px",
                  });
                  dialogsheet.afterClosed().subscribe((data) => {
                    // this.EventType({ eventSku: data.clientId })
                  });
                }
                // else if (data.arkivVideos.length > 0 && $event.action.icon == 'credit_score') {
                //   let answerSheetPDF = (data.arkivOMRSheets.filter((x) => x.extension == 'pdf'))[0];
                //   this.openDialogToReEvaluate(answerSheetPDF);
                // }
                else {
                  this.snackbar.callSnackbaronWarning(
                    $event.action.icon == "featured_video"
                      ? "Shredding video is not available."
                      : "Pdf or Image is not available."
                  );
                }
              }
            });
        }

        break;

      case actionTypes.Import:
        const dialogImport = this.dialog.open(OMRVideoImage, {
          data: {
            actiontype: $event.action.icon,
            element: {
              isProcessed: $event.element.isProcessed,
              Error: $event.element.errorMessage,
            },
          },
          width: "400px",
        });
        dialogImport.afterClosed().subscribe((data) => {
          // this.EventType({ eventSku: data.clientId })
        });

        break;
      case actionTypes.Bookletview:
        const bookletdialog = this.dialog.open(OMRVideoImage, {
          data: { actiontype: "booklet_view", element: $event.element },
          width: "550px",
        });
        bookletdialog.afterClosed().subscribe((data) => {
          // this.EventType({ eventSku: data.clientId })
        });
        break;
      case actionTypes.Refund_payment:
        const dialogRefu = this.dialog.open(PaymentRefundPopupComponent, {
          data: {
            id: 80,
            personEventId: $event.element.id,
          },
          width: "400px",
        });
        dialogRefu.afterClosed().subscribe((data) => {
          if (data.confirmed == true) {
            this.refreshData(1, 0);
          }
        });
        break;
      case actionTypes.Certificdown:
        $event.element.stateId === StateLists.PA &&
        $event.element?.registryDetail?.registryStatusId == 4 &&
        this.curModuleId != "regular-certificate"
          ? this.https
              .post(
                `${environment.baseUrl}registry/api/Certificate/view-cetificatewithDetail?registryId=${$event.element.registryDetail.personFormRegistryId}&certificateTypeId=${$event.element.registryDetail.certType}`,
                {},
                { responseType: "text" }
              )
              .pipe()
              .subscribe(
                (data) => {
                  if (data) {
                    const printWindow = window.open("", "_blank");
                    printWindow.document.open();
                    printWindow.document.write(`
                                    ${data}
                        `);
                    printWindow.document.close();

                    const images: any =
                      printWindow.document.getElementsByTagName("img");
                    const totalImages = images.length;
                    let loadedImagesCount = 0;

                    const handleImageLoad = () => {
                      loadedImagesCount++;
                      if (loadedImagesCount === totalImages) {
                        printWindow.print();
                        setTimeout(() => {
                          printWindow.close();
                        }, 500);
                      }
                    };

                    for (let img of images) {
                      img.onload = handleImageLoad;
                      img.onerror = handleImageLoad; // Count errors as loaded
                    }

                    // Print immediately if there are no images
                    if (totalImages === 0) {
                      printWindow.print();
                      setTimeout(() => {
                        printWindow.close();
                      }, 500);
                    }
                  }
                },
                (error) => {
                  console.error("Error fetching certificate:", error);
                }
              )
          : this.curModuleId == "regular-certificate" &&
            $event.element.stateId === StateLists.PA &&
            $event.element.registryStatusId == 4
          ? this.https
              .post(
                `${environment.baseUrl}registry/api/Certificate/view-cetificatewithDetail?registryId=${$event.element.registryId}&certificateTypeId=${$event.element.certType}`,
                {},
                { responseType: "text" }
              )
              .pipe()
              .subscribe(
                (data) => {
                  if (data) {
                    const printWindow = window.open("", "_blank");
                    printWindow.document.open();
                    printWindow.document.write(`
                                    ${data}
                        `);
                    printWindow.document.close();

                    const images: any =
                      printWindow.document.getElementsByTagName("img");
                    const totalImages = images.length;
                    let loadedImagesCount = 0;

                    const handleImageLoad = () => {
                      loadedImagesCount++;
                      if (loadedImagesCount === totalImages) {
                        printWindow.print();
                        setTimeout(() => {
                          printWindow.close();
                        }, 500);
                      }
                    };

                    for (let img of images) {
                      img.onload = handleImageLoad;
                      img.onerror = handleImageLoad; // Count errors as loaded
                    }

                    // Print immediately if there are no images
                    if (totalImages === 0) {
                      printWindow.print();
                      setTimeout(() => {
                        printWindow.close();
                      }, 500);
                    }
                  }
                },
                (error) => {
                  console.error("Error fetching certificate:", error);
                }
              )
          : this.snackbar.callSnackbaronError(
              "Inactive certificate are not allowed to download"
            );
        break;
      case actionTypes.Preregistration_view:
        this.https
          .get(
            `${environment.baseUrl}client/api/traininginstitute/uploaded-candidate/${$event.element.trainingProgramId}`
          )
          .subscribe(
            (data: { result: Array<object> }) => {
              if (data.result.length > 0) {
                const dialogPreregistration_view = this.dialog.open(
                  OMRVideoImage,
                  {
                    data: {
                      actiontype: $event.action.icon,
                      element: { id: data.result },
                    },
                    width: "1220px",
                  }
                );
                dialogPreregistration_view.afterClosed().subscribe((data) => {
                  // this.EventType({ eventSku: data.clientId })
                });
              } else {
                this.snackbar.callSnackbaronWarning("NO RECORD FOUND");
              }
            },
            (err) => {
              this.snackbar.callSnackbaronError(`${err.message.message.error}`);
            }
          );
        break;
      case actionTypes.suspension_List_remove:
        const dialogSuspension_email = this.dialog.open(popupcomponent, {
          data: {
            title: `${$event.element.emailId}`,
            message: this.lngSrvc.curLangObj.value.remove_suspension,
            cancelButton: this.lngSrvc.curLangObj.value.cancel,
            OKButton: this.lngSrvc.curLangObj.value.cancel,
            OkButton: this.lngSrvc.curLangObj.value.save,
          },
        });
        dialogSuspension_email.afterClosed().subscribe((data: any) => {
          if (data.confirmed == true || data.confirmed.confirmed == true) {
            this.https
              .post(
                `${environment.baseUrl}client/api/operationstaff/remove-suppression?emailId=${$event.element.emailId}`,
                {}
              )
              .subscribe(
                (data) => {
                  if (data) {
                    this.refreshData(0, 0);
                  }
                },
                (err: HttpErrorResponse | any) => {
                  this.snackbar.callSnackbaronError(
                    `${err.message.error.text}`
                  );
                }
              );
          }
        });

      //  scheduleExam/${this.candidateId}/payment/page
    }
  }

  handleServerSidePagination(
    event: tablePageEvent,
    rowIndex: number,
    columnIndex: number
  ) {
    var tempLoadingArray = [];
    for (let i = 0; i < event.pageEvent.pageSize; i++)
      tempLoadingArray.push({});
    this.tableData = new MatTableDataSource<any>(tempLoadingArray);
    var widgetClone = lodash.cloneDeep(
      this.curNestingType == nestingTypes.ColumnsInRows
        ? this.consoleLoaderJson.modules[this.curModuleIndex].rows[rowIndex]
            .columns[columnIndex].widget
        : this.consoleLoaderJson.modules[this.curModuleIndex].columns[
            columnIndex
          ].rows[rowIndex].widget
    );
    var indexOfPageIndex = widgetClone.requestDetails[
      this.tabIndexStream.value
    ].requestParams.findIndex((x: requestParam) => {
      return x.paramName == "pageNo" || x.paramName == "pageNumber";
    });
    var indexOfPageSize = widgetClone.requestDetails[
      this.tabIndexStream.value
    ].requestParams.findIndex((x: requestParam) => {
      return x.paramName == "pageSize";
    });

    if (event.searchText && event.searchText != " ") {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "searchText",
        paramValue: event.searchText,
      });
      if (event.searchColumn) {
        widgetClone.requestDetails[
          this.tabIndexStream.value
        ].requestParams.push({
          paramType: paramTypes.Optional,
          paramName: "searchColumn",
          paramValue: event.searchColumn,
        });
      }
    }
    widgetClone.requestDetails[this.tabIndexStream.value].requestParams[
      indexOfPageIndex
    ].paramValue = event.pageEvent.pageIndex;
    widgetClone.requestDetails[this.tabIndexStream.value].requestParams[
      indexOfPageSize
    ].paramValue = event.pageEvent.pageSize;
    this.setParamValues(widgetClone.requestDetails[this.tabIndexStream.value]);
    if (this.selectedStateId) {
      if (
        (this.consoleLoaderJson.modules[this.curModuleIndex].moduleID ==
          ModuleIdName.Manage_Candidate &&
          this.globalUserService.userDetails.value.roleId ===
            Roles.SupportingStaff &&
          this.globalUserService.userDetails.value.stateId != null) ||
        (this.consoleLoaderJson.modules[this.curModuleIndex].moduleID ==
          ModuleIdName.Training_Institute &&
          this.globalUserService.userDetails.value.roleId ===
            Roles.OperationStaff &&
          this.globalUserService.userDetails.value.stateId != null)
      ) {
        widgetClone.requestDetails[this.tabIndexStream.value].requestParams =
          widgetClone.requestDetails[
            this.tabIndexStream.value
          ].requestParams.filter((x) => x.paramName != "stateId");
      }
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "stateId",
        paramValue: this.selectedStateId,
      });
    }

    if (this.selectedbookletStatus) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName:
          this.curModuleId == "manage-grievence"
            ? "grievanceStatusId"
            : this.curModuleId == "candidate-search-slot"
            ? "eventStatusId"
            : "orderStatusId",
        paramValue: this.selectedbookletStatus,
      });
    }

    if (this.selectedExamMode && this.curModuleId == "candidate-search-slot") {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "examModeId",
        paramValue: this.selectedExamMode,
      });
    }

    if (
      this.selectedTestCenterPreference &&
      this.curModuleId == "candidate-search-slot"
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "testCenterPreference",
        paramValue: this.selectedTestCenterPreference,
      });
    }

    if (
      (this.SelectedDateRange?.FormDate &&
        this.SelectedDateRange?.todate &&
        this.SelectedDateRange?.tableselected == "Approved") ||
      (this.SelectedDateRange?.FormDate &&
        this.SelectedDateRange?.todate &&
        (this.curModuleId == "manage_nae" ||
          this.curModuleId == "regular-certificate" ||
          this.curModuleId == "Pre-RegisterCandidate" ||
          this.curModuleId == "booklet_inventory" ||
          this.curModuleId == "candidate-search-slot"))
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push(
        {
          paramType: paramTypes.Optional,
          paramName:
            this.curModuleId == "Pre-RegisterCandidate"
              ? "createdOnStartDate"
              : this.curModuleId == "booklet_inventory"
              ? "startDate"
              : this.curModuleId == "candidate-search-slot"
              ? "slotStartDate"
              : "fromDate",
          paramValue: this.SelectedDateRange.FormDate,
        },
        {
          paramType: paramTypes.Optional,
          paramName:
            this.curModuleId == "Pre-RegisterCandidate"
              ? "createdOnEndDate"
              : this.curModuleId == "booklet_inventory"
              ? "endDate"
              : this.curModuleId == "candidate-search-slot"
              ? "slotEndDate"
              : "toDate",
          paramValue: this.SelectedDateRange.todate,
        }
      );
    }
    if (
      this.SelectedReviewDateRange?.ReviewFormDate &&
      this.SelectedReviewDateRange?.Reviewtodate &&
      this.SelectedReviewDateRange?.tableselected == "Approved" &&
      this.curModuleId == "duplicate-certificates"
    ) {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push(
        {
          paramType: paramTypes.Optional,
          paramName: "reviewFromDate",
          paramValue: this.SelectedReviewDateRange.ReviewFormDate,
        },
        {
          paramType: paramTypes.Optional,
          paramName: "reviewToDate",
          paramValue: this.SelectedReviewDateRange.Reviewtodate,
        }
      );
    }

    if (this.selectedScore !== "") {
      widgetClone.requestDetails[this.tabIndexStream.value].requestParams.push({
        paramType: paramTypes.Optional,
        paramName: "IsScoreReceived",
        paramValue: this.selectedScore,
      });
    }

    this.store.dispatch(
      fetchData({
        widgetType: widgetClone.type,
        moduleIndex: this.curModuleIndex,
        requestDetails: widgetClone.requestDetails[this.tabIndexStream.value],
      })
    );
  }

  fetchCompleteModuleData() {
    if (this.curNestingType == nestingTypes.ColumnsInRows)
      for (let row of this.consoleLoaderJson.modules[this.curModuleIndex].rows)
        for (let column of row.columns)
          this.processAndDispatchFetchAction(column.widget);
    else
      for (let column of this.consoleLoaderJson.modules[this.curModuleIndex]
        .columns)
        for (let row of column.rows)
          this.processAndDispatchFetchAction(row.widget);
  }

  processAndDispatchFetchAction(widget: widget) {
    if (widget.backwardsDataDependency && !this.recentLinkClickEvent) {
      this.router.navigate([
        "loader",
        this.processReferences(widget.grandParentReference),
      ]);
    } else {
      var requestDetailsClone = lodash.cloneDeep(widget.requestDetails);
      for (let reqDet of requestDetailsClone) this.setParamValues(reqDet);
      const requestDetails =
        widget.type == widgetTypes.DynamicForms
          ? requestDetailsClone
          : requestDetailsClone[0];

      this.store.dispatch(
        fetchData({
          widgetType: widget.type,
          moduleIndex: this.curModuleIndex,
          requestDetails,
        })
      );
    }
  }
  setParamValues(
    xArr: requestDetails,
    clickEvent: cellClickEvent | performedAction = null
  ) {
    xArr?.requestParams?.forEach(async (x: requestParam) => {
      if (x.extractedFromElement) {
        x.paramValue = clickEvent
          ? clickEvent.element[x.elementPropertyToBeExtracted]
          : this.recentLinkClickEvent.element[x.elementPropertyToBeExtracted];

        if (
          (x.paramName == "trainingInstituteId" ||
            x.paramName == "id" ||
            x.paramName == null) &&
          (JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify([
              "client",
              "api",
              "traininginstitute",
              "eligibilityroutes",
            ]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify([
                "client",
                "api",
                "traininginstitute",
                "candidates",
              ]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify(["client", "api", "traininginstitute", "user"]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify([
                "client",
                "api",
                "traininginstitute",
                "admins",
              ])) &&
          (this.globalUserService.trainingId != undefined ||
            this.globalUserService.trainingId != null)
        ) {
          x.paramValue =
            this.globalUserService.trainingId != 0
              ? this.globalUserService.trainingId
              : this.globalUserService.traningInstitute;
        } else if (
          x.elementPropertyToBeExtracted == "id" &&
          x.paramName == null &&
          x.paramType == paramTypes.Required &&
          x.paramValue == undefined &&
          (JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "traininginstitute", "admins"]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify([
                "client",
                "api",
                "traininginstitute",
                "eligibilityroutes",
              ]))
        ) {
          x.paramValue = this.globalUserService.traningInstitute;
        } else if (
          (x.paramName === "registryId" &&
            x.paramType == "optional" &&
            (x.paramValue == null ||
              x.paramValue == undefined ||
              x.paramValue == "") &&
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify([
                "registry",
                "api",
                "certificate",
                "get-details",
              ])) ||
          (x.paramName === "registryId" &&
            x.paramType == "optional" &&
            (x.paramValue == null ||
              x.paramValue == undefined ||
              x.paramValue == "") &&
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify(["registry", "api", "certificate", "get-details"]))
        ) {
          x.paramValue =
            (this.curModuleId == "regular-certificate" ||
              this.curModuleId == null) &&
            this.globalUserService?.personEventId?.registryDetail == null
              ? this.globalUserService?.personEventId?.registryId
              : this.globalUserService?.personEventId?.registryDetail == null
              ? 909
              : this.globalUserService?.personEventId?.registryDetail
                  ?.personFormRegistryId;
        } else if (
          x.elementPropertyToBeExtracted == "id" &&
          (x.paramName == "id" || x.paramName == "trainingInstituteId") &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          (JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify([
              "client",
              "api",
              "traininginstitute",
              "candidates",
            ]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify(["client", "api", "traininginstitute", "user"]))
        ) {
          x.paramValue = this.globalUserService.traningInstitute;
        } else if (
          x.elementPropertyToBeExtracted == "personTenantRoleId" &&
          x.paramName == "personTenantRoleId" &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "account", "getusers"])
        ) {
          x.paramValue =
            this.globalUserService.personEventId.PersonTenantRoleId;
        } else if (
          (x.elementPropertyToBeExtracted == "personTenantRoleId" ||
            x.elementPropertyToBeExtracted == "PersonId") &&
          (x.paramName == "personTenantRoleId" || x.paramName == "Id") &&
          x.paramType == paramTypes.Optional &&
          (x.paramValue == undefined || x.paramName == null) &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "account", "getuser"])
        ) {
          x.paramValue =
            this.curModuleId != "regular-certificate" &&
            this.curModuleId != null
              ? this.globalUserService?.personEventId?.PersonTenantRoleId
              : this.globalUserService?.personEventId?.personTenantRoleId;
        } else if (
          x.elementPropertyToBeExtracted == "personId" &&
          x.paramName == "personId" &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["registry", "api", "certificate", "get-all"])
        ) {
          x.paramValue = this.globalUserService.personEventId.PersonId;
        } else if (
          x.elementPropertyToBeExtracted == "personFormRegistryId" &&
          x.paramName == "registryId" &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["registry", "api", "certificate", "get-details"])
        ) {
          x.paramValue = this.globalUserService.RegistryID;
        } else if (
          (x.elementPropertyToBeExtracted == "formTypeId" ||
            x.elementPropertyToBeExtracted == "formTenantId" ||
            x.elementPropertyToBeExtracted == "eligibilityRouteId" ||
            x.elementPropertyToBeExtracted == "stateId") &&
          (x.paramName == "formTypeId" ||
            x.paramName == "stateId" ||
            x.paramName == "eligibilityId") &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "form", "formsbyformtypeid"])
        ) {
          x.paramValue =
            x.elementPropertyToBeExtracted == "formTypeId"
              ? this.globalUserService.formTypeId
              : x.elementPropertyToBeExtracted == "formTenantId" ||
                x.elementPropertyToBeExtracted == "stateId"
              ? this.globalUserService.personEventId.StateId
              : x.elementPropertyToBeExtracted == "eligibilityRouteId"
              ? 0
              : null;
        } else if (
          x.elementPropertyToBeExtracted == "personFormId" &&
          x.paramName == "personFormId" &&
          x.paramType == paramTypes.Optional &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "form", "personform", "list"])
        ) {
          x.paramValue = this.globalUserService.personFormId;
        } else if (
          x.paramName == null &&
          x.paramName == undefined &&
          this.globalUserService.PersonTentantRoleId != null &&
          this.globalUserService.PersonTentantRoleId != undefined &&
          (JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify([
              "client",
              "api",
              "Candidate",
              "registered-exams",
            ]) ||
            JSON.stringify(xArr.fetchUrl) ==
              JSON.stringify(["client", "api", "Candidate", "forms-filled"]))
        ) {
          x.paramValue = this.globalUserService.PersonTentantRoleId;
        } else if (
          x.paramName == "candidateId" &&
          this.globalUserService.PersonTentantRoleId != null &&
          this.globalUserService.PersonTentantRoleId != undefined &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["customerservice", "api", "tickets"])
        ) {
          x.paramValue = this.globalUserService.PersonTentantRoleId;
        } else if (
          x.elementPropertyToBeExtracted == "employerId" &&
          x.paramName == null &&
          x.paramValue == null &&
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["client", "api", "api", "Employer", "users"])
        ) {
          x.paramValue = this.globalUserService.employerId;
        }
      } else if (x.extractedFromGlobal) {
        x.paramValue =
          this.globalUserService.userDetails.value[
            x.elementPropertyToBeExtracted
          ];
        if (
          JSON.stringify(xArr.fetchUrl) ==
            JSON.stringify(["customerservice", "api", "tickets"]) &&
          x.paramName == "roleId" &&
          this.globalUserService.userDetails.value[
            x.elementPropertyToBeExtracted
          ] == 8
        ) {
          x.paramValue = 8;
        }
      }
      const hasPageSize = xArr.requestParams.find(
        (x) => x.paramName == "pageSize"
      );
      const manageCandidates =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify(["client", "api", "account", "getusers-page"]);
      const manageCertificates =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify(["registry", "api", "certificate", "get-all"]);
      const personFormReview =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify([
          "client",
          "api",
          "eligibility",
          "personformreview",
          "list",
        ]);
      const manageAcomodatitions =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify([
          "client",
          "api",
          "operationstaff",
          "accommodation",
          "candidates",
        ]);
      const manageTrainingPrograms =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify(["client", "api", "traininginstitute", "list"]);
      const supportTickets =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify(["customerservice", "api", "tickets"]);
      const problemReports =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify(["client", "api", "form", "GetProblemReports"]);
      const personFormRegistry =
        JSON.stringify(xArr.fetchUrl) ==
        JSON.stringify([
          "client",
          "api",
          "eligibility",
          "personformregistry",
          "list",
        ]);

      const paginatedRequest =
        personFormRegistry ||
        manageCandidates ||
        manageCertificates ||
        personFormReview ||
        manageAcomodatitions ||
        manageTrainingPrograms ||
        supportTickets ||
        problemReports;
      if (paginatedRequest && hasPageSize) {
        if (x.paramName == "pageSize") {
          this.store.select(getPageSize).subscribe((res) => {
            Object.assign({}, x, {
              paramValue: res.pageSize,
            });
            x.paramValue = res.pageSize;
          });
        }
      }
    });
  }

  // the following can be substituted with something more efficient
  // it is done to prevent loading of the notes-sidebar which keeps
  // on calling the getNotes and the other apis
  setCurWidgetTypes() {
    let tempWidgetTypes = [];
    if (
      this.consoleLoaderJson.modules.length > 0 &&
      this.curNestingType &&
      this.curModuleIndex &&
      this.curModuleId
    ) {
      if (this.curNestingType == nestingTypes.ColumnsInRows)
        for (let row of this.consoleLoaderJson.modules[this.curModuleIndex]
          .rows)
          for (let column of row.columns)
            tempWidgetTypes.push(column.widget.type);
      else
        for (let column of this.consoleLoaderJson.modules[this.curModuleIndex]
          .columns)
          for (let row of column.rows) tempWidgetTypes.push(row.widget.type);
    }
    this.curWidgetTypes.next(tempWidgetTypes);
  }

  openRichTextEditorDialog(): void {
    const dialogRef = this.dialog.open(RichTextEditorDialogComponent, {
      width: "50vw",
      height: "fit-content",
      minWidth: "50vw",
      disableClose: true,
      id: "richTextEditorDialog",
      data: {},
    });

    dialogRef.afterClosed().subscribe((result) => {});
  }

  openSidenavAndSetViewId(event: any) {
    this.notesSidebarViewId = event.whatToOpen;
    if (this.notesSidebarViewId === 3) {
      this.openRichTextEditorDialog();
      // this.sidenavRef.toggle();
    } else {
      this.sidenavRef.toggle();
    }
  }
  processReferences(reference: Map<number, string> | string): string {
    var returnValue = "dashboard";
    if (typeof reference === "string") returnValue = reference;
    else {
      reference.forEach((value: string, key: number) => {
        if (key == this.globalUserService.userDetails.value.roleId) {
          returnValue = value;
        }
      });
    }
    return returnValue;
  }
  findWidgetRowAndColByWidgetType(widgetType: widgetTypes): Array<number> {
    var retValue = [-1, -1];
    if (
      this.consoleLoaderJson.modules.length > 0 &&
      this.curNestingType &&
      this.curModuleIndex &&
      this.curModuleId
    ) {
      if (this.curNestingType == nestingTypes.ColumnsInRows) {
        for (
          let i = 0;
          i < this.consoleLoaderJson.modules[this.curModuleIndex].rows.length;
          i++
        )
          for (
            let j = 0;
            j <
            this.consoleLoaderJson.modules[this.curModuleIndex].rows[i].columns
              .length;
            j++
          )
            if (
              this.consoleLoaderJson.modules[this.curModuleIndex].rows[i]
                .columns[j].widget.type == widgetType
            ) {
              retValue = [i, j];
            }
      } else {
        for (
          let i = 0;
          i <
          this.consoleLoaderJson.modules[this.curModuleIndex].columns.length;
          i++
        )
          for (
            let j = 0;
            j <
            this.consoleLoaderJson.modules[this.curModuleIndex].columns[i].rows
              .length;
            j++
          )
            if (
              this.consoleLoaderJson.modules[this.curModuleIndex].columns[i]
                .rows[j].widget.type == widgetType
            ) {
              retValue = [i, j];
            }
      }
    }
    return retValue;
  }
  raiseTickets() {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointobserver
      .observe(["(min-width : 1024px)"])
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = "60vw";
          dialogConfig.minHeight = "20vw";
          // dialogConfig.maxWidth = '47vw';
          dialogConfig.maxHeight = "90vh";
        } else {
          dialogConfig.minWidth = "90vw";
          dialogConfig.minHeight = "90vh";
        }
      });
    const dialogRef = this.dialog.open(HelpComponent, dialogConfig);
  }

  handleScoreSelection(event: any, rowIndex: number, columnIndex: number) {
    // make api call with stateId...
    this.selectedScore = event.value ? event.value : "";
    this.refreshData(rowIndex, columnIndex);
  }

  handleStateSelection(event: any, rowIndex: number, columnIndex: number) {
    // make api call with stateId...
    this.selectedStateId = event.value ? event.value : null;
    if (event.value == "Clear Selection" || event.value == null) {
      this.refreshData(rowIndex, columnIndex);
    } else if (typeof event.value == "number") {
      this.refreshData(rowIndex, columnIndex);
    }
  }

  bookletorderStatusSelect($event) {
    this.selectedbookletStatus = $event.value;
    $event.value != "all"
      ? (this.selectedbookletStatus = $event.value)
      : (this.selectedbookletStatus = null);
    this.refreshData();
  }

  examModeSelect($event) {
    this.selectedExamMode = $event.value;
    $event.value != "all"
      ? (this.selectedExamMode = $event.value)
      : (this.selectedExamMode = null);
    this.refreshData();
  }

  testCenterPreferenceSelect($event) {
    this.selectedTestCenterPreference = $event.value;
    $event.value != "all"
      ? (this.selectedTestCenterPreference = $event.value)
      : (this.selectedTestCenterPreference = null);
    this.refreshData();
  }

  handleDateSelection(event: any, rowIndex: number, columnIndex: number) {
    if (event) {
      this.SelectedReviewDateRange = {
        ReviewFormDate: event.reviewFromDate,
        Reviewtodate: event.reviewToDate,
        tableselected: event.tableselected,
      };
      this.SelectedDateRange = {
        FormDate: event.fromDate,
        todate: event.toDate,
        tableselected: event.tableselected,
      };
      this.refreshData(rowIndex, columnIndex);
    }
  }

  disableCandidiate() {
    this.dialog
      .open(DynamicPopupMesageComponent, {
        data: {
          title: this.lngSrvc.curLangObj.value.DeactivateMessage,
          cancelButton: this.lngSrvc.curLangObj.value.no,
          OkButton: this.lngSrvc.curLangObj.value.yes,
        },
      })
      .afterClosed()
      .subscribe((confirmed: any) => {
        if (confirmed == true || confirmed.confirmed == true) {
          this.store.dispatch(
            disableAccount({
              personTenantRoleId:
                this.singleEntryTableData[0].personTenantRoleId,
              personId: this.singleEntryTableData[0].personId,
            })
          );
        }
      });
  }
  mergeRequested(event: accMerge) {
    let reqBody = {
      primaryPersonId: event.selected[0].personId,
      secondaryPersonId: event.selected[1].personId,
    };
    this.http.mergeRequest(reqBody).subscribe(
      (response: boolean) => {
        let msg = `Merge of accounts with ids: ${
          event.selected[0].personId
        } and ${event.selected[1].personId} ${
          response ? "successfull" : "unsuccessfull"
        }.`;
        response
          ? this.snackbar.callSnackbaronSuccess(msg)
          : this.snackbar.callSnackbaronError(msg);
        if (response) {
          this.refreshData(0, 0);
          this.tabIndexStream.next(0);
        }
      },
      (error: any) => {
        this.snackbar.callSnackbaronError(`Merge Unsuccessfull`);
      }
    );
  }

  async BulkCertificate($event) {
    let PersonId = [];
    const Certype =
      this.curModuleId == "reciprocity-request"
        ? 4
        : this.curModuleId == "certificate-renewal"
        ? 2
        : this.curModuleId == "duplicate-certificates"
        ? 5
        : 1;
    $event.selected.forEach((x) => {
      if (
        (x.registryDetail != null &&
          x.registryDetail != undefined &&
          x.registryDetail != "" &&
          this.curModuleId != "regular-certificate") ||
        this.curModuleId == "regular-certificate"
      ) {
        const expiryDate =
          this.curModuleId != "regular-certificate"
            ? new Date(x.registryDetail.expirationDate)
            : new Date(x.expirationDate);
        const today = new Date();
        if (expiryDate >= today) {
          x.stateId == 4 &&
          (this.curModuleId != "regular-certificate"
            ? x.registryDetail.certType
            : x.certType == Certype)
            ? PersonId.push(
                this.curModuleId != "regular-certificate"
                  ? x.registryDetail.personFormRegistryId
                  : x.registryId
              )
            : null;
        }
      }
    });

    if (PersonId.length > 0) {
      this.curModuleId == "duplicate-certificates"
        ? this.https
            .post(
              `${environment.baseUrl}registry/api/Certificate/view-cetificatewithDetails`,
              PersonId,
              { observe: "body", responseType: "blob" as "json" }
            )
            .subscribe(
              (data: any) => {
                if (data) {
                  // Convert the response to a Blob
                  const blobResponse = data as Blob;

                  // Use FileSaver.js to trigger a download
                  FS.saveAs(blobResponse, "PA_Certificate.pdf"); // Save as .pdf
                  this.curModuleId != "regular-certificate"
                    ? this.tabIndexStream.next(1)
                    : this.tabIndexStream.next(0);
                } else {
                  console.error("No data received from the API.");
                }
              },
              (error) => {
                console.error(
                  "Error occurred while downloading the file:",
                  error
                );
              }
            )
        : this.https
            .post(
              `${environment.baseUrl}registry/api/Certificate/view-cetificatewithDetails?certificateTypeId=${Certype}`,
              PersonId,
              { observe: "body", responseType: "blob" as "json" }
            )
            .subscribe(
              (data: any) => {
                if (data) {
                  // Convert the response to a Blob
                  const blobResponse = data as Blob;

                  // Use FileSaver.js to trigger a download
                  FS.saveAs(blobResponse, "PA_Certificate.pdf"); // Save as .pdf
                  this.curModuleId != "regular-certificate"
                    ? this.tabIndexStream.next(1)
                    : this.tabIndexStream.next(0);
                } else {
                  console.error("No data received from the API.");
                }
              },
              (error) => {
                console.error(
                  "Error occurred while downloading the file:",
                  error
                );
              }
            );
    } else {
      this.snackbar.callSnackbaronError("NO active certificate found");
      this.refreshData(0, 0);
      this.curModuleId != "regular-certificate"
        ? this.tabIndexStream.next(1)
        : this.tabIndexStream.next(0);
    }
  }
  checkStatus() {
    let userDetails: {};
    this.http
      .checkStatus(userDetails, this.globalUserService.PersonId)
      .subscribe(
        (data: any) => {
          if (data != null && data) {
            this.Noconfirm = data;
            const accountVerified =
              data.status == this.lngSrvc.curLangObj.value.confirmed;
            // const message = accountVerified ? 'CONFIRMED' : 'UNCONFIRMED';

            this.dialog
              .open(popupcomponent, {
                data: {
                  title: accountVerified
                    ? this.lngSrvc.curLangObj.value.user + `${data.status}`
                    : this.lngSrvc.curLangObj.value.users,
                  message: accountVerified
                    ? null
                    : this.lngSrvc.curLangObj.value.status,
                  cancelButton: accountVerified
                    ? null
                    : this.lngSrvc.curLangObj.value.no,
                  OkButton: accountVerified
                    ? null
                    : this.lngSrvc.curLangObj.value.yes,
                  OKButton: accountVerified
                    ? this.lngSrvc.curLangObj.value.ok
                    : null,
                },
              })
              .afterClosed()
              .subscribe((confirmed: any) => {
                if (confirmed == true || confirmed.confirmed == true) {
                  this.http.confirmuser(userDetails).subscribe((data) => {
                    if (data != null) {
                      this.snackbar.callSnackbaronSuccess("User Got Confirmed");
                    }
                  });
                }
              });
          }
        },
        (error: any) => {
          this.snackbar.callSnackbaronError(error.message.message.error);
        }
      );
  }
  candidateRecord() {
    this.http.getUser(this.persontentantRole).subscribe((data: any) => {
      if (data != null && data) {
        this.dialog.open(CandidateRecordPopupComponent, {
          data: data,
        });
      }
    });
  }
  emulateCandidate() {
    if (sessionStorage.getItem("UserData") !== null) {
      sessionStorage.removeItem("UserData");
    }
    this.store.dispatch(
      setPopupConfig({
        popupConfig: {
          type: popupTypes.EmulateCandidate,
          popupDataType: popupDataTypes.FetchedFromApi,
          popupData: {
            element: this.recentLinkClickEvent,
          },
        },
      })
    );
  }

  openConfirmation(status) {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.hasBackdrop = true;
    this.breakpointobserver
      .observe(["(min-width : 1024px)"])
      .subscribe((result: { matches: any }) => {
        if (result.matches) {
          dialogConfig.minWidth = "40vw";
          dialogConfig.minHeight = "fit-content";
          dialogConfig.maxWidth = "40vw";
        } else {
          dialogConfig.minWidth = "90vw";
          dialogConfig.minHeight = "90vh";
        }
      });
    dialogConfig.data = {
      type: formActionTypes[status],
      message: confirmationMessage[status],
      value: formActionTypes[status] == "Approved" ? 1 : 2,
    };

    const dialogRef = this.dialog.open(
      ConfirmationPopupComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((data) => {
      const value = data;
      if (data && data.confirmed) {
        let body = {
          personEventMisconductInfoId: 0,
          personEventId: this.recentLinkClickEvent.element.personEventId,
          incidentTypeId: this.recentLinkClickEvent.element.incidentTypeId,
          misconductStatus: data.value === 1 ? 1 : 0,
          active: true,
          IncidentDescription: null,
          IncidentComments: data.message,
          IncidentStatus: 1,
          createdBy: this.globalUserService.personId,
          createdOn: new Date().toISOString(),
        };
        this.https
          .post(
            `${environment.baseUrl}schedulemsvc/api/scheduler/SavePersonEventMisconductInfo`,
            body,
            {
              responseType: "text" as "json", // or use 'text' as 'json' if response is plain text
            }
          )
          .subscribe(
            (data) => {
              if (data) {
                this.router.navigateByUrl("/loader/misconduct");
                this.snackbar.callSnackbaronSuccess(`${data}`);
              }
            },
            (err) => {
              this.snackbar.callSnackbaronError(`${err.mesage.message.error}`);
            }
          );
      }
    });
  }

  emulateClient(item) {
    this.store.dispatch(
      setPopupConfig({
        popupConfig: {
          type: popupTypes.EmulateClient,
          popupDataType: popupDataTypes.FetchedFromApi,
          popupData: {
            element: item,
          },
        },
      })
    );
  }
  // checkStatus(){
  //   this.store.dispatch(setPopupConfig({
  //     popupConfig: {
  //       type: popupTypes.Changestatus,
  //       popupDataType: popupDataTypes.FetchedFromApi,
  //       popupData: {
  //         element: this.recentLinkClickEvent
  //       }
  //     }
  //   }))
  // }

  getclear() {
    let body = {};
    this.http.getclear(body).subscribe(
      (data: any) => {
        if (data) {
          this.refreshData(1, 0);
          this.snackbar.callSnackbaronSuccess(
            "Cleared Cart Items SuccessFully"
          );
        }
      },
      (error: any) => {
        this.snackbar.callSnackbaronError(error.message.message.error);
      }
    );
  }

  storeSelectedPagination($event) {
    this.preSelecetedIndex = $event.preSelectedIndex;
  }
  ngOnDestroy() {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}

export enum confirmationMessage {
  Approved = "Are you sure you want to approve this application?",
  Rejected = "Are you sure you want to reject this application?",
  ChangeRequest = "Are you sure you want to create a Change Request",
}
export enum formActionTypes {
  Approved = "Approved",
  Rejected = "Rejected",
  ChangeRequest = "Change Request",
}
